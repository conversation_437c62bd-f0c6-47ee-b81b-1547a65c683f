apply plugin: 'com.android.library'
//apply plugin: 'com.novoda.bintray-release'
android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName
    }
}
dependencies {
}
//publish {
//    userOrg = bintray.userOrg
//    groupId = bintray.groupId
//    artifactId = 'wifimanager'
//    publishVersion = bintray.publishVersion
//    desc = bintray.desc
//    website = bintray.website
//}