<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="UserIconView">
        <!--默认头像-->
        <attr name="default_image" format="reference" />
        <attr name="image_radius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="SynthesizedImageView">
        <!--合成图片的背景-->
        <attr name="synthesized_image_bg" format="color" />
        <!--合成图片的默认图片-->
        <attr name="synthesized_default_image" format="reference" />
        <!--合成图片的尺寸-->
        <attr name="synthesized_image_size" format="dimension" />
        <!--多图片之间的空隙间隔-->
        <attr name="synthesized_image_gap" format="dimension" />
    </declare-styleable>

    <declare-styleable name="LineControllerView">
        <!-- 名称 -->
        <attr name="name" format="string"/>
        <!-- 内容或当前状态 -->
        <attr name="subject" format="string"/>
        <!-- 是否是列表中最后一个 -->
        <attr name="isBottom" format="boolean"/>
        <!-- 是否是列表中第一个 -->
        <attr name="isTop" format="boolean"/>
        <!-- 是否可以跳转 -->
        <attr name="canNav" format="boolean"/>
        <!-- 是否是开关 -->
        <attr name="isSwitch" format="boolean"/>
    </declare-styleable>
</resources>