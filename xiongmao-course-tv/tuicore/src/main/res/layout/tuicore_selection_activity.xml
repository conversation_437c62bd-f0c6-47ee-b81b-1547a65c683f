<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.tencent.qcloud.tuicore.component.TitleBarLayout
        android:id="@+id/edit_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/core_page_title_height" />

    <EditText
        android:id="@+id/edit_content_et"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:singleLine="true"
        android:textCursorDrawable="@drawable/core_edit_cursor" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/select_list"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>
