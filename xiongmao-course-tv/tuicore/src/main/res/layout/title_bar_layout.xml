<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/page_title_layout">

    <LinearLayout
        android:id="@+id/page_title_left_group"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:gravity="center_vertical"
        android:minWidth="80dp"
        android:orientation="horizontal"
        android:paddingLeft="16dp">

        <ImageView
            android:id="@+id/page_title_left_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:clickable="false"
            android:src="@drawable/title_bar_back" />

        <com.tencent.qcloud.tuicore.component.UnreadCountTextView
            android:id="@+id/new_message_total_unread"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:layout_marginEnd="5dp"
            android:minWidth="18.4dp"
            android:minHeight="18.4dp"
            android:textColor="#ffffff"
            android:textSize="13.4sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/page_title_left_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black_font_color"
            android:gravity="center"
            android:textSize="14sp"/>

    </LinearLayout>


    <TextView
        android:id="@+id/page_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:singleLine="true"
        android:textAlignment="center"
        android:textColor="@color/black_font_color"
        android:textSize="16.3sp"
        android:textStyle="bold" />

    <LinearLayout
        android:id="@+id/page_title_right_group"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:gravity="center_vertical|right"
        android:minWidth="80dp"
        android:orientation="horizontal"
        android:paddingRight="16dp">

        <TextView
            android:id="@+id/page_title_right_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="right"
            android:textColor="@color/black_font_color"
            android:textSize="14sp"/>

        <ImageView
            android:id="@+id/page_title_right_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:clickable="false" />
    </LinearLayout>

</RelativeLayout>