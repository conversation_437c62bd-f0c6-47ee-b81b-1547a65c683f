plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.3.1'
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'

//    def projects = this.rootProject.getAllprojects().stream().map{project->project.name}.collect()
//    println "all projects : {$projects}"
//    if (projects.contains("imsdk-plus")) {
//        api project(':imsdk-plus')
//    } else {

    api 'com.tencent.imsdk:imsdk-plus:5.9.1872'
//    }

}
repositories {
    mavenCentral()
}