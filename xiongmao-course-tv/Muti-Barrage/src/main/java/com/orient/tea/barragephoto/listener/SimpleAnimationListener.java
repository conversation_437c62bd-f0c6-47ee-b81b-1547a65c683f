package com.orient.tea.barragephoto.listener;

import android.animation.Animator;

/**
 * 简单的监听器
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/16.
 */

public abstract class SimpleAnimationListener implements Animator.AnimatorListener {

    @Override
    public void onAnimationStart(Animator animation) {

    }

    @Override
    public void onAnimationEnd(Animator animation) {

    }

    @Override
    public void onAnimationCancel(Animator animation) {

    }

    @Override
    public void onAnimationRepeat(Animator animation) {

    }
}
