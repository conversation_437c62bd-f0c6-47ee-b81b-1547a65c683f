plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    id 'org.greenrobot.greendao'
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    defaultConfig {
        applicationId "com.panda.course"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 70
        versionName "1.5.9"
        multiDexEnabled true

        ndk {
            //设置支持的SO库架构
            abiFilters "mips", "mips64", "x86_64", "armeabi", "armeabi-v7a", "arm64-v8a"
        }

        packagingOptions {
            doNotStrip "/armeabi/.so"
            doNotStrip "/armeabi-v7a/.so"
            doNotStrip "/x86/.so"
        }

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        //使用asXxx方法时必须，告知RxHttp你依赖的rxjava版本，可传入rxjava2、rxjava3
                        rxhttp_rxjava: 'rxjava3',
                ]
            }
        }
    }


    greendao {
        schemaVersion 1  //数据库版本号，升级数据库需要修改版本号
        daoPackage 'com.panda.course.dao'  //一般为app包名+生成文件的文件夹名
        targetGenDir 'src/main/java'  //自动生成的greendao代码存放路径
    }


    signingConfigs {
        release {
            storeFile file("../release.jks")
            storePassword "XiongMao2016"
            keyAlias "xiongmao"
            keyPassword "XiongMao2016"

            // ***** 系统级别的签名 ******
//            storeFile file("../signApk/giftedcat.jks")
//            storePassword "123456"
//            keyAlias "key0"
//            keyPassword "123456"
        }

    }


    buildTypes {
        debug {
            minifyEnabled true//关闭混淆
            shrinkResources true
            zipAlignEnabled true
            debuggable = true
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled true //开启混淆
            shrinkResources true
            zipAlignEnabled true
            debuggable false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    // 自定义打包apk名称
    android.applicationVariants.all {
        variant ->
            variant.outputs.all {
                outputFileName = "xiongmao_ai_${variant.versionName}.apk"
            }
    }


    useLibrary 'org.apache.http.legacy'

    aaptOptions {
        noCompress "webp"
    }

    buildFeatures {
        dataBinding = true
        viewBinding = true
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    repositories {
        jcenter()
        google()
        mavenCentral()
        flatDir {
            dirs 'libs'
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation fileTree(include: ['*.aar'], dir: 'libs')

    implementation files('libs/droidlogic.jar')
    implementation files('libs/libotaupdate.jar')


    //腾讯IM
    implementation project(':tuicore')
    // 腾讯超级播放器
    implementation project(':superplayerkit')
    // wifi扫描
    implementation project(':wifimanager')
    //视频字幕
    implementation project(':subtitles')
    //视频弹幕
    implementation project(':Muti-Barrage')
    //百家云
    //依赖排除一下，因为跟本地引入的腾讯的播放器库冲突
    api("com.baijiayun.live:liveplayer-sdk-core:4.3.0") {
        exclude group: 'com.baijia.player', module: 'LiteAVSDK_TRTC'
    }

//    api("com.baijiayun.live:live-ui-base:3.26.0") {
//        exclude group: 'com.baijia.player', module: 'LiteAVSDK_TRTC'
//    }

    implementation 'androidx.core:core-ktx:1.3.2'
    implementation 'androidx.leanback:leanback:1.0.0'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'

    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

    implementation 'com.github.bumptech.glide:glide:4.11.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.11.0'
    implementation 'com.github.bumptech.glide:okhttp3-integration:1.5.0'

    //rxHttp
    implementation "com.squareup.okhttp3:okhttp:4.9.1"
    implementation "com.ljx.rxhttp:rxhttp:2.5.5"
    implementation "com.ljx.rxlife:rxlife-coroutine:2.0.1"
    kapt "com.ljx.rxhttp:rxhttp-compiler:2.5.5"

    //rxjava3
    implementation 'io.reactivex.rxjava3:rxjava:3.0.6'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    implementation 'com.github.liujingxing.rxlife:rxlife-rxjava3:2.1.0' //管理RxJava3生命周期，页面销毁，关闭请求
    implementation 'com.zhy:okhttputils:2.6.2'

    // 协程基础库
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.2"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.2"

    //lifecycle
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.3.0"
    implementation "androidx.lifecycle:lifecycle-common-java8:2.2.0"
    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"

    // viewModel
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.0"

    // liveData
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:2.2.0"

    //log
    implementation "com.github.zhaokaiqiang.klog:library:1.6.0"

    // 管理界面状态
    implementation "com.kingja.loadsir:loadsir:1.3.8"

    // 存储
//    implementation 'com.tencent:mmkv-static:1.2.10'

    //利用liveData发送消息
    implementation 'com.kunminx.arch:unpeek-livedata:7.1.0-beta1'

    //通过标签直接生成shape，无需再写shape.xml
    implementation 'com.github.JavaNoober.BackgroundLibrary:libraryx:1.7.4'

    //material-dialog
    implementation "com.afollestad.material-dialogs:core:3.3.0"

    // auto
    implementation "me.jessyan:autosize:1.2.1"

    //baseRecyclerView
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.4'

    //app-updater
    implementation 'com.github.jenly1314.AppUpdater:app-updater:1.1.0'

    // 阿里fastjson
    implementation 'com.alibaba:fastjson:1.1.52.android'

    //内容
    implementation 'com.github.li-xiaojun:XPopup:2.6.7'

    //bugly   其中latest.release指代最新版本号，也可以指定明确的版本号，例如2.1.5
    implementation 'com.tencent.bugly:crashreport:latest.release'
    implementation 'com.tencent.bugly:nativecrashreport:latest.release'


    implementation 'org.linwg1988:lcardview-kt:0.0.1'
    //BGA - Banner - 广告占位
    implementation 'io.github.youth5201314:banner:2.2.2'


    //加载动画
    implementation 'me.samlss:broccoli:1.0.0'

    // 友盟统计SDK
    implementation 'com.umeng.umsdk:common:9.4.4'// 必选
    implementation 'com.umeng.umsdk:asms:1.4.1'// 必选
    implementation 'com.umeng.umsdk:apm:1.5.2' // 错误分析升级为独立SDK，看crash数据请一定集成，可选

    // 内存检测
//    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.9.1'

    // 异步初始化加载
    implementation 'com.github.DSAppTeam:Anchors:v1.1.7'

    //数据库
    implementation 'org.greenrobot:greendao:3.3.0'

    //局域网通信
    implementation 'com.yanzhenjie:andserver:1.1.3'

    //七牛云
    implementation 'com.qiniu:qiniu-android-sdk:8.2.0'

    implementation 'org.jsoup:jsoup:1.13.1'

    //pdf
    implementation('com.github.barteksc:android-pdf-viewer:2.8.2')

    //zxing二维码
    implementation 'com.google.zxing:core:3.4.1'

    //多语言
    implementation 'com.mallotec.reb:plugin-locale:1.0.12'

}

