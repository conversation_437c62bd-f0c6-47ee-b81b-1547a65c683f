<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.panda.course"
    tools:ignore="MissingLeanbackLauncher"> <!-- 打包的时候在增加这个 属性      android:sharedUserId="android.uid.system" -->
    <!-- 增加属性，从这里往下增加 -->
    <uses-sdk tools:overrideLibrary="org.webrtc,io.agora.rtc,com.tencent.qcloud.imsdk_bugly" />

    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.RECOVERY" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.READ_INSTALL_SESSIONS" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SHUTDOWN" />
    <uses-permission android:name="android.permission.INJECT_EVENTS" />

    <uses-feature
        android:name="android.software.leanback"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <application
        android:name=".config.App"
        android:allowBackup="true"
        android:extractNativeLibs="true"
        android:hardwareAccelerated="true"
        android:icon="@drawable/logo"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/TVTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:networkSecurityConfig">
        <activity
            android:name=".ui.activity.PdfChooseActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.PdfViewActivity"
            android:exported="false" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity android:name=".ui.activity.MainActivity2" />
        <activity
            android:name=".ui.activity.MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activity.CourseActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:launchMode="singleInstance" />
        <activity
            android:name=".ui.activity.ContentActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.PropagandaActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.PlayerActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:screenOrientation="landscape" />
        <activity
            android:name=".ui.activity.dialog.UserOutDialogActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.dialog.UpdateDialogActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.dialog.CheckNetDialogActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.WebViewActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.dialog.RankingDialogActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.dialog.MarketingDialogActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.dialog.SurpriseDialogActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.dialog.SurpriseContentActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.dialog.GraduateDialogActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.dialog.GraduationMapActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.dialog.CourseImgDialogActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.BleActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.wifi.WiFiConnectActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activity.wifi.WiFiActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.OTAUpdateActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode" />
        <activity
            android:name=".ui.activity.wifi.NetDetailsActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode" />
        <activity
            android:name=".ui.activity.HdmiActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode" />
        <activity
            android:name=".ui.activity.RemoteControlActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode" />
        <activity
            android:name=".ui.activity.SystemInfoActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode" />
        <activity
            android:name=".ui.activity.ScreenAdjustmentActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode" />
        <activity
            android:name=".ui.activity.SettingsActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode" />
        <activity
            android:name=".ui.activity.dialog.TeacherStudentInteractionActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.NoticeActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.dialog.TeacherReplyActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.dialog.QrSkillActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.SkillActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.OtherPlayActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.PhoneRemoteActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.dialog.SkillRecordActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:theme="@style/dialog_style" />
        <activity
            android:name=".ui.activity.FeedbackActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.ExcellentRankingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.OrderHallActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.dialog.ExpandInterfaceActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:launchMode="singleInstance" />
        <activity
            android:name=".ui.activity.ExamRankActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.LiveClassHourActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.LiveListActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity
            android:name=".ui.activity.PlayerLiveActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true" />
        <activity android:name=".ui.activity.OnlineRenewalActivity" /> <!-- 用于测试新的播放器 -->
        <activity android:name=".ui.activity.PlayerNewActivity" /> <!-- 后台下载 -->
        <service android:name=".service.DownloadVideoService" />
        <service
            android:name=".service.CoreService"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="com.minsheng.controller.server" />
            </intent-filter>
        </service>

        <receiver
            android:name=".receiver.ServerManager"
            android:permission="android.permission.RECEIVE_BOOT_COMPLETED"
            tools:ignore="Instantiatable">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="com.panda.course.receiver.receiver" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </receiver> <!-- value的值填写你在友盟后台申请的应用Appkey -->
        <meta-data
            android:name="UMENG_APPKEY"
            android:value="61bc8dfae014255fcbbc828e" /> <!-- value的值填写渠道名称，例如yingyongbao。这里设置动态渠道名称变量 -->
        <meta-data
            android:name="UMENG_CHANNEL"
            android:value="T95Plus" />
        <meta-data
            android:name="design_width_in_dp"
            android:value="1280" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="720" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>