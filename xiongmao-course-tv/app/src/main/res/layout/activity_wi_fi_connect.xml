<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg"
        android:orientation="vertical"
        android:padding="40dp"
        tools:context=".ui.activity.wifi.WiFiConnectActivity">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="无线网络"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_40"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:minWidth="500dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_wifi_current"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请输入密码连接"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />

            <EditText
                android:id="@+id/tv_wifi_pwd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/base_border_wifi_edittext"
                android:padding="20dp"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30" />

            <Button
                android:id="@+id/but_wifi_connect"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/black1"
                android:padding="20dp"
                android:text="连接"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30" />
        </LinearLayout>

    </RelativeLayout>
</layout>