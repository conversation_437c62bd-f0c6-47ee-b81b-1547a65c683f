<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_item_top_layout"
    style="@style/FocusCloseStyle"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginLeft="5dp"
    android:layout_marginTop="20dp"
    android:layout_marginRight="5dp"
    android:layout_marginBottom="5dp"
    android:orientation="vertical">


    <ImageView
        android:id="@+id/iv_item_new_pic"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center" />

    <TextView
        android:id="@+id/tv_item_name_new_sign"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="10dp"
        android:ellipsize="end"
        android:lines="1"
        android:maxEms="4"
        android:singleLine="true"
        android:text="我是某某某"
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_20"
        android:textStyle="bold"
        android:visibility="visible" />

    <TextView
        android:id="@+id/tv_item_name_new_score"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="6dp"
        android:ellipsize="end"
        android:lines="1"
        android:maxEms="4"
        android:singleLine="true"
        android:text="我是某某某"
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_18"
        android:textStyle="bold"
        android:visibility="visible" />
</LinearLayout>


