<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_item_view_class_hours"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusedByDefault="true"
    app:bl_corners_radius="10dp"
    app:bl_solid_color="@color/transparent"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/item_iv_course_cover"
                android:layout_width="360dp"
                android:layout_height="200dp"
                android:scaleType="centerCrop"
                android:src="@drawable/icon_placeholder" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="12dp"
                android:paddingTop="4dp"
                android:paddingRight="12dp"
                android:paddingBottom="6dp"
                app:bl_corners_bottomRightRadius="16dp"
                app:bl_solid_color="#FFFFFF">

                <ImageView
                    android:id="@+id/iv_item_class_hour_status"
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_gravity="center"
                    android:src="@drawable/icon_live_ing" />

                <TextView
                    android:id="@+id/tv_ai_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="4dp"
                    android:textColor="@color/blue"
                    android:textSize="@dimen/font_size_16"
                    android:visibility="visible" />
            </LinearLayout>

        </FrameLayout>

        <LinearLayout
            android:id="@+id/ll_item_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_bg2"
            android:orientation="vertical">


            <TextView
                android:id="@+id/tv_item_live_sub"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="10dp"
                android:ellipsize="end"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:singleLine="true"
                android:text="11点"
                android:textColor="#50ffffff"
                android:textSize="@dimen/font_size_18" />

            <TextView
                android:id="@+id/tv_item_live_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="16dp"
                android:ellipsize="end"
                android:maxLines="5"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_18" />
        </LinearLayout>

    </LinearLayout>


</androidx.cardview.widget.CardView>



