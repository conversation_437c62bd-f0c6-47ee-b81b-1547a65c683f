<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>


    <LinearLayout
        android:layout_width="450dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="200dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/app_update_bg" />

            <TextView
                android:id="@+id/but_dialog_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="50dp"
                android:gravity="center"
                android:text="发现新版本"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_32"
                android:textStyle="bold" />
        </RelativeLayout>

        <TextView
            android:id="@+id/but_dialog_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingLeft="20dp"
            android:paddingTop="15dp"
            android:paddingRight="20dp"
            android:paddingBottom="20dp"
            android:textColor="@color/black"
            android:textSize="22sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#E3E3E6" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:bl_corners_bottomLeftRadius="10dp"
            app:bl_corners_bottomRightRadius="10dp"
            app:bl_solid_color="@color/transparent">

            <RelativeLayout
                android:id="@+id/rl_update_progress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp"
                android:visibility="gone"
                app:bl_corners_bottomLeftRadius="10dp"
                app:bl_corners_bottomRightRadius="10dp"
                app:bl_solid_color="@color/white">

                <TextView
                    android:id="@+id/tvProgress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:lineSpacingMultiplier="1.2"
                    android:text="@string/app_updater_start_notification_content"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_16" />

                <ProgressBar
                    android:id="@+id/progressBar"
                    style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_below="@+id/tvProgress"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:max="100" />
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ll_select_layout"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                app:bl_corners_bottomLeftRadius="10dp"
                app:bl_corners_bottomRightRadius="10dp"
                app:bl_solid_color="@color/white">

                <TextView
                    android:id="@+id/but_dialog_commit"
                    style="@style/FocusStyle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/base_border_bottom_left_round"
                    android:gravity="center"
                    android:text="立即升级"
                    android:textColor="@color/white"
                    android:textSize="24sp" />

                <TextView
                    android:id="@+id/but_dialog_cancel"
                    style="@style/FocusStyle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/base_border_bottom_right_round"
                    android:gravity="center"
                    android:text="下次再说"
                    android:textColor="@color/green"
                    android:textSize="24sp" />

            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

</layout>