<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="50dp"
    app:bl_corners_radius="10dp"
    app:bl_solid_color="@color/white">


    <TextView
        android:id="@+id/iv_dialog_store_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="50dp"
        android:gravity="center"
        android:text="网络未连接，请联网后再试"
        android:textColor="@color/black"
        android:textSize="30sp"
        android:textStyle="bold" />


    <Button
        android:id="@+id/but_dialog_connect"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_dialog_store_name"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:layout_marginRight="10dp"
        android:gravity="center"
        android:paddingLeft="34dp"
        android:paddingTop="6dp"
        android:paddingRight="34dp"
        android:paddingBottom="6dp"
        android:text="刷新"
        android:textColor="@color/white"
        android:textSize="30sp"
        android:textStyle="bold"
        app:bl_corners_radius="50dp"
        app:bl_solid_color="@color/green" />

    <Button
        android:id="@+id/but_dialog_cancel"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_dialog_store_name"
        android:layout_gravity="center"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="20dp"
        android:layout_toRightOf="@+id/but_dialog_connect"
        android:gravity="center"
        android:paddingLeft="34dp"
        android:paddingTop="6dp"
        android:paddingRight="34dp"
        android:paddingBottom="6dp"
        android:text="去设置"
        android:textColor="@color/white"
        android:textSize="30sp"
        android:textStyle="bold"
        app:bl_corners_radius="50dp"
        app:bl_solid_color="@color/base_bg" />


</RelativeLayout>