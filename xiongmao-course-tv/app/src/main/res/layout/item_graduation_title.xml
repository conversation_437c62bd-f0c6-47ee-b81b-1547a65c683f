<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/FocusCloseStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_item_graduation_title"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:gravity="center|left"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_26"
        android:textStyle="bold" />
</LinearLayout>


