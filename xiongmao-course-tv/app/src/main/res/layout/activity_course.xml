<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>


    <RelativeLayout
        android:id="@+id/rl_course_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/base_bg3">

        <include
            android:id="@+id/base_head"
            layout="@layout/base_head" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/base_head"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="horizontal">

            <com.panda.course.widget.TabVerticalGridView
                android:id="@+id/recycler_row"
                android:layout_width="280dp"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:layout_gravity="center"
                android:background="#1F2E37"
                android:paddingTop="10dp"
                android:paddingBottom="10dp" />

            <com.panda.course.widget.TabVerticalGridView
                android:id="@+id/recycler_course"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/base_head"
                android:layout_toRightOf="@+id/recycler_row"
                android:layout_weight="1" />

            <com.panda.course.widget.TabVerticalGridView
                android:id="@+id/recycler_course_experience"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/base_head"
                android:layout_toRightOf="@+id/recycler_row"
                android:layout_weight="1"
                android:visibility="gone" />

            <com.panda.course.widget.TVBoxRecyclerView
                android:id="@+id/recycler_history"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/base_head"
                android:layout_toRightOf="@+id/recycler_row"
                android:layout_weight="1"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:visibility="gone" />

        </LinearLayout>


        <ImageView
            android:id="@+id/iv_test"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_centerInParent="true"
            android:src="@drawable/logo"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_test1"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_alignParentBottom="true"
            android:src="@drawable/logo"
            android:visibility="gone" />
    </RelativeLayout>
</layout>