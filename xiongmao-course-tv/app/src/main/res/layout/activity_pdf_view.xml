<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.activity.PdfViewActivity">

        <com.github.barteksc.pdfviewer.PDFView
            android:id="@+id/pdfView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/tv_page_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginTop="50dp"
            android:paddingLeft="20dp"
            android:paddingRight="10dp"
            android:text="0/10"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_32"
            app:bl_corners_bottomLeftRadius="20dp"
            app:bl_corners_topLeftRadius="20dp"
            app:bl_solid_color="#50000000" />

        <FrameLayout
            android:id="@+id/fl_progress"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_bg3">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="64dp"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:background="@color/white"
                android:gravity="center"
                android:orientation="horizontal">

                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:indeterminateTintMode="src_atop" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/loading_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:gravity="center"
                    android:text="稍等，正在加载中..."
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_30" />
            </LinearLayout>
        </FrameLayout>

        <Button
            android:id="@+id/but_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|bottom"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="30dp"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            android:text="按遥控器【下键/上键】翻页查看"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_28"
            android:textStyle="bold"
            android:visibility="gone"
            app:bl_corners_radius="50dp"
            app:bl_solid_color="@color/green" />
    </FrameLayout>
</layout>