<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/base_bg3"
        android:orientation="vertical">

        <com.panda.course.widget.ScaleConstraintLayout
            android:id="@+id/ll_user_layout"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="14dp"
            android:background="@drawable/base_border_round"
            android:nextFocusLeft="@id/ll_user_layout"
            android:nextFocusRight="@id/ll_notice_layout"
            android:nextFocusUp="@id/ll_user_layout"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_head_store_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxEms="14"
                android:minWidth="100dp"
                android:paddingStart="50dp"
                android:paddingTop="5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="5dp"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="20dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_head_pic"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/def_pic"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

        </com.panda.course.widget.ScaleConstraintLayout>

        <TextView
            android:id="@+id/tv_head_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/ll_user_layout"
            android:paddingStart="50dp"
            android:paddingTop="5dp"
            android:paddingEnd="10dp"
            android:paddingBottom="5dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="20dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.panda.course.widget.ScaleConstraintLayout
            android:id="@+id/ll_notice_layout"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="14dp"
            android:layout_toStartOf="@+id/ll_update_layout"
            android:background="@drawable/base_border_round"
            android:nextFocusLeft="@id/ll_user_layout"
            android:nextFocusRight="@id/ll_update_layout"
            android:nextFocusUp="@id/ll_notice_layout"
            android:paddingRight="20dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_notice"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginStart="20dp"
                android:src="@drawable/icon_notice"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <com.panda.course.widget.MarqueeTextView
                android:id="@+id/tv_notice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:ellipsize="marquee"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:maxWidth="180dp"
                android:paddingTop="5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="5dp"
                android:singleLine="true"
                android:text="@string/messageNotification"
                android:textColor="@color/white"
                android:textSize="20dp"
                app:layout_constraintLeft_toRightOf="@+id/iv_notice"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/tv_notice_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="8dp"
                android:paddingTop="4dp"
                android:paddingRight="8dp"
                android:paddingBottom="4dp"
                android:text="0"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_14"
                android:visibility="gone"
                app:bl_corners_radius="100dp"
                app:bl_solid_color="@color/red"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_notice"
                app:layout_constraintTop_toTopOf="parent" />

        </com.panda.course.widget.ScaleConstraintLayout>

        <com.panda.course.widget.ScaleConstraintLayout
            android:id="@+id/ll_update_layout"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="14dp"
            android:layout_toStartOf="@+id/ll_surprise_layout"
            android:background="@drawable/base_border_round"
            android:nextFocusLeft="@id/ll_notice_layout"
            android:nextFocusRight="@id/ll_surprise_layout"
            android:nextFocusUp="@id/ll_update_layout"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="180dp"
                android:paddingStart="55dp"
                android:paddingTop="5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="5dp"
                android:text="@string/HelpandFeedback"
                android:textColor="@color/white"
                android:textSize="20dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_update"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginStart="20dp"
                android:src="@drawable/icon_home_help"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

        </com.panda.course.widget.ScaleConstraintLayout>

        <com.panda.course.widget.ScaleConstraintLayout
            android:id="@+id/ll_surprise_layout"
            style="@style/FocusCloseStyle"
            android:layout_width="234dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="14dp"
            android:layout_toStartOf="@+id/ll_setting_layout"
            android:background="@drawable/base_border_round"
            android:nextFocusLeft="@id/ll_update_layout"
            android:nextFocusRight="@id/ll_setting_layout"
            android:nextFocusUp="@id/ll_surprise_layout"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_surprise"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="marquee"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="44dp"
                android:paddingTop="5dp"
                android:paddingEnd="30dp"
                android:paddingBottom="5dp"
                android:singleLine="true"
                android:text="看课瓜分百万福利"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_surprise"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/surprise_gif"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </com.panda.course.widget.ScaleConstraintLayout>

        <com.panda.course.widget.ScaleConstraintLayout
            android:id="@+id/ll_setting_layout"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="14dp"
            android:layout_toStartOf="@+id/tv_head_app_version_code"
            android:background="@drawable/base_border_round"
            android:nextFocusLeft="@id/ll_surprise_layout"
            android:nextFocusRight="@id/ll_setting_layout"
            android:nextFocusUp="@id/ll_setting_layout"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingStart="55dp"
                android:paddingTop="5dp"
                android:paddingEnd="30dp"
                android:paddingBottom="5dp"
                android:text="@string/settings"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginStart="20dp"
                android:src="@drawable/icon_home_setting"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />
        </com.panda.course.widget.ScaleConstraintLayout>

        <TextView
            android:id="@+id/tv_head_app_version_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_24"
            android:visibility="invisible" />


        <View
            android:id="@+id/view_main_line"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_below="@+id/ll_user_layout"
            android:background="@color/green" />
    </RelativeLayout>
</layout>