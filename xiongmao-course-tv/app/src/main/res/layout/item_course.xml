<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">


    <LinearLayout
        android:id="@+id/ll_item_layout"
        android:layout_width="match_parent"
        android:layout_height="150dp">

        <FrameLayout
            android:layout_width="250dp"
            android:layout_height="150dp">

            <ImageView
                android:id="@+id/item_iv_course_cover"
                android:layout_width="250dp"
                android:layout_height="150dp"
                android:scaleType="centerCrop"
                android:src="@drawable/icon_placeholder"
                app:corner_bottom_left_size="10"
                app:corner_bottom_right_size="0"
                app:corner_top_left_size="10"
                app:corner_top_right_size="0"
                app:label_backgroundColor="@color/red"
                app:label_orientation="LEFT_TOP"
                app:label_text="新"
                app:label_textColor="@color/white" />

            <ImageView
                android:id="@+id/iv_item_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|right"
                android:src="@drawable/icon_player"
                android:visibility="gone" />
        </FrameLayout>


        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="150dp"
            android:layout_weight="1"
            android:background="@drawable/base_border_bottom_right_round"
            android:padding="12dp">

            <TextView
                android:id="@+id/tv_item_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="高级小儿推拿师"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_item_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_item_title"
                android:layout_marginTop="10dp"
                android:ellipsize="end"
                android:lineSpacingExtra="6dp"
                android:maxLines="3"
                android:text="内容"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_18" />


            <TextView
                android:id="@+id/tv_item_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_marginRight="10dp"
                android:text=""
                android:textColor="#60FFFFFF"
                android:textSize="@dimen/font_size_18" />

        </RelativeLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_item_is_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_is_new" />
</androidx.cardview.widget.CardView>

