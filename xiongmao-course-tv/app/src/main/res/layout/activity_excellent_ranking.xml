<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/icon_excellent_rank_bg"
        android:clipChildren="false"
        android:clipToPadding="false"
        tools:context=".ui.activity.ExcellentRankingActivity">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="480dp"
            android:scaleType="fitXY"
            android:src="@drawable/icon_excellent_rank_left" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="480dp"
            android:layout_alignParentRight="true"
            android:scaleType="fitXY"
            android:src="@drawable/icon_excellent_rank_right" />

        <TextView
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="180dp"
            android:layout_marginTop="55dp"
            android:text="优秀学员排行"
            android:textColor="#FFF3D8BE"
            android:textSize="@dimen/font_size_34"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="50dp">

            <Button
                android:id="@+id/but_month"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_rank_left_selector"
                android:paddingLeft="16dp"
                android:paddingTop="10dp"
                android:paddingRight="16dp"
                android:paddingBottom="10dp"
                android:text="本月排行榜(0)"
                android:textColor="#FF890613"
                android:textSize="@dimen/font_size_26" />

            <Button
                android:id="@+id/but_last_month"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_rank_right_unselector"
                android:paddingLeft="16dp"
                android:paddingTop="10dp"
                android:paddingRight="16dp"
                android:paddingBottom="10dp"
                android:text="上月排行榜(0)"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26" />
        </LinearLayout>

        <com.panda.course.widget.FocusKeepRecyclerView
            android:id="@+id/recycler"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="10dp"
            android:nextFocusUp="@id/grid_knowledge_points"
            android:nextFocusDown="@id/grid_definition" />

        <RelativeLayout
            android:id="@+id/ll_no1"
            style="@style/FocusStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="140dp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">


            <LinearLayout
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="175dp"
                android:layout_below="@+id/ll_no1_head"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="-20dp"
                android:background="@drawable/icon_excellent_rank_no3_bg"
                android:gravity="center"
                android:minWidth="230dp"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:id="@+id/tv_no1_name"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:ellipsize="end"
                    android:maxEms="5"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="#FF890613"
                    android:textSize="@dimen/font_size_24"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_no1_content"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="答题次数：0\n答题平均分：0\n正确答题率：0%"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_20" />
            </LinearLayout>

            <FrameLayout
                android:id="@+id/ll_no1_head"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true">

                <ImageView
                    android:id="@+id/iv_no1"
                    style="@style/FocusCloseStyle"
                    android:layout_width="140dp"
                    android:layout_height="140dp"
                    android:layout_gravity="center" />

                <ImageView
                    android:layout_width="180dp"
                    android:layout_height="180dp"
                    android:src="@drawable/icon_excellent_rank_no1" />
            </FrameLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/ll_no2"
            style="@style/FocusStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="185dp"
            android:layout_toLeftOf="@+id/ll_no1"
            android:gravity="center"
            android:minWidth="200dp"
            android:orientation="vertical"
            android:visibility="gone">


            <LinearLayout
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="160dp"
                android:layout_below="@+id/ll_no2_head"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="-30dp"
                android:background="@drawable/icon_excellent_rank_no2_bg"
                android:gravity="center"
                android:minWidth="200dp"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:id="@+id/tv_no2_name"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:ellipsize="end"
                    android:maxEms="5"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="#FF890613"
                    android:textSize="@dimen/font_size_20"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_no2_content"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="答题次数：0\n答题平均分：0\n正确答题率：0%"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_18" />
            </LinearLayout>

            <FrameLayout
                android:id="@+id/ll_no2_head"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true">

                <ImageView
                    android:id="@+id/iv_no2"
                    style="@style/FocusCloseStyle"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_gravity="center" />

                <ImageView
                    android:layout_width="160dp"
                    android:layout_height="160dp"
                    android:src="@drawable/icon_excellent_rank_no2" />
            </FrameLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/ll_no3"
            style="@style/FocusStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="185dp"
            android:layout_toRightOf="@+id/ll_no1"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">


            <LinearLayout
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="160dp"
                android:layout_below="@+id/ll_no3_head"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="-30dp"
                android:background="@drawable/icon_excellent_rank_no1_bg"
                android:gravity="center"
                android:minWidth="200dp"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:id="@+id/tv_no3_name"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:ellipsize="end"
                    android:maxEms="5"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="#FF890613"
                    android:textSize="@dimen/font_size_20"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_no3_content"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="答题次数：0\n答题平均分：0\n正确答题率：0%"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_18" />
            </LinearLayout>

            <FrameLayout
                android:id="@+id/ll_no3_head"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true">

                <ImageView
                    android:id="@+id/iv_no3"
                    style="@style/FocusCloseStyle"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_gravity="center" />

                <ImageView
                    android:layout_width="160dp"
                    android:layout_height="160dp"
                    android:src="@drawable/icon_excellent_rank_no3" />
            </FrameLayout>
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_empty"
            style="@style/FocusCloseStyle"
            android:layout_width="500dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/shape_rank_empty_selector"
            android:gravity="center|top"
            android:minHeight="400dp"
            android:orientation="vertical"
            android:padding="20dp"
            android:visibility="visible">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:src="@drawable/icon_excellent_rank_empty" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="本月还没有人扫码答题，请扫码考试吧"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size_26"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/base_app_dash_line"
                android:layerType="software" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:drawableLeft="@drawable/icon_excellent_rank_tips"
                android:drawablePadding="4dp"
                android:paddingLeft="16dp"
                android:paddingTop="8dp"
                android:paddingRight="16dp"
                android:paddingBottom="8dp"
                android:text="提示"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20"
                app:bl_corners_radius="10dp"
                app:bl_solid_color="#FF890613" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="如果有阿姨在店里，可以给阿姨看课学习，多做练习，阿姨上户才会更有保障"
                android:textColor="#FF890613"
                android:textSize="@dimen/font_size_24"
                android:textStyle="bold" />
        </LinearLayout>
    </RelativeLayout>
</layout>