<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/row_card_interactive_list_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="20dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="20dp"
    android:layout_marginBottom="10dp"
    android:background="@drawable/base_border_unselecter"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:focusedByDefault="true"
    android:paddingLeft="20dp"
    android:paddingTop="10dp"
    android:paddingRight="20dp"
    android:paddingBottom="10dp"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <com.panda.course.widget.RoundCornerImageView
        android:id="@+id/iv_item_interactive_pic"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginBottom="8dp"
        app:corner_size="200" />

    <TextView
        android:id="@+id/iv_item_interactive_store"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="10dp"
        android:text="门店"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_20" />

    <TextView
        android:id="@+id/iv_item_interactive_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginTop="2dp"
        android:layout_marginRight="100dp"
        android:layout_toRightOf="@+id/iv_item_interactive_pic"
        android:ellipsize="end"
        android:maxEms="9"
        android:singleLine="true"
        android:text="名字"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18" />

    <TextView
        android:id="@+id/iv_item_interactive_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_item_interactive_name"
        android:layout_marginLeft="4dp"
        android:layout_toRightOf="@+id/iv_item_interactive_pic"
        android:text="时间"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_16" />

    <TextView
        android:id="@+id/iv_item_interactive_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_item_interactive_pic"
        android:text="我是标题呀"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_item_interactive"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_item_interactive_title"
        android:visibility="visible" />

    <View
        android:id="@+id/recycler_item_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/recycler_item_interactive"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:background="#60FFFFFF" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/recycler_item_line">

        <TextView
            android:id="@+id/iv_item_interactive_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_18"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/iv_item_interactive_content1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_18"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_item_look_interactive_content"
            app:layout_constraintTop_toBottomOf="@+id/iv_item_interactive_content" />

        <TextView
            android:id="@+id/iv_item_look_interactive_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="3dp"
            android:layout_marginTop="2dp"
            android:background="@drawable/base_border_teacher"
            android:gravity="center"
            android:paddingLeft="3dp"
            android:paddingTop="1dp"
            android:paddingRight="3dp"
            android:paddingBottom="1dp"
            android:text="查看更多"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_18"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintLeft_toRightOf="@+id/iv_item_interactive_content1"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_item_interactive_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--    <LinearLayout-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_below="@+id/recycler_item_line"-->
    <!--        android:orientation="horizontal">-->

    <!--        <TextView-->
    <!--            android:id="@+id/iv_item_interactive_content"-->
    <!--            android:layout_width="0dp"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_marginTop="4dp"-->
    <!--            android:layout_weight="1"-->
    <!--            android:ellipsize="end"-->
    <!--            android:maxHeight="100dp"-->
    <!--            android:text="老师回复"-->
    <!--            android:textColor="@color/white"-->
    <!--            android:textSize="@dimen/font_size_22" />-->

    <!--        <TextView-->
    <!--            android:id="@+id/iv_item_look_interactive_content"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_below="@+id/iv_item_interactive_content"-->
    <!--            android:layout_gravity="center"-->
    <!--            android:layout_marginLeft="4dp"-->
    <!--            android:background="@drawable/base_border"-->
    <!--            android:baselineAligned="false"-->
    <!--            android:focusable="true"-->
    <!--            android:focusableInTouchMode="true"-->
    <!--            android:text="#查看更多"-->
    <!--            android:textColor="@color/white"-->
    <!--            android:textSize="@dimen/font_size_18"-->
    <!--            android:visibility="gone" />-->

    <!--    </LinearLayout>-->

</RelativeLayout>


