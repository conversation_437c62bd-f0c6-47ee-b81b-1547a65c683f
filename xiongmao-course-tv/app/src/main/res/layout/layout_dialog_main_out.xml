<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="horizontal"
    android:paddingLeft="40dp"
    android:paddingTop="40dp"
    android:paddingBottom="40dp"
    app:bl_corners_radius="10dp"
    app:bl_solid_color="@color/black1">

    <LinearLayout
        android:id="@+id/ll_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:minWidth="250dp"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_dialog_pic"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_service_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/iv_dialog_pic"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="服务日期"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_24" />

    </LinearLayout>


    <View
        android:id="@+id/view_line"
        android:layout_width="1dp"
        android:layout_height="280dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="50dp"
        android:layout_toRightOf="@+id/ll_layout"
        android:background="@color/white" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:layout_toRightOf="@+id/view_line"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="40dp"
        android:paddingRight="40dp">


        <TextView
            android:id="@+id/tv_dialog_cancel"
            style="@style/FocusStyle"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/base_border_round"
            android:gravity="center"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:text="@string/wait_look"
            android:textColor="@color/white"
            android:textSize="28sp"
            android:textStyle="bold" />


        <TextView
            android:id="@+id/tv_dialog_out"
            style="@style/FocusStyle"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:background="@drawable/base_border_round"
            android:gravity="center"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:text="@string/exit_look"
            android:textColor="@color/white"
            android:textSize="28sp"
            android:textStyle="bold" />
    </LinearLayout>

</RelativeLayout>