<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_common_item_view"
    style="@style/FocusStyle"
    android:layout_width="400dp"
    android:layout_height="100dp"
    android:layout_marginLeft="16dp"
    android:layout_marginTop="16dp"
    android:layout_marginBottom="16dp"
    android:background="@drawable/base_border">

    <ImageView
        android:id="@+id/iv_item_poster"
        android:layout_width="147dp"
        android:layout_height="88dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_placeholder" />

    <TextView
        android:id="@+id/tv_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="8dp"
        android:layout_toRightOf="@+id/iv_item_poster"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_27" />

    <TextView
        android:id="@+id/tv_item_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="10dp"
        android:layout_toRightOf="@+id/iv_item_poster"
        android:ellipsize="marquee"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_20" />

</RelativeLayout>


