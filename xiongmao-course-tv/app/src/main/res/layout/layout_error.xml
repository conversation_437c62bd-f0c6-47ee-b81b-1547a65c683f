<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/state_error_linear"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg3"
    android:gravity="center"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/state_error_img"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="@drawable/load_error"
        android:visibility="gone" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        android:text="@string/helper_loading_error_tip"
        android:textColor="@color/white"
        android:textSize="26sp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/state_error_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:text="@string/helper_loading_net_error"
        android:textColor="@color/white"
        android:textSize="26sp" />
</LinearLayout>
