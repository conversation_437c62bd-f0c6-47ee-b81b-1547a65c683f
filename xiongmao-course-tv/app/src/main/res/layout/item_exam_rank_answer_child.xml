<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_item_layout"
    style="@style/FocusCloseStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="horizontal"
    android:paddingLeft="16dp"
    android:paddingTop="10dp"
    android:paddingRight="16dp"
    android:paddingBottom="10dp">

    <ImageView
        android:id="@+id/iv_item_status"
        style="@style/FocusCloseStyle"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/icon_exam_ok"
        android:visibility="visible" />

    <CheckBox
        android:id="@+id/checkbox_item"
        style="@style/FocusCloseStyle"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginLeft="20dp"
        android:button="@drawable/shape_exam_checkbox" />

    <TextView
        android:id="@+id/tv_item_name"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="111"
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_24" />

    <ImageView
        android:id="@+id/iv_item_pic"
        style="@style/FocusCloseStyle"
        android:layout_width="300dp"
        android:layout_height="200dp"
        android:scaleType="centerCrop" />

</LinearLayout>



