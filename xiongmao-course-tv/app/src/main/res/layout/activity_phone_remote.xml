<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="50dp"
            android:layout_marginBottom="20dp"
            android:text="@string/remote_tips"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_36"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="50dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="200dp"
                android:gravity="center"
                android:orientation="vertical">

                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:padding="10dp">

                    <ImageView
                        android:layout_width="200dp"
                        android:layout_height="200dp"
                        android:src="@drawable/icon_qr_code_app_remote" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:text="@string/remote_tips_1"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_24" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:gravity="center|left|top"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:text="@string/remote_tips_2"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_setting_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_22"
            android:visibility="gone" />
    </LinearLayout>
</layout>