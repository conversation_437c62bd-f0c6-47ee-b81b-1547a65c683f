<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        tools:context=".ui.activity.PlayerActivity">


        <com.tencent.liteav.demo.superplayer.SuperPlayerView
            android:id="@+id/superVodPlayerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/superview_tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="20dp"
            android:layout_marginBottom="30dp"
            android:padding="10dp"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_24"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/superview_ll_more_tips_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="20dp"
            android:layout_marginBottom="30dp"
            android:background="@color/black"
            android:orientation="horizontal"
            android:paddingLeft="20dp"
            android:paddingTop="16dp"
            android:paddingRight="20dp"
            android:paddingBottom="16dp"
            android:visibility="gone"
            app:bl_corners_radius="6dp"
            app:bl_solid_color="@color/black">

            <TextView
                android:id="@+id/superview_tv_more_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center|left"
                android:padding="10dp"
                android:singleLine="true"
                android:text="网络状态不佳，建议切换\n清晰度至标清480P"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_24"
                android:visibility="visible" />

            <TextView
                android:id="@+id/superview_but_more_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:background="@color/green"
                android:gravity="center"
                android:minWidth="120dp"
                android:paddingLeft="10dp"
                android:paddingTop="6dp"
                android:paddingRight="10dp"
                android:paddingBottom="6dp"
                android:text="立即切换"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_21"
                app:bl_corners_radius="4dp"
                app:bl_solid_color="@color/green" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_error"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_bg3"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="网速太慢了，播放视频失败"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="建议尝试以下方法解决：\n1、把路由器断电1分钟再重新插上\n2、长按AI遥控器电源，选择'重新启动'\n\n如果以上方法还是不行，可以联系售后经理解决。"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26" />

            <Button
                android:id="@+id/but_retry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/base_border_selecter"
                android:paddingLeft="50dp"
                android:paddingTop="10dp"
                android:paddingRight="50dp"
                android:paddingBottom="10dp"
                android:text="重试"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_company"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_company_head"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:src="@drawable/def_pic" />

            <TextView
                android:id="@+id/tv_company_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size_60" />
        </LinearLayout>


        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="30dp"
            android:layout_marginRight="30dp"
            android:src="@drawable/def_pic"
            android:alpha="0.5"
            android:visibility="visible" />

        <FrameLayout
            android:id="@+id/fl_debug_content"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_below="@+id/iv_logo"
            android:layout_alignParentRight="true"
            android:layout_marginTop="30dp"
            android:layout_marginRight="50dp"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_debug_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text=""
                android:textColor="@color/red"
                android:textSize="@dimen/font_size_20" />

        </FrameLayout>


        <com.noober.background.view.BLRelativeLayout
            android:id="@+id/rl_share_course_layout"
            style="@style/FocusCloseStyle"
            android:layout_width="700dp"
            android:layout_height="340dp"
            android:layout_centerInParent="true"
            android:background="@color/white"
            android:visibility="gone"
            app:bl_corners_radius="4dp"
            app:bl_solid_color="@color/white">

            <com.noober.background.view.BLTextView
                android:id="@+id/tv_share_course_tips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="10dp"
                android:gravity="center"
                android:paddingLeft="20dp"
                android:paddingTop="10dp"
                android:paddingRight="20dp"
                android:paddingBottom="10dp"
                android:text="@string/base_back_text"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20"
                android:textStyle="bold"
                app:bl_gradient_angle="0"
                app:bl_gradient_endColor="#03AF82"
                app:bl_gradient_startColor="#07B74C" />

            <ImageView
                android:id="@+id/iv_share_course"
                android:layout_width="400dp"
                android:layout_height="230dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:scaleType="centerCrop"
                android:src="@drawable/icon_placeholder" />

            <com.noober.background.view.BLLinearLayout
                android:id="@+id/ll_qr_code_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginRight="20dp"
                android:layout_toRightOf="@+id/iv_share_course"
                android:gravity="center"
                android:orientation="vertical"
                app:bl_corners_radius="4dp"
                app:bl_stroke_color="@color/gray"
                app:bl_stroke_width="1dp">

                <ImageView
                    android:id="@+id/iv_share_course_qrcode"
                    android:layout_width="200dp"
                    android:layout_height="200dp"
                    android:layout_marginLeft="14dp"
                    android:layout_marginTop="14dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="14dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/icon_placeholder" />

            </com.noober.background.view.BLLinearLayout>

            <TextView
                android:id="@+id/tv_share_course_pay_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_qr_code_layout"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:text="微信扫码购买该课程，可以随时随地观看"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size_20" />

            <TextView
                android:id="@+id/tv_share_title_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_qr_code_layout"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black"
                android:textSize="@dimen/font_size_20"
                android:visibility="gone" />
        </com.noober.background.view.BLRelativeLayout>

        <!--视频控制器-->
        <FrameLayout
            android:id="@+id/fl_controller"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentBottom="true"
            android:background="@drawable/superplayer_bottom_bg"
            android:visibility="gone">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scrollView_controller"
                style="@style/FocusCloseStyle"
                android:layout_width="match_parent"
                android:layout_height="380dp"
                android:layout_gravity="bottom"
                android:paddingTop="16dp"
                android:paddingBottom="16dp">

                <LinearLayout
                    style="@style/FocusCloseStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <com.panda.course.widget.IQYMenuGroup
                        android:id="@+id/menu_common_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/menu_tv_common_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:text="快捷操作"
                            android:textColor="@color/gray_1"
                            android:textSize="@dimen/font_size_32"
                            android:textStyle="bold" />

                        <com.panda.course.widget.FocusKeepRecyclerView
                            android:id="@+id/grid_common"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nextFocusDown="@id/grid_course" />
                    </com.panda.course.widget.IQYMenuGroup>

                    <com.panda.course.widget.IQYMenuGroup
                        android:id="@+id/menu_knowledge_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_grid_knowledge_points"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:text="本节知识点"
                            android:textColor="@color/gray_1"
                            android:textSize="@dimen/font_size_32"
                            android:textStyle="bold" />

                        <com.panda.course.widget.FocusKeepRecyclerView
                            android:id="@+id/grid_knowledge_points"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nextFocusUp="@id/grid_course"
                            android:nextFocusDown="@id/menu_course_group" />
                    </com.panda.course.widget.IQYMenuGroup>

                    <com.panda.course.widget.IQYMenuGroup
                        android:id="@+id/menu_course_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_grid_course_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:text="本课程列表"
                            android:textColor="@color/gray_1"
                            android:textSize="@dimen/font_size_32"
                            android:textStyle="bold" />

                        <com.panda.course.widget.FocusKeepRecyclerView
                            android:id="@+id/grid_course"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nextFocusUp="@id/grid_knowledge_points"
                            android:nextFocusDown="@id/grid_definition" />
                    </com.panda.course.widget.IQYMenuGroup>

                    <com.panda.course.widget.IQYMenuGroup
                        android:id="@+id/menu_definition_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/menu_tv_definition_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:text="清晰度"
                            android:textColor="@color/gray_1"
                            android:textSize="@dimen/font_size_32"
                            android:textStyle="bold" />

                        <com.panda.course.widget.FocusKeepRecyclerView
                            android:id="@+id/grid_definition"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nextFocusUp="@id/grid_course"
                            android:nextFocusDown="@id/grid_speed" />
                    </com.panda.course.widget.IQYMenuGroup>

                    <com.panda.course.widget.IQYMenuGroup
                        android:id="@+id/menu_speed_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/menu_tv_speed_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:text="倍速播放"
                            android:textColor="@color/gray_1"
                            android:textSize="@dimen/font_size_32"
                            android:textStyle="bold" />

                        <com.panda.course.widget.FocusKeepRecyclerView
                            android:id="@+id/grid_speed"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nextFocusDown="@id/grid_definition" />
                    </com.panda.course.widget.IQYMenuGroup>

                    <com.panda.course.widget.IQYMenuGroup
                        android:id="@+id/menu_sub_title_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_sub_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:text="字幕"
                            android:textColor="@color/gray_1"
                            android:textSize="@dimen/font_size_32"
                            android:textStyle="bold" />

                        <com.panda.course.widget.FocusKeepRecyclerView
                            android:id="@+id/grid_sub_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nextFocusDown="@id/grid_definition" />
                    </com.panda.course.widget.IQYMenuGroup>
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

        </FrameLayout>


    </RelativeLayout>
</layout>