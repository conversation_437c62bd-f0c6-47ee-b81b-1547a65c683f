<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.activity.dialog.ExpandInterfaceActivity">

        <ImageView
            android:id="@+id/iv_red_paper_big_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY" />

        <ImageView
            android:id="@+id/iv_red_paper_drift_down"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <FrameLayout
            android:id="@+id/fl_red_paper_qr_code_layout"
            android:layout_width="300dp"
            android:layout_height="450dp"
            android:layout_marginLeft="100dp"
            android:layout_marginTop="170dp"
            android:background="@drawable/red_paper_qr_code_bg"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_red_paper_qr_code"
                android:layout_width="240dp"
                android:layout_height="240dp"
                android:layout_gravity="center"
                android:layout_marginTop="20dp"
                android:src="@drawable/min_wx_code" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center"
                android:layout_marginBottom="40dp"
                android:text="使用微信扫一扫"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size_22"
                android:textStyle="bold" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_red_paper_rank_layout"
            android:layout_width="300dp"
            android:layout_height="450dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="170dp"
            android:layout_marginRight="100dp"
            android:background="@drawable/red_paper_rank_bg"
            android:visibility="gone">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="350dp"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="80dp"
                android:layout_marginRight="32dp" />

            <TextView
                android:id="@+id/tv_recycler_empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="50dp"
                android:text="还没有人抢红包\n快扫码抢红包吧！"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size_22"
                android:textStyle="bold" />
        </FrameLayout>

        <TextView
            android:id="@+id/tv_red_paper_start_tips"
            android:layout_width="540dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="100dp"
            android:layout_marginTop="300dp"
            android:layout_marginRight="100dp"
            android:gravity="center"
            android:text=""
            android:textColor="#FFFEF6C8"
            android:textSize="@dimen/font_size_36"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_red_paper_first_down"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="290dp"
            android:layout_marginTop="220dp"
            android:gravity="center"
            android:minWidth="150dp"
            android:paddingLeft="40dp"
            android:paddingTop="10dp"
            android:paddingRight="40dp"
            android:paddingBottom="10dp"
            android:text="0s"
            android:textColor="#FEF6C8"
            android:textSize="@dimen/font_size_30"
            android:textStyle="bold"
            android:visibility="gone"
            app:bl_corners_radius="50dp"
            app:bl_gradient_endColor="#FB5931"
            app:bl_gradient_startColor="#E33B0E"
            app:bl_stroke_color="#FEF6C8"
            app:bl_stroke_width="4dp" />

        <ImageView
            android:id="@+id/iv_red_title_logo"
            android:layout_width="500dp"
            android:layout_height="180dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:src="@drawable/red_paper_title_logo"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_red_paper_down"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="180dp"
            android:gravity="center"
            android:minWidth="150dp"
            android:paddingLeft="40dp"
            android:paddingTop="10dp"
            android:paddingRight="40dp"
            android:paddingBottom="10dp"
            android:text="0s"
            android:textColor="#FEF6C8"
            android:textSize="@dimen/font_size_30"
            android:textStyle="bold"
            android:visibility="gone"
            app:bl_corners_radius="50dp"
            app:bl_gradient_endColor="#FB5931"
            app:bl_gradient_startColor="#E33B0E"
            app:bl_stroke_color="#FEF6C8"
            app:bl_stroke_width="4dp" />

        <ImageView
            android:layout_marginBottom="-240dp"
            android:id="@+id/iv_red_paper_yao"
            android:layout_width="700dp"
            android:layout_height="800dp"
            android:layout_alignParentBottom="true"
            android:layout_centerInParent="true" />

        <FrameLayout
            android:id="@+id/fl_red_paper_finish_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/color_dark_transparent" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@color/white"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingLeft="150dp"
                android:paddingTop="50dp"
                android:paddingRight="150dp"
                android:paddingBottom="50dp"
                app:bl_corners_radius="10dp"
                app:bl_solid_color="@color/white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="抢红包活动已结束"
                    android:textColor="#E33B0E"
                    android:textSize="@dimen/font_size_32"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="请继续看课吧"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_28" />

                <TextView
                    android:id="@+id/tv_red_paper_finish_down"
                    android:layout_width="250dp"
                    android:layout_height="250dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:text="0s"
                    android:textColor="#FEF6C8"
                    android:textSize="@dimen/font_size_70"
                    android:textStyle="bold"
                    app:bl_corners_radius="200dp"
                    app:bl_gradient_endColor="#FB5931"
                    app:bl_gradient_startColor="#E33B0E"
                    app:bl_stroke_color="#FEF6C8"
                    app:bl_stroke_width="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="倒计时结束后接着播放课程"
                    android:textColor="#E33B0E"
                    android:textSize="@dimen/font_size_30" />

            </LinearLayout>
        </FrameLayout>

    </RelativeLayout>
</layout>