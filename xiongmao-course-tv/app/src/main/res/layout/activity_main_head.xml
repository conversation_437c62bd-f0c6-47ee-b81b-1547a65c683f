<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data android:layout_width="match_parent">

    </data>

    <RelativeLayout
        android:id="@+id/rl_head_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:clipToPadding="false"
        android:focusable="false"
        android:paddingLeft="35dp"
        android:paddingRight="35dp">

        <Button
            android:id="@+id/tv_login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingLeft="30dp"
            android:paddingTop="4dp"
            android:paddingRight="30dp"
            android:paddingBottom="4dp"
            android:text="立即登录"
            android:textColor="@color/white"
            android:textSize="32sp"
            app:bl_corners_radius="50dp"
            app:bl_solid_color="@color/base_bg" />

        <LinearLayout
            android:id="@+id/ll_user_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:focusable="false"
            android:gravity="center"
            android:paddingLeft="20dp"
            android:paddingTop="4dp"
            android:paddingRight="20dp"
            android:paddingBottom="4dp"
            app:bl_corners_radius="100dp"
            app:bl_solid_color="@color/base_bg">

            <ImageView
                android:id="@+id/iv_head_pic"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/icon_placeholder" />

            <TextView
                android:id="@+id/tv_head_store_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:textColor="@color/white"
                android:textSize="32sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerInParent="true"
            android:text=""
            android:textColor="@color/white"
            android:textSize="32sp"
            android:textStyle="bold" />

    </RelativeLayout>
</layout>