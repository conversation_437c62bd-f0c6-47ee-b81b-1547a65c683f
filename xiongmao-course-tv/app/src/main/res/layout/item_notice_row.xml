<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/row_card_notice_view"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:layout_marginLeft="20dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="20dp"
    android:layout_marginBottom="10dp"
    android:focusedByDefault="true"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <!--    <com.panda.course.widget.RoundCornerImageView-->
    <!--        android:id="@+id/iv_item_row"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="120dp"-->
    <!--        android:background="@drawable/base_border_unselecter"-->
    <!--        android:focusable="true"-->
    <!--        android:focusableInTouchMode="true"-->
    <!--        android:gravity="center"-->
    <!--        android:visibility="visible"-->
    <!--        app:corner_size="20" />-->

    <TextView
        android:id="@+id/iv_item_notice_row"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/base_border"
        android:gravity="center"
        android:text="消息通知"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_30"
        android:textStyle="bold" />


</androidx.cardview.widget.CardView>


