<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#0C1D26"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/main_title"
            android:textColor="@color/white"
            android:textSize="50dp" />

        <LinearLayout
            android:id="@+id/ll_qr_layout"
            android:layout_width="400dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:background="@color/white"
            android:orientation="vertical"
            app:bl_corners_radius="2dp"
            app:bl_solid_color="@color/white">

            <ImageView
                android:id="@+id/iv_splash_login"
                android:layout_width="match_parent"
                android:layout_height="400dp"
                android:scaleType="fitXY" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:lineSpacingMultiplier="1.2"
                android:text="@string/main_title_tips"
                android:textColor="@color/black"
                android:textSize="26dp" />
        </LinearLayout>

    </LinearLayout>
</layout>