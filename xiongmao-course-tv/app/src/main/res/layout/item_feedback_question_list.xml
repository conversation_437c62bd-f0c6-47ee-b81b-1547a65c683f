<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/row_card_feedback_list_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="40dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="40dp"
    android:layout_marginBottom="10dp"
    android:focusedByDefault="true"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/base_border_unselecter"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_feedback_title_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingLeft="30dp"
            android:paddingTop="20dp"
            android:paddingRight="30dp"
            android:paddingBottom="20dp">


            <TextView
                android:id="@+id/tv_item_feedback_list_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_toRightOf="@+id/tv_item_feedback_list_status"
                android:layout_weight="1"
                android:text="帮助通知"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26"
                android:textStyle="bold" />

        </LinearLayout>


        <TextView
            android:id="@+id/tv_item_feedback_list_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:layerType="software"
            android:paddingLeft="20dp"
            android:paddingTop="20dp"
            android:paddingRight="20dp"
            android:textColor="@color/black"
            android:textSize="@dimen/font_size_24"
            android:visibility="gone" />

    </LinearLayout>
</androidx.cardview.widget.CardView>


