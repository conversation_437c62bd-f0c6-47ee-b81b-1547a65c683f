<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tv_main_title"
    android:layout_width="wrap_content"
    android:layout_height="30dp"
    android:background="@drawable/selector_focus_bg_corner15_without_default_bg"
    android:clickable="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:gravity="center"
    android:paddingStart="10dp"
    android:paddingEnd="10dp"
    android:textColor="@color/white"
    android:textSize="20sp" />
