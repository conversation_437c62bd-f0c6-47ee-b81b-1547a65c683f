<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:orientation="horizontal"
        tools:context=".ui.activity.dialog.SkillRecordActivity">

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/recycler_child"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="20dp"
            android:layout_toRightOf="@+id/recycler_row"
            android:layout_weight="1" />

        <FrameLayout
            android:id="@+id/fl_controller"
            style="@style/FocusCloseStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="20dp"
            android:layout_weight="1">

            <WebView
                android:id="@+id/webView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="6dp"
                android:fadeScrollbars="false"
                android:focusable="true"
                android:scrollbarThumbVertical="@drawable/custom_scrollbar_focus"
                android:scrollbarTrackVertical="@drawable/custom_scrollbar_normal"
                android:scrollbars="vertical" />

            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_gravity="bottom"
                android:background="@color/gray_1"
                android:drawableBottom="@drawable/icon_arrow_down"
                android:gravity="center"
                android:text="按遥控器下键查看更多错题"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:visibility="gone" />

            <ProgressBar
                android:id="@+id/progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center" />

        </FrameLayout>

    </LinearLayout>
</layout>