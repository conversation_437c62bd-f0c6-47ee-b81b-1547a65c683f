<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/white"
        tools:context=".ui.activity.dialog.MarketingDialogActivity">

        <TextView
            android:id="@+id/title"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/base_border_selecter_top_selecter"
            android:gravity="center"
            android:padding="16dp"
            android:text="发放记录"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_30" />

        <TextView
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:padding="16dp"
            android:text="@string/base_back_text"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_30" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="500dp"
            android:layout_below="@+id/title" />
    </RelativeLayout>
</layout>