<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_item_top_layout"
    style="@style/FocusCloseStyle"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="5dp"
    android:layout_marginBottom="5dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_item_number"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:minWidth="64dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:textSize="@dimen/font_size_26"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="#E3B666" />

    <RelativeLayout
        android:id="@+id/rl_info_layout"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:minHeight="70dp"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="#E3B666">

        <ImageView
            android:id="@+id/iv_item_pic"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginLeft="10dp" />

        <TextView
            android:id="@+id/tv_item_name_sign"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@+id/iv_item_pic"
            android:ellipsize="end"
            android:maxEms="6"
            android:singleLine="true"
            android:text="我是某某某"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_26"
            android:textStyle="bold"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_item_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@+id/iv_item_pic"
            android:ellipsize="end"
            android:maxEms="6"
            android:singleLine="true"
            android:text="我是某某某"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_26"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_item_score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_item_name"
            android:layout_gravity="center"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@+id/iv_item_pic"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="1000分"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_22"
            android:textStyle="bold" />
    </RelativeLayout>
</LinearLayout>


