<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content_card_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="160dp"
    android:layout_marginBottom="20dp"
    android:clipChildren="false"
    android:clipToPadding="false"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">


    <FrameLayout
        android:id="@+id/fl_item_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="2dp"
        android:visibility="gone"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/transparent"
        app:bl_stroke_color="@color/white"
        app:bl_stroke_width="2dp"
        tools:ignore="MissingPrefix">


    </FrameLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        app:bl_corners_bottomLeftRadius="10dp"
        app:bl_corners_bottomRightRadius="10dp">

        <ImageView
            android:id="@+id/iv_item_big_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="visible" />


        <ImageView
            android:id="@+id/iv_item_shadow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/tv_item_tips"
            android:scaleType="fitXY"
            android:src="@drawable/bg_item_map"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_item_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:ellipsize="end"
            android:paddingLeft="20dp"
            android:paddingTop="2dp"
            android:paddingRight="20dp"
            android:paddingBottom="2dp"
            android:singleLine="true"
            android:text="遥控器确认键全屏查看"
            android:textColor="@color/black"
            android:textSize="@dimen/font_size_24"
            android:visibility="gone"
            app:bl_corners_bottomLeftRadius="8dp"
            app:bl_corners_bottomRightRadius="8dp"
            app:bl_solid_color="@color/white"
            tools:ignore="MissingPrefix" />
    </RelativeLayout>

</androidx.cardview.widget.CardView>