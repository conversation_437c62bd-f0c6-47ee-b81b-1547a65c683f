<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        tools:context=".ui.activity.dialog.SurpriseDialogActivity">

        <ImageView
            android:id="@+id/iv_surprise"
            style="@style/FocusCloseStyle"
            android:layout_width="500dp"
            android:layout_height="400dp"
            android:src="@drawable/surprise_bg" />

        <Button
            android:id="@+id/tv_surprise_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/base_border_round"
            android:gravity="center"
            android:nextFocusLeft="@id/tv_surprise_ok"
            android:nextFocusRight="@id/tv_surprise_ok"
            android:nextFocusUp="@id/tv_surprise_ok"
            android:nextFocusDown="@id/tv_surprise_ok"
            android:paddingLeft="30dp"
            android:paddingTop="10dp"
            android:paddingRight="30dp"
            android:paddingBottom="10dp"
            android:text="按【OK键】查看详情"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_30" />
    </LinearLayout>
</layout>