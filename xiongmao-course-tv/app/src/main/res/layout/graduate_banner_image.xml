<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <ImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />


    <TextView
        android:id="@+id/tv_store_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="300dp"
        android:layout_marginTop="280dp"
        android:textColor="@color/red"
        android:textSize="@dimen/font_size_36"
        android:textStyle="bold" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/tv_store_student"
        android:layout_width="match_parent"
        android:layout_height="165dp"
        android:layout_below="@+id/tv_store_content"
        android:layout_marginLeft="250dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="200dp"
        android:maxLines="3"
        android:textColor="@color/red"
        android:textSize="@dimen/font_size_28" />

    <TextView
        android:id="@+id/tv_store_tip1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center"
        android:layout_marginLeft="100dp"
        android:layout_marginBottom="80dp"
        android:gravity="center"
        android:text="特此表彰，以资鼓励"
        android:textColor="@color/red"
        android:textSize="@dimen/font_size_24"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="100dp"
        android:layout_marginBottom="80dp"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_store_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text=""
            android:textColor="@color/red"
            android:textSize="@dimen/font_size_28"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text=""
            android:textColor="@color/black"
            android:textSize="@dimen/font_size_28"
            android:visibility="visible" />
    </LinearLayout>


</RelativeLayout>