<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:orientation="vertical"
        android:clipChildren="false"
        android:clipToPadding="false"
        tools:context=".ui.activity.SettingsActivity">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="50dp"
            android:layout_marginBottom="20dp"
            android:text="@string/sys_tips_text"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_36"
            android:textStyle="bold" />

        <com.panda.course.widget.AppVerticalGridView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:paddingLeft="20dp"
            android:paddingRight="20dp" />

        <TextView
            android:id="@+id/tv_setting_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_marginBottom="20dp"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_22" />
    </LinearLayout>
</layout>