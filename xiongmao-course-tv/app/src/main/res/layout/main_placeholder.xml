<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:id="@+id/ll_placeholder_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#0C1D26"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.28"
            android:background="#1F2E37"
            android:orientation="vertical"
            android:paddingLeft="20dp"
            android:paddingTop="20dp"
            android:paddingRight="20dp"
            android:paddingBottom="20dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="111dp"
                android:layout_margin="10dp"
                app:bl_corners_radius="6dp"
                app:bl_solid_color="@color/color_light_gray" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="111dp"
                android:layout_margin="10dp"
                app:bl_corners_radius="6dp"
                app:bl_solid_color="@color/color_light_gray" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="111dp"
                android:layout_margin="10dp"
                app:bl_corners_radius="6dp"
                app:bl_solid_color="@color/color_light_gray" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="111dp"
                android:layout_margin="10dp"
                app:bl_corners_radius="6dp"
                app:bl_solid_color="@color/color_light_gray" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="111dp"
                android:layout_margin="10dp"
                app:bl_corners_radius="6dp"
                app:bl_solid_color="@color/color_light_gray" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/fragment_head"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@+id/ll_left_layout"
                android:layout_toRightOf="@+id/ll_left_layout"
                android:clipToPadding="false"
                android:focusable="false"
                android:paddingLeft="20dp"
                android:paddingTop="20dp"
                android:paddingRight="30dp">

                <TextView
                    android:id="@+id/tv_fr_head_title"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:background="@color/color_light_gray"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="38sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_fr_head_lecturer"
                    android:layout_width="50dp"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/tv_fr_head_title"
                    android:layout_marginLeft="10dp"
                    android:layout_toRightOf="@+id/tv_fr_head_title"
                    android:background="@color/color_light_gray"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_fr_head_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_fr_head_title"
                    android:layout_marginTop="13dp"
                    android:layout_marginBottom="10dp"
                    android:background="@color/color_light_gray"
                    android:textSize="18dp" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="20dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="#1F2E37"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="164dp"
                        android:layout_height="83dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:bl_corners_radius="6dp"
                        app:bl_solid_color="@color/color_light_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/color_light_gray"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_19"
                            android:textStyle="bold"
                            app:bl_solid_color="@color/color_light_gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@color/color_light_gray"
                            android:ellipsize="end"
                            android:textSize="@dimen/font_size_16"
                            app:bl_solid_color="@color/color_light_gray" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="#1F2E37"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="164dp"
                        android:layout_height="83dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:bl_corners_radius="6dp"
                        app:bl_solid_color="@color/color_light_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/color_light_gray"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_19"
                            android:textStyle="bold"
                            app:bl_solid_color="@color/color_light_gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@color/color_light_gray"
                            android:ellipsize="end"
                            android:textSize="@dimen/font_size_16"
                            app:bl_solid_color="@color/color_light_gray" />
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="20dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="#1F2E37"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="164dp"
                        android:layout_height="83dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:bl_corners_radius="6dp"
                        app:bl_solid_color="@color/color_light_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/color_light_gray"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_19"
                            android:textStyle="bold"
                            app:bl_solid_color="@color/color_light_gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@color/color_light_gray"
                            android:ellipsize="end"
                            android:textSize="@dimen/font_size_16"
                            app:bl_solid_color="@color/color_light_gray" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="#1F2E37"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="164dp"
                        android:layout_height="83dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:bl_corners_radius="6dp"
                        app:bl_solid_color="@color/color_light_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/color_light_gray"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_19"
                            android:textStyle="bold"
                            app:bl_solid_color="@color/color_light_gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@color/color_light_gray"
                            android:ellipsize="end"
                            android:textSize="@dimen/font_size_16"
                            app:bl_solid_color="@color/color_light_gray" />
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="20dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="#1F2E37"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="164dp"
                        android:layout_height="83dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:bl_corners_radius="6dp"
                        app:bl_solid_color="@color/color_light_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/color_light_gray"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_19"
                            android:textStyle="bold"
                            app:bl_solid_color="@color/color_light_gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@color/color_light_gray"
                            android:ellipsize="end"
                            android:textSize="@dimen/font_size_16"
                            app:bl_solid_color="@color/color_light_gray" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="#1F2E37"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="164dp"
                        android:layout_height="83dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:bl_corners_radius="6dp"
                        app:bl_solid_color="@color/color_light_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/color_light_gray"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_19"
                            android:textStyle="bold"
                            app:bl_solid_color="@color/color_light_gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@color/color_light_gray"
                            android:ellipsize="end"
                            android:textSize="@dimen/font_size_16"
                            app:bl_solid_color="@color/color_light_gray" />
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="20dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="#1F2E37"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="164dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:bl_corners_radius="6dp"
                        app:bl_solid_color="@color/color_light_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/color_light_gray"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_19"
                            android:textStyle="bold"
                            app:bl_solid_color="@color/color_light_gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@color/color_light_gray"
                            android:ellipsize="end"
                            android:textSize="@dimen/font_size_16"
                            app:bl_solid_color="@color/color_light_gray" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="#1F2E37"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="164dp"
                        android:layout_height="83dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:bl_corners_radius="6dp"
                        app:bl_solid_color="@color/color_light_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/color_light_gray"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_19"
                            android:textStyle="bold"
                            app:bl_solid_color="@color/color_light_gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@color/color_light_gray"
                            android:ellipsize="end"
                            android:textSize="@dimen/font_size_16"
                            app:bl_solid_color="@color/color_light_gray" />
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="20dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="#1F2E37"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="164dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:bl_corners_radius="6dp"
                        app:bl_solid_color="@color/color_light_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/color_light_gray"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_19"
                            android:textStyle="bold"
                            app:bl_solid_color="@color/color_light_gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@color/color_light_gray"
                            android:ellipsize="end"
                            android:textSize="@dimen/font_size_16"
                            app:bl_solid_color="@color/color_light_gray" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:background="#1F2E37"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="10dp">

                    <TextView
                        android:layout_width="164dp"
                        android:layout_height="83dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:bl_corners_radius="6dp"
                        app:bl_solid_color="@color/color_light_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/color_light_gray"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_19"
                            android:textStyle="bold"
                            app:bl_solid_color="@color/color_light_gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@color/color_light_gray"
                            android:ellipsize="end"
                            android:textSize="@dimen/font_size_16"
                            app:bl_solid_color="@color/color_light_gray" />
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</layout>