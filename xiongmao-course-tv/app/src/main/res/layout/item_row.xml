<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/row_card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="20dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="20dp"
    android:layout_marginBottom="10dp"
    android:focusedByDefault="true"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">


    <com.panda.course.widget.RoundCornerImageView
        android:id="@+id/iv_item_row"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/base_border_unselecter"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center"
        android:visibility="visible"
        app:corner_size="20"
        app:label_backgroundColor="#FF8533"
        app:label_height="40dp"
        app:label_orientation="LEFT_TOP"
        app:label_strokeColor="@color/transparent"
        app:label_strokeWidth="0dp"
        app:label_text="新"
        app:label_textColor="@color/white"
        app:label_textSize="18dp"
        app:label_textStyle="BOLD" />

    <ImageView
        android:id="@+id/iv_item_is_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_is_new" />

</androidx.cardview.widget.CardView>


