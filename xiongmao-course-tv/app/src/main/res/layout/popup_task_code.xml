<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="320dp"
            android:layout_height="432dp"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_arrow_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:rotation="-90"
                android:src="@drawable/icon_arrow" />

            <ImageView
                android:id="@+id/iv_arrow_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:src="@drawable/icon_arrow" />

            <ImageView
                android:id="@+id/iv_popup_qr_code"
                android:layout_width="300dp"
                android:layout_height="300dp"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:src="@drawable/min_wx_code" />

            <ImageView
                android:id="@+id/iv_qr_type"
                android:layout_width="300dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/iv_popup_qr_code"
                android:layout_marginLeft="10dp"
                android:scaleType="fitXY"
                android:src="@drawable/teacher_bg" />

            <ImageView
                android:id="@+id/iv_arrow_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:rotation="180"
                android:src="@drawable/icon_arrow" />

            <ImageView
                android:id="@+id/iv_arrow_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:rotation="90"
                android:src="@drawable/icon_arrow" />
        </RelativeLayout>
    </RelativeLayout>

</layout>