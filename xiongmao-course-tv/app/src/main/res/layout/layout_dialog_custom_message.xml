<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingLeft="30dp"
    android:paddingTop="20dp"
    android:paddingRight="30dp"
    android:paddingBottom="20dp"
    app:bl_corners_radius="10dp"
    app:bl_solid_color="@color/white">


    <TextView
        android:id="@+id/iv_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="我是标题"
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_34"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/iv_dialog_sub"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="这里是内容"
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_28" />

    <Button
        android:id="@+id/but_dialog_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="50dp"
        android:gravity="center"
        android:paddingLeft="34dp"
        android:paddingTop="6dp"
        android:paddingRight="34dp"
        android:paddingBottom="6dp"
        android:text="我知道了"
        android:textColor="@color/white"
        android:textSize="30sp"
        android:textStyle="bold"
        app:bl_corners_radius="50dp"
        app:bl_solid_color="@color/green" />

</LinearLayout>