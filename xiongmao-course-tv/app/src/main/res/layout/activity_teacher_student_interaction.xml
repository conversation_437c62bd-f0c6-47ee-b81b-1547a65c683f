<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:id="@+id/ll_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/popup_drawer_bg">

        <LinearLayout
            android:layout_width="320dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="190dp"
            android:layout_marginTop="50dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_teacher"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center"
                android:src="@drawable/def_pic" />

            <TextView
                android:id="@+id/tv_teacher_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26" />
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rl_qr_teacher_layout"
            android:layout_width="320dp"
            android:layout_height="432dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="150dp"
            android:orientation="vertical"
            android:visibility="visible">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:rotation="-90"
                android:src="@drawable/icon_arrow" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:src="@drawable/icon_arrow" />

            <ImageView
                android:id="@+id/iv_popup_qr_teacher_code"
                android:layout_width="300dp"
                android:layout_height="300dp"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:src="@drawable/min_wx_code" />

            <ImageView
                android:layout_width="300dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/iv_popup_qr_teacher_code"
                android:layout_marginLeft="10dp"
                android:scaleType="fitXY"
                android:src="@drawable/teacher_bg" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:rotation="180"
                android:src="@drawable/icon_arrow" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:rotation="90"
                android:src="@drawable/icon_arrow" />
        </RelativeLayout>


        <ImageView
            android:id="@+id/view_line"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:layout_toRightOf="@+id/rl_qr_teacher_layout"
            android:rotation="180"
            android:src="@drawable/icon_base_arrow"
            android:visibility="visible" />


        <FrameLayout
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:layout_toRightOf="@+id/view_line">

            <TextView
                android:id="@+id/tv_empty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black1"
                android:gravity="center"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:text="还没有互动内容\n请扫码和老师互动吧"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold"
                app:bl_corners_radius="6dp"
                app:bl_solid_color="@color/black1" />

            <ProgressBar
                android:id="@+id/progress"
                style="@style/FocusCloseStyle"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:visibility="gone" />

            <com.panda.course.widget.AppVerticalGridView
                android:id="@+id/recycler_task"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingTop="20dp" />
        </FrameLayout>

    </RelativeLayout>

</layout>