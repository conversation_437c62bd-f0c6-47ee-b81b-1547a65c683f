<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:orientation="vertical"
        tools:context=".ui.activity.SystemInfoActivity">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="50dp"
            android:layout_marginBottom="20dp"
            android:text="系统信息"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_36"
            android:textStyle="bold" />

        <com.panda.course.widget.AppVerticalGridView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:layout_marginLeft="100dp"
            android:layout_marginRight="100dp"
            android:layout_marginBottom="50dp"
            android:layout_weight="1"
            android:background="@color/black1" />
    </LinearLayout>
</layout>