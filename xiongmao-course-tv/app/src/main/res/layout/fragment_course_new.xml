<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/fragment_head"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@+id/rv_cover"
            android:layout_toRightOf="@+id/rv_cover"
            android:clipToPadding="false"
            android:focusable="false"
            android:paddingLeft="45dp"
            android:paddingTop="13pt"
            android:paddingRight="45dp">

            <TextView
                android:id="@+id/tv_fr_head_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textColor="@color/white"
                android:textSize="20pt"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_fr_head_lecturer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:text=""
                android:textColor="@color/white"
                android:textSize="16pt"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_fr_head_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_fr_head_title"
                android:layout_marginTop="13pt"
                android:layout_marginBottom="10pt"
                android:background="@color/base_bg"
                android:padding="10pt"
                android:text=""
                android:textColor="@color/white"
                android:textSize="12pt" />
        </RelativeLayout>


    </LinearLayout>
</layout>