<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.activity.dialog.SurpriseContentActivity">

        <ImageView
            android:id="@+id/iv_root"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/surprise_content_bg" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:paddingTop="50dp" />

        <Button
            android:id="@+id/tv_surprise_record"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="50dp"
            android:layout_marginRight="40dp"
            android:background="@drawable/base_border_round_record"
            android:gravity="center"
            android:nextFocusLeft="@id/tv_surprise_record"
            android:nextFocusRight="@id/tv_surprise_record"
            android:nextFocusUp="@id/tv_surprise_record"
            android:nextFocusDown="@id/tv_surprise_close"
            android:paddingLeft="30dp"
            android:paddingTop="10dp"
            android:paddingRight="30dp"
            android:paddingBottom="10dp"
            android:text="发放记录(0)"
            android:textColor="#DB5C63"
            android:textSize="@dimen/font_size_30" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_above="@+id/ll_surprise_bottom_layout"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="10dp"
            android:background="@color/white" />

        <LinearLayout
            android:id="@+id/ll_surprise_bottom_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="10dp"
            android:paddingBottom="10dp">

            <TextView
                android:id="@+id/tv_surprise_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="20dp"
                android:layout_weight="1"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_18" />

            <Button
                android:id="@+id/tv_surprise_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_gravity="center|bottom"
                android:layout_marginRight="40dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/base_border_round"
                android:focusable="true"
                android:nextFocusLeft="@id/tv_surprise_close"
                android:nextFocusRight="@id/tv_surprise_close"
                android:nextFocusUp="@id/tv_surprise_record"
                android:nextFocusDown="@id/tv_surprise_close"
                android:paddingLeft="30dp"
                android:paddingTop="10dp"
                android:paddingRight="30dp"
                android:paddingBottom="10dp"
                android:text="按【返回键】返回"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30" />
        </LinearLayout>

    </RelativeLayout>
</layout>