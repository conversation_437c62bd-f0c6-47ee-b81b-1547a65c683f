<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/FocusCloseStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="4dp"
    android:clipToPadding="false"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingLeft="10dp"
    android:paddingTop="2dp"
    android:paddingRight="10dp"
    android:paddingBottom="2dp"
    app:bl_stroke_color="@color/red"
    app:bl_corners_radius="10dp"
    app:bl_solid_color="#E3B666"
    app:bl_stroke_width="1dp">

    <ImageView
        android:id="@+id/iv_red_paper"
        android:layout_width="40dp"
        android:layout_height="40dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_red_paper_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxEms="10"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="@dimen/font_size_18" />

        <TextView
            android:id="@+id/tv_red_paper_money"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/red"
            android:textSize="@dimen/font_size_16" />
    </LinearLayout>

</LinearLayout>
