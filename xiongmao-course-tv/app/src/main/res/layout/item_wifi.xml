<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:id="@+id/ll_item_wifi_layout"
        style="@style/FocusStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/blue1"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="20dp"
        tools:context=".ui.activity.wifi.WiFiActivity">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_wifi_logo" />

        <TextView
            android:id="@+id/tv_item_wifi_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_weight="1"
            android:text="无线网络"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_30" />

        <TextView
            android:id="@+id/tv_item_wifi_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_24" />

        <ImageView
            android:id="@+id/iv_item_wifi_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_wifi_lock" />
    </LinearLayout>
</layout>