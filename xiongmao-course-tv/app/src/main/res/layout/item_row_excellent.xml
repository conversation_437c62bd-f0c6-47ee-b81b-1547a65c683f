<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fl_item_layout"
    style="@style/FocusStyle"
    android:layout_width="173dp"
    android:layout_height="210dp"
    android:layout_marginLeft="16dp"
    android:layout_marginRight="16dp"
    android:background="@drawable/shape_rank_empty_selector"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:focusedByDefault="true">

    <TextView
        android:id="@+id/tv_item_index_tag"
        style="@style/FocusCloseStyle"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_excellent_rank_tag"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingTop="10dp"
        android:paddingRight="16dp"
        android:paddingBottom="16dp"
        android:text="0"
        android:textColor="#FFF2D3BA"
        android:textSize="20dp" />

    <LinearLayout
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="16dp"
        android:paddingTop="20dp"
        android:paddingRight="16dp"
        android:paddingBottom="20dp">

        <ImageView
            android:id="@+id/iv_item_pic"
            style="@style/FocusCloseStyle"
            android:layout_width="80dp"
            android:layout_height="80dp" />

        <TextView
            android:id="@+id/iv_item_name"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:singleLine="true"
            android:textColor="#FF890613"
            android:textSize="@dimen/font_size_18"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/iv_item_content"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:textColor="#FF1A1A1A"
            android:textSize="@dimen/font_size_16" />
    </LinearLayout>
</FrameLayout>