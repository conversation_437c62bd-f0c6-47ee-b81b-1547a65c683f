<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_item_layout"
    style="@style/FocusCloseStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingLeft="20dp"
    android:paddingTop="10dp"
    android:paddingRight="20dp"
    android:paddingBottom="10dp">

    <TextView
        android:id="@+id/tv_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_30" />

    <TextView
        android:id="@+id/tv_item_sub"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/gray_1"
        android:textSize="@dimen/font_size_26" />

</LinearLayout>



