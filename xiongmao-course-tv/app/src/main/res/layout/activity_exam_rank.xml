<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:orientation="horizontal"
        tools:context=".ui.activity.dialog.SkillRecordActivity">

        <LinearLayout
            style="@style/FocusCloseStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="20dp">

                <TextView
                    android:id="@+id/tv_popup_day"
                    style="@style/FocusStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/base_border_round"
                    android:defaultFocusHighlightEnabled="true"
                    android:focusedByDefault="true"
                    android:gravity="center"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="当天排行榜"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_30"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_popup_interaction"
                    style="@style/FocusStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="@drawable/base_border_round"
                    android:gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="10dp"
                    android:text="当月排行榜"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_30"
                    android:textStyle="bold" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_child"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="20dp" />
        </LinearLayout>

        <FrameLayout
            android:id="@+id/fl_controller"
            style="@style/FocusStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="20dp"
            android:layout_weight="1">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler"
                style="@style/FocusCloseStyle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="6dp"
                android:fadeScrollbars="false"
                android:focusable="true" />

            <ProgressBar
                android:id="@+id/progress"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_gravity="bottom"
                android:background="@color/gray_1"
                android:drawableBottom="@drawable/icon_arrow_down"
                android:gravity="center"
                android:text="按遥控器下键查看更多错题"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:visibility="gone" />
        </FrameLayout>

    </LinearLayout>
</layout>