<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/row_card_notice_list_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="40dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="40dp"
    android:layout_marginBottom="10dp"
    android:focusedByDefault="true"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/base_border_unselecter"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_notice_title_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingLeft="30dp"
            android:paddingTop="20dp"
            android:paddingRight="30dp"
            android:paddingBottom="20dp">

            <TextView
                android:id="@+id/tv_item_notice_list_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingTop="4dp"
                android:paddingRight="10dp"
                android:paddingBottom="4dp"
                android:text="未读"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_16"
                app:bl_corners_radius="100dp"
                app:bl_solid_color="@color/red"
                app:bl_stroke_color="@color/white"
                app:bl_stroke_width="2dp"
                tools:ignore="MissingPrefix" />

            <TextView
                android:id="@+id/tv_item_notice_list_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_toRightOf="@+id/tv_item_notice_list_status"
                android:layout_weight="1"
                android:text="课程上架通知"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26"
                android:textStyle="bold" />


            <TextView
                android:id="@+id/tv_item_notice_list_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:text="课程上架通知"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26"
                android:textStyle="bold" />
        </LinearLayout>


        <FrameLayout
            android:id="@+id/ll_item_notice_list_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_item_notice_list_title"
            android:background="@color/white"
            android:orientation="vertical"
            android:paddingLeft="20dp"
            android:paddingTop="20dp"
            android:paddingRight="20dp"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_item_notice_list_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_item_notice_list_view"
                android:background="@android:color/transparent"
                android:layerType="software"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size_24"
                android:visibility="visible" />

            <Button
                android:id="@+id/but_item_notice_look"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center|bottom"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/base_border_selecter_red"
                android:paddingLeft="50dp"
                android:paddingRight="50dp"
                android:text="按确认键查看课程"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26" />
        </FrameLayout>


    </LinearLayout>
</androidx.cardview.widget.CardView>


