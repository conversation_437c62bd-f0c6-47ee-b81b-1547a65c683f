<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:orientation="horizontal"
        tools:context=".ui.activity.SkillActivity">

        <TextView
            android:id="@+id/tv_custom_title"
            android:layout_width="280dp"
            android:layout_height="wrap_content"
            android:background="@color/base_bg2"
            android:gravity="center"
            android:paddingTop="30dp"
            android:text="帮助与反馈"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_34"
            android:textStyle="bold" />

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/recycler_row"
            android:layout_width="280dp"
            android:layout_height="match_parent"
            android:layout_below="@+id/tv_custom_title"
            android:layout_alignParentStart="true"
            android:layout_alignParentLeft="true"
            android:layout_gravity="center"
            android:background="@color/base_bg2"
            android:paddingTop="20dp"
            android:paddingBottom="10dp" />

        <RelativeLayout
            android:id="@+id/rl_custom_layou"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toRightOf="@+id/recycler_row"
            android:background="@color/base_bg3"
            android:paddingLeft="20dp"
            android:paddingTop="30dp"
            android:paddingRight="20dp">

            <ImageView
                android:id="@+id/iv_feedback_pic"
                style="@style/FocusCloseStyle"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_marginRight="20dp"
                android:src="@drawable/def_pic" />

            <TextView
                android:id="@+id/tv_feedback_name"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/iv_feedback_pic"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26" />

            <TextView
                android:id="@+id/tv_feedback_phone"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_toRightOf="@+id/tv_feedback_name"
                android:text=""
                android:textColor="@color/bright_green"
                android:textSize="@dimen/font_size_26" />

            <TextView
                android:id="@+id/tv_feedback_content"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_feedback_name"
                android:layout_marginTop="10dp"
                android:layout_toRightOf="@+id/iv_feedback_pic"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_18" />


            <View
                android:id="@+id/view_feedback_line"
                style="@style/FocusCloseStyle"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_below="@+id/iv_feedback_pic"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="20dp"
                android:background="@color/white" />

            <TextView
                android:id="@+id/tv_feedback_tips"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/view_feedback_line"
                android:layout_centerHorizontal="true"
                android:text="请选择问题类型，按【确认键】提交您的反馈"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_24" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_tag"
                style="@style/FocusCloseStyle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/tv_feedback_tips"
                android:background="@color/base_bg3"
                android:paddingTop="20dp" />

        </RelativeLayout>

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toRightOf="@+id/recycler_row"
            android:background="@color/base_bg3"
            android:paddingLeft="10dp"
            android:paddingTop="20dp"
            android:paddingRight="10dp"
            android:visibility="gone" />
    </RelativeLayout>
</layout>