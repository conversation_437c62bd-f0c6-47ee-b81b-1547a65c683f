<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:id="@+id/ll_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/popup_drawer_bg"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_popup_task_graduate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/rl_qr_layout"
            android:layout_marginTop="20dp"
            android:layout_marginRight="60dp"
            android:layout_toStartOf="@+id/view_line"
            android:background="@drawable/base_border_round"
            android:focusable="false"
            android:nextFocusRight="@id/tv_popup_interaction"
            android:paddingLeft="30dp"
            android:paddingTop="10dp"
            android:paddingRight="30dp"
            android:paddingBottom="10dp"
            android:text="点击生成毕业表彰"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_30" />

        <RelativeLayout
            android:id="@+id/rl_qr_layout"
            android:layout_width="320dp"
            android:layout_height="432dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="300dp"
            android:layout_marginRight="50dp"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_arrow_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:rotation="-90"
                android:src="@drawable/icon_arrow" />

            <ImageView
                android:id="@+id/iv_arrow_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:src="@drawable/icon_arrow" />

            <ImageView
                android:id="@+id/iv_popup_qr_code"
                android:layout_width="300dp"
                android:layout_height="300dp"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:src="@drawable/min_wx_code" />

            <ImageView
                android:id="@+id/iv_qr_type"
                android:layout_width="300dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/iv_popup_qr_code"
                android:layout_marginLeft="10dp"
                android:scaleType="fitXY"
                android:src="@drawable/scan_bg" />

            <ImageView
                android:id="@+id/iv_arrow_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:rotation="180"
                android:src="@drawable/icon_arrow" />

            <ImageView
                android:id="@+id/iv_arrow_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:rotation="90"
                android:src="@drawable/icon_arrow" />
        </RelativeLayout>

        <View
            android:id="@+id/view_line"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_marginRight="50dp"
            android:layout_toRightOf="@+id/rl_qr_layout"
            android:background="@color/green"
            android:visibility="visible" />

        <RelativeLayout
            android:id="@+id/rl_top_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_marginRight="50dp"
            android:layout_toRightOf="@+id/view_line"
            android:paddingTop="50dp">

            <TextView
                android:id="@+id/tv_popup_interaction"
                style="@style/FocusStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/base_border_round"
                android:gravity="center"
                android:nextFocusLeft="@id/tv_popup_task_graduate"
                android:nextFocusDown="@id/tv_popup_day"
                android:paddingLeft="30dp"
                android:paddingTop="10dp"
                android:paddingRight="30dp"
                android:paddingBottom="10dp"
                android:text="门店排行榜"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_popup_examination"
                style="@style/FocusStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:background="@drawable/base_border_round"
                android:gravity="center"
                android:nextFocusDown="@id/tv_popup_day"
                android:paddingLeft="30dp"
                android:paddingTop="10dp"
                android:paddingRight="30dp"
                android:paddingBottom="10dp"
                android:text="全国排行榜"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/ll_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_popup_interaction"
                android:layout_centerInParent="true"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="10dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_popup_day"
                    style="@style/FocusStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="30dp"
                    android:background="@drawable/base_border_round"
                    android:gravity="center"
                    android:nextFocusLeft="@id/tv_popup_task_graduate"
                    android:paddingLeft="20dp"
                    android:paddingTop="6dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="6dp"
                    android:text="当天"
                    android:textColor="@color/blue"
                    android:textSize="@dimen/font_size_20" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/white" />

                <TextView
                    android:id="@+id/tv_popup_month"
                    style="@style/FocusStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginRight="30dp"
                    android:background="@drawable/base_border_round"
                    android:gravity="center"
                    android:nextFocusUp="@id/tv_popup_interaction"
                    android:paddingLeft="20dp"
                    android:paddingTop="6dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="6dp"
                    android:text="当月"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_20" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/white" />

                <TextView
                    android:id="@+id/tv_popup_history"
                    style="@style/FocusStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:background="@drawable/base_border_round"
                    android:gravity="center"
                    android:paddingLeft="20dp"
                    android:paddingTop="6dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="6dp"
                    android:text="历史"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_20" />

            </LinearLayout>

            <FrameLayout
                style="@style/FocusCloseStyle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/ll_date"
                android:layout_marginBottom="20dp">

                <ProgressBar
                    android:id="@+id/progress"
                    style="@style/FocusCloseStyle"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_gravity="center" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_task"
                    style="@style/FocusCloseStyle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </FrameLayout>

        </RelativeLayout>
    </RelativeLayout>

</layout>