<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:orientation="horizontal"
        tools:context=".ui.activity.SkillActivity">

        <TextView
            android:id="@+id/tv_custom_title"
            android:layout_width="280dp"
            android:layout_height="wrap_content"
            android:background="@color/base_bg2"
            android:gravity="center"
            android:paddingTop="30dp"
            android:text="课件"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_34"
            android:textStyle="bold" />

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/recycler_row"
            android:layout_width="280dp"
            android:layout_height="match_parent"
            android:layout_below="@+id/tv_custom_title"
            android:layout_alignParentStart="true"
            android:layout_alignParentLeft="true"
            android:layout_gravity="center"
            android:background="@color/base_bg2"
            android:paddingTop="20dp"
            android:paddingBottom="10dp" />

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toRightOf="@+id/recycler_row"
            android:background="@color/base_bg3"
            android:paddingLeft="10dp"
            android:paddingTop="20dp"
            android:paddingRight="10dp"
            android:visibility="visible" />
    </RelativeLayout>
</layout>