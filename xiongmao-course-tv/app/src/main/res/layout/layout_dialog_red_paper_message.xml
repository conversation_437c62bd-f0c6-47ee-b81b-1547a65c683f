<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingLeft="30dp"
    android:paddingTop="20dp"
    android:paddingRight="30dp"
    android:paddingBottom="20dp"
    app:bl_corners_radius="10dp"
    app:bl_solid_color="@color/white">


    <TextView
        android:id="@+id/iv_dialog_red_paper_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="我是标题"
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_34"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/iv_dialog_red_paper_sub"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:gravity="center|left"
        android:text="这里是内容"
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_22" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/but_dialog_red_paper_sure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginRight="10dp"
            android:layout_weight="1"
            android:background="@drawable/base_border_selecter_round"
            android:gravity="center"
            android:paddingLeft="34dp"
            android:paddingTop="6dp"
            android:paddingRight="34dp"
            android:paddingBottom="6dp"
            android:text="开启红包活动"
            android:textColor="@color/white"
            android:textSize="30sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/but_dialog_red_paper_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="10dp"
            android:layout_weight="1"
            android:background="@drawable/base_border_unselecter_no_bg_stroke_round"
            android:gravity="center"
            android:paddingLeft="34dp"
            android:paddingTop="6dp"
            android:paddingRight="34dp"
            android:paddingBottom="6dp"
            android:text="关闭红包活动"
            android:textColor="@color/black"
            android:textSize="30sp"
            android:textStyle="bold" />
    </LinearLayout>

</LinearLayout>