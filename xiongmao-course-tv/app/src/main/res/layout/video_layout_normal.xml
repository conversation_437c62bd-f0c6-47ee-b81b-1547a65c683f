<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

<!--    <RelativeLayout-->
<!--        android:id="@+id/surface_container"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:gravity="center">-->

<!--    </RelativeLayout>-->

<!--    <RelativeLayout-->
<!--        android:id="@+id/thumb"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:layout_alignParentStart="true"-->
<!--        android:layout_alignParentLeft="true"-->
<!--        android:layout_alignParentTop="true"-->
<!--        android:layout_alignParentEnd="true"-->
<!--        android:layout_alignParentRight="true"-->
<!--        android:layout_alignParentBottom="true"-->
<!--        android:background="#000000"-->
<!--        android:scaleType="fitCenter" />-->

<!--    <LinearLayout-->
<!--        android:id="@+id/layout_bottom"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="40dp"-->
<!--        android:layout_alignParentBottom="true"-->
<!--        android:background="#99000000"-->
<!--        android:gravity="center_vertical"-->
<!--        android:orientation="horizontal"-->
<!--        android:visibility="invisible">-->

<!--        <TextView-->
<!--            android:id="@+id/current"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginLeft="16dp"-->
<!--            android:text="00:00"-->
<!--            android:textColor="#ffffff"-->
<!--            android:textSize="30sp" />-->

<!--        <SeekBar-->
<!--            android:id="@+id/progress"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center_vertical"-->
<!--            android:layout_weight="1.0"-->
<!--            android:background="@null"-->
<!--            android:max="100"-->
<!--            android:maxHeight="4dp"-->
<!--            android:minHeight="4dp"-->
<!--            android:paddingTop="8dp"-->
<!--            android:paddingBottom="8dp"-->
<!--            android:progressDrawable="@drawable/video_seek_progress"-->
<!--            android:thumb="@drawable/video_seek_thumb" />-->

<!--        <TextView-->
<!--            android:id="@+id/total"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginRight="16dp"-->
<!--            android:text="00:00"-->
<!--            android:textColor="#ffffff"-->
<!--            android:textSize="30dp" />-->

<!--        <ImageView-->
<!--            android:id="@+id/fullscreen"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="fill_parent"-->
<!--            android:paddingRight="16dp"-->
<!--            android:scaleType="center"-->
<!--            android:src="@drawable/video_enlarge" />-->
<!--    </LinearLayout>-->

<!--    <ProgressBar-->
<!--        android:id="@+id/bottom_progressbar"-->
<!--        style="?android:attr/progressBarStyleHorizontal"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="1.5dp"-->
<!--        android:layout_alignParentBottom="true"-->
<!--        android:max="100"-->
<!--        android:progressDrawable="@drawable/video_progress" />-->

<!--    <ImageView-->
<!--        android:id="@+id/back_tiny"-->
<!--        android:layout_width="24dp"-->
<!--        android:layout_height="24dp"-->
<!--        android:layout_marginLeft="6dp"-->
<!--        android:layout_marginTop="6dp"-->
<!--        android:visibility="gone" />-->

<!--    <LinearLayout-->
<!--        android:id="@+id/layout_top"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:background="@drawable/video_title_bg"-->
<!--        android:gravity="center_vertical">-->

<!--        <ImageView-->
<!--            android:id="@+id/back"-->
<!--            android:layout_width="48dp"-->
<!--            android:layout_height="48dp"-->
<!--            android:layout_gravity="center"-->
<!--            android:paddingLeft="10dp"-->
<!--            android:scaleType="centerInside"-->
<!--            android:src="@drawable/video_back" />-->

<!--        <TextView-->
<!--            android:id="@+id/title"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:gravity="center"-->
<!--            android:paddingLeft="10dp"-->
<!--            android:textColor="@android:color/white"-->
<!--            android:textSize="@dimen/font_size_30" />-->
<!--    </LinearLayout>-->


<!--    <ProgressBar-->
<!--        android:id="@+id/loading"-->
<!--        style="?android:attr/progressBarStyleLarge"-->
<!--        android:layout_width="28dp"-->
<!--        android:layout_height="28dp"-->
<!--        android:layout_centerHorizontal="true"-->
<!--        android:layout_centerVertical="true"-->
<!--        android:visibility="invisible" />-->

<!--    <ImageView-->
<!--        android:id="@+id/start"-->
<!--        android:layout_width="60dp"-->
<!--        android:layout_height="60dp"-->
<!--        android:layout_centerHorizontal="true"-->
<!--        android:layout_centerVertical="true"-->
<!--        android:layout_gravity="center_vertical"-->
<!--        android:src="@drawable/video_click_play_selector" />-->


<!--    <ImageView-->
<!--        android:id="@+id/small_close"-->
<!--        android:layout_width="30dp"-->
<!--        android:layout_height="30dp"-->
<!--        android:paddingLeft="10dp"-->
<!--        android:paddingTop="10dp"-->
<!--        android:scaleType="centerInside"-->
<!--        android:src="@drawable/video_small_close"-->
<!--        android:visibility="gone" />-->

<!--    <ImageView-->
<!--        android:id="@+id/lock_screen"-->
<!--        android:layout_width="30dp"-->
<!--        android:layout_height="30dp"-->
<!--        android:layout_alignParentRight="true"-->
<!--        android:layout_centerVertical="true"-->
<!--        android:layout_marginRight="50dp"-->
<!--        android:scaleType="centerInside"-->
<!--        android:src="@drawable/unlock"-->
<!--        android:visibility="gone" />-->

</RelativeLayout>
