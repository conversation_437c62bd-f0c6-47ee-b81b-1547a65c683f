<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="20dp"
    app:bl_corners_radius="10dp"
    app:bl_solid_color="@color/white">


    <TextView
        android:id="@+id/name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="10dp"
        android:text="wifi name"
        android:textColor="@android:color/black"
        android:textSize="28dp" />

    <EditText
        android:id="@+id/pwd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:hint="请输入Wi-Fi密码"
        android:inputType="textPassword"
        android:letterSpacing="0.2"
        android:singleLine="true"
        android:textSize="20dp" />

    <TextView
        android:id="@+id/confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="8dp"
        android:background="@color/base_bg"
        android:focusable="auto"
        android:paddingLeft="40dp"
        android:paddingTop="8dp"
        android:paddingRight="40dp"
        android:paddingBottom="8dp"
        android:text="连接"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:bl_corners_radius="50dp"
        app:bl_solid_color="@color/base_bg" />


</LinearLayout>