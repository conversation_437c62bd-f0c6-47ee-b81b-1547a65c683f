<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_item_view_class_hours"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusedByDefault="true"
    app:bl_corners_radius="10dp"
    app:bl_solid_color="@color/transparent"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <RelativeLayout
        android:id="@+id/rl_item_live_list_layout"
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:background="@drawable/base_app_no_focus"
        android:visibility="visible">


        <ImageView
            android:id="@+id/item_iv_course_cover"
            android:layout_width="400dp"
            android:layout_height="match_parent"
            android:layout_marginRight="20dp"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_placeholder" />

        <TextView
            android:id="@+id/tv_ai_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="16dp"
            android:layout_marginRight="20dp"
            android:text="进行中"
            android:textColor="@color/blue"
            android:textSize="@dimen/font_size_24"
            android:textStyle="bold" />


        <TextView
            android:id="@+id/tv_item_live_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginRight="120dp"
            android:layout_marginBottom="4dp"
            android:layout_toRightOf="@+id/item_iv_course_cover"
            android:ellipsize="end"
            android:paddingTop="4dp"
            android:paddingRight="10dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_28" />

        <TextView
            android:id="@+id/tv_item_live_sub"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_item_live_title"
            android:layout_marginBottom="10dp"
            android:layout_toRightOf="@+id/item_iv_course_cover"
            android:ellipsize="end"
            android:maxLines="2"
            android:paddingRight="10dp"
            android:paddingBottom="6dp"
            android:text="内容"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_22" />

        <TextView
            android:id="@+id/tv_item_live_timer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="10dp"
            android:layout_toRightOf="@+id/item_iv_course_cover"
            android:ellipsize="end"
            android:paddingRight="10dp"
            android:paddingBottom="6dp"
            android:singleLine="true"
            android:text="内容"
            android:textColor="@color/gray"
            android:textSize="@dimen/font_size_22" />

    </RelativeLayout>


</androidx.cardview.widget.CardView>



