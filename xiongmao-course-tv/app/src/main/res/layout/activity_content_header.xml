<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        tools:context=".ui.activity.ContentActivity">


        <TextView
            android:id="@+id/tv_content_teacher"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="课程讲师:"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_22"
            android:visibility="visible" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_content_desc"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:gravity="center|left"
                android:lineSpacingExtra="4dp"
                android:lineSpacingMultiplier="1.2"
                android:text="简介简介:"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_18"
                android:visibility="visible" />

            <Button
                android:id="@+id/but_course"
                android:layout_width="wrap_content"
                android:layout_height="60dp"
                android:layout_gravity="center"
                android:layout_marginRight="20dp"
                android:background="@drawable/base_border"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:text="@string/class_text"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_22"
                android:visibility="gone" />
        </LinearLayout>

    </LinearLayout>
</layout>