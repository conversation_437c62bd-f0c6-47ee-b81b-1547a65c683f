<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/row_card_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginBottom="10dp"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="186dp"
        android:background="@drawable/base_border"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        android:paddingTop="20dp"
        android:paddingRight="20dp"
        android:paddingBottom="20dp">

        <TextView
            android:id="@+id/tv_item_skill_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="母婴护理师"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_26"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_item_skill_salary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_item_skill_name"
            android:layout_marginTop="10dp"
            android:text="行业工资"
            android:textColor="@color/bright_green"
            android:textSize="@dimen/font_size_22" />
        h

        <View
            android:id="@+id/view_line"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:layout_toRightOf="@+id/tv_item_skill_salary"
            android:background="#60FFFFFF" />

        <TextView
            android:id="@+id/tv_item_skill_info"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toRightOf="@+id/view_line"
            android:ellipsize="end"
            android:maxLines="7"
            android:text="描述的情况"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_20" />

    </RelativeLayout>

</androidx.cardview.widget.CardView>


