<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/row_card_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginBottom="10dp"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <RelativeLayout
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="110dp"
        android:background="@drawable/base_border"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        android:paddingTop="20dp"
        android:paddingRight="20dp"
        android:paddingBottom="20dp">

        <ImageView
            android:id="@+id/iv_item_skill_record_pic"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp" />

        <TextView
            android:id="@+id/tv_item_skill_record_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/iv_item_skill_record_pic"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="演示门店"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_22"
            android:textStyle="bold" />


        <TextView
            android:id="@+id/tv_item_skill_record_score_course_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="3dp"
            android:text="母婴护理师"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_20" />

        <TextView
            android:id="@+id/tv_item_skill_record_score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_item_skill_record_name"
            android:layout_marginTop="10dp"
            android:layout_toRightOf="@+id/iv_item_skill_record_pic"
            android:text="100 | 良"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_20" />

        <TextView
            android:id="@+id/tv_item_skill_record_score_course_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_item_skill_record_score_course_name"
            android:layout_alignParentRight="true"
            android:layout_marginTop="10dp"
            android:text="2023/1/30"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_20" />
    </RelativeLayout>

</androidx.cardview.widget.CardView>


