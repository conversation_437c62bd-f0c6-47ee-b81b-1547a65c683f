<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        tools:context=".ui.activity.dialog.GraduateDialogActivity">


        <com.youth.banner.Banner
            android:id="@+id/banner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:banner_indicator_marginBottom="10dp"
            app:banner_indicator_normal_color="@android:color/white"
            app:banner_indicator_selected_color="@color/colorPrimary"
            app:banner_indicator_space="10dp" />

        <Button
            android:id="@+id/but_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|bottom"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="30dp"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            android:text="按遥控器【左键/右键】查看"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_28"
            android:textStyle="bold"
            android:visibility="visible"
            app:bl_corners_radius="50dp"
            app:bl_solid_color="@color/green" />

        <LinearLayout
            android:id="@+id/ll_progress"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_bg3"
            android:gravity="center"
            android:orientation="vertical">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="正在加载中，稍后..."
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30" />
        </LinearLayout>

    </FrameLayout>
</layout>