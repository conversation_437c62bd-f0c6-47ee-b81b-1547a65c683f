<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:id="@+id/rl_root_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <!--动态效果-->
        <RelativeLayout
            android:id="@+id/rl_dialog_custom_sign_layout"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_bg3"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_dialog_custom_sign_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY" />

            <TextView
                android:id="@+id/tv_dialog_sign_timer"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center"
                android:layout_marginTop="110dp"
                android:gravity="center"
                android:maxLines="1"
                android:singleLine="true"
                android:text=""
                android:textSize="@dimen/font_size_24"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_dialog_content"
                android:layout_width="300dp"
                android:layout_height="350dp"
                android:layout_centerInParent="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="30dp"
                android:layout_toRightOf="@+id/iv_dialog_qr_code"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26" />

            <ImageView
                android:id="@+id/iv_dialog_cat"
                android:layout_width="200dp"
                android:layout_height="300dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="60dp" />


            <ImageView
                android:id="@+id/iv_dialog_qr_code"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginLeft="30dp"
                android:layout_toRightOf="@+id/iv_dialog_cat"
                android:scaleType="centerCrop" />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_dialog"
                android:layout_width="380dp"
                android:layout_height="400dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="20dp" />

            <TextView
                android:id="@+id/tv_dialog_sign_number"
                android:layout_width="370dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="40dp"
                android:gravity="center"
                android:paddingLeft="30dp"
                android:paddingTop="10dp"
                android:paddingRight="30dp"
                android:paddingBottom="10dp"
                android:text="签到人数：0人"
                android:textColor="@color/black"
                android:textSize="30dp"
                android:textStyle="bold"
                app:bl_corners_radius="10dp"
                app:bl_solid_color="#E3B666" />

            <com.panda.course.widget.LikeAnimView
                android:id="@+id/live_dialog_view"
                android:layout_width="200dp"
                android:layout_height="500dp"
                android:layout_marginTop="100dp"
                android:layout_marginRight="100dp"
                android:layout_marginBottom="20dp"
                android:layout_toStartOf="@+id/recycler_dialog" />
        </RelativeLayout>


        <!--新动效-->
        <RelativeLayout
            android:id="@+id/rl_new_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingLeft="20dp"
            android:paddingTop="20dp"
            android:paddingRight="20dp"
            android:paddingBottom="20dp"
            android:visibility="gone">

            <RelativeLayout
                android:layout_width="500dp"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/iv_new_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY" />

                <LinearLayout
                    android:id="@+id/ll_new_top"
                    android:layout_width="match_parent"
                    android:layout_height="450dp"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="10dp"
                    app:bl_corners_radius="20dp">

                    <TextView
                        android:id="@+id/tv_new_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="20dp"
                        android:text="签到"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_size_34"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="20dp">

                        <TextView
                            android:id="@+id/tv_new_timer1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="50dp"
                            android:text="倒计时："
                            android:textColor="@color/bright_green_1"
                            android:textSize="@dimen/font_size_22"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_new_timer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:minWidth="100dp"
                            android:text="倒计时"
                            android:textColor="@color/bright_green_1"
                            android:textSize="@dimen/font_size_22"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <ImageView
                        android:id="@+id/iv_new_qrcode"
                        android:layout_width="200dp"
                        android:layout_height="200dp"
                        android:layout_gravity="center"
                        android:layout_marginTop="40dp"
                        android:layout_marginBottom="30dp"
                        android:scaleType="fitXY" />

                    <TextView
                        android:id="@+id/tv_new_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="打开微信扫码"
                        android:textColor="@color/bright_green_1"
                        android:textSize="@dimen/font_size_22"
                        android:textStyle="bold" />
                </LinearLayout>

                <!--列表-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/ll_new_top"
                    android:layout_marginTop="12dp"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="10dp"
                    app:bl_corners_radius="20dp">

                    <TextView
                        android:id="@+id/tv_new_number"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="20dp"
                        android:text="签到人数"
                        android:textColor="@color/bright_green_1"
                        android:textSize="@dimen/font_size_22"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_new"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </LinearLayout>


                <com.panda.course.widget.LikeAnimView
                    android:id="@+id/live_dialog_view1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="20dp" />
            </RelativeLayout>


        </RelativeLayout>

        <!--新动效 答题效果-->
        <RelativeLayout
            android:id="@+id/rl_new_answer_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingLeft="20dp"
            android:paddingTop="20dp"
            android:paddingRight="20dp"
            android:paddingBottom="20dp">

            <RelativeLayout
                android:layout_width="600dp"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/iv_new_answer_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY" />

                <LinearLayout
                    android:id="@+id/ll_new_answer_top"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="10dp"
                    app:bl_corners_radius="20dp">

                    <TextView
                        android:id="@+id/tv_new_answer_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="30dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="30dp"
                        android:maxLines="2"
                        android:ellipsize="end"
                        android:text=""
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_size_30"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="10dp">

                        <TextView
                            android:id="@+id/tv_new_answer_timer1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="50dp"
                            android:text="倒计时："
                            android:textColor="@color/bright_green_1"
                            android:textSize="@dimen/font_size_22"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_new_answer_timer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:minWidth="100dp"
                            android:text="倒计时"
                            android:textColor="@color/bright_green_1"
                            android:textSize="@dimen/font_size_22"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_new_answer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/ll_new_answer_top"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp" />

                    <TextView
                        android:id="@+id/tv_new_answer_result"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="6dp"
                        android:text="请使用遥控器选择正确答案"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_size_30"
                        android:textStyle="bold" />

                </LinearLayout>

            </RelativeLayout>


        </RelativeLayout>


        <!--播放器-->
        <com.tencent.liteav.demo.superplayer.SuperPlayerView
            android:id="@+id/superplayer_dialog"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <!--弹幕效果-->
        <com.orient.tea.barragephoto.ui.BarrageView
            android:id="@+id/danmuku_dialog_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </RelativeLayout>

</layout>