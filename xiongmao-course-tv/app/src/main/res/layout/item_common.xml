<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_common_item_view"
    style="@style/FocusStyle"
    android:layout_width="180dp"
    android:layout_height="80dp"
    android:layout_marginLeft="16dp"
    android:layout_marginTop="16dp"
    android:layout_marginBottom="16dp"
    android:background="@drawable/base_border"
    android:padding="10dp">

    <TextView
        android:id="@+id/tv_item_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_27" />

    <TextView
        android:id="@+id/tv_item_pure_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_27"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_item_title">


    </LinearLayout>

    <TextView
        android:id="@+id/tv_item_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp"
        android:ellipsize="marquee"
        android:focusable="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:textColor="@color/bright_green"
        android:textSize="@dimen/font_size_20" />

    <ImageView
        android:id="@+id/iv_item_arrow"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_below="@+id/tv_item_title"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="4dp"
        android:rotation="44"
        android:src="@drawable/icon_arrow" />
</RelativeLayout>


