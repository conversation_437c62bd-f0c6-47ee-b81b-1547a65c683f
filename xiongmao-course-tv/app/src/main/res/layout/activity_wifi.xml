<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg"
        android:orientation="vertical"
        android:padding="40dp"
        tools:context=".ui.activity.wifi.WiFiActivity">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="无线网络"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_40"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/ll_wifi_state_layout"
            style="@style/FocusStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="150dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="150dp"
            android:background="@color/blue1"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="20dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="无线网络开关"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_wifi_arrow_left" />

            <TextView
                android:id="@+id/tv_wifi_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:text="开"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_wifi_arrow_right" />
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="150dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="150dp"
            android:background="@color/blue1">

            <LinearLayout
                android:id="@+id/ll_progress"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical">

                <ProgressBar
                    android:layout_width="100dp"
                    android:layout_height="100dp" />

                <TextView
                    android:id="@+id/tv_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="扫描中，请稍后..."
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_30" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </FrameLayout>

    </LinearLayout>
</layout>