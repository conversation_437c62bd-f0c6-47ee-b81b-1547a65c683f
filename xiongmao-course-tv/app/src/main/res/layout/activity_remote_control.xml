<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#0B203F"
        tools:context=".ui.activity.RemoteControlActivity">

        <LinearLayout
            android:id="@+id/ll_first"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_hello"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/icon_first_hello" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/iv_hello"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="20dp"
                android:text="欢迎使用熊猫AI课"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_50"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_title"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="30dp"
                android:text="简单几步，开启智能家政培训之旅"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_34" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_remote"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="150dp"
            android:src="@drawable/icon_first_remote" />

        <TextView
            android:id="@+id/tv_title_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_toRightOf="@+id/iv_remote"
            android:text="请安装好电池"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_38"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_sub_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_title_tips"
            android:layout_centerInParent="true"
            android:layout_marginTop="20dp"
            android:layout_toRightOf="@+id/iv_remote"
            android:text="按住主页+菜单键开始连接"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_30" />
    </RelativeLayout>
</layout>