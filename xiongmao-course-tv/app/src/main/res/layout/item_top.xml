<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_item_top_layout"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:layout_marginLeft="10dp"
    android:layout_marginTop="5dp"
    android:layout_marginRight="10dp"
    android:layout_marginBottom="5dp"
    android:background="@drawable/base_border_rank_unselecter"
    android:orientation="horizontal">

    <FrameLayout
        android:id="@+id/fl_layout"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="10dp">

        <ImageView
            android:id="@+id/iv_item_top"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/icon_top_1" />

        <TextView
            android:id="@+id/tv_item_top"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="100"
            android:textColor="@color/white"
            android:textSize="50dp"
            android:textStyle="bold" />
    </FrameLayout>

    <ImageView
        android:id="@+id/iv_item_pic"
        style="@style/FocusCloseStyle"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:layout_toRightOf="@+id/fl_layout" />

    <LinearLayout
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="6dp"
        android:layout_toRightOf="@+id/iv_item_pic"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_item_top_name"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center|left"
            android:layout_marginTop="20dp"
            android:layout_toRightOf="@+id/iv_item_pic"
            android:ellipsize="middle"
            android:gravity="center|left"
            android:singleLine="true"
            android:text="里面"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_26"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_item_top_score"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_item_top_name"
            android:layout_gravity="center|left"
            android:layout_toRightOf="@+id/iv_item_pic"
            android:gravity="center|left"
            android:text="80分｜优秀"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_18" />
    </LinearLayout>


</RelativeLayout>


