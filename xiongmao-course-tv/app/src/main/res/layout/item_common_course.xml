<?xml version="1.0" encoding="utf-8"?>


<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_common_item_view"
    style="@style/FocusStyle"
    android:layout_width="227dp"
    android:layout_height="136dp"
    android:layout_marginLeft="16dp"
    android:layout_marginTop="16dp"
    android:layout_marginBottom="16dp"
    android:background="@drawable/base_border">

    <com.panda.course.widget.RoundCornerImageView
        android:id="@+id/item_iv_course_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_placeholder"
        app:corner_size="12" />


    <RelativeLayout
        android:id="@+id/ll_item_layout"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_alignParentBottom="true"
        android:background="#80000000"
        android:orientation="vertical"
        app:bl_corners_bottomLeftRadius="10dp"
        app:bl_corners_bottomRightRadius="10dp"
        app:bl_solid_color="#80000000"
        tools:ignore="MissingPrefix">

        <ImageView
            android:id="@+id/iv_item_history_playing"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/icon_is_player"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_item_history_title"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginBottom="4dp"
            android:layout_toRightOf="@+id/iv_item_history_playing"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:maxLines="2"
            android:paddingTop="4dp"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_18" />

        <TextView
            android:id="@+id/tv_item_history_title_sub"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="4dp"
            android:layout_toRightOf="@+id/iv_item_history_playing"
            android:ellipsize="end"
            android:marqueeRepeatLimit="marquee_forever"
            android:maxHeight="51dp"
            android:maxLines="2"
            android:paddingTop="4dp"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_18"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/tv_item_history_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/tv_item_history_title_sub"
            android:layout_marginBottom="4dp"
            android:ellipsize="end"
            android:marqueeRepeatLimit="marquee_forever"
            android:maxHeight="51dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_16"
            android:visibility="gone" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_item_play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_gravity="center|right"
        android:layout_marginTop="32dp"
        android:src="@drawable/icon_player"
        android:visibility="gone" />
</RelativeLayout>

