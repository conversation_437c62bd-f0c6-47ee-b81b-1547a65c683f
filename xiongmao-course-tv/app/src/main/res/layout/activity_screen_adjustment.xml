<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#0B203F"
        tools:context=".ui.activity.ScreenAdjustmentActivity">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="100dp"
            android:gravity="center"
            android:text="@string/sys_screen_sub_tips"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_28" />

        <ImageView
            android:id="@+id/iv_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:src="@drawable/icon_ykq" />

        <ImageView
            android:id="@+id/iv_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:src="@drawable/icon_arrow_left" />

        <ImageView
            android:id="@+id/iv_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:src="@drawable/icon_arrow_top" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@drawable/icon_arrow_right" />

        <ImageView
            android:id="@+id/iv_bom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:src="@drawable/icon_arrow_bom" />
    </RelativeLayout>
</layout>