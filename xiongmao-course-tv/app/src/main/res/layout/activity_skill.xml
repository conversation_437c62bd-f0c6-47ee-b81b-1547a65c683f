<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        tools:context=".ui.activity.SkillActivity">

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/recycler_row"
            android:layout_width="280dp"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:layout_alignParentLeft="true"
            android:layout_gravity="center"
            android:background="@color/base_bg2"
            android:paddingTop="20dp"
            android:paddingBottom="10dp" />

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/recycler_child"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toRightOf="@+id/recycler_row"
            android:background="@color/base_bg3"
            android:paddingLeft="10dp"
            android:paddingTop="20dp"
            android:paddingRight="10dp" />

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/recycler_record"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toRightOf="@+id/recycler_row"
            android:background="@color/base_bg3"
            android:paddingLeft="10dp"
            android:paddingTop="20dp"
            android:paddingRight="10dp" />
    </RelativeLayout>
</layout>