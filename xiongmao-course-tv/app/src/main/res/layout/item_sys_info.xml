<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_common_item_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/base_border_no_cantons"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="35dp"
        android:layout_marginRight="35dp"
        android:paddingTop="20dp"
        android:paddingBottom="20dp">

        <TextView
            android:id="@+id/tv_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_weight="1"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_26" />

        <TextView
            android:id="@+id/tv_item_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerInParent="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:gravity="center"
            android:text=""
            android:textColor="@color/gray"
            android:textSize="@dimen/font_size_26"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/iv_item_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/icon_wifi_connect"
            android:visibility="gone" />
    </LinearLayout>

    <View
        android:id="@+id/view_item_sys"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/tv_item_time"
        android:layout_marginLeft="35dp"
        android:layout_marginRight="35dp"
        android:background="@color/gray" />
</LinearLayout>


