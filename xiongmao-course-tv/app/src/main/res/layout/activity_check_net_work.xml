<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/white"
        tools:context=".ui.activity.dialog.CheckNetDialogActivity">

        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/base_border_selecter_top_selecter"
                android:gravity="center"
                android:padding="16dp"
                android:text="@string/sys_net_check"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_24" />

            <TextView
                android:id="@+id/tv_top_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginRight="20dp"
                android:gravity="center"
                android:padding="16dp"
                android:text="@string/base_back_text"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_24"
                android:visibility="gone" />

        </RelativeLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="350dp">

            <Button
                android:id="@+id/but_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="30dp"
                android:paddingLeft="50dp"
                android:paddingRight="50dp"
                android:text="@string/base_back_text"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold"
                app:bl_corners_radius="50dp"
                app:bl_solid_color="@color/green" />

            <LinearLayout
                android:id="@+id/ll_net_check_ing_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingBottom="50dp"
                android:visibility="visible">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/net_check_tips"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_28"
                    android:textStyle="bold" />

                <ProgressBar
                    android:id="@+id/progress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="100dp"
                    android:layout_marginTop="30dp"
                    android:layout_marginRight="100dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_net_check_end_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingBottom="20dp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_net_check_end_tips"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="30dp"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_20" />

                <TextView
                    android:id="@+id/tv_net_check_end_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginBottom="20dp"
                    android:gravity="center"
                    android:textSize="@dimen/font_size_70" />

                <TextView
                    android:id="@+id/tv_net_upload_speed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="0"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_26"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_net_download_speed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="0"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_26"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_ip_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_23" />
            </LinearLayout>

        </RelativeLayout>

    </LinearLayout>
</layout>