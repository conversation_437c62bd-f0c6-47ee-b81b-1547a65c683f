<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="10dp"
        android:paddingTop="13pt"
        android:paddingRight="10dp">

        <TextView
            android:id="@+id/tv_fr_head_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="高级育婴师(高级版)"
            android:textColor="@color/white"
            android:textSize="20pt"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_fr_head_lecturer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:text="课程讲师：李江月"
            android:textColor="@color/white"
            android:textSize="20pt"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_fr_head_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_fr_head_title"
            android:layout_marginTop="18pt"
            android:layout_marginBottom="10pt"
            android:background="@color/base_bg"
            android:lineSpacingMultiplier="1.3"
            android:padding="10pt"
            android:text="课程简介：很多妈妈在怀孕的时候已经开始计划宝宝未来的生活，因为现在职场女性很多，大部分妈妈生了孩子休完产假后返回职场都不能自己带孩子，坐月子期间产后妈妈也需要时间恢复身体，照顾宝宝选择月子中心和请育婴师都可以解决问题，两者之间怎么选择呢？"
            android:textColor="@color/white"
            android:textSize="12pt" />
    </RelativeLayout>
</layout>