<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        tools:context=".ui.activity.OnlineRenewalActivity">

        <TextView
            android:id="@+id/tv_online_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="50dp"
            android:text="在线续费"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_38"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/ll_qr_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_online_title"
            android:layout_marginLeft="100dp"
            android:background="@drawable/base_border_unselecter"
            android:orientation="vertical"
            android:paddingBottom="20dp">

            <FrameLayout
                android:layout_width="350dp"
                android:layout_height="350dp">

                <ImageView
                    android:id="@+id/iv_online_renewal_qr"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="30dp"
                    android:scaleType="fitXY" />

                <ProgressBar
                    android:id="@+id/progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="支持"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_30" />

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginLeft="10dp"
                    android:src="@drawable/icon_pay_wx" />

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginLeft="10dp"
                    android:src="@drawable/icon_pay_aliay" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/tv_online_title"
            android:layout_toRightOf="@+id/ll_qr_layout"
            android:orientation="vertical">

            <TextView
                android:layout_marginLeft="50dp"
                android:id="@+id/tv_renewal_devices"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设备信息："
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />

            <TextView
                android:layout_marginLeft="50dp"
                android:id="@+id/tv_renewal_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="当前到期时间："
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />

            <TextView
                android:layout_marginLeft="50dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:text="请选择续费时长"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler"
                android:layout_marginLeft="40dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp" />

            <TextView
                android:layout_marginLeft="50dp"
                android:id="@+id/tv_renewal_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="应付金额："
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />
        </LinearLayout>
    </RelativeLayout>
</layout>