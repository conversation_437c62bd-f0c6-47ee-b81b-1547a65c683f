<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:orientation="vertical"
        tools:context=".ui.activity.wifi.NetDetailsActivity">

        <TextView
            android:id="@+id/tv_wifi_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="40dp"
            android:padding="20dp"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_30"
            android:textStyle="bold" />

        <Button
            android:id="@+id/but_dis_wifi"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="200dp"
            android:layout_marginRight="200dp"
            android:background="@drawable/base_border_no_cantons"
            android:gravity="left|center"
            android:padding="20dp"
            android:text="断开当前网络"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_26"
            android:textStyle="bold" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="200dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="200dp"
            android:background="@color/black1">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="20dp"
                android:text="IP地址"
                android:textColor="#E3E3E6"
                android:textSize="@dimen/font_size_22" />

            <TextView
                android:id="@+id/tv_net_details_ip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:padding="20dp"
                android:text=""
                android:textColor="#E3E3E6"
                android:textSize="@dimen/font_size_22" />

            <View
                android:id="@+id/view_ip"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_below="@+id/tv_net_details_ip"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:background="@color/white" />


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/view_ip"
                android:padding="20dp"
                android:text="子网掩码"
                android:textColor="#E3E3E6"
                android:textSize="@dimen/font_size_22" />

            <TextView
                android:id="@+id/tv_net_details_subnet_mask"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/view_ip"
                android:layout_alignParentRight="true"
                android:padding="20dp"
                android:text=""
                android:textColor="#E3E3E6"
                android:textSize="@dimen/font_size_22" />

            <View
                android:id="@+id/view_subnet_mask"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_below="@+id/tv_net_details_subnet_mask"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:background="@color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/view_subnet_mask"
                android:padding="20dp"
                android:text="网关"
                android:textColor="#E3E3E6"
                android:textSize="@dimen/font_size_22" />

            <TextView
                android:id="@+id/tv_net_details_gateway"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/view_subnet_mask"
                android:layout_alignParentRight="true"
                android:padding="20dp"
                android:text=""
                android:textColor="#E3E3E6"
                android:textSize="@dimen/font_size_22" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_below="@+id/tv_net_details_gateway"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="20dp"
                android:background="@color/white" />

        </RelativeLayout>

    </LinearLayout>
</layout>