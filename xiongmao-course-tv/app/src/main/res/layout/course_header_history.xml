<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/fl_expiration_notice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:background="#FFFFE7B1"
            android:visibility="gone"
            app:bl_corners_radius="4dp"
            app:bl_solid_color="#FFFFE7B1">

            <TextView
                android:id="@+id/tv_expiration_notice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="100dp"
                android:paddingTop="10dp"
                android:paddingRight="100dp"
                android:paddingBottom="10dp"
                android:text=""
                android:textColor="#FFAF6E0D"
                android:textSize="@dimen/font_size_24"
                android:textStyle="bold" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right|bottom"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="-10dp"
                android:src="@drawable/icon_home_vip" />
        </FrameLayout>


        <LinearLayout
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">


                <FrameLayout
                    android:id="@+id/fl_header_skill"
                    style="@style/FocusStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="14dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="10dp"
                    android:layout_weight="1">

                    <ImageView
                        android:id="@+id/iv_header_skill"
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:scaleType="centerCrop" />

                    <TextView
                        android:id="@+id/tv_floating_sub_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:background="@color/white"
                        android:gravity="center"
                        android:padding="10dp"
                        android:text="按【确认键】查看详情"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_size_22"
                        android:textStyle="bold"
                        android:visibility="gone"
                        app:bl_corners_bottomLeftRadius="10dp"
                        app:bl_corners_bottomRightRadius="10dp"
                        app:bl_solid_color="@color/white" />
                </FrameLayout>

                <FrameLayout
                    android:id="@+id/fl_order_hall_layout"
                    style="@style/FocusStyle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="10dp"
                    android:layout_weight="1"
                    android:visibility="visible">

                    <ImageView
                        android:id="@+id/iv_order_hall"
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:scaleType="centerCrop"
                        android:text="订单大厅"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_28"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_order_hall_sub_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:background="@color/white"
                        android:gravity="center"
                        android:padding="10dp"
                        android:text="按【确认键】查看详情"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_size_22"
                        android:textStyle="bold"
                        android:visibility="gone"
                        app:bl_corners_bottomLeftRadius="10dp"
                        app:bl_corners_bottomRightRadius="10dp"
                        app:bl_solid_color="@color/white" />
                </FrameLayout>

                <FrameLayout
                    android:id="@+id/fl_rank_layout"
                    style="@style/FocusStyle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="10dp"
                    android:layout_weight="1"
                    android:visibility="visible">

                    <ImageView
                        android:id="@+id/iv_rank"
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:scaleType="centerCrop"
                        android:text="优秀排行"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_28"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_rank_sub_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:background="@color/white"
                        android:gravity="center"
                        android:padding="10dp"
                        android:text="按【确认键】查看详情"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_size_22"
                        android:textStyle="bold"
                        android:visibility="gone"
                        app:bl_corners_bottomLeftRadius="10dp"
                        app:bl_corners_bottomRightRadius="10dp"
                        app:bl_solid_color="@color/white" />
                </FrameLayout>

                <FrameLayout
                    android:id="@+id/fl_renewal_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_renewal"
                        style="@style/FocusStyle"
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="15dp"
                        android:layout_marginBottom="10dp"
                        android:background="@drawable/base_border"
                        android:gravity="center"
                        android:text="在线续费"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_28"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_renewal_sub_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:layout_marginLeft="1dp"
                        android:background="@color/white"
                        android:gravity="center"
                        android:padding="10dp"
                        android:text="按【确认键】查看详情"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_size_22"
                        android:textStyle="bold"
                        android:visibility="gone"
                        app:bl_corners_bottomLeftRadius="10dp"
                        app:bl_corners_bottomRightRadius="10dp"
                        app:bl_solid_color="@color/white" />
                </FrameLayout>

            </LinearLayout>

            <ImageView
                android:id="@+id/iv_header_honor"
                style="@style/FocusCloseStyle"
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="10dp"
                android:layout_weight="1"
                android:scaleType="centerCrop"
                android:visibility="gone" />

        </LinearLayout>
    </LinearLayout>

</layout>


