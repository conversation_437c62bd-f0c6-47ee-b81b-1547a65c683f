<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        tools:context=".ui.activity.NoticeActivity">

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/recycler_notice_row"
            android:layout_width="280dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:background="#1F2E37"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:visibility="gone" />

        <include
            android:id="@+id/empty"
            layout="@layout/layout_empty" />

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/recycler_notice"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="20dp" />
    </RelativeLayout>
</layout>