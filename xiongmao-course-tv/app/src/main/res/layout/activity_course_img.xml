<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_bg3"
            android:visibility="visible"
            tools:context=".ui.activity.dialog.CourseImgDialogActivity">

        </androidx.recyclerview.widget.RecyclerView>

        <FrameLayout
            android:id="@+id/fl_progress"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_bg">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="64dp"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:background="@color/white"
                android:gravity="center"
                android:orientation="horizontal">

                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:indeterminateTintMode="src_atop" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/loading_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:gravity="center"
                    android:text="@string/helper_loading_tip"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_size_30" />
            </LinearLayout>
        </FrameLayout>

        <Button
            android:id="@+id/but_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="30dp"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            android:text="按遥控器【下键/上键】查看"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_28"
            android:textStyle="bold"
            android:visibility="gone"
            app:bl_corners_radius="50dp"
            app:bl_solid_color="@color/green" />
    </RelativeLayout>

</layout>