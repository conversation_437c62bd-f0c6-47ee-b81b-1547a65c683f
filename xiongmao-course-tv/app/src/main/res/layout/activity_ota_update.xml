<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:orientation="vertical"
        android:padding="40dp"
        tools:context=".ui.activity.OTAUpdateActivity">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="系统升级"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_40"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:minWidth="400dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/white_1"
                android:textSize="@dimen/font_size_24"
                android:textStyle="bold" />

            <Button
                android:id="@+id/but_ota_update"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_gravity="center|bottom"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/base_border_round"
                android:focusable="true"
                android:paddingLeft="30dp"
                android:paddingTop="10dp"
                android:paddingRight="30dp"
                android:paddingBottom="10dp"
                android:text="立即升级"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30"
                android:visibility="gone" />
        </LinearLayout>

    </LinearLayout>
</layout>