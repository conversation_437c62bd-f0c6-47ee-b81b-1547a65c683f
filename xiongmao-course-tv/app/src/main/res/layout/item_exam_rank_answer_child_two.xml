<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_item_layout"
    style="@style/FocusCloseStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingLeft="16dp"
    android:paddingTop="10dp"
    android:paddingRight="16dp"
    android:paddingBottom="10dp">

    <TextView
        android:id="@+id/tv_item_exam_result"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="错误"
        android:textColor="@color/red"
        android:textSize="@dimen/font_size_26" />


    <TextView
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="正确答案"
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_24" />

    <TextView
        android:id="@+id/tv_item_exam_correct_answer"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text=""
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_22" />

    <TextView
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="答案解析"
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_24" />

    <TextView
        android:id="@+id/tv_item_exam_answer_key"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text=""
        android:textColor="@color/black"
        android:textSize="@dimen/font_size_22" />


</LinearLayout>



