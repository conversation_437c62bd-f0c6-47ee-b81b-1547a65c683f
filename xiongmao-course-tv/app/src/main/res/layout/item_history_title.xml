<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/FocusCloseStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <FrameLayout
        android:id="@+id/fl_item_history_title"
        style="@style/FocusStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="18dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="50dp"
        android:layout_marginBottom="50dp"
        android:background="@drawable/base_app_focus_selector"
        android:padding="4dp">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:padding="@dimen/dp_10"
            app:bl_corners_radius="4dp"
            app:bl_solid_color="@color/white">

            <ImageView
                android:id="@+id/iv_item_history_title"
                style="@style/FocusCloseStyle"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:src="@drawable/icon_qr_code_app_my_video" />
        </FrameLayout>

    </FrameLayout>

    <TextView
        android:id="@+id/tv_item_history_title"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="14dp"
        android:gravity="center|left"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_26"
        android:textStyle="bold"
        tools:text="10" />
</LinearLayout>


