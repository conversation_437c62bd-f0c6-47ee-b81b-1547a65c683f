<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg"
        tools:context=".ui.activity.LivePlayerActivity">

        <LinearLayout
            android:id="@+id/main_menu_player_remote_video"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_bg3"
            android:orientation="horizontal" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="80dp"
            android:layout_marginBottom="10dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            app:bl_corners_radius="100dp"
            app:bl_solid_color="#20ffffff">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/icon_live_user" />

            <TextView
                android:id="@+id/tv_live_user_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="6dp"
                android:text="0"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20" />
        </LinearLayout>

        <!-- 网速相关 -->
        <LinearLayout
            android:id="@+id/ll_init_net"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible">

            <TextView
                android:id="@+id/tv_init_title"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textColor="@color/superplayer_white"
                android:textSize="36dp" />

            <com.tencent.liteav.demo.superplayer.ui.view.CustomLoadingProgress
                android:id="@+id/progress_init"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_marginLeft="300dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="300dp"
                android:layout_marginBottom="20dp"
                android:indeterminateBehavior="repeat" />

            <TextView
                android:id="@+id/tv_net_init_content"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text=""
                android:textColor="@color/superplayer_color_green"
                android:textSize="@dimen/font_size_20" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_live_top"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/superplayer_top_shadow"
            android:gravity="left"
            android:orientation="horizontal"
            android:paddingLeft="30dp"
            android:paddingTop="30dp"
            android:paddingRight="30dp">

            <TextView
                android:id="@+id/tv_live_course_name"
                style="@style/FocusCloseStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text=""
                android:textColor="@android:color/white"
                android:textSize="@dimen/font_size_40" />

            <TextView
                android:id="@+id/tv_live_course_status_name"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text=""
                android:textColor="@android:color/white"
                android:textSize="@dimen/font_size_30" />

        </LinearLayout>

        <FrameLayout
            android:id="@+id/ll_live_buttom"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:background="@drawable/superplayer_bottom_shadow"
            android:clipChildren="false"
            android:clipToPadding="false">

        </FrameLayout>


        <!--直播状态-->
        <LinearLayout
            android:id="@+id/ll_live_finish_layout"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_logo"
                style="@style/FocusCloseStyle"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_alignParentRight="true"
                android:layout_marginTop="30dp"
                android:src="@drawable/icon_live_finish"
                android:visibility="visible" />

            <TextView
                android:id="@+id/tv_live_finish_course_name"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:gravity="center"
                android:text="老师正在赶来，请您耐心等待~"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20" />
        </LinearLayout>

        <!--网络问题-->
        <LinearLayout
            android:id="@+id/ll_error"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/base_bg3"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="网速太慢了，播放视频失败"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28" />

            <TextView
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="建议尝试以下方法解决：\n1、把路由器断电1分钟再重新插上\n2、长按AI遥控器电源，选择'重新启动'\n\n如果以上方法还是不行，可以联系售后经理解决。"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_26" />

            <TextView
                android:id="@+id/tv_player_code"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28" />

            <Button
                android:id="@+id/but_retry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/base_border_selecter"
                android:paddingLeft="50dp"
                android:paddingTop="10dp"
                android:paddingRight="50dp"
                android:paddingBottom="10dp"
                android:text="重试"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />
        </LinearLayout>

        <!--视频控制器-->
        <FrameLayout
            android:id="@+id/fl_controller"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentBottom="true"
            android:background="@drawable/superplayer_bottom_bg"
            android:visibility="gone">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scrollView_controller"
                style="@style/FocusCloseStyle"
                android:layout_width="match_parent"
                android:layout_height="400dp"
                android:layout_gravity="bottom"
                android:paddingTop="16dp"
                android:paddingBottom="16dp">

                <LinearLayout
                    style="@style/FocusCloseStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/menu_tv_common_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:text="弹幕"
                        android:textColor="@color/gray_1"
                        android:textSize="@dimen/font_size_32"
                        android:textStyle="bold" />

                    <com.panda.course.widget.FocusKeepRecyclerView
                        android:id="@+id/grid_sub_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:text="互动二维码"
                        android:textColor="@color/gray_1"
                        android:textSize="@dimen/font_size_32"
                        android:textStyle="bold" />

                    <com.panda.course.widget.FocusKeepRecyclerView
                        android:id="@+id/grid_sub_qr"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

        </FrameLayout>

        <!--二维码-->
        <LinearLayout
            android:id="@+id/ll_live_qr_code"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:layout_marginTop="20dp"
            android:layout_marginRight="30dp"
            android:layout_marginBottom="30dp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible">

            <ImageView
                android:id="@+id/iv_live_qr_code"
                style="@style/FocusCloseStyle"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:src="@drawable/min_wx_code" />

            <TextView
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="扫码互动"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20"
                android:textStyle="bold" />

        </LinearLayout>
        <!--弹幕效果-->
        <com.orient.tea.barragephoto.ui.BarrageView
            android:id="@+id/danmuku_view"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

</layout>