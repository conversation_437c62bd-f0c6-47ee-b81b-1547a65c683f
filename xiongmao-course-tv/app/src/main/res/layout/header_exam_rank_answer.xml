<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <RelativeLayout
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingTop="50dp"
        android:paddingBottom="30dp">

        <TextView
            android:id="@+id/tv_exam_rank_class_name"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:text="母婴护理师"
            android:textColor="@color/blue"
            android:textSize="@dimen/font_size_40"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_exam_rank_class_tips"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_exam_rank_class_name"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:text="技能测评"
            android:textColor="@color/blue"
            android:textSize="@dimen/font_size_40"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_exam_rank_class_score_tips"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_exam_rank_class_tips"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:text="XXX，您本次的评分是"
            android:textColor="@color/black"
            android:textSize="@dimen/font_size_24" />

        <TextView
            android:id="@+id/tv_exam_rank_class_score"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_exam_rank_class_score_tips"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:text="100分"
            android:textColor="@color/red"
            android:textSize="@dimen/font_size_70"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/ll_exam_rank_class_score_line"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_exam_rank_class_score"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp">

            <TextView
                style="@style/FocusCloseStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="0分"
                android:textColor="@color/yellow"
                android:textSize="@dimen/font_size_24"
                android:textStyle="bold" />

            <TextView
                style="@style/FocusCloseStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:text="60分"
                android:textColor="@color/yellow"
                android:textSize="@dimen/font_size_24"
                android:textStyle="bold" />

            <TextView
                style="@style/FocusCloseStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:gravity="right"
                android:text="80分"
                android:textColor="@color/yellow"
                android:textSize="@dimen/font_size_24"
                android:textStyle="bold" />

            <TextView
                style="@style/FocusCloseStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.7"
                android:gravity="right"
                android:text="100分"
                android:textColor="@color/yellow"
                android:textSize="@dimen/font_size_24"
                android:textStyle="bold" />
        </LinearLayout>

        <View
            android:id="@+id/view_exam_rank_class_score_view"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:layout_below="@+id/ll_exam_rank_class_score_line"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="40dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/base_app_dash_exam_line" />

        <LinearLayout
            android:id="@+id/ll_exam_rank_class_score_view"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/view_exam_rank_class_score_view"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:minHeight="80dp">

            <TextView
                style="@style/FocusCloseStyle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_exam_one"
                android:gravity="center"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="30%\n人到达"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20"
                android:textStyle="bold" />

            <TextView
                style="@style/FocusCloseStyle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.6"
                android:background="@drawable/shape_exam_two"
                android:gravity="center"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="51%\n人到达"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20"
                android:textStyle="bold" />

            <TextView
                style="@style/FocusCloseStyle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.4"
                android:background="@drawable/shape_exam_three"
                android:gravity="center"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="15%\n人到达"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20"
                android:textStyle="bold" />
        </LinearLayout>

        <com.panda.course.widget.TriangleView
            android:id="@+id/triangle_up"
            style="@style/FocusCloseStyle"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_below="@+id/ll_exam_rank_class_score_view"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="40dp" />

        <View
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:layout_below="@+id/triangle_up"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/shape_exam_line" />

        <ImageView
            android:id="@+id/iv_exam_rank_score_level"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/icon_exam_rank_2" />

        <TextView
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/triangle_up"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="40dp"
            android:layout_marginRight="40dp"
            android:text="错题解析"
            android:textColor="@color/black"
            android:textSize="@dimen/font_size_30"
            android:textStyle="bold" />
    </RelativeLayout>
</layout>



