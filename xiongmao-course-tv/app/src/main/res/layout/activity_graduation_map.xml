<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_top_title"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:text="@string/graduate_title"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_36"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_top_store"
                style="@style/FocusCloseStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingLeft="20dp"
                android:paddingTop="10dp"
                android:paddingRight="20dp"
                android:paddingBottom="10dp"
                android:text="@string/graduate_store_title"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_top_student"
                style="@style/FocusCloseStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingLeft="20dp"
                android:paddingTop="10dp"
                android:paddingRight="20dp"
                android:paddingBottom="10dp"
                android:text="@string/graduate_studen_title"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30"
                android:textStyle="bold" />

        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.panda.course.widget.TVBoxRecyclerView
                android:id="@+id/recycler"
                style="@style/FocusCloseStyle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingLeft="40dp"
                android:paddingRight="40dp" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/tv_top_title"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="horizontal">

                <com.panda.course.widget.TVBoxRecyclerView
                    android:id="@+id/recycler_student"
                    style="@style/FocusCloseStyle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:paddingBottom="30dp" />

            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</layout>