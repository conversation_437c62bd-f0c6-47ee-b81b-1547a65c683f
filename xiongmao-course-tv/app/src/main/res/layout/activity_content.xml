<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="vertical"
        tools:context=".ui.activity.ContentActivity">

        <include
            android:id="@+id/base_head"
            layout="@layout/base_head" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <com.panda.course.widget.TVBoxRecyclerView
                android:id="@+id/recycler"
                style="@style/FocusCloseStyle"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/base_bg3"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:paddingLeft="20dp"
            android:paddingTop="20dp"
            android:paddingBottom="20dp"
            android:visibility="gone">

            <Button
                android:id="@+id/tv_final_courseware"
                style="@style/FocusStyle"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginRight="20dp"
                android:layout_weight="1"
                android:background="@drawable/base_border"
                android:gravity="center"
                android:scaleType="centerCrop"
                android:text="AI课课件"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />

            <Button
                android:id="@+id/tv_final_exam_pic"
                style="@style/FocusStyle"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginRight="20dp"
                android:layout_weight="1"
                android:background="@drawable/base_border"
                android:gravity="center"
                android:scaleType="centerCrop"
                android:text="@string/final_test"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />

            <Button
                android:id="@+id/tv_teacher_interactive_pic"
                style="@style/FocusStyle"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginRight="20dp"
                android:layout_weight="1"
                android:background="@drawable/base_border"
                android:gravity="center"
                android:scaleType="centerCrop"
                android:text="@string/teacher"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />

            <Button
                android:id="@+id/tv_graduation_photo_pic"
                style="@style/FocusStyle"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginRight="20dp"
                android:layout_weight="1"
                android:background="@drawable/base_border"
                android:gravity="center"
                android:scaleType="centerCrop"
                android:text="@string/graduate"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />

            <View
                android:id="@+id/view1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:visibility="gone" />

            <View
                android:id="@+id/view2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:visibility="gone" />

            <View
                android:id="@+id/view3"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:visibility="gone" />
        </LinearLayout>
    </LinearLayout>
</layout>