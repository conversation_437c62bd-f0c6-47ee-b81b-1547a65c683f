<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/base_bg3"
        android:clipToPadding="false"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="20dp"
            android:paddingTop="16dp"
            android:paddingRight="20dp"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="订单大厅"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_32" />

            <TextView
                android:id="@+id/tv_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="(海量高薪工作等你来，学好技能接高薪订单)"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_24"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_order_hall_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:text="岗位数量：0"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_24"
                android:visibility="gone" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/green" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:orientation="horizontal"
            tools:context=".ui.activity.SkillActivity">

            <com.panda.course.widget.TVBoxRecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/base_bg3"
                android:clipToPadding="false"
                android:paddingLeft="10dp"
                android:paddingTop="20dp"
                android:paddingRight="10dp"
                android:paddingBottom="20dp" />

            <LinearLayout
                android:id="@+id/empty_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingTop="50dp"
                    android:paddingBottom="20dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingLeft="300dp">

                        <FrameLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="20dp"
                            android:background="@color/white"
                            android:padding="10dp">

                            <ImageView
                                android:layout_width="200dp"
                                android:layout_height="200dp"
                                android:layout_gravity="center"
                                android:src="@drawable/icon_qr_code_app_sendorder" />

                        </FrameLayout>

                    </LinearLayout>


                    <LinearLayout
                        android:layout_gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingLeft="50dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="20dp"
                            android:text="暂无客户订单"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_28" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="10dp"
                            android:text="打开熊猫系统app，使用扫一扫快速录入订单\n请确保熊猫系统App版本号是6.5.3以上\n(或者：首页点击“招聘栏  一键发单，录入订单信息”）"
                            android:textColor="@color/white"
                            android:textSize="@dimen/font_size_22" />


                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center|left"
                    android:gravity="center"
                    android:text="录入完成后效果如下"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_22" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="horizontal">

                    <LinearLayout
                        style="@style/FocusStyle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/base_border_order_hall"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:layout_marginRight="10dp"
                                android:src="@drawable/icon_order_hall_worry" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:text="保姆"
                                android:textColor="@color/black"
                                android:textSize="@dimen/font_size_23"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_weight="1"
                                android:ellipsize="end"
                                android:gravity="right"
                                android:singleLine="true"
                                android:text="5000元"
                                android:textColor="@color/red"
                                android:textSize="@dimen/font_size_20"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:text="时间：越快越好"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_size_20" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="66dp"
                            android:layout_marginTop="6dp"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:text="备注：家里有2个老人，主要照顾2个老师的生活起居，能接的联系我"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_size_20" />


                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            android:background="@color/black" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center">

                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:layout_gravity="center"
                                android:src="@drawable/def_pic" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginLeft="10dp"
                                android:text="杨老师"
                                android:textColor="@color/gray_1"
                                android:textSize="@dimen/font_size_20" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_weight="1"
                                android:gravity="right"
                                android:text="2个小时前发布"
                                android:textColor="@color/gray_1"
                                android:textSize="@dimen/font_size_20" />
                        </LinearLayout>
                    </LinearLayout>

                    <Space
                        android:layout_width="20dp"
                        android:layout_height="match_parent" />

                    <LinearLayout
                        style="@style/FocusStyle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/base_border_order_hall"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:layout_marginRight="10dp"
                                android:src="@drawable/icon_order_hall_worry" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:text="保姆"
                                android:textColor="@color/black"
                                android:textSize="@dimen/font_size_23"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_weight="1"
                                android:ellipsize="end"
                                android:gravity="right"
                                android:singleLine="true"
                                android:text="5000元"
                                android:textColor="@color/red"
                                android:textSize="@dimen/font_size_20"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:text="时间：越快越好"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_size_20" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="66dp"
                            android:layout_marginTop="6dp"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:text="备注：家里有2个老人，主要照顾2个老师的生活起居，能接的联系我"
                            android:textColor="@color/black"
                            android:textSize="@dimen/font_size_20" />


                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            android:background="@color/black" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center">

                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:layout_gravity="center"
                                android:src="@drawable/def_pic" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginLeft="10dp"
                                android:text="杨老师"
                                android:textColor="@color/gray_1"
                                android:textSize="@dimen/font_size_20" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_weight="1"
                                android:gravity="right"
                                android:text="2个小时前发布"
                                android:textColor="@color/gray_1"
                                android:textSize="@dimen/font_size_20" />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>

</layout>