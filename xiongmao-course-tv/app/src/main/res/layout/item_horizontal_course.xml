<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="240dp">

            <ImageView
                android:id="@+id/item_iv_course_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/icon_placeholder"
                app:corner_bottom_left_size="10"
                app:corner_bottom_right_size="0"
                app:corner_top_left_size="10"
                app:corner_top_right_size="0"
                app:label_backgroundColor="@color/red"
                app:label_orientation="LEFT_TOP"
                app:label_text="新"
                app:label_textColor="@color/white" />

            <ImageView
                android:id="@+id/iv_item_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|right"
                android:src="@drawable/icon_player"
                android:visibility="gone" />
        </FrameLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/base_border_bottom_top_round"
            android:padding="12dp">

            <TextView
                android:id="@+id/tv_item_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="高级小儿推拿师"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_20"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_item_teacher"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBaseline="@+id/tv_item_title"
                android:layout_marginLeft="20dp"
                android:layout_toRightOf="@+id/tv_item_title"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_16"
                android:textStyle="bold" />


        </RelativeLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_item_is_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_is_new" />
</androidx.cardview.widget.CardView>

