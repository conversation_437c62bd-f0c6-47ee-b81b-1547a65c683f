<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content_card_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="150dp"
    android:clipChildren="false"
    android:clipToPadding="false"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:id="@+id/ll_item_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_item_big_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/fl_item_cover_layout"
            android:layout_width="250dp"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_item_cover"
                android:layout_width="250dp"
                android:layout_height="150dp"
                android:scaleType="centerCrop"
                android:src="@drawable/icon_placeholder"
                app:corner_bottom_left_size="10"
                app:corner_bottom_right_size="0"
                app:corner_top_left_size="10"
                app:corner_top_right_size="0"
                app:label_backgroundColor="@color/red"
                app:label_orientation="LEFT_TOP"
                app:label_text="新"
                app:label_textColor="@color/white" />

            <ImageView
                android:id="@+id/iv_item_is_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_is_new" />

            <ImageView
                android:id="@+id/iv_player"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:src="@drawable/icon_player"
                android:visibility="gone" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_item_text_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/base_border_bottom_right_round"
            android:orientation="vertical"
            android:paddingLeft="12dp"
            android:paddingTop="12dp"
            android:paddingRight="12dp"
            android:paddingBottom="12dp">

            <TextView
                android:id="@+id/iv_item_course_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="第一天"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_19"
                android:textStyle="bold"
                android:visibility="gone" />

            <TextView
                android:id="@+id/iv_item_course_teacher"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBaseline="@+id/iv_item_course_title"
                android:layout_marginLeft="20dp"
                android:layout_toRightOf="@+id/iv_item_course_title"
                android:ellipsize="end"
                android:maxLines="4"
                android:text="课程讲师:"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_16"
                android:textStyle="bold"
                android:visibility="gone" />

            <TextView
                android:id="@+id/iv_item_course_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/iv_item_course_title"
                android:ellipsize="end"
                android:maxLines="4"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_16" />

            <TextView
                android:id="@+id/iv_item_course_teaching_aids"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:ellipsize="marquee"
                android:marqueeRepeatLimit="marquee_forever"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_16" />

            <TextView
                android:id="@+id/iv_item_course_update_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_16" />

        </RelativeLayout>
    </LinearLayout>

</androidx.cardview.widget.CardView>