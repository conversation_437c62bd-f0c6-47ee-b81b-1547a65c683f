<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_common_item_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="14dp">

        <TextView
            android:id="@+id/tv_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="20dp"
            android:layout_weight="1"
            android:text=""
            android:textColor="@color/green"
            android:textSize="@dimen/font_size_26" />

        <TextView
            android:id="@+id/tv_item_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerInParent="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginRight="30dp"
            android:gravity="center"
            android:text=""
            android:textColor="@color/gray"
            android:textSize="@dimen/font_size_26"
            android:visibility="visible" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/tv_item_time"
        android:layout_marginLeft="30dp"
        android:background="@color/gray" />
</LinearLayout>


