<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_item_view_history"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="10dp"
    android:nextFocusLeft="@id/recycler_row"
    app:bl_corners_radius="10dp"
    app:bl_solid_color="@color/transparent"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/rl_item_def"
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="visible">

            <LinearLayout
                android:id="@+id/ll_ai_status_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="visible">

                <ImageView
                    android:id="@+id/iv_ai_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/icon_live_ing"
                    android:text="进行中"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_24" />

                <TextView
                    android:id="@+id/tv_ai_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="6dp"
                    android:text="进行中"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_24" />
            </LinearLayout>

            <ImageView
                android:id="@+id/item_iv_course_cover"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_below="@+id/ll_ai_status_layout"
                android:layout_marginStart="1dp"
                android:layout_marginTop="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                android:scaleType="centerCrop"
                android:src="@drawable/icon_placeholder" />

            <ProgressBar
                android:id="@+id/progressBar"
                style="@android:style/Widget.ProgressBar.Horizontal"
                android:layout_width="match_parent"
                android:layout_height="3dp"
                android:layout_below="@+id/item_iv_course_cover"
                android:max="100"
                android:progress="5"
                android:progressDrawable="@drawable/progress_bg" />

            <LinearLayout
                android:id="@+id/ll_item_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/progressBar"
                android:background="#25333D"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_item_history_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingLeft="10dp"
                    android:paddingTop="4dp"
                    android:paddingRight="10dp"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_18" />

                <TextView
                    android:id="@+id/tv_item_history_sub"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:ellipsize="end"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="6dp"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_16" />
            </LinearLayout>

            <ImageView
                android:id="@+id/iv_item_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_gravity="center|right"
                android:layout_marginTop="80dp"
                android:src="@drawable/icon_player"
                android:visibility="gone" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_item_ai_def"
            android:layout_width="match_parent"
            android:layout_height="230dp"
            android:background="@drawable/ai_live_def_no_bg"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_item_ai_logo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/icon_ai_video" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="全部直播课"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_30" />
        </LinearLayout>

    </FrameLayout>

</androidx.cardview.widget.CardView>

