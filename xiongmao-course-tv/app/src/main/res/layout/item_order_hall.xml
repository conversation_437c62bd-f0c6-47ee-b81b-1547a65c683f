<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/row_card_view"
    style="@style/FocusCloseStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginBottom="10dp"
    android:clipToPadding="false"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">


    <LinearLayout
        style="@style/FocusStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/base_border_order_hall"
        android:orientation="vertical"
        android:padding="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_item_order_hall_worry"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginRight="10dp"
                android:src="@drawable/icon_order_hall_worry" />

            <TextView
                android:id="@+id/tv_item_order_hall_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:singleLine="true"
                android:text="保姆"
                android:textColor="@color/black2"
                android:textSize="@dimen/font_size_28"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_item_order_hall_salary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:ellipsize="end"
                android:gravity="right"
                android:singleLine="true"
                android:text="5000左右"
                android:textColor="@color/red"
                android:textSize="@dimen/font_size_24"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_item_order_hall_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="时间：越快越好"
            android:textColor="@color/black2"
            android:textSize="@dimen/font_size_24" />

        <TextView
            android:id="@+id/tv_item_order_hall_remark"
            android:layout_width="match_parent"
            android:layout_height="66dp"
            android:layout_marginTop="6dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="备注：我需要一个保姆，急需"
            android:textColor="@color/black2"
            android:textSize="@dimen/font_size_24" />

        <TextView
            android:id="@+id/tv_item_order_hall_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text=""
            android:textColor="@color/gray_2"
            android:textSize="@dimen/font_size_20" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.1dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:background="@color/gray" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center">

            <ImageView
                android:id="@+id/iv_item_order_hall_pic"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="center"
                android:src="@drawable/super_default_thumb" />

            <TextView
                android:id="@+id/tv_item_order_hall_release_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:text=""
                android:textColor="@color/gray_1"
                android:textSize="@dimen/font_size_24" />

            <TextView
                android:id="@+id/tv_item_order_hall_release_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:gravity="right"
                android:text=""
                android:textColor="@color/gray_1"
                android:textSize="@dimen/font_size_24" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>


