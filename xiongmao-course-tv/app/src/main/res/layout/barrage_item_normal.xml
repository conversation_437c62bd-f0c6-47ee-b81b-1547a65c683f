<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_item_bg_normal"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingLeft="20dp"
    android:paddingTop="6dp"
    android:paddingRight="20dp"
    android:paddingBottom="6dp">

    <ImageView
        android:id="@+id/iv_barrage_pic"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginRight="4dp"
        android:scaleType="centerCrop"
        android:src="@drawable/logo"
        android:visibility="visible" />

    <TextView
        android:id="@+id/tv_barrage_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:text="sssss"
        android:textColor="#ffffff"
        android:textSize="@dimen/font_size_20" />


</LinearLayout>
