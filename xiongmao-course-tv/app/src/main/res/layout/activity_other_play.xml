<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg"
        tools:context=".ui.activity.OtherPlayActivity">

        <com.tencent.liteav.demo.superplayer.SuperPlayerView
            android:id="@+id/superplayer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible" />

        <TextView
            android:id="@+id/superview_tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="20dp"
            android:layout_marginBottom="30dp"
            android:padding="10dp"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_24"
            android:visibility="gone" />
        <!--视频控制器-->
        <FrameLayout
            android:id="@+id/fl_controller"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentBottom="true"
            android:background="@drawable/superplayer_bottom_bg"
            android:visibility="gone">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scrollView_controller"
                style="@style/FocusCloseStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:paddingTop="16dp"
                android:paddingBottom="16dp">

                <LinearLayout
                    style="@style/FocusCloseStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:text="常用功能"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_32"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/grid_common"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:nextFocusDown="@id/grid_course" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:text="清晰度"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_32"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/grid_definition"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:nextFocusUp="@id/grid_course"
                        android:nextFocusDown="@id/grid_speed" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:text="倍速播放"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_32"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/grid_speed"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:nextFocusDown="@id/grid_definition" />

                </LinearLayout>
            </androidx.core.widget.NestedScrollView>
        </FrameLayout>

        <TextView
            android:id="@+id/tv_back"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="50dp"
            android:layout_marginBottom="50dp"
            android:paddingLeft="30dp"
            android:paddingTop="10dp"
            android:paddingRight="30dp"
            android:paddingBottom="10dp"
            android:text="@string/base_back_text"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_24"
            android:visibility="visible"
            app:bl_corners_radius="100dp"
            app:bl_solid_color="@color/green" />
    </RelativeLayout>

</layout>