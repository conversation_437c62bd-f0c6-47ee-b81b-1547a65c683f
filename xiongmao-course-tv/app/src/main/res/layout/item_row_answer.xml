<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusedByDefault="true">


    <com.panda.course.widget.RoundCornerImageView
        android:id="@+id/iv_item_answer_row"
        style="@style/FocusStyle"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_margin="10dp"
        android:gravity="center"
        android:padding="8dp"
        android:visibility="visible"
        app:corner_size="20" />

    <TextView
        android:id="@+id/tv_item_answer_index"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="center|bottom"
        android:background="@drawable/shape_oval_answer"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_24" />

    <ImageView
        android:id="@+id/iv_item_answer_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:src="@drawable/icon_answer_ok"
        android:visibility="gone" />
</FrameLayout>



