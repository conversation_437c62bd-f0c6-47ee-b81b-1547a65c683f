<?xml version="1.0" encoding="utf-8"?>


<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_common_item_view"
    style="@style/FocusStyle"
    android:layout_width="400dp"
    android:layout_height="100dp"
    android:layout_marginLeft="16dp"
    android:layout_marginTop="16dp"
    android:layout_marginBottom="16dp"
    android:background="@drawable/base_border"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/item_iv_course_cover"
        android:layout_width="192dp"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_placeholder" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_item_history_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="4dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:paddingTop="4dp"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_22" />

        <TextView
            android:id="@+id/tv_item_history_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="4dp"
            android:ellipsize="end"
            android:paddingTop="4dp"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_18" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_item_play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_gravity="center|right"
        android:layout_marginTop="80dp"
        android:src="@drawable/icon_player"
        android:visibility="gone" />
</LinearLayout>

