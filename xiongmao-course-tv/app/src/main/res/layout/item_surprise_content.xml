<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center">

        <ImageView
            android:id="@+id/iv_item_surprise_red_envelopes"
            android:layout_width="180dp"
            android:layout_height="180dp"
            android:src="@drawable/icon_surprise_red_envelopes" />

        <TextView
            android:id="@+id/tv_surprise_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="2dp"
            android:text="奖励"
            android:textColor="@color/red"
            android:textSize="@dimen/font_size_14" />

        <LinearLayout
            android:id="@+id/ll_surprise_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_surprise_title"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp">

            <TextView
                android:id="@+id/tv_surprise_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10"
                android:textColor="@color/red"
                android:textSize="@dimen/font_size_46"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/tv_surprise_content"
                android:layout_toRightOf="@+id/tv_surprise_price"
                android:text="元"
                android:textColor="@color/red"
                android:textSize="@dimen/font_size_14"
                android:textStyle="bold" />

        </LinearLayout>


        <TextView
            android:id="@+id/tv_surprise_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_surprise_price"
            android:layout_centerHorizontal="true"
            android:text="价值"
            android:textColor="@color/red"
            android:textSize="@dimen/font_size_16" />

        <ImageView
            android:id="@+id/iv_item_surprise_red_envelopes_layer"
            android:layout_width="180dp"
            android:layout_height="180dp"
            android:src="@drawable/icon_surprise_red_envelopes_layer"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_item_surprise_lock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:paddingTop="140dp"
            android:src="@drawable/icon_surprise_lock"
            android:visibility="visible" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tv_item_reward_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="20dp"
        android:gravity="center|top"
        android:lineSpacingMultiplier="1.2"
        android:minHeight="150dp"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_22"
        android:textStyle="bold" />
</LinearLayout>

