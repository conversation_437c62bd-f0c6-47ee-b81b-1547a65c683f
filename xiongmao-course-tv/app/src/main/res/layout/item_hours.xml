<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/row_card_view"
    style="@style/FocusStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="20dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="20dp"
    android:layout_marginBottom="10dp"
    android:focusedByDefault="true"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <TextView
        android:paddingLeft="20dp"
        android:id="@+id/tv_item_row_skill"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/base_border"
        android:ellipsize="end"
        android:gravity="center|left"
        android:maxLines="2"
        android:text="11"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_30"
        android:visibility="visible" />

</androidx.cardview.widget.CardView>


