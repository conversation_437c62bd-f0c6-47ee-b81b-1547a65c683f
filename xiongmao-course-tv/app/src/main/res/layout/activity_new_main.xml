<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false">

        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.panda.course.widget.ScaleConstraintLayout
                android:id="@+id/cl_search"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="37dp"
                android:layout_marginTop="15dp"
                android:clickable="true"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@+id/hg_title"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="30dp"
                    android:paddingTop="5dp"
                    android:paddingEnd="10dp"
                    android:paddingBottom="5dp"
                    android:text="已安装应用"
                    android:textColor="@color/white"
                    android:textSize="13sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/icon_logout"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </com.panda.course.widget.ScaleConstraintLayout>

        </LinearLayout>



    </RelativeLayout>
</layout>