<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="500dp"
    android:layout_height="match_parent"
    android:background="@drawable/popup_shape_bg"
    android:orientation="vertical"
    android:paddingLeft="40dp"
    android:paddingRight="40dp">

    <ImageView
        android:id="@+id/iv_teacher"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_gravity="center"
        android:layout_marginTop="100dp" />

    <TextView
        android:id="@+id/tv_teacher"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        android:text="李江月"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18"
        android:textStyle="bold" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="个人简介"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_teacher_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18"
        android:textStyle="bold" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp"
        android:text="课程简介"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_class_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简介我是简"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_18"
        android:textStyle="bold" />
</LinearLayout>