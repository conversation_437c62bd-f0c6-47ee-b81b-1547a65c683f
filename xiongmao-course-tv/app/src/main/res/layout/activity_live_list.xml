<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg3"
        android:orientation="horizontal"
        tools:context=".ui.activity.LiveListActivity">

        <com.panda.course.widget.TabVerticalGridView
            android:id="@+id/row_recycler"
            android:layout_width="250dp"
            android:layout_height="match_parent"
            android:background="@color/base_bg2"
            android:paddingTop="20dp" />

        <com.panda.course.widget.TVBoxRecyclerView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toRightOf="@+id/row_recycler" />

    </RelativeLayout>

</layout>