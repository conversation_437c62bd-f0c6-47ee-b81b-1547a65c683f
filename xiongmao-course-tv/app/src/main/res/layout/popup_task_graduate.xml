<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <ImageView
            android:id="@+id/iv_graduate"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/tv_store_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_store_name"
            android:layout_marginLeft="300dp"
            android:textColor="@color/red"
            android:textSize="@dimen/font_size_36"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_store_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="300dp"
            android:layout_marginTop="280dp"
            android:textColor="@color/red"
            android:textSize="@dimen/font_size_50"
            android:textStyle="bold" />
    </RelativeLayout>

</layout>