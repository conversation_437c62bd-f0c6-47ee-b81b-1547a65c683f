<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:id="@+id/rl_layout"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:background="@drawable/base_border"
        android:clickable="true"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:padding="@dimen/font_size_20">

        <TextView
            android:id="@+id/tv_item_setting_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="无线网络"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_26" />

        <TextView
            android:id="@+id/tv_item_setting_right_sub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="6dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="请更新"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_18"
            android:textStyle="bold"
            app:bl_corners_radius="10dp"
            app:bl_solid_color="@color/red" />

        <TextView
            android:id="@+id/tv_item_setting_sub_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_item_setting_name"
            android:layout_marginTop="8dp"
            android:text="设置当前无线网络"
            android:textColor="@color/base_color_body"
            android:textSize="@dimen/font_size_18" />

        <TextView
            android:id="@+id/tv_item_setting_status_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_item_setting_sub_name"
            android:layout_marginTop="10dp"
            android:textColor="@color/white"
            android:textSize="@dimen/font_size_24" />

        <ImageView
            android:id="@+id/iv_item_setting_logo"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_below="@+id/tv_item_setting_sub_name"
            android:layout_alignParentRight="true"
            android:layout_marginTop="8dp" />

        <ImageView
            android:id="@+id/iv_item_setting_logo_status"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_below="@+id/tv_item_setting_sub_name"
            android:layout_alignParentRight="true"
            android:layout_marginTop="10dp" />
    </RelativeLayout>
</layout>