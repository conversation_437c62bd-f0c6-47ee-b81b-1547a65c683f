<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- 背景  gradient是渐变,corners定义的是圆角 -->
    <item
        android:id="@android:id/background"
        android:layout_width="wrap_content">
        <shape>

            <solid android:color="@color/black1" />
        </shape>
    </item>

    <!-- 进度条 -->
    <item android:id="@android:id/progress">
        <clip>
            <shape>
                <corners
                    android:bottomRightRadius="10dp"
                    android:topRightRadius="10dp" />

                <gradient
                    android:angle="90.0"
                    android:centerColor="#07B74C"
                    android:centerY="0.45"
                    android:endColor="#03AF82"
                    android:startColor="#07B74C" />
            </shape>
        </clip>
    </item>

</layer-list>