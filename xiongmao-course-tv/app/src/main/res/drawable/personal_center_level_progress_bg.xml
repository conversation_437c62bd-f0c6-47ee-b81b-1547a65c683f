<?xml version="1.0" encoding="utf-8"?>
<animated-rotate xmlns:android="http://schemas.android.com/apk/res/android"
    android:duration="2000"
    android:fromDegrees="0"
    android:pivotX="50%"
    android:pivotY="50%"
    android:toDegrees="360">
    <shape
        android:innerRadiusRatio="3"
        android:shape="ring"
        android:thicknessRatio="18"
        android:useLevel="false">
        <gradient
            android:centerColor="@color/green"
            android:centerY="0.50"
            android:endColor="#fff"
            android:startColor="@color/green"
            android:type="sweep"
            android:useLevel="false" />
    </shape>
</animated-rotate>



