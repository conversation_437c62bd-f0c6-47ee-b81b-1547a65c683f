<resources>

    <style name="TVTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/green</item>
        <item name="colorAccent">@color/green</item>
        <!-- 设置无标题-->
        <item name="android:windowNoTitle">true</item>
        <!-- 设置全屏-->
        <item name="android:windowFullscreen">true</item>
        <!-- 是否有遮盖-->
        <item name="android:windowContentOverlay">@null</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:keepScreenOn">true</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <!--将Activity的Theme设置成透明-->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 全局背景图 -->
        <!--        <item name="android:windowBackground">@drawable/shape_splash_bg</item>-->
        <item name="android:windowBackground">@android:color/transparent</item><!--背景透明-->

    </style>


    <!--dialog样式的界面-->
    <style name="dialog_style" parent="Theme.AppCompat.Light.Dialog">
        <!--是否悬浮在activity上-->
        <item name="android:windowIsFloating">true</item>
        <!--透明是否-->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:background">@null</item>
        <!--设置没有窗口标题、dialog标题等各种标题-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:title">@null</item>
        <item name="windowNoTitle">true</item>
        <item name="android:dialogTitle">@null</item>
        <!--点击 dialog Activity 周围是否关闭弹窗 true 关闭（默认为true） false 为不关闭-->
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:windowFrame">@null</item><!--边框-->
        <item name="android:windowBackground">@android:color/transparent</item><!--背景透明-->
        <item name="android:backgroundDimEnabled">true</item><!--模糊，背景透明是这个-->
    </style>

</resources>