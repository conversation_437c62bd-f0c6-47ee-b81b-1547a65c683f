<resources>


    <style name="loadingDialogTheme" parent="@android:style/Theme.Holo.Light.Dialog">
        <item name="android:windowIsFloating">true</item><!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">false</item><!-- 半透明 -->
        <item name="android:windowNoTitle">true</item><!-- 无标题 -->
        <item name="android:backgroundDimEnabled">false</item><!-- 无遮罩层 -->
        <!--透明背景-->
        <item name="android:background">@android:color/transparent</item>
        <!--窗口背景透明-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
    </style>


    <style name="dialogTransparent" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item><!--边框-->
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">true</item><!--半透明-->
        <item name="android:windowBackground">@android:color/transparent</item><!--背景透明-->
        <item name="android:windowNoTitle">true</item><!--无标题-->
        <item name="android:backgroundDimEnabled">true</item><!--模糊，背景透明是这个-->
    </style>


    <style name="BaseDialog" parent="android:Theme.Light">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>


    <style name="FocusStyle">
        <!-- Customize your theme here. -->
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
    </style>



    <style name="FocusCloseStyle">
        <!-- Customize your theme here. -->
        <item name="android:focusableInTouchMode">false</item>
        <item name="android:focusable">false</item>
        <item name="android:clickable">false</item>

    </style>


    <style name="recy_vertical_style">
        <item name="android:scrollbarSize">10dp</item>
        <item name="android:scrollbars">vertical</item>
        <item name="android:scrollbarThumbVertical">@drawable/recy_vertical_scrollbar</item>
        <item name="android:scrollbarTrackVertical">@drawable/recy_vertical_scrollbar_bg</item>
    </style>

    <!-- 自定义阴影组件-->
    <declare-styleable name="Fp_ShadowLayout">
        <attr name="fp_shadowColor" format="color" />
        <attr name="fp_shadowRadius" format="dimension" />
        <attr name="fp_shadowRoundRadius" format="dimension" />
        <attr name="fp_shadowShape">
            <flag name="fp_rectangle" value="0x0001" />
            <flag name="fp_round_rectangle" value="0x0100" />
        </attr>
        <attr name="fp_shadowSide">
            <flag name="fp_all" value="0x1111" />
            <flag name="fp_left" value="0x0001" />
            <flag name="fp_top" value="0x0010" />
            <flag name="fp_right" value="0x0100" />
            <flag name="fp_bottom" value="0x1000" />
        </attr>
        <attr name="fp_round_corner">
            <flag name="fp_corner_all" value="0x1111" />
            <flag name="fp_corner_leftTop" value="0x0001" />
            <flag name="fp_corner_leftBottom" value="0x0010" />
            <flag name="fp_corner_rightTop" value="0x0100" />
            <flag name="fp_corner_rightBottom" value="0x1000" />
        </attr>
    </declare-styleable>

</resources>
