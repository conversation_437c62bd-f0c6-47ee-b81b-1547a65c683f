<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="ScaleConstraintLayout">
        <attr name="scale_mode">
            <enum name="ZOOM_FACTOR_SMALL" value="1" />
            <enum name="ZOOM_FACTOR_XSMALL" value="4" />
            <enum name="ZOOM_FACTOR_XXSMALL" value="5" />
            <enum name="ZOOM_FACTOR_XXXSMALL" value="6" />
            <enum name="ZOOM_FACTOR_MEDIUM" value="2" />
            <enum name="ZOOM_FACTOR_LARGE" value="3" />
        </attr>
    </declare-styleable>


    <declare-styleable name="RoundCornerImageView">
        <attr name="corner_size" format="integer" />
        <attr name="corner_top_left_size" format="integer" />
        <attr name="corner_top_right_size" format="integer" />
        <attr name="corner_bottom_right_size" format="integer" />
        <attr name="corner_bottom_left_size" format="integer" />
    </declare-styleable>


    <declare-styleable name="AutofitTextView">
        <!-- Minimum size of the text. -->
        <attr name="minTextSize" format="dimension" />
        <!-- Amount of precision used to calculate the correct text size to fit within its
        bounds. Lower precision is more precise and takes more time. -->
        <attr name="precision" format="float" />
        <!-- Defines whether to automatically resize text to fit to the view's bounds. -->
        <attr name="sizeToFit" format="boolean" />
    </declare-styleable>

    <attr name="autoScaleTextViewStyle" />
    <declare-styleable name="AutoScaleTextView">
        <attr name="minssTextSize" format="dimension" />
    </declare-styleable>


    <declare-styleable name="FitImageView">
        <attr name="fiv_direction">
            <enum name="landscape" value="0" />
            <enum name="portrait" value="1" />
        </attr>
    </declare-styleable>


    <declare-styleable name="IQYMenuGroup">
        <attr name="iqy_targetViewId" format="reference" />
    </declare-styleable>


    <declare-styleable name="LabelView">
        <attr name="label_distance" format="dimension" />
        <attr name="label_height" format="dimension" />
        <attr name="label_strokeWidth" format="dimension" />
        <attr name="label_backgroundColor" format="color" />
        <attr name="label_strokeColor" format="color" />
        <attr name="label_text" format="string" />
        <attr name="label_textSize" format="dimension" />
        <attr name="label_textStyle" format="enum">
            <enum name="NORMAL" value="0" />
            <enum name="BOLD" value="1" />
            <enum name="ITALIC" value="2" />
            <enum name="BOLD_ITALIC" value="3" />
        </attr>
        <attr name="label_textColor" format="color" />
        <attr name="label_visual" format="boolean" />
        <attr name="label_orientation" format="enum">
            <enum name="LEFT_TOP" value="1" />
            <enum name="RIGHT_TOP" value="2" />
            <enum name="LEFT_BOTTOM" value="3" />
            <enum name="RIGHT_BOTTOM" value="4" />
        </attr>
    </declare-styleable>
</resources>