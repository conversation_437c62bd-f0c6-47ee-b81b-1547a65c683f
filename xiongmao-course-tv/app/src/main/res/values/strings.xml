<resources>
    <string name="app_name">熊猫AI课</string>


    <string name="helper_loading_net_error">请检查网络后点击重试</string>
    <string name="helper_loading_net_empty">点击重试</string>
    <string name="helper_loading_error_tip">数据获取失败</string>
    <string name="helper_loading_empty_tip">暂时没有数据</string>
    <string name="helper_loading_tip">网速太慢了，请求中...</string>


    <string name="TUIKitErrorInProcess">执行中</string>
    <string name="TUIKitErrorInvalidParameters">参数无效</string>
    <string name="TUIKitErrorIOOperateFaild">操作本地 IO 错误</string>
    <string name="TUIKitErrorInvalidJson">错误的 JSON 格式</string>
    <string name="TUIKitErrorOutOfMemory">内存不足</string>
    <string name="TUIKitErrorParseResponseFaild">PB 解析失败</string>
    <string name="TUIKitErrorSerializeReqFaild">PB 序列化失败</string>
    <string name="TUIKitErrorSDKNotInit">IM SDK 未初始化</string>
    <string name="TUIKitErrorLoadMsgFailed">加载本地数据库操作失败</string>
    <string name="TUIKitErrorDatabaseOperateFailed">本地数据库操作失败</string>
    <string name="TUIKitErrorCrossThread">跨线程错误</string>
    <string name="TUIKitErrorTinyIdEmpty">用户信息为空</string>
    <string name="TUIKitErrorInvalidIdentifier">Identifier 非法</string>
    <string name="TUIKitErrorFileNotFound">文件不存在</string>
    <string name="TUIKitErrorFileTooLarge">文件大小超出了限制</string>
    <string name="TUIKitErrorEmptyFile">空文件</string>
    <string name="TUIKitErrorFileOpenFailed">文件打开失败</string>
    <string name="TUIKitErrorNotLogin">IM SDK 未登陆</string>
    <string name="TUIKitErrorNoPreviousLogin">并没有登录过该用户</string>
    <string name="TUIKitErrorUserSigExpired">UserSig 过期</string>
    <string name="TUIKitErrorLoginKickedOffByOther">其他终端登录同一账号</string>
    <string name="TUIKitErrorTLSSDKInit">TLS SDK 初始化失败</string>
    <string name="TUIKitErrorTLSSDKUninit">TLS SDK 未初始化</string>
    <string name="TUIKitErrorTLSSDKTRANSPackageFormat">TLS SDK TRANS 包格式错误</string>
    <string name="TUIKitErrorTLSDecrypt">TLS SDK 解密失败</string>
    <string name="TUIKitErrorTLSSDKRequest">TLS SDK 请求失败</string>
    <string name="TUIKitErrorTLSSDKRequestTimeout">TLS SDK 请求超时</string>
    <string name="TUIKitErrorInvalidConveration">会话无效</string>
    <string name="TUIKitErrorFileTransAuthFailed">文件传输鉴权失败</string>
    <string name="TUIKitErrorFileTransNoServer">文件传输获取 Server 列表失败</string>
    <string name="TUIKitErrorFileTransUploadFailed">文件传输上传失败，请检查网络是否连接</string>
    <string name="TUIKitErrorFileTransUploadFailedNotImage">文件传输上传失败，请检查上传的图片是否能够正常打开</string>
    <string name="TUIKitErrorFileTransDownloadFailed">文件传输下载失败，请检查网络，或者文件、语音是否已经过期</string>
    <string name="TUIKitErrorHTTPRequestFailed">HTTP 请求失败</string>
    <string name="TUIKitErrorInvalidMsgElem">IM SDK 无效消息 elem</string>
    <string name="TUIKitErrorInvalidSDKObject">无效的对象</string>
    <string name="TUIKitSDKMsgBodySizeLimit">消息长度超出限制</string>
    <string name="TUIKitErrorSDKMsgKeyReqDifferRsp">消息 KEY 错误</string>
    <string name="TUIKitErrorSDKGroupInvalidID">群组 ID 非法，自定义群组 ID 必须为可打印 ASCII 字符（0x20-0x7e），最长48个字节，且前缀不能为 @TGS#</string>
    <string name="TUIKitErrorSDKGroupInvalidName">群名称非法，群名称最长30字节</string>
    <string name="TUIKitErrorSDKGroupInvalidIntroduction">群简介非法，群简介最长240字节</string>
    <string name="TUIKitErrorSDKGroupInvalidNotification">群公告非法，群公告最长300字节</string>
    <string name="TUIKitErrorSDKGroupInvalidFaceURL">群头像 URL 非法，群头像 URL 最长100字节</string>
    <string name="TUIKitErrorSDKGroupInvalidNameCard">群名片非法，群名片最长50字节</string>
    <string name="TUIKitErrorSDKGroupMemberCountLimit">超过群组成员数的限制</string>
    <string name="TUIKitErrorSDKGroupJoinPrivateGroupDeny">不允许申请加入 Private 群组</string>
    <string name="TUIKitErrorSDKGroupInviteSuperDeny">不允许邀请角色为群主的成员</string>
    <string name="TUIKitErrorSDKGroupInviteNoMember">不允许邀请0个成员</string>
    <string name="TUIKitErrorSDKFriendShipInvalidProfileKey">资料字段非法</string>
    <string name="TUIKitErrorSDKFriendshipInvalidAddRemark">备注字段非法，最大96字节</string>
    <string name="TUIKitErrorSDKFriendshipInvalidAddWording">请求添加好友的请求说明字段非法，最大120字节</string>
    <string name="TUIKitErrorSDKFriendshipInvalidAddSource">请求添加好友的添加来源字段非法，来源需要添加“AddSource_Type_”前缀。</string>
    <string name="TUIKitErrorSDKFriendshipFriendGroupEmpty">好友分组字段非法，必须不为空，每个分组的名称最长30字节</string>
    <string name="TUIKitErrorSDKNetEncodeFailed">网络链接加密失败</string>
    <string name="TUIKitErrorSDKNetDecodeFailed">网络链接解密失败</string>
    <string name="TUIKitErrorSDKNetAuthInvalid">网络链接未完成鉴权</string>
    <string name="TUIKitErrorSDKNetCompressFailed">数据包压缩失败</string>
    <string name="TUIKitErrorSDKNetUncompressFaile">数据包解压失败</string>
    <string name="TUIKitErrorSDKNetFreqLimit">调用频率限制，最大每秒发起 5 次请求</string>
    <string name="TUIKitErrorSDKnetReqCountLimit">请求队列満，超过同时请求的数量限制，最大同时发起1000个请求。</string>
    <string name="TUIKitErrorSDKNetDisconnect">网络已断开，未建立连接，或者建立 socket 连接时，检测到无网络。</string>
    <string name="TUIKitErrorSDKNetAllreadyConn">网络连接已建立，重复创建连接</string>
    <string name="TUIKitErrorSDKNetConnTimeout">建立网络连接超时，请等网络恢复后重试。</string>
    <string name="TUIKitErrorSDKNetConnRefuse">网络连接已被拒绝，请求过于频繁，服务端拒绝服务。</string>
    <string name="TUIKitErrorSDKNetNetUnreach">没有到达网络的可用路由，请等网络恢复后重试。</string>
    <string name="TUIKitErrorSDKNetSocketNoBuff">系统中没有足够的缓冲区空间资源可用来完成调用，系统过于繁忙，内部错误。</string>
    <string name="TUIKitERRORSDKNetResetByPeer">对端重置了连接</string>
    <string name="TUIKitErrorSDKNetSOcketInvalid">socket 套接字无效</string>
    <string name="TUIKitErrorSDKNetHostGetAddressFailed">IP 地址解析失败</string>
    <string name="TUIKitErrorSDKNetConnectReset">网络连接到中间节点或服务端重置</string>
    <string name="TUIKitErrorSDKNetWaitInQueueTimeout">请求包等待进入待发送队列超时</string>
    <string name="TUIKitErrorSDKNetWaitSendTimeout">请求包已进入待发送队列，等待进入系统的网络 buffer 超时</string>
    <string name="TUIKitErrorSDKNetWaitAckTimeut">请求包已进入系统的网络 buffer ，等待服务端回包超时</string>
    <string name="TUIKitErrorSDKSVRSSOConnectLimit">Server 的连接数量超出限制，服务端拒绝服务。</string>
    <string name="TUIKitErrorSDKSVRSSOVCode">验证码下发超时。</string>
    <string name="TUIKitErrorSVRSSOD2Expired">Key 过期。Key 是根据 UserSig 生成的内部票据，Key 的有效期小于或等于 UserSig 的有效期。请重新调用 TIMManager.getInstance().login 登录接口生成新的 Key。</string>
    <string name="TUIKitErrorSVRA2UpInvalid">Ticket 过期。Ticket 是根据 UserSig 生成的内部票据，Ticket 的有效期小于或等于 UserSig 的有效期。请重新调用 TIMManager.getInstance().login 登录接口生成新的 Ticket。</string>
    <string name="TUIKitErrorSVRA2DownInvalid">票据验证没通过或者被安全打击。请重新调用 TIMManager.getInstance().login 登录接口生成新的票据。</string>
    <string name="TUIKitErrorSVRSSOEmpeyKey">不允许空 Key。</string>
    <string name="TUIKitErrorSVRSSOUinInvalid">Key 中的帐号和请求包头的帐号不匹配。</string>
    <string name="TUIKitErrorSVRSSOVCodeTimeout">验证码下发超时。</string>
    <string name="TUIKitErrorSVRSSONoImeiAndA2">需要带上 Key 和 Ticket。</string>
    <string name="TUIKitErrorSVRSSOCookieInvalid">Cookie 检查不匹配。</string>
    <string name="TUIKitErrorSVRSSODownTips">下发提示语，Key 过期。</string>
    <string name="TUIKitErrorSVRSSODisconnect">断链锁屏。</string>
    <string name="TUIKitErrorSVRSSOIdentifierInvalid">失效身份。</string>
    <string name="TUIKitErrorSVRSSOClientClose">终端自动退出。</string>
    <string name="TUIKitErrorSVRSSOMSFSDKQuit">MSFSDK 自动退出。</string>
    <string name="TUIKitErrorSVRSSOD2KeyWrong">解密失败次数超过阈值，通知终端需要重置，请重新调用 TIMManager.getInstance().login 登录接口生成新的 Key。</string>
    <string name="TUIKitErrorSVRSSOUnsupport">不支持聚合，给终端返回统一的错误码。终端在该 TCP 长连接上停止聚合。</string>
    <string name="TUIKitErrorSVRSSOPrepaidArrears">预付费欠费。</string>
    <string name="TUIKitErrorSVRSSOPacketWrong">请求包格式错误。</string>
    <string name="TUIKitErrorSVRSSOAppidBlackList">SDKAppID 黑名单。</string>
    <string name="TUIKitErrorSVRSSOCmdBlackList">SDKAppID 设置 service cmd 黑名单。</string>
    <string name="TUIKitErrorSVRSSOAppidWithoutUsing">SDKAppID 停用。</string>
    <string name="TUIKitErrorSVRSSOFreqLimit">频率限制(用户)，频率限制是设置针对某一个协议的每秒请求数的限制。</string>
    <string name="TUIKitErrorSVRSSOOverload">过载丢包(系统)，连接的服务端处理过多请求，处理不过来，拒绝服务。</string>
    <string name="TUIKitErrorSVRResNotFound">要发送的资源文件不存在。</string>
    <string name="TUIKitErrorSVRResAccessDeny">要发送的资源文件不允许访问。</string>
    <string name="TUIKitErrorSVRResSizeLimit">文件大小超过限制。</string>
    <string name="TUIKitErrorSVRResSendCancel">用户取消发送，如发送过程中登出等原因。</string>
    <string name="TUIKitErrorSVRResReadFailed">读取文件内容失败。</string>
    <string name="TUIKitErrorSVRResTransferTimeout">资源文件传输超时</string>
    <string name="TUIKitErrorSVRResInvalidParameters">参数非法。</string>
    <string name="TUIKitErrorSVRResInvalidFileMd5">文件 MD5 校验失败。</string>
    <string name="TUIKitErrorSVRResInvalidPartMd5">分片 MD5 校验失败。</string>
    <string name="TUIKitErrorSVRCommonInvalidHttpUrl">HTTP 解析错误 ，请检查 HTTP 请求 URL 格式。</string>
    <string name="TUIKitErrorSVRCommomReqJsonParseFailed">HTTP 请求 JSON 解析错误，请检查 JSON 格式。</string>
    <string name="TUIKitErrorSVRCommonInvalidAccount">请求 URI 或 JSON 包体中 Identifier 或 UserSig 错误。</string>
    <string name="TUIKitErrorSVRCommonInvalidSdkappid">SDKAppID 失效，请核对 SDKAppID 有效性。</string>
    <string name="TUIKitErrorSVRCommonRestFreqLimit">REST 接口调用频率超过限制，请降低请求频率。</string>
    <string name="TUIKitErrorSVRCommonRequestTimeout">服务请求超时或 HTTP 请求格式错误，请检查并重试。</string>
    <string name="TUIKitErrorSVRCommonInvalidRes">请求资源错误，请检查请求 URL。</string>
    <string name="TUIKitErrorSVRCommonIDNotAdmin">REST API 请求的 Identifier 字段请填写 App 管理员帐号。</string>
    <string name="TUIKitErrorSVRCommonSdkappidFreqLimit">SDKAppID 请求频率超限，请降低请求频率。</string>
    <string name="TUIKitErrorSVRCommonSdkappidMiss">REST 接口需要带 SDKAppID，请检查请求 URL 中的 SDKAppID。</string>
    <string name="TUIKitErrorSVRCommonRspJsonParseFailed">HTTP 响应包 JSON 解析错误。</string>
    <string name="TUIKitErrorSVRCommonExchangeAccountTimeout">置换帐号超时。</string>
    <string name="TUIKitErrorSVRCommonInvalidIdFormat">请求包体 Identifier 类型错误，请确认 Identifier 为字符串格式。</string>
    <string name="TUIKitErrorSVRCommonSDkappidForbidden">SDKAppID 被禁用</string>
    <string name="TUIKitErrorSVRCommonReqForbidden">请求被禁用</string>
    <string name="TUIKitErrorSVRCommonReqFreqLimit">请求过于频繁，请稍后重试。</string>
    <string name="TUIKitErrorSVRCommonInvalidService">您的专业版套餐包已到期并停用，请登录 即时通信 IM 购买页面 重新购买套餐包。购买后，将在5分钟后生效。</string>
    <string name="TUIKitErrorSVRCommonSensitiveText">文本安全打击，文本中可能包含敏感词汇。</string>
    <string name="TUIKitErrorSVRCommonBodySizeLimit">发消息包体过长，目前支持最大8k消息包体长度，请减少包体大小重试。</string>
    <string name="TUIKitErrorSVRAccountUserSigExpired">UserSig 已过期，请重新生成 UserSig</string>
    <string name="TUIKitErrorSVRAccountUserSigEmpty">UserSig 长度为0</string>
    <string name="TUIKitErrorSVRAccountUserSigCheckFailed">UserSig 校验失败</string>
    <string name="TUIKitErrorSVRAccountUserSigMismatchPublicKey">用公钥验证 UserSig 失败</string>
    <string name="TUIKitErrorSVRAccountUserSigMismatchId">请求的 Identifier 与生成 UserSig 的 Identifier 不匹配。</string>
    <string name="TUIKitErrorSVRAccountUserSigMismatchSdkAppid">请求的 SDKAppID 与生成 UserSig 的 SDKAppID 不匹配。</string>
    <string name="TUIKitErrorSVRAccountUserSigPublicKeyNotFound">验证 UserSig 时公钥不存在</string>
    <string name="TUIKitErrorSVRAccountUserSigSdkAppidNotFount">SDKAppID 未找到，请在云通信 IM 控制台确认应用信息。</string>
    <string name="TUIKitErrorSVRAccountInvalidUserSig">UserSig 已经失效，请重新生成，再次尝试。</string>
    <string name="TUIKitErrorSVRAccountNotFound">请求的用户帐号不存在。</string>
    <string name="TUIKitErrorSVRAccountSecRstr">安全原因被限制。</string>
    <string name="TUIKitErrorSVRAccountInternalTimeout">服务端内部超时，请重试。</string>
    <string name="TUIKitErrorSVRAccountInvalidCount">请求中批量数量不合法。</string>
    <string name="TUIkitErrorSVRAccountINvalidParameters">参数非法，请检查必填字段是否填充，或者字段的填充是否满足协议要求。</string>
    <string name="TUIKitErrorSVRAccountAdminRequired">请求需要 App 管理员权限。</string>
    <string name="TUIKitErrorSVRAccountFreqLimit">因失败且重试次数过多导致被限制，请检查 UserSig 是否正确，一分钟之后再试。</string>
    <string name="TUIKitErrorSVRAccountBlackList">帐号被拉入黑名单。</string>
    <string name="TUIKitErrorSVRAccountCountLimit">创建帐号数量超过免费体验版数量限制，请升级为专业版。</string>
    <string name="TUIKitErrorSVRAccountInternalError">服务端内部错误，请重试。</string>
    <string name="TUIKitErrorSVRProfileInvalidParameters">请求参数错误，请根据错误描述检查请求是否正确。</string>
    <string name="TUIKitErrorSVRProfileAccountMiss">请求参数错误，没有指定需要拉取资料的用户帐号。</string>
    <string name="TUIKitErrorSVRProfileAccountNotFound">请求的用户帐号不存在。</string>
    <string name="TUIKitErrorSVRProfileAdminRequired">请求需要 App 管理员权限。</string>
    <string name="TUIKitErrorSVRProfileSensitiveText">资料字段中包含敏感词。</string>
    <string name="TUIKitErrorSVRProfileInternalError">服务端内部错误，请稍后重试。</string>
    <string name="TUIKitErrorSVRProfileReadWritePermissionRequired">没有资料字段的读权限，详情可参见 资料字段。</string>
    <string name="TUIKitErrorSVRProfileTagNotFound">资料字段的 Tag 不存在。</string>
    <string name="TUIKitErrorSVRProfileSizeLimit">资料字段的 Value 长度超过500字节。</string>
    <string name="TUIKitErrorSVRProfileValueError">标配资料字段的 Value 错误，详情可参见 标配资料字段。</string>
    <string name="TUIKitErrorSVRProfileInvalidValueFormat">资料字段的 Value 类型不匹配，详情可参见 标配资料字段。</string>
    <string name="TUIKitErrorSVRFriendshipInvalidParameters">请求参数错误，请根据错误描述检查请求是否正确。</string>
    <string name="TUIKitErrorSVRFriendshipInvalidSdkAppid">SDKAppID 不匹配。</string>
    <string name="TUIKitErrorSVRFriendshipAccountNotFound">请求的用户帐号不存在。</string>
    <string name="TUIKitErrorSVRFriendshipAdminRequired">请求需要 App 管理员权限。</string>
    <string name="TUIKitErrorSVRFriendshipSensitiveText">关系链字段中包含敏感词。</string>
    <string name="TUIKitErrorSVRFriendshipNetTimeout">网络超时，请稍后重试。</string>
    <string name="TUIKitErrorSVRFriendshipWriteConflict">并发写导致写冲突，建议使用批量方式。</string>
    <string name="TUIKitErrorSVRFriendshipAddFriendDeny">后台禁止该用户发起加好友请求。</string>
    <string name="TUIkitErrorSVRFriendshipCountLimit">自己的好友数已达系统上限。</string>
    <string name="TUIKitErrorSVRFriendshipGroupCountLimit">分组已达系统上限。</string>
    <string name="TUIKitErrorSVRFriendshipPendencyLimit">未决数已达系统上限。</string>
    <string name="TUIKitErrorSVRFriendshipBlacklistLimit">黑名单数已达系统上限。</string>
    <string name="TUIKitErrorSVRFriendshipPeerFriendLimit">对方的好友数已达系统上限。</string>
    <string name="TUIKitErrorSVRFriendshipInSelfBlacklist">对方在自己的黑名单中，不允许加好友。</string>
    <string name="TUIKitErrorSVRFriendshipAllowTypeDenyAny">对方的加好友验证方式是不允许任何人添加自己为好友。</string>
    <string name="TUIKitErrorSVRFriendshipInPeerBlackList">自己在对方的黑名单中，不允许加好友。</string>
    <string name="TUIKitErrorSVRFriendshipAllowTypeNeedConfirm">请求已发送，等待对方同意</string>
    <string name="TUIKitErrorSVRFriendshipAddFriendSecRstr">添加好友请求被安全策略打击，请勿频繁发起添加好友请求。</string>
    <string name="TUIKitErrorSVRFriendshipPendencyNotFound">请求的未决不存在。</string>
    <string name="TUIKitErrorSVRFriendshipDelFriendSecRstr">删除好友请求被安全策略打击，请勿频繁发起删除好友请求。</string>
    <string name="TUIKirErrorSVRFriendAccountNotFoundEx">请求的用户帐号不存在。</string>
    <string name="TUIKitErrorSVRMsgPkgParseFailed">解析请求包失败。</string>
    <string name="TUIKitErrorSVRMsgInternalAuthFailed">内部鉴权失败。</string>
    <string name="TUIKitErrorSVRMsgInvalidId">Identifier 无效</string>
    <string name="TUIKitErrorSVRMsgNetError">网络异常，请重试。</string>
    <string name="TUIKitErrorSVRMsgPushDeny">触发发送单聊消息之前回调，App 后台返回禁止下发该消息。</string>
    <string name="TUIKitErrorSVRMsgInPeerBlackList">发送单聊消息，被对方拉黑，禁止发送。</string>
    <string name="TUIKitErrorSVRMsgBothNotFriend">消息发送双方互相不是好友，禁止发送。</string>
    <string name="TUIKitErrorSVRMsgNotPeerFriend">发送单聊消息，自己不是对方的好友（单向关系），禁止发送。</string>
    <string name="TUIkitErrorSVRMsgNotSelfFriend">发送单聊消息，对方不是自己的好友（单向关系），禁止发送。</string>
    <string name="TUIKitErrorSVRMsgShutupDeny">因禁言，禁止发送消息。</string>
    <string name="TUIKitErrorSVRMsgRevokeTimeLimit">消息撤回超过了时间限制（默认2分钟）。</string>
    <string name="TUIKitErrorSVRMsgDelRambleInternalError">删除漫游内部错误。</string>
    <string name="TUIKitErrorSVRMsgJsonParseFailed">JSON 格式解析失败，请检查请求包是否符合 JSON 规范。</string>
    <string name="TUIKitErrorSVRMsgInvalidJsonBodyFormat">JSON 格式请求包中 MsgBody 不符合消息格式描述</string>
    <string name="TUIKitErrorSVRMsgInvalidToAccount">JSON 格式请求包体中缺少 To_Account 字段或者 To_Account 字段不是 Integer 类型</string>
    <string name="TUIKitErrorSVRMsgInvalidRand">JSON 格式请求包体中缺少 MsgRandom 字段或者 MsgRandom 字段不是 Integer 类型</string>
    <string name="TUIKitErrorSVRMsgInvalidTimestamp">JSON 格式请求包体中缺少 MsgTimeStamp 字段或者 MsgTimeStamp 字段不是 Integer 类型</string>
    <string name="TUIKitErrorSVRMsgBodyNotArray">JSON 格式请求包体中 MsgBody 类型不是 Array 类型</string>
    <string name="TUIKitErrorSVRMsgInvalidJsonFormat">JSON 格式请求包不符合消息格式描述</string>
    <string name="TUIKitErrorSVRMsgToAccountCountLimit">批量发消息目标帐号超过500</string>
    <string name="TUIKitErrorSVRMsgToAccountNotFound">To_Account 没有注册或不存在</string>
    <string name="TUIKitErrorSVRMsgTimeLimit">消息离线存储时间错误（最多不能超过7天）。</string>
    <string name="TUIKitErrorSVRMsgInvalidSyncOtherMachine">JSON 格式请求包体中 SyncOtherMachine 字段不是 Integer 类型</string>
    <string name="TUIkitErrorSVRMsgInvalidMsgLifeTime">JSON 格式请求包体中 MsgLifeTime 字段不是 Integer 类型</string>
    <string name="TUIKitErrorSVRMsgBodySizeLimit">JSON 数据包超长，消息包体请不要超过8k。</string>
    <string name="TUIKitErrorSVRmsgLongPollingCountLimit">Web 端长轮询被踢（Web 端同时在线实例个数超出限制）。</string>
    <string name="TUIKitErrorUnsupporInterface">您购买的套餐包不支持该功能</string>

    <string name="TUIKitErrorSVRGroupApiNameError">请求中的接口名称错误</string>
    <string name="TUIKitErrorSVRGroupAccountCountLimit">请求包体中携带的帐号数量过多。</string>
    <string name="TUIkitErrorSVRGroupFreqLimit">操作频率限制，请尝试降低调用的频率。</string>
    <string name="TUIKitErrorSVRGroupPermissionDeny">操作权限不足</string>
    <string name="TUIKitErrorSVRGroupInvalidReq">请求非法</string>
    <string name="TUIKitErrorSVRGroupSuperNotAllowQuit">该群不允许群主主动退出。</string>
    <string name="TUIKitErrorSVRGroupNotFound">群组不存在</string>
    <string name="TUIKitErrorSVRGroupJsonParseFailed">解析 JSON 包体失败，请检查包体的格式是否符合 JSON 格式。</string>
    <string name="TUIKitErrorSVRGroupInvalidId">发起操作的 Identifier 非法，请检查发起操作的用户 Identifier 是否填写正确。</string>
    <string name="TUIKitErrorSVRGroupAllreadyMember">被邀请加入的用户已经是群成员。</string>
    <string name="TUIKitErrorSVRGroupFullMemberCount">群已满员，无法将请求中的用户加入群组</string>
    <string name="TUIKitErrorSVRGroupInvalidGroupId">群组 ID 非法，请检查群组 ID 是否填写正确。</string>
    <string name="TUIKitErrorSVRGroupRejectFromThirdParty">App 后台通过第三方回调拒绝本次操作。</string>
    <string name="TUIKitErrorSVRGroupShutDeny">因被禁言而不能发送消息，请检查发送者是否被设置禁言。</string>
    <string name="TUIKitErrorSVRGroupRspSizeLimit">应答包长度超过最大包长</string>
    <string name="TUIKitErrorSVRGroupAccountNotFound">请求的用户帐号不存在。</string>
    <string name="TUIKitErrorSVRGroupGroupIdInUse">群组 ID 已被使用，请选择其他的群组 ID。</string>
    <string name="TUIKitErrorSVRGroupSendMsgFreqLimit">发消息的频率超限，请延长两次发消息时间的间隔。</string>
    <string name="TUIKitErrorSVRGroupReqAllreadyBeenProcessed">此邀请或者申请请求已经被处理。</string>
    <string name="TUIKitErrorSVRGroupGroupIdUserdForSuper">群组 ID 已被使用，并且操作者为群主，可以直接使用。</string>
    <string name="TUIKitErrorSVRGroupSDkAppidDeny">该 SDKAppID 请求的命令字已被禁用</string>
    <string name="TUIKitErrorSVRGroupRevokeMsgNotFound">请求撤回的消息不存在。</string>
    <string name="TUIKitErrorSVRGroupRevokeMsgTimeLimit">消息撤回超过了时间限制（默认2分钟）。</string>
    <string name="TUIKitErrorSVRGroupRevokeMsgDeny">请求撤回的消息不支持撤回操作。</string>
    <string name="TUIKitErrorSVRGroupNotAllowRevokeMsg">群组类型不支持消息撤回操作。</string>
    <string name="TUIKitErrorSVRGroupRemoveMsgDeny">该消息类型不支持删除操作。</string>
    <string name="TUIKitErrorSVRGroupNotAllowRemoveMsg">音视频聊天室和在线成员广播大群不支持删除消息。</string>
    <string name="TUIKitErrorSVRGroupAvchatRoomCountLimit">音视频聊天室创建数量超过了限制</string>
    <string name="TUIKitErrorSVRGroupCountLimit">单个用户可创建和加入的群组数量超过了限制”。</string>
    <string name="TUIKitErrorSVRGroupMemberCountLimit">群成员数量超过限制</string>
    <string name="TUIKitErrorSVRNoSuccessResult">批量操作无成功结果</string>
    <string name="TUIKitErrorSVRToUserInvalid">IM: 无效接收方</string>
    <string name="TUIKitErrorSVRRequestTimeout">请求超时</string>
    <string name="TUIKitErrorSVRInitCoreFail">INIT CORE模块失败</string>
    <string name="TUIKitErrorExpiredSessionNode">SessionNode为null</string>
    <string name="TUIKitErrorLoggedOutBeforeLoginFinished">在登录完成前进行了登出（在登录时返回）</string>
    <string name="TUIKitErrorTLSSDKNotInitialized">tlssdk未初始化</string>
    <string name="TUIKitErrorTLSSDKUserNotFound">TLSSDK没有找到相应的用户信息</string>
    <string name="TUIKitErrorBindFaildRegTimeout">注册超时</string>
    <string name="TUIKitErrorBindFaildIsBinding">正在bind操作中</string>
    <string name="TUIKitErrorPacketFailUnknown">发包未知错误</string>
    <string name="TUIKitErrorPacketFailReqNoNet">发送请求包时没有网络,处理时转换成case ERR_REQ_NO_NET_ON_REQ:</string>
    <string name="TUIKitErrorPacketFailRespNoNet">发送回复包时没有网络,处理时转换成case ERR_REQ_NO_NET_ON_RSP:</string>
    <string name="TUIKitErrorPacketFailReqNoAuth">发送请求包时没有权限</string>
    <string name="TUIKitErrorPacketFailSSOErr">SSO错误</string>
    <string name="TUIKitErrorPacketFailRespTimeout">回复超时</string>
    <string name="TUIKitErrorFriendshipProxySyncing">proxy_manager没有完成svr数据同步</string>
    <string name="TUIKitErrorFriendshipProxySyncedFail">proxy_manager同步失败</string>
    <string name="TUIKitErrorFriendshipProxyLocalCheckErr">proxy_manager请求参数，在本地检查不合法</string>
    <string name="TUIKitErrorGroupInvalidField">group assistant请求字段中包含非预设字段</string>
    <string name="TUIKitErrorGroupStoreageDisabled">group assistant群资料本地存储没有开启</string>
    <string name="TUIKitErrorLoadGrpInfoFailed">无法从存储中加载groupinfo</string>
    <string name="TUIKitErrorReqNoNetOnReq">请求的时候没有网络</string>
    <string name="TUIKitErrorReqNoNetOnResp">响应的时候没有网络</string>
    <string name="TUIKitErrorServiceNotReady">QALSDK服务未就绪</string>
    <string name="TUIKitErrorLoginAuthFailed">账号认证失败（用户信息获取失败）</string>
    <string name="TUIKitErrorNeverConnectAfterLaunch">在应用启动后没有尝试联网</string>
    <string name="TUIKitErrorReqFailed">QAL执行失败</string>
    <string name="TUIKitErrorReqInvaidReq">请求非法，toMsgService非法</string>
    <string name="TUIKitErrorReqOnverLoaded">请求队列满</string>
    <string name="TUIKitErrorReqKickOff">已经被其他终端踢了</string>
    <string name="TUIKitErrorReqServiceSuspend">服务被暂停</string>
    <string name="TUIKitErrorReqInvalidSign">SSO签名错误</string>
    <string name="TUIKitErrorReqInvalidCookie">SSO cookie无效</string>
    <string name="TUIKitErrorLoginTlsRspParseFailed">登录时TLS回包校验，包体长度错误</string>
    <string name="TUIKitErrorLoginOpenMsgTimeout">登录时OPENSTATSVC向OPENMSG上报状态超时</string>
    <string name="TUIKitErrorLoginOpenMsgRspParseFailed">登录时OPENSTATSVC向OPENMSG上报状态时解析回包失败</string>
    <string name="TUIKitErrorLoginTslDecryptFailed">登录时TLS解密失败</string>
    <string name="TUIKitErrorWifiNeedAuth">wifi需要认证</string>
    <string name="TUIKitErrorUserCanceled">用户已取消</string>
    <string name="TUIkitErrorRevokeTimeLimitExceed">消息撤回超过了时间限制（默认2分钟）</string>
    <string name="TUIKitErrorLackUGExt">缺少UGC扩展包</string>
    <string name="TUIKitErrorAutoLoginNeedUserSig">自动登录，本地票据过期，需要userSig手动登录</string>
    <string name="TUIKitErrorQALNoShortConneAvailable">没有可用的短连接sso</string>
    <string name="TUIKitErrorReqContentAttach">消息内容安全打击</string>
    <string name="TUIKitErrorLoginSigExpire">登录返回，票据过期</string>
    <string name="TUIKitErrorSDKHadInit">SDK 已经初始化无需重复初始化</string>
    <string name="TUIKitErrorOpenBDHBase">openbdh 错误码</string>
    <string name="TUIKitErrorRequestNoNetOnReq">请求时没有网络，请等网络恢复后重试</string>
    <string name="TUIKitErrorRequestNoNetOnRsp">响应时没有网络，请等网络恢复后重试</string>
    <string name="TUIKitErrorRequestOnverLoaded">请求队列満</string>
    <string name="TUIKitErrorEnableUserStatusOnConsole">您未开启用户状态功能，请先登录控制台开启</string>


    <string name="messageNotification">消息通知</string>
    <string name="HelpandFeedback">帮助与反馈</string>
    <string name="settings">设置</string>
    <string name="wait_look">再看一会儿</string>
    <string name="exit_look">退出不看了</string>

    <string name="empty_text">暂无消息通知</string>
    <string name="final_test">结业考试</string>
    <string name="teacher">师生互动</string>
    <string name="graduate">毕业表彰</string>
    <string name="class_text">课程表</string>

    <string name="sys_tips_text">系统设置</string>
    <string name="sys_tips_commit">感谢您的提交~</string>


    <string name="sys_app_version_update">AI课版本更新</string>
    <string name="sys_app_update">更新</string>
    <string name="sys_app_version">当前已经是最新版本了</string>
    <string name="sys_app_new_version">发现新版本</string>
    <string name="sys_net_x_no">有线网络未连接</string>
    <string name="sys_net_no">当前无网络，请检查当前网络情况是否正常</string>

    <string name="sys_hdmi_auto">自动</string>
    <string name="sys_hdmi_crurren">当前：%s</string>
    <string name="sys_connect">已连接</string>
    <string name="sys_unconnect">未连接</string>
    <string name="sys_net_w">无线网络</string>
    <string name="sys_net_w_sub">设置当前无线网络</string>
    <string name="sys_net_x">有线网络</string>
    <string name="sys_net_x_sub">设置当前有线网络</string>
    <string name="sys_screen">输出分辨率</string>
    <string name="sys_screen_sub">设置屏幕分辨率</string>
    <string name="sys_hdmi">画面调整</string>
    <string name="sys_hdmi_sub">设置图像显示尺寸</string>
    <string name="sys_net_check">网速检测</string>
    <string name="sys_net_check_sub">检测当前网情况</string>
    <string name="sys_bluetooth">蓝牙设置</string>
    <string name="sys_bluetooth_sub">连接蓝牙设备</string>
    <string name="sys_info">系统信息</string>
    <string name="sys_info_sub">查看设备号等信息</string>
    <string name="sys_phone_remote">手机遥控器</string>
    <string name="sys_phone_remote_sub">可以用手机充当遥控器来控制</string>
    <string name="sys_bug_report">故障上报</string>
    <string name="sys_bug_report_sub">把问题上报给我们</string>


    <string name="sys_screen_sub_tips">使用遥控器上/下方向键放大/缩小屏幕</string>
    <string name="base_back_text">按【返回键】关闭</string>
    <string name="net_check_tips">网速检测中，大概需要30至60秒，请稍后...</string>
    <string name="net_check_upload">上传速度：%s Mbps</string>
    <string name="net_check_download">下载速度：%s Mbps</string>
    <string name="net_check_speed_128">您的网速相当于1兆宽带，超越了全国0.1%的用户</string>
    <string name="net_check_speed_128_text">极差</string>
    <string name="net_check_speed_512">您的网速相当于5兆～20兆宽带，超越了全国10%的用户</string>
    <string name="net_check_speed_512_text">差</string>
    <string name="net_check_speed_6400">您的网速相当于20兆~50兆宽带，超越了全国50%的用户</string>
    <string name="net_check_speed_6400_text">良</string>
    <string name="net_check_speed">您的网速相当于1兆宽带，超越了全国0.1%的用户</string>
    <string name="net_check_speed_text">优</string>

    <string name="remote_tips">手机遥控器连接方式</string>
    <string name="remote_tips_1">使用熊猫系统APP扫一扫\n连接更快一步</string>
    <string name="remote_tips_2">步骤1：打开熊猫系统App的首页\n步骤2：选择&quot;做培训-AI课&quot;\n步骤3：选择右上角的&quot;手机遥控器&quot;\n提示：1.请确保手机熊猫系统App版本号是6.5.3以上\n            2.手机遥控器暂时不支持苹果手机连接</string>

    <string name="sys_info_factory_default">恢复出厂设置</string>
    <string name="sys_info_factory_default_tips">该操作将删除本机所有数据，并将所有设置还原至出厂状态</string>
    <string name="sys_info_factory_default_ok">确认还原</string>

    <string name="sys_info_internal_storage">内部存储</string>
    <string name="sys_info_device_id">设备ID</string>
    <string name="sys_info_device_mac">MAC号</string>
    <string name="sys_info_version">系统版本</string>
    <string name="sys_info_g_version">固件版本</string>
    <string name="sys_info_version_update">系统升级</string>


    <string name="settings_current">AI课当前版本：V%s %s %s</string>

    <string name="rank_day">当天排行榜</string>
    <string name="rank_month">当月排行榜</string>
    <string name="rank_all">全国排行榜</string>
    <string name="rank_empty">还没有人提交结业考试成绩\n请扫码考试吧</string>

    <string name="graduate_title">毕业表彰</string>
    <string name="graduate_store_title">门店毕业表彰</string>
    <string name="graduate_studen_title">学员毕业表彰</string>


    <string name="main_title">打开熊猫系统APP扫码登录</string>
    <string name="main_title_tips">1.打开熊猫系统的首页\n2.点击右上角“+”下的扫一扫登录\n3.按OK键 刷新二维码</string>




</resources>