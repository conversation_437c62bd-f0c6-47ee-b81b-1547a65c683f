<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="font_size_5">5sp</dimen>
    <dimen name="font_size_6">6sp</dimen>
    <dimen name="font_size_7">7sp</dimen>
    <dimen name="font_size_8">8sp</dimen>
    <dimen name="font_size_9">9sp</dimen>
    <dimen name="font_size_10">10sp</dimen>
    <dimen name="font_size_11">11sp</dimen>
    <dimen name="font_size_12">12sp</dimen>
    <dimen name="font_size_13">13sp</dimen>
    <dimen name="font_size_14">14sp</dimen>
    <dimen name="font_size_15">15sp</dimen>
    <dimen name="font_size_16">16sp</dimen>
    <dimen name="font_size_17">17sp</dimen>
    <dimen name="font_size_18">18sp</dimen>
    <dimen name="font_size_19">19sp</dimen>
    <dimen name="font_size_20">20sp</dimen>
    <dimen name="font_size_21">21sp</dimen>
    <dimen name="font_size_22">22sp</dimen>
    <dimen name="font_size_23">23sp</dimen>
    <dimen name="font_size_24">24sp</dimen>
    <dimen name="font_size_25">25sp</dimen>
    <dimen name="font_size_26">26sp</dimen>
    <dimen name="font_size_28">28sp</dimen>
    <dimen name="font_size_30">30sp</dimen>
    <dimen name="font_size_32">32sp</dimen>
    <dimen name="font_size_34">34sp</dimen>
    <dimen name="font_size_36">36sp</dimen>
    <dimen name="font_size_38">38sp</dimen>
    <dimen name="font_size_40">40sp</dimen>
    <dimen name="font_size_42">42sp</dimen>
    <dimen name="font_size_44">44sp</dimen>
    <dimen name="font_size_46">46sp</dimen>
    <dimen name="font_size_48">48sp</dimen>
    <dimen name="font_size_50">50sp</dimen>
    <dimen name="font_size_54">54sp</dimen>
    <dimen name="font_size_58">58sp</dimen>
    <dimen name="font_size_60">60sp</dimen>
    <dimen name="font_size_64">64sp</dimen>
    <dimen name="font_size_68">68sp</dimen>
    <dimen name="font_size_70">70sp</dimen>
    <dimen name="font_size_100">100sp</dimen>
    <dimen name="duigou_margin_right">20dp</dimen>

    <dimen name="height">14dp</dimen>
    <dimen name="Offset">-7dp</dimen>
    <dimen name="Top">15dp</dimen>
    <dimen name="Width">8dp</dimen>
    <dimen name="angle">5dp</dimen>

    <dimen name="notice_content_marginleft_bashi">80dp</dimen>
    <dimen name="notice_content_margintop_ten">10dp</dimen>
    <dimen name="notice_content_marginbottom_shier">12dp</dimen>
    <dimen name="notice_content_marginright_ershi">20dp</dimen>
    <dimen name="notice_two_text_dpace_six">6dp</dimen>
    <dimen name="notice_time_marginbottom_ten">10dp</dimen>
    <dimen name="block_gray_width_ba">10dp</dimen>
    <dimen name="new_custom_line_marginbottom_shisan">13dp</dimen>
    <dimen name="home_icon_margintop_ershiwu">25dp</dimen>
    <dimen name="home_content_text_margintop_shiliu">10dp</dimen>
    <dimen name="block_gray_width_nine">9dp</dimen>
    <dimen name="home_content_text_margintop_shiba">18dp</dimen>
    <dimen name="font_size_middle_shisi">14dp</dimen>
    <dimen name="font_size_middle_shiliu">16dp</dimen>


    <dimen name="ucrop_default_crop_grid_stoke_width">1dp</dimen>
    <dimen name="ucrop_default_crop_frame_stoke_width">1dp</dimen>
    <dimen name="ucrop_default_crop_rect_corner_touch_threshold">30dp</dimen>
    <dimen name="ucrop_default_crop_rect_min_size">100dp</dimen>
    <dimen name="ucrop_default_crop_rect_corner_touch_area_line_length">10dp</dimen>

    <dimen name="header_hight">250dp</dimen>
    <dimen name="edit_text_hight">180dp</dimen>
    <dimen name="save_album_bottom">30dp</dimen>


    <dimen name="ucrop_padding_crop_frame">16dp</dimen>
    <dimen name="ucrop_size_dot_scale_text_view">8dp</dimen>
    <dimen name="ucrop_height_horizontal_wheel_progress_line">20dp</dimen>
    <dimen name="ucrop_width_horizontal_wheel_progress_line">2dp</dimen>
    <dimen name="ucrop_margin_horizontal_wheel_progress_line">10dp</dimen>
    <dimen name="ucrop_height_wrapper_controls">64dp</dimen>
    <dimen name="ucrop_height_wrapper_states">72dp</dimen>
    <dimen name="ucrop_height_divider_shadow">3dp</dimen>
    <dimen name="ucrop_text_size_widget_text">13dp</dimen>
    <dimen name="ucrop_margit_top_widget_text">10dp</dimen>
    <dimen name="ucrop_size_wrapper_rotate_button">50dp</dimen>
    <dimen name="ucrop_height_crop_adpect_ratio_text">40dp</dimen>
    <dimen name="ucrop_progress_size">30dp</dimen>
    <dimen name="left_safe_distence">12dp</dimen>
    <dimen name="block_gray_distence">12dp</dimen>
    <dimen name="pic_size">44dp</dimen>

    <dimen name="room_status_cell_length">70dp</dimen>
    <dimen name="room_status_cell_height">56dp</dimen>

    <dimen name="title">18sp</dimen>
    <dimen name="sub_content">14sp</dimen>

    <!-- Default screen margins, per the Android Design guidelines. -->


    <!--大小-->
    <!--h1-->
    <!--h2-->
    <!--h3-->
    <!--h4-->
    <!--h5-->
    <dimen name="h5">12sp</dimen>
    <!--h6-->
    <dimen name="h6">14sp</dimen>
    <!--h7-->
    <dimen name="h7">15sp</dimen>
    <!--h8-->
    <dimen name="h8">16sp</dimen>
    <!--h9-->
    <dimen name="h9">17sp</dimen>
    <!--h10-->
    <dimen name="h10">18sp</dimen>
    <!--h11-->

    <dimen name="h12">20sp</dimen>

    <!-- 飘星 -->
    <dimen name="heart_anim_bezier_x_rand">50.0dp</dimen>
    <dimen name="heart_anim_length">100.0dp</dimen>
    <dimen name="heart_anim_length_rand">350.0dp</dimen>


    <dimen name="live_btn_size">36dp</dimen>


    <!-- AVSDK *******************************************************************************************************************************-->

    <!-- 视频聊天中小窗口移动时的阈值 -->

    <!-- AVSDK *******************************************************************************************************************************-->

    <!-- UGC*******************************************************************************************************************************-->
    <!-- UGC*******************************************************************************************************************************-->
    <!--上面的是小直播-->

    <!--所有表单的字号大小-->
    <dimen name="base_form_with_font_size">15sp</dimen>
    <dimen name="base_filter_font_size">16sp</dimen>
    <dimen name="font_size_27">27sp</dimen>

</resources>
