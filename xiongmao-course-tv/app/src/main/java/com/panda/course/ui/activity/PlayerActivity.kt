package com.panda.course.ui.activity

import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.text.TextUtils
import android.text.format.DateFormat
import android.util.ArrayMap
import android.util.Log
import android.view.Gravity
import android.view.KeyEvent
import android.view.View
import android.view.animation.Animation
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.fastjson.JSON
import com.avery.subtitle.widget.SimpleSubtitleView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.gson.Gson
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import com.panda.course.BuildConfig
import com.panda.course.R
import com.panda.course.callback.OnVideoEffectsListener
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.TXCSDKService
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.appContext
import com.panda.course.dao.MyVideoInfo
import com.panda.course.data.*
import com.panda.course.databinding.ActivityPlayerBinding
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadStatusEntity
import com.panda.course.network.NetUrl
import com.panda.course.ui.activity.dialog.RankingDialogActivity
import com.panda.course.ui.activity.dialog.TeacherStudentInteractionActivity
import com.panda.course.ui.adapter.CommonCourseAdapter
import com.panda.course.ui.adapter.CommonKonwledgeAdapter
import com.panda.course.ui.adapter.CommonSpecialFunctionsAdapter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.util.*
import com.panda.course.util.im.TUIUtils
import com.panda.course.widget.popup.VideoEffectsPopupView
import com.tencent.imsdk.v2.*
import com.tencent.liteav.demo.superplayer.SuperPlayerDef
import com.tencent.liteav.demo.superplayer.SuperPlayerModel
import com.tencent.liteav.demo.superplayer.SuperPlayerModel.SuperPlayerURL
import com.tencent.liteav.demo.superplayer.SuperPlayerView
import com.tencent.liteav.demo.superplayer.entity.KnowledgePointsListEntity
import com.tencent.liteav.demo.superplayer.entity.ThumbnailEntity
import com.tencent.liteav.demo.superplayer.model.ISuperPlayerListener
import com.tencent.liteav.demo.superplayer.model.entity.VideoQuality
import com.tencent.qcloud.tuicore.TUILogin
import com.tencent.rtmp.TXLiveConstants
import com.tencent.rtmp.TXVodPlayer
import com.tencent.rtmp.downloader.TXVodDownloadManager
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Semaphore
import rxhttp.wrapper.param.RxHttp
import java.io.File
import java.lang.Runnable
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashMap


/**
 * 1.改成视频列表的方式 初始的写法是只播放1个视频，只需要url 现在需要列表 改动如下
 * 2.在课时详情页面进行存储  标识code
 * 3.在播放页通过code 获取本地的数据，同时去请求新数据并且存储到本地
 * 4.把数据放到adapter 筛选客户点击的是那个，播放具体的
 * 5.把播放的list给到SuperPlayerView
 * 6.自动切到下一节课后，重新拿打点信息
 */
class PlayerActivity : BaseDbActivity<CourseViewModel, ActivityPlayerBinding>(),
    OnVideoEffectsListener {

    private val TAG = "PlayerActivity"

    // 课时相关的info
    private var mCourseInfo: CourseDetailInfo? = null

    //课程主题
    private var mCourseThemeName = ""

    // 打点信息的it
    private var mDotHashMap = ArrayMap<Long, VideoDotEntity>()

    private var mDotList = ArrayMap<Int, String>()

    // 记录播放器初始化的时长
    private var mInitTime: Long = 0

    // 记录播放器的时长
    private var isUploadFirstSuccess = false

    // 更新观看播放进度
    private var mVideoWatchUploadCount: Int = 0

    // 是否按Back键退出
    private var mActionBack = false

    private var mCurrentVideoProgress: Long = -1

    // 记录是否打点信息是空的
    private var mDotHasList = true

    //记录当前是那个动画 .默认签到
    private var type = 1

    //记录某个渠道来的
    private var JumpType = 0

    // IM 重试策略
    private var mRetryCount = 0

    // 分享课程码的连接
    private var mShareCourseUrl: String? = null

    //视频seek
    private var isFirstSeekTo = false

    private val mEventHashMap = HashMap<String, Any>()

    //先默认记录一下扫码答题的图状态
    private var isBannerLoadSuccess = false
    private var isQrCodeLoadSuccess = false

    //记录当前的图片链接
    private var mQrCodeUrl: String? = ""
    private var mBitmapError: String? = ""

    //短暂记录倒计时的情况
    private var mCountDownTimer: Int = 0

    //记录当前动态效的数据
    private var videoDot: VideoDotEntity? = null

    //记录是否匹配到本地视频
    private var findLocalVideo = false

    // 二维码重试次数
    private var mLoadBannerCount = 0

    // 上报当前网速 每90/s 一次
    private var mNetSpeedCount = 0

    //按键处理
    private var shortPress = false

    //启动任务协程
    private var mSpristAsyncTask: Job? = null

    //记录消息
    private var stringBuilder = StringBuilder()

    // 控制器的adpater
    val commonFunctionsAdapter = CommonSpecialFunctionsAdapter()
    val commonDefinitionAdapter = CommonSpecialFunctionsAdapter()
    val commonSpeedAdapter = CommonSpecialFunctionsAdapter()
    val commonSubTitleAdapter = CommonSpecialFunctionsAdapter()
    val commonCourseAdapter = CommonCourseAdapter()
    val commonKonwledgeAdapter = CommonKonwledgeAdapter()
    val pointManger = LinearLayoutManager(this)

    private var mCommonPosition = 0
    private var mCoursePosition = 0
    private var mDefinitionPosition = 0
    private var mSpeedPosition = 0
    private var mSubTitlePosition = 0
    private var mKonwledgePosition = 0

    //记录清晰度、倍速
    private var currentSpeedPosition = 0
    private var currentSubTitlePosition = 0
    private var currentDefinitionPosition = 0
    private var currentKonwledgePosition = 0

    private var mCourseCode: String? = ""
    private var mCourseStatus: String? = ""

    /** 常用功能 **/
    private val focus_common = 1

    /** 课程列表 **/
    private val focus_course = 2

    /** 清晰度 **/
    private val focus_definition = 3

    /** 倍速 **/
    private val focus_speed = 4

    /** 知识点 **/
    private val focus_konw = 5

    /** 字幕 **/
    private val focus_sub_title = 6

    //是否有结业考试
    private var isExam = false

    //记录是否弹过这个isExam了
    private var recordShowExam = false

    /**
     * 记录当前的焦点在哪里
     */
    private var currentFocusType: Int = focus_common

    private var currentPlayerPosition = 0 //记录播放列表中，第几个

    private var mNetWorkSpeedArr = ArrayList<Int>() //存放统计网络速度的内容

    private var mBottomShowType = 0 // 0是网络 1是即将播放

    private var mSubTitleView: SimpleSubtitleView? = null //字幕的View

    private var mVideoEffectsPopupView: VideoEffectsPopupView? = null;//创建动态Pop


    override fun initView(savedInstanceState: Bundle?) {
        if (!NetworkUtil.isAvailable(this)) {
            ToastUtil.show("当前无网络，请检查当前网络情况是否正常")
            finishActivity(this)
            return
        }

        if (intent == null) {
            return
        }

        if (!TXCSDKService.INIT_TX_BASE) {//校验是否初始化过了
            TXCSDKService.init(appContext)
        }

        JumpType = intent.getIntExtra("JumpType", 0)
        mCourseInfo = intent.getParcelableExtra("course_detail")
        mCourseCode = intent.getStringExtra("course_detail_code")
        mCourseStatus = intent.getStringExtra("course_status")
//        isExam = intent.getBooleanExtra("isExam", false)

        intent.getStringExtra("store_logo").notTextNull {
            GlideUtil.loadPicRound(this, it, mDataBind.ivLogo)
        }

        mCourseCode.notTextNull {
            mViewModel.getCourseDetail(true, it, eventViewModel.appUserInfo.value?.uuid, false)
        }


        if (mCourseInfo == null) { // 这个必须在之前，一下数据依靠此对象
            return
        }

        mCourseInfo?.course_cover_url?.let { cover ->
            GlideUtil.loadPic(this@PlayerActivity, cover, mDataBind.ivShareCourse)
        }
        mCourseInfo?.course_code?.let { mViewModel.getShareCourseQRCode(it) } //获取暂停时的广告


        mCourseInfo?.code?.let { mViewModel.getKnowledgePoints(it) }

        initOtherView() //初始化一些其他的View  *** 顺序不能乱哈
        initRecyclerView() //初始化Recycler
        initIMService() //im服务检测
        readLocalDot() //查询本地打点信息
        mViewModel.getVideoAllDot()//获取最新的打点信息，如果请求成功，那么就同步本地打点数据

        "观看历史 -- ${mCourseInfo?.video_watch_time}".logE()
        if (!TextUtils.isEmpty(mCourseInfo?.video_watch_time)) {
            mCourseInfo?.video_watch_time?.toInt()?.let { mDataBind.superVodPlayerView.setVideoWatchTime(it) }
        }

    }

    /**
     * 初始化字幕
     */
    private fun initSubTitle(subTitleUrl: String) {
        "播放字幕 = $subTitleUrl".logE()
        if (mSubTitleView == null) {
            mSubTitleView = SimpleSubtitleView(this)
            mDataBind.superVodPlayerView.windowPlayer.flVodSubTitle.addView(mSubTitleView)
        }
        mSubTitleView?.bindToMediaPlayer(mDataBind.superVodPlayerView)
        mSubTitleView?.setTextColor(Color.parseColor("#FFFFFF"))
        mSubTitleView?.setSubtitlePath(subTitleUrl)
        mSubTitleView?.gravity = Gravity.CENTER
        mSubTitleView?.typeface = Typeface.DEFAULT_BOLD
        mSubTitleView?.textSize = 40f
        mSubTitleView?.setShadowLayer(6f, 3f, 3f, Color.BLACK)
        mSubTitleView?.start()
        mSubTitleView?.setOnSubtitleChangeListener {
            if (TextUtils.isEmpty(it?.content)) {
                mSubTitleView?.text = ""
            } else {
                mSubTitleView?.text = it?.content.toString().replace("<br />", "")
            }
        }
        mSubTitleView?.setOnSubtitlePreparedListener {
            "播放字幕， ${it?.size}".logE()
        }

        mDataBind.superVodPlayerView.windowPlayer.flVodSubTitle.visible()
    }

    /**
     * 更改默认选中下一天的数据
     */
    private fun otherConfig() {
        // 设置常用功能下一天
        if (commonCourseAdapter.data.size > 1) { //说明有多个视频，那么更新常用功能的下一天的数据
            commonFunctionsAdapter.data[0].url =
                commonCourseAdapter.data[mDataBind.superVodPlayerView.playNextVideoIndex()].video_cover_image
            commonFunctionsAdapter.data[0].content =
                commonCourseAdapter.data[mDataBind.superVodPlayerView.playNextVideoIndex()].video_introduction
            commonFunctionsAdapter.notifyItemChanged(0)
        }
    }


    /**
     * 初始化播放器的
     */
    private fun initializeVideo() {
        if (!TextUtils.isEmpty(mCourseInfo?.video_watch_time) && mCourseInfo?.video_watch_time!!.toInt() > 5) {
            playVideo()
        } else {
            playLocalCustomView()
        }
    }

    private fun initOtherView() {
        mDataBind.tvShareTitleTips.text = mCourseInfo?.title_main


        mCourseStatus.notTextNull {
            if ("1" == it) { //1免费  2收费
                mDataBind.tvShareCoursePayTips
            }
        }

        if (!TextUtils.isEmpty(mCourseStatus) && "1" == mCourseStatus) {//处理一下扫码带走是否免费 //1免费  2收费
            mDataBind.tvShareCoursePayTips.text = "微信扫码免费领取该课程，可以随时随地观看"
            mDataBind.tvShareCoursePayTips.text = changeFontColor(
                content = mDataBind.tvShareCoursePayTips.text.toString(),
                startIndex = 2,
                endIndex = 8,
                color = ContextCompat.getColor(this, R.color.green)
            )
        } else {
            mDataBind.tvShareCoursePayTips.text = changeFontColor(
                content = mDataBind.tvShareCoursePayTips.text.toString(),
                startIndex = 2,
                endIndex = 6,
                color = ContextCompat.getColor(this, R.color.green)
            )
        }


        mDataBind.flDebugContent.visibility = if (BuildConfig.DEBUG) View.VISIBLE else View.GONE
    }

    private fun initRecyclerView() {

        // 设置控制器的recycler
        mDataBind.gridCommon.initPlayRecyclerCommon(
            this,
            commonFunctionsAdapter,
            CommonDataUtil().commonFunctions
        )

        mDataBind.gridDefinition.initPlayRecyclerCommon(
            this,
            commonDefinitionAdapter,
            CommonDataUtil().videoDefinitions
        )

        mDataBind.gridSpeed.initPlayRecyclerCommon(
            this,
            commonSpeedAdapter,
            CommonDataUtil().videoSpeed
        )

        mDataBind.gridSubTitle.initPlayRecyclerCommon(
            this,
            commonSubTitleAdapter,
            CommonDataUtil().videoSubTitle
        )

        val manger = LinearLayoutManager(this)
        manger.orientation = LinearLayoutManager.HORIZONTAL
        mDataBind.gridCourse.layoutManager = manger
        mDataBind.gridCourse.adapter = commonCourseAdapter
        commonCourseAdapter.setRecycler(mDataBind.gridCourse)


        pointManger.orientation = LinearLayoutManager.HORIZONTAL
        mDataBind.gridKnowledgePoints.layoutManager = pointManger
        mDataBind.gridKnowledgePoints.adapter = commonKonwledgeAdapter
        commonKonwledgeAdapter.setRecycler(mDataBind.gridKnowledgePoints)

        mDataBind.menuCommonGroup.setTargetView(mDataBind.gridCommon)
        mDataBind.menuCommonGroup.setTargetViewFocusChangeListener {
            mDataBind.menuTvCommonTitle.setTextColor(if (it) getColor(R.color.white) else getColor(R.color.gray_1))
        }
        mDataBind.menuDefinitionGroup.setTargetView(mDataBind.gridDefinition)
        mDataBind.menuDefinitionGroup.setTargetViewFocusChangeListener {
            mDataBind.menuTvDefinitionTitle.setTextColor(
                if (it) getColor(R.color.white) else getColor(
                    R.color.gray_1
                )
            )
        }
        mDataBind.menuSpeedGroup.setTargetView(mDataBind.gridSpeed)
        mDataBind.menuSpeedGroup.setTargetViewFocusChangeListener {
            mDataBind.menuTvSpeedTitle.setTextColor(if (it) getColor(R.color.white) else getColor(R.color.gray_1))
        }
        mDataBind.menuSubTitleGroup.setTargetView(mDataBind.gridSubTitle)
        mDataBind.menuSubTitleGroup.setTargetViewFocusChangeListener {
            mDataBind.tvSubTitle.setTextColor(if (it) getColor(R.color.white) else getColor(R.color.gray_1))
        }
        mDataBind.menuCourseGroup.setTargetView(mDataBind.gridCourse)
        mDataBind.menuCourseGroup.setTargetViewFocusChangeListener {
            mDataBind.tvGridCourseTitle.setTextColor(if (it) getColor(R.color.white) else getColor(R.color.gray_1))
        }
        mDataBind.menuKnowledgeGroup.setTargetView(mDataBind.gridKnowledgePoints)
        mDataBind.menuKnowledgeGroup.setTargetViewFocusChangeListener {
            mDataBind.tvGridKnowledgePoints.setTextColor(
                if (it) getColor(R.color.white) else getColor(
                    R.color.gray_1
                )
            )
        }
    }

    /**
     * 查询本地数据，查询出后标记本地的数据
     */
    private fun readLocalCourseList(key: String) {
        val jsonData = MMKVHelper.decodeString(key)
        if (jsonData != null) {
            val data = Gson().fromJson(jsonData, CourseDetail::class.java)
            mCourseThemeName = data.title_main.toString()
            if (data != null && data.video_list != null && data.video_list!!.size > 0) {
                for (i in data.video_list!!.indices) {
                    if (mCourseInfo != null) {
                        if (mCourseInfo!!.code == data.video_list!![i].code) { //找出第几个
                            currentPlayerPosition = i
                            commonCourseAdapter.setPlayCode(data.video_list!![i].code)
                            break
                        }
                    }
                }
                commonCourseAdapter.setList(data.video_list)
                // 处理一下特殊情况， 如果只有单个的情况下就隐藏课程列表
                if (commonCourseAdapter.data.size == 1) {
                    mDataBind.gridCourse.gone()
                    mDataBind.tvGridCourseTitle.gone()
                    commonFunctionsAdapter.data.removeAt(0)
                    commonFunctionsAdapter.notifyDataSetChanged()
                }
            }
        }

    }

    /**
     * 查看是否本地有此code的打点,如果没有，就是此视频没有打点信息
     */
    private fun readLocalDot() {
        mDotHashMap.clear()
        mDotList.clear()

        val cacheVideoList = PlayerDotHelper.handlerDot(this, mCourseInfo?.code)
        if (cacheVideoList != null && cacheVideoList.size > 0) {
            spanDebugText("本地找到的打点信息")

            for (j in cacheVideoList.indices) {
                cacheVideoList[j].seconds.notTextNull {
                    mDotHashMap[it.toLong()] = cacheVideoList[j]
                    mDotList[mDataBind.superVodPlayerView.roundToTarget(it.toLong(), 10)] = cacheVideoList[j].effect_type
                    spanDebugText("${cacheVideoList[j].effect_type} , 触发位置是 ${it.toLong()}")
                }
            }
            spanDebugText("打点列表大小=${mDotHashMap.size} ")
            mDotHasList = true

        } else {
            mDotHasList = false
            mViewModel.getVideoDotList(mCourseInfo?.code)
        }
    }


    private fun playLocalCustomView() {
        isFirstSeekTo = true
        mDataBind.ivLogo.visibility = View.GONE
        mDataBind.llCompany.visibility = View.VISIBLE
        if (eventViewModel.appUserInfo.value != null) {
            GlideUtil.loadPicRound(
                this, eventViewModel.appUserInfo.value?.store_log, mDataBind.ivCompanyHead
            )
            mDataBind.tvCompanyName.text = "${eventViewModel.appUserInfo.value?.store_name}"
            mDataBind.llCompany.startAnimation(
                AnimationUtils.getHiddenAlphaAnimation(5000, object : Animation.AnimationListener {

                    override fun onAnimationStart(animation: Animation?) {
                    }

                    override fun onAnimationEnd(animation: Animation?) {
                        playVideo()
                    }


                    override fun onAnimationRepeat(animation: Animation?) {
                    }

                })
            )
        } else {
            playVideo()
        }
    }

    /**
     * 重新开始播放视频
     */
    private fun resetPlayerVideo(isResetPlay: Boolean) {
        if (isResetPlay) {
            MediaHelper.stop()
            MediaHelper.release()
            mDataBind.ivLogo.visibility = View.VISIBLE
            mDataBind.superVodPlayerView.onResume()
        } else {
            mDataBind.ivLogo.visibility = View.GONE
            mDataBind.superVodPlayerView.onPause()
            hideControllerView() //隐藏控制台
        }
    }


    override fun onRequestError(loadStatus: LoadStatusEntity) {
        super.onRequestError(loadStatus)
    }


    /**
     * 根据title 去删除指定的item
     */
    private fun deletedFunctionAdapter(title: String) {
        for (i in commonFunctionsAdapter.data.indices) {
            if (title == commonFunctionsAdapter.data[i].title) {
                commonFunctionsAdapter.removeAt(i)
                break
            }
        }
    }


    /**
     * 查找是否有相关的内容
     */
    private fun findFunctionAdapter(title: String): Boolean {
        for (i in commonFunctionsAdapter.data.indices) {
            if (title == commonFunctionsAdapter.data[i].title) {
                return true
            }
        }
        return false
    }


    override fun onRequestSuccess() {
        mViewModel.apply {

            spritesEntity.observe(this@PlayerActivity, androidx.lifecycle.Observer {
                "雪碧图 总量 ${it.total} ，雪碧图数量 ${it.list.size} , id = ${mCourseInfo?.tencent_video_id}".logE()
                executeAsyncTask(it.list, it.total)
            })

            mKnowledgePointsEntity.observe(this@PlayerActivity, androidx.lifecycle.Observer {
                if (it == null) {
                    return@Observer
                }
                deletedFunctionAdapter("本节知识点")
//                "知识点----> ${it.list.toString()}".logE(TAG)
                commonKonwledgeAdapter.setList(it.list)
                val konwledge = CommonFunctions("本节知识点", "", CommonFunctions.VIEW_BIG_DEF)
                if (it.list.size > 0) {
                    konwledge.content = it.list[0].title
                    "雪碧图 打点信息${mDotHashMap.size}".logE()

                    // 遍历所有键
                    for ((key, value) in mDotHashMap) {
                        "打点信息 --> ${key}".logE()
                        // 处理每个键值对
                        if ("4" != value.effect_type && "14" != value.effect_type) {
                            it.list.add(KnowledgePointsListEntity(1, key.toString(), key.toString(), key.toString(), key.toString()))
                        }
                    }

                    mDataBind.superVodPlayerView.setDrawLines(it.list)
                    mDataBind.tvGridKnowledgePoints.visible()
                    mDataBind.superVodPlayerView.windowPlayer.vodKnowledgeView.visible()
                    if (!commonFunctionsAdapter.data.contains(konwledge)) {
                        commonFunctionsAdapter.addData(1, konwledge)
                    }
                } else {
                    // 遍历所有键
                    for ((key, value) in mDotHashMap) {
                        "打点信息 --> ${key}".logE()
                        // 处理每个键值对
                        if ("4" != value.effect_type && "14" != value.effect_type) {
                            it.list.add(KnowledgePointsListEntity(1, key.toString(), key.toString(), key.toString(), key.toString()))
                        }
                    }
                    mDataBind.superVodPlayerView.setDrawLines(it.list)
                    mDataBind.tvGridKnowledgePoints.gone()
                    mDataBind.superVodPlayerView.windowPlayer.vodKnowledgeView.gone()
                }
                commonFunctionsAdapter.notifyDataSetChanged()
                pointManger.scrollToPosition(0)

            })
            mCourseDetail.observe(this@PlayerActivity, androidx.lifecycle.Observer { // 课程列表
                if (it == null) {
                    return@Observer
                }
                mCourseCode.notTextNull { code ->
                    MMKVHelper.encode(code, JSON.toJSONString(it))
                    readLocalCourseList(code)
                    otherConfig()

                    initializeVideo() // 初始化播放器要放在课程列表回来后
                }

            })

            mIMInfoEntity.observe(this@PlayerActivity, androidx.lifecycle.Observer {
                if (it != null && it.im_user_id != null && it.im_user_sig != null) {
                    mRetryCount++
                    if (mRetryCount >= 3) {
                        return@Observer
                    }
                    TUIUtils.login(it.im_user_id, it.im_user_sig, object : V2TIMCallback {
                        override fun onSuccess() {
                            mRetryCount = 3
                            initIMService()
                        }

                        override fun onError(p0: Int, p1: String?) {
                            "登录失败 code = $p0 , msg = $p1".logE("HttpLog-IM")
//                            spanDebugText("登录失败 code = $p0 , msg = $p1")
                            mViewModel.getIMInfo()
                            val hashMap = HashMap<String, Any>()
                            hashMap["user_id"] = "${it.im_user_id}"
                            hashMap["im_code"] = "$p0"
                            hashMap["im_error"] = "$p1"
                            appUMEventObject(UMConstant.KEY_IM_ERROR, hashMap)
                        }
                    })
                }
            })

            // 进度上报
            seeProgress.observe(this@PlayerActivity, androidx.lifecycle.Observer {
                if (mActionBack && JumpType != 1) {
//                    eventViewModel.progressEvent.value = mCourseInfo?.code?.let {
//                        ProgressNotice(it, "" + mDataBind.superVodPlayerView.vodProgress)
//                    }
                }
            })

            //视频Video
            mVideoDotEntity.observe(
                this@PlayerActivity,
                androidx.lifecycle.Observer { dotEntity: VideoDotList ->
                    if (dotEntity.list != null && dotEntity.list.size > 0) {
                        spanDebugText("从网络获取的打点信息")
                        mDotHasList = true
                        for (i in dotEntity.list.indices) {
                            dotEntity.list[i].seconds.notTextNull {
                                mDotHashMap[it.toLong()] = dotEntity.list[i]
                                mDotList[mDataBind.superVodPlayerView.roundToTarget(it.toLong(), 10)] = dotEntity.list[i].effect_type
                                spanDebugText("${dotEntity.list[i].effect_type} = 触发位置是 ${it.toLong()}")
                            }
                        }
                        spanDebugText("成功放到对象里面大小为= ${mDotHashMap.size}")
                    } else {
                        spanDebugText("此课程没有打点信息")
                        mDotHasList = false
                    }
                })

            // 分享的弹窗
            shareQRCodeEntity.observe(this@PlayerActivity, androidx.lifecycle.Observer {
                mShareCourseUrl = it?.qr_code_content
                loadBanner()
            })

            //打点信息
            cacheVideoDotList.observe(this@PlayerActivity, androidx.lifecycle.Observer {
                if (it == null) {
                    return@Observer
                }
                MMKVHelper.encode(ConstantMMVK.VIDEO_CACHE_DOT, it)//替换本地的,重新读取
                readLocalDot()
            })
            //打开qr
            qrCodeEntity.observe(this@PlayerActivity, androidx.lifecycle.Observer {
                if (it == null) {
                    return@Observer
                }
                if (!TextUtils.isEmpty(it.qr_code_content)) {
                    val bundle = Bundle()
                    bundle.putBoolean("isHideTeacher", false)
                    bundle.putString("course_detail_code", mCourseCode)
                    bundle.putString("qr_code_url", it.qr_code_content)
                    bundle.putString("mCourseName", mCourseDetail.value?.title_main)
                    toStartActivity(RankingDialogActivity::class.java, bundle)
                    finish()
                }
            })
        }
    }


    /**
     * 加载扫码带走的的二维码
     */
    private fun loadBanner() {
        if (TextUtils.isEmpty(mShareCourseUrl)) {
            mDataBind.rlShareCourseLayout.gone()
            return
        }
        mQrCodeUrl = mShareCourseUrl
        if (!isBannerLoadSuccess) {
            mDataBind.ivShareCourseQrcode.setImageBitmap(QRCodeUtil.getInstance().createQRCode(mQrCodeUrl))
            mLoadBannerCount = 0
            isBannerLoadSuccess = true
//            GlideUtil.loadQrPic(
//                this@PlayerActivity,
//                mQrCodeUrl,
//                mDataBind.ivShareCourseQrcode,
//                object : GlideUtil.PictureListener {
//                    override fun success() {
//                        mLoadBannerCount = 0
//                        isBannerLoadSuccess = true
//                    }
//
//                    override fun fail(msg: String) {
//                        mBitmapError = msg
//                        isBannerLoadSuccess = false
//                        ("广告的链接=$mShareCourseUrl \n广告页图片的错误\n$mBitmapError \n isBannerLoadSuccess = $isBannerLoadSuccess").logD(
//                            "Glide"
//                        )
//                        mLoadBannerCount++
//                        "Glide ${Thread.currentThread().name}".logD(TAG)
//                        if (mDataBind.rlShareCourseLayout.visibility == View.VISIBLE) {
//                            "哎呀，网络不好，图片没有加载出来".toast()
//                        }
//                        Handler().post {
//                            if (mLoadBannerCount <= 5) {
//                                if (mDataBind.rlShareCourseLayout.visibility == View.VISIBLE && !<EMAIL>) {
//                                    loadBanner()
//                                }
//                            } else {
//                                mLoadBannerCount = 0
//                            }
//                        }
//                    }
//                })
        }
    }


    override fun initObserver() {

        mDataBind.butRetry.setOnClickListener {
            mDataBind.llError.gone()
            initView(null)
        }

        controllerObserver()

        mViewModel.checkTipsDevice.observe(this, androidx.lifecycle.Observer {
            mDataBind.superVodPlayerView.onPause()
            showMessageDialog(it)
        })

        eventViewModel.appCommonViewState.observe(this, androidx.lifecycle.Observer {
            var mStr = ""
            if (it != null) {
                if (it.channelState == ControllerViewState.VIEW_CONTROLLER_DEFINITION) { //清晰度
                    if (mDefinitionPosition == 0) {
                        mStr = "已成功切换成超清"
                        mDataBind.superVodPlayerView.windowPlayer.updateVodDefinition("超清")
                    } else {
                        mStr = "已成功切换成高清"
                        mDataBind.superVodPlayerView.windowPlayer.updateVodDefinition("高清")
                    }
                    mDataBind.superviewTvTips.text = changeFontColor(
                        content = mStr,
                        startIndex = 6,
                        endIndex = mStr.length,
                        color = ContextCompat.getColor(this, R.color.green)
                    )
                    mDataBind.superviewTvTips.text.logD(TAG)
                    CommonViewUtil().startTipsAnimation(mDataBind.superviewTvTips)
                } else if (it.channelState == ControllerViewState.VIEW_CONTROLLER_SPEED) { //速度
                    if (mDataBind.superVodPlayerView.videoRete == 1.0f) {
                        mDataBind.superviewTvTips.text = changeFontColor(
                            content = "已切换为正常播放倍速",
                            startIndex = 4,
                            endIndex = 10,
                            color = ContextCompat.getColor(this, R.color.green)
                        )
                    } else {
                        mDataBind.superviewTvTips.text = changeFontColor(
                            content = "已切换为${mDataBind.superVodPlayerView.videoRete}倍，若倍速未生效或卡顿，请切换至正常",
                            startIndex = 4,
                            endIndex = if (mDataBind.superVodPlayerView.videoRete == 1.25f) 9 else 8,
                            color = ContextCompat.getColor(this, R.color.green)
                        )
                    }

                    mDataBind.superVodPlayerView.windowPlayer.updateVodSpeed("${mDataBind.superVodPlayerView.videoRete}倍")
                    mDataBind.superviewTvTips.text.logD(TAG)
                    CommonViewUtil().startTipsAnimation(mDataBind.superviewTvTips)
                }
            }
        })

        mDataBind.superVodPlayerView.setSuperPlayerListener(object : ISuperPlayerListener {
            override fun onVodPlayEvent(player: TXVodPlayer?, event: Int, param: Bundle?) {
                if (event == 2101 || event == 2102 || event == 2106 || event == -2303 || event == -2304) {
                    playSuperVideo()
                }
            }

            override fun onVodNetStatus(player: TXVodPlayer?, bundle: Bundle?) {
                bundle?.let {
                    val speed: Int = it.getInt(TXLiveConstants.NET_STATUS_NET_SPEED)
                    if (mDefinitionPosition == 0) {
                        mNetSpeedCount++
                        mNetWorkSpeedArr.add(speed)
                        if (mNetSpeedCount >= 120) { //计算存放的平均值 120 秒区分一次
                            var sum = 0.0
                            for (i in mNetWorkSpeedArr.indices) {
                                sum += mNetWorkSpeedArr[i]
                            } //                            "平均值 = ${sum * 1.0f / mNetWorkSpeedArr.size}".toast()
                            //                            "平均值 = ${sum * 1.0f / mNetWorkSpeedArr.size}".logD("网速监听")
                            if ((sum * 1.0f / mNetWorkSpeedArr.size) <= 500.0 && mDefinitionPosition == 0) { //如果不是480P 那就切换480p
                                if (mDataBind.superVodPlayerView.windowPlayer.isShowing || mDataBind.flController.visibility == View.VISIBLE) {
                                    return@let
                                }
                                mBottomShowType = 0
                                mDataBind.superviewButMoreTips.text = "立即切换"
                                mDataBind.superviewTvMoreTips.text = ""
                                mDataBind.superviewTvMoreTips.text = changeFontColor(
                                    content = "网络状态不佳，建议切换\n清晰度至高清",
                                    startIndex = mDataBind.superviewTvMoreTips.text.length - 6,
                                    endIndex = mDataBind.superviewTvMoreTips.text.length,
                                    color = ContextCompat.getColor(
                                        this@PlayerActivity,
                                        R.color.green
                                    )
                                )
                                mDataBind.superviewLlMoreTipsLayout.startAnimation(
                                    AnimationUtils.getBottomAnimation(
                                        2000,
                                        object : Animation.AnimationListener {

                                            override fun onAnimationStart(animation: Animation?) {
                                                mDataBind.superviewLlMoreTipsLayout.visible()
                                                mDataBind.superviewButMoreTips.visible()
                                            }


                                            override fun onAnimationEnd(animation: Animation?) {
                                                mDataBind.superviewLlMoreTipsLayout.postDelayed({
                                                    if (mDataBind.superviewLlMoreTipsLayout.visibility == View.VISIBLE) {
                                                        mDataBind.superviewLlMoreTipsLayout.startAnimation(
                                                            AnimationUtils.getBottomHideAnimation(
                                                                2000,
                                                                object :
                                                                    Animation.AnimationListener {

                                                                    override fun onAnimationStart(
                                                                        animation: Animation?
                                                                    ) {

                                                                    }


                                                                    override fun onAnimationEnd(
                                                                        animation: Animation?
                                                                    ) {
                                                                        mDataBind.superviewButMoreTips.gone()
                                                                        mDataBind.superviewLlMoreTipsLayout.gone()
                                                                    }


                                                                    override fun onAnimationRepeat(
                                                                        animation: Animation?
                                                                    ) {
                                                                    }
                                                                })
                                                        )
                                                    }
                                                }, 3000)
                                            }


                                            override fun onAnimationRepeat(animation: Animation?) {
                                            }
                                        })
                                )
                            }
                            mNetSpeedCount = 0
                        }
                    }
                }
            }

            override fun onLivePlayEvent(event: Int, param: Bundle?) {
            }

            override fun onLiveNetStatus(status: Bundle?) {
            }

        })
        mDataBind.superVodPlayerView.setPlayerViewCallback(object :
            SuperPlayerView.OnSuperPlayerViewCallback {
            /**
             * 开始全屏播放
             */
            override fun onStartFullScreenPlay() {

            }

            /**
             * 结束全屏播放
             */
            override fun onStopFullScreenPlay() {
            }

            /**
             * 点击悬浮窗模式下的x按钮
             */
            override fun onClickFloatCloseBtn() {
            }

            /**
             * 点击小播放模式的返回按钮
             */
            override fun onClickSmallReturnBtn() {
            }

            /**
             * 开始悬浮窗播放
             */
            override fun onStartFloatWindowPlay() {
            }

            /**
             * 开始播放回调
             */
            override fun onPlaying() {
                if (!isUploadFirstSuccess) {
                    "${mInitTime}".logD("上报视频初始化")
                    "${System.currentTimeMillis()}".logD("上报视频初始化")
                    "${System.currentTimeMillis() - mInitTime}".logD("上报视频初始化")
                    SuperPlayStatistics(System.currentTimeMillis() - mInitTime)
                }
                mDataBind.rlShareCourseLayout.removeCallbacks(mHideBannerRunnable)
            }

            /**
             * 播放暂停
             */
            override fun onPause() {
                mVideoWatchUploadCount = 0 //防止多统计，快进快退的时候归0
                if (!TextUtils.isEmpty(mShareCourseUrl)) {
                    mDataBind.rlShareCourseLayout.removeCallbacks(mHideBannerRunnable)
                    mDataBind.rlShareCourseLayout.post(mHideBannerRunnable)
                } else {
                    mCourseInfo?.course_code?.let { mViewModel.getShareCourseQRCode(it) }
                }
            }

            /**
             * 播放结束，如果有结业考试，并且是最后一天，那么就弹出结业考试的界面
             */
            override fun onPlayEnd() {
                mDataBind.superviewTvTips.text = ""
                mDataBind.superviewTvTips.gone()
            }

            /**
             * 播放下一节 , 清空CourseInfo、打点信息
             */
            override fun playNext() {
                if (commonCourseAdapter.data.size > 0) {
                    if (isExam && recordShowExam) {//如果是最后一个，并且有结业考试，那么结束的时候，就跳结业考试
                        mDataBind.superVodPlayerView.onPause()
                        mCourseCode?.let { mViewModel.getClassQrCode(it, ConstantMMVK.POPUP_EXAMINATION) }
                        return
                    }
                    GlideCacheUtil.getInstance().clearImageMemoryCache(this@PlayerActivity)
                    mDataBind.flController.gone()
                    mDataBind.rlShareCourseLayout.gone()
                    mDataBind.superviewLlMoreTipsLayout.gone()
                    mDataBind.superviewTvTips.gone()
                    mDataBind.superVodPlayerView.setTotal(0)
                    mDataBind.superVodPlayerView.windowPlayer.hide()
                    mDotHashMap.clear()
                    mDotList.clear()
                    mCountDownTimer = 0
                    mCourseInfo = null
                    mDefinitionPosition = 0
                    spanDebugText(clean = true)
                    hideControllerView()
                    cleanControllerMenuReset()
                    mCourseInfo = commonCourseAdapter.data[mDataBind.superVodPlayerView.playIndex]

                    mCourseInfo?.tencent_video_id?.let { mViewModel.getImageSprites(it) }

                    if (!TextUtils.isEmpty(mCourseInfo?.hd_video_subtitle_url)) {
                        mDataBind.superVodPlayerView.windowPlayer.vodSubTitleView.visible()
                        mDataBind.gridSubTitle.visible()
                        mDataBind.tvSubTitle.visible()
                        mCourseInfo?.hd_video_subtitle_url?.let { initSubTitle(it) }
                    } else {
                        mDataBind.superVodPlayerView.windowPlayer.flVodSubTitle.gone()
                        deletedFunctionAdapter("字幕")
                        mDataBind.gridSubTitle.gone()
                        mDataBind.tvSubTitle.gone()
                        mDataBind.superVodPlayerView.windowPlayer.vodSubTitleView.gone()
                    }

                    mDataBind.superVodPlayerView.updateTitle(mCourseInfo?.title_main)
                    commonCourseAdapter.setPlayCode(mCourseInfo?.code)
                    commonCourseAdapter.notifyDataSetChanged()
                    spanDebugText("播放下一集拉：${mDataBind.superVodPlayerView.currentSuperPlayerModel.url}")
                    mCourseInfo?.code?.notTextNull { mViewModel.getKnowledgePoints(it) }
                    readLocalDot()
                }

            }

            /**
             * 当播放失败的时候回调
             *
             * @param code
             */
            override fun onError(code: Int) {
                mDataBind.llError.visible()
                mDataBind.butRetry.requestFocus(500)
            }

            /**
             * 更新进度回调
             *
             * @param current
             * @param duration
             */
            override fun updateVideoProgress(current: Long, duration: Long) {
                if (current == duration) { //当前的时间点与总时间点一致，说明播放完成了，没必须继续了
                    return
                }

                if (mCurrentVideoProgress == current) {
                    if (mDataBind.superVodPlayerView.playerState == SuperPlayerDef.PlayerState.PLAYING && mDataBind.rlShareCourseLayout.visibility == View.VISIBLE) {
                        mDataBind.rlShareCourseLayout.visibility = View.GONE
                        uploadADEvent(UMConstant.KEY_QR_CODE, "Playing", "消失")
                    }
                    return
                } else {
                    mDataBind.superVodPlayerView.windowPlayer.updatePlayState(SuperPlayerDef.PlayerState.PLAYING)
                    mCurrentVideoProgress = current
                }

                // 处理如果当前播放小于5秒就提示即将进入下一节
                if (mCurrentVideoProgress >= (duration - 15) && isExam && mDataBind.superVodPlayerView.playIndex == mDataBind.superVodPlayerView.superPlayerModelList.size - 1) {
                    if (mDataBind.superviewLlMoreTipsLayout.visibility == View.GONE && !mDataBind.superVodPlayerView.windowPlayer.isShowing) {
                        recordShowExam = true
                        mBottomShowType = 1
                        mDataBind.superviewLlMoreTipsLayout.visible()
                        mDataBind.superviewButMoreTips.gone()
                        mDataBind.superviewTvMoreTips.text = "本期课程即将结束，将为您跳转至结业考试"
                        mViewModel.uploadVideoProgress(mCourseInfo?.code, 0)
                        eventViewModel.progressEvent.value = mCourseInfo?.code?.let {
                            ProgressNotice(it, "0")
                        }
                    }
                } else {
                    if (mCurrentVideoProgress >= (duration - 10)) {
                        if (commonCourseAdapter.data.size > 0) { //如果只有一节课，那么不提示
                            if (mDataBind.superviewLlMoreTipsLayout.visibility == View.GONE && !mDataBind.superVodPlayerView.windowPlayer.isShowing) {
                                mBottomShowType = 1
                                mDataBind.superviewLlMoreTipsLayout.visible()
                                mDataBind.superviewButMoreTips.visible()
                                mDataBind.superviewButMoreTips.text = "立即播放"
                                mDataBind.superviewTvMoreTips.text =
                                    "即将播放：$mCourseThemeName - ${commonCourseAdapter.data[mDataBind.superVodPlayerView.playNextVideoIndex()].title_main}"
                                mViewModel.uploadVideoProgress(mCourseInfo?.code, 0)
                                eventViewModel.progressEvent.value = mCourseInfo?.code?.let {
                                    ProgressNotice(it, "0")
                                }
                            }
                        }
                    } else {
//                    eventViewModel.progressEvent.value = mCourseInfo?.code?.let {
//                        ProgressNotice(it, "" + mDataBind.superVodPlayerView.vodProgress)
//                    }
                    }
                }

                mRestReminder += 1//判断是否达到6个小时，如果到达了，那么暂停视频，并提示

                if (mRestReminder >= 216000) {
                    "今天已经看了6小时的课程了，休息一下吧".toast()
                    mDataBind.superVodPlayerView.onPause()
                    mRestReminder = 0
                    return
                }

                updateVideoProgress(current)
            }

            /**
             * 下载页面，点击了缓存列表按钮
             */
            override fun onShowCacheListClick() {
            }

            /**
             * 切换清晰度
             */
            override fun onChangeVideoQuality() {

            }
        })
    }

    /**
     * 清除常用功能控制器的焦点
     */
    private fun cleanControllerMenuReset() {

        currentDefinitionPosition = 0
        currentKonwledgePosition = 0
        currentPlayerPosition = 0
        currentSpeedPosition = 0
        currentSubTitlePosition = 0

        mCommonPosition = 0
        mCoursePosition = 0
        mDefinitionPosition = 0
        mSpeedPosition = 0
        mSubTitlePosition = 0
        mKonwledgePosition = 0

        CommonViewUtil().changeAdapterSelected(
            commonFunctionsAdapter,
            mDataBind.flController,
            commonSpeedAdapter,
            ControllerViewState.VIEW_CONTROLLER_SPEED,
            currentSpeedPosition
        )

        CommonViewUtil().changeAdapterSelected(
            commonFunctionsAdapter,
            mDataBind.flController,
            commonDefinitionAdapter,
            ControllerViewState.VIEW_CONTROLLER_DEFINITION,
            currentDefinitionPosition
        )

        CommonViewUtil().changeAdapterSelected(
            commonFunctionsAdapter,
            mDataBind.flController,
            commonSubTitleAdapter,
            ControllerViewState.VIEW_CONTROLLER_SUBTITLE,
            currentSubTitlePosition
        )

        mDataBind.superVodPlayerView.setVideoRete(0)//恢复正常倍速

        if (mDataBind.superVodPlayerView.currentSuperPlayerModel.videoQualityList.size > 0) { //恢复超清
            if (!TextUtils.isEmpty(mDataBind.superVodPlayerView.currentSuperPlayerModel.videoQualityList[0].url)) {
                mDataBind.superVodPlayerView.changeVideoQuality(mDataBind.superVodPlayerView.currentSuperPlayerModel.videoQualityList[0])
            }
        }
    }

    /**
     * 上传离线视频的
     */
    private fun uploadOffLineVideoProgress() {
        "我来上报了".logE("本地视频")
        val dateFormat: CharSequence = DateFormat.format("yyyy-MM-dd", System.currentTimeMillis())
        val info = MyVideoInfo()
        info.code = mCourseInfo?.code
        info.watch_time_long = 5
        info.watch_time = mDataBind.superVodPlayerView.vodProgress
        info.watch_date = dateFormat.toString()
        info.ts = System.currentTimeMillis().toString()
        info.version = getAppVersion(appContext)
        MyVideoInfoDaoUtil.insertDao(info)
    }


    /**
     * 隐藏控制器，降低圈复杂度，单独提取成一个方法
     */
    private fun hideControllerView() {
        CommonViewUtil().hideControllerView(mDataBind.flController)
    }


    /**
     * 控制器滑动到最底部
     */
    private fun controllerScrollViewUp() {
        mDataBind.scrollViewController.post {
            mDataBind.scrollViewController.smoothScrollBy(0, 0)
            mDataBind.scrollViewController.scrollTo(0, 0)
        }
    }


    /**
     * 视频常用功能事件监听
     */
    private fun controllerObserver() {

        // 常用功能 - 课程分类
        commonCourseAdapter.setOnItemClickListener { _, _, position ->
            appUMEvent(UMConstant.PLAY_CLICK_SWITCH_CURRICULUM)
            hideControllerView()
            mDataBind.flController.gone()
            commonCourseAdapter.setPlayCode(commonCourseAdapter.data[position]?.code)
            commonCourseAdapter.notifyDataSetChanged()
            mDataBind.superVodPlayerView.playModelInList(position)
            otherConfig()
        }

        // 常用功能
        commonFunctionsAdapter.setOnItemClickListener { _, _, position ->
            when (commonFunctionsAdapter.data[position].title) {
                "下一个" -> {
                    appUMEvent(UMConstant.PLAY_CLICK_NEXT)
                    mDataBind.superVodPlayerView.playNextVideo()
                    otherConfig()
                    hideControllerView()
                }

                "本节知识点" -> {
                    "点击 --> $currentKonwledgePosition".logE("知识点测试")
                    appUMEvent(UMConstant.PLAY_CLICK_KNOWLEDGE)
                    dynamicChangeScrollView()

                    if (currentKonwledgePosition < 0) {
                        currentKonwledgePosition = 0
                    }

                    pointManger.scrollToPosition(currentKonwledgePosition)

                    mDataBind.menuKnowledgeGroup.postDelayed({
                        //需要隐藏别的View
                        CommonViewUtil().requestFocusDelay(
                            commonKonwledgeAdapter,
                            currentKonwledgePosition
                        )
                    }, 10)

                }

                "清晰度" -> {
                    appUMEvent(UMConstant.PLAY_CLICK_DEFINITION)
                    dynamicChangeScrollView()
                    CommonViewUtil().requestFocusDelay(
                        commonDefinitionAdapter,
                        currentDefinitionPosition
                    )
                    mDataBind.menuCommonGroup.hideTargetView()
                    mDataBind.menuCourseGroup.hideTargetView()
                    mDataBind.menuKnowledgeGroup.hideTargetView()
                    mDataBind.menuSpeedGroup.visibleTargetView()
                    if (findFunctionAdapter("字幕")) {
                        mDataBind.menuSubTitleGroup.visibleTargetView()
                    }
                }

                "倍速" -> {
                    appUMEvent(UMConstant.PLAY_CLICK_SPEED)
                    dynamicChangeScrollView()
                    CommonViewUtil().requestFocusDelay(commonSpeedAdapter, currentSpeedPosition)
                    mDataBind.menuCommonGroup.hideTargetView()
                    mDataBind.menuCourseGroup.hideTargetView()
                    mDataBind.menuKnowledgeGroup.hideTargetView()
                    mDataBind.menuDefinitionGroup.hideTargetView()
                    if (findFunctionAdapter("字幕")) {
                        mDataBind.menuSubTitleGroup.visibleTargetView()
                    }
                }

                "字幕" -> {
                    appUMEvent(UMConstant.PLAY_CLICK_SUBTITLE)
                    dynamicChangeScrollView()
                    CommonViewUtil().requestFocusDelay(
                        commonSubTitleAdapter,
                        currentSubTitlePosition
                    )
                    mDataBind.menuCommonGroup.hideTargetView()
                    mDataBind.menuCourseGroup.hideTargetView()
                    mDataBind.menuSpeedGroup.hideTargetView()
                    mDataBind.menuKnowledgeGroup.hideTargetView()
                    mDataBind.menuDefinitionGroup.hideTargetView()
                }
            }

        }

        // 常用功能 - 本节知识点
        commonKonwledgeAdapter.setOnItemClickListener { adapter, view, position ->
            appUMEvent(UMConstant.PLAY_CLICK_SWITCH_KNOWLEDGE)
            currentKonwledgePosition = position
            //更新常用功能的主入口的数据
            if (commonFunctionsAdapter.data.size >= 4) {
                commonFunctionsAdapter.data[1].content = commonKonwledgeAdapter.data[position].title
            } else {
                commonFunctionsAdapter.data[0].content = commonKonwledgeAdapter.data[position].title
            }
            commonFunctionsAdapter.notifyDataSetChanged()
            commonKonwledgeAdapter.data[position].millisecond.notTextNull {
                mDataBind.superVodPlayerView.seekToMillisecond(
                    (it.toFloat() / 1000)
                )
            }
            when (mDataBind.superVodPlayerView.playerState) {
                SuperPlayerDef.PlayerState.PAUSE -> {
                    mDataBind.superVodPlayerView.onResume()
                }

                else -> {

                }
            }
            hideControllerView()
        }


        // 常用功能 - 字幕
        commonSubTitleAdapter.setOnItemClickListener { _, _, position -> // 字幕
            appUMEvent(UMConstant.PLAY_CLICK_SWITCH_SUBTITLE)
            if (currentSubTitlePosition == position) {
                hideControllerView()
                eventViewModel.appCommonViewState.postValue(
                    ControllerViewState(
                        ControllerViewState.VIEW_CONTROLLER_SUBTITLE,
                        true
                    )
                )
                return@setOnItemClickListener
            }
            currentSubTitlePosition = position

            if (0 == position) {
                mDataBind.superVodPlayerView.windowPlayer.flVodSubTitle.visible()
//                mDataBind.superVodPlayerView.setMirror(true)
            } else {
                mDataBind.superVodPlayerView.windowPlayer.flVodSubTitle.gone()
//                mDataBind.superVodPlayerView.setMirror(false)
            }
            CommonViewUtil().changeAdapterSelected(
                commonFunctionsAdapter,
                mDataBind.flController,
                commonSubTitleAdapter,
                ControllerViewState.VIEW_CONTROLLER_SUBTITLE,
                position
            )
            onResume()
        }

        // 常用功能 - 倍速
        commonSpeedAdapter.setOnItemClickListener { _, _, position -> // 倍速
            appUMEvent(UMConstant.PLAY_CLICK_SWITCH_SPEED)
            if (currentSpeedPosition == position) {
                hideControllerView()
                eventViewModel.appCommonViewState.postValue(
                    ControllerViewState(
                        ControllerViewState.VIEW_CONTROLLER_SPEED,
                        true
                    )
                )
                return@setOnItemClickListener
            }
            currentSpeedPosition = position
            mDataBind.superVodPlayerView.setVideoRete(position) //调整倍速
            CommonViewUtil().changeAdapterSelected(
                commonFunctionsAdapter,
                mDataBind.flController,
                commonSpeedAdapter,
                ControllerViewState.VIEW_CONTROLLER_SPEED,
                position
            )
            onResume()
        }

        // 常用功能 - 清晰度
        commonDefinitionAdapter.setOnItemClickListener { _, _, position -> // 清晰度
            appUMEvent(UMConstant.PLAY_CLICK_SWITCH_DEFINITION)
            if (currentDefinitionPosition == position) {
                hideControllerView()
                eventViewModel.appCommonViewState.postValue(
                    ControllerViewState(ControllerViewState.VIEW_CONTROLLER_DEFINITION, true)
                )
                return@setOnItemClickListener
            }

            currentDefinitionPosition = position

            if (mDataBind.superVodPlayerView.currentSuperPlayerModel.videoQualityList.size > 0) {
                if (!TextUtils.isEmpty(mDataBind.superVodPlayerView.currentSuperPlayerModel.videoQualityList[position].url)) {
                    mDataBind.superVodPlayerView.changeVideoQuality(mDataBind.superVodPlayerView.currentSuperPlayerModel.videoQualityList[position])
                }
            }

            CommonViewUtil().changeAdapterSelected(
                commonFunctionsAdapter,
                mDataBind.flController,
                commonDefinitionAdapter,
                ControllerViewState.VIEW_CONTROLLER_DEFINITION,
                position
            )
            onResume()
        }

        commonFunctionsAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mCommonPosition = position
                currentFocusType = focus_common
                mDataBind.scrollViewController.scrollTo(0, 0)
                val layoutParams = mDataBind.scrollViewController.layoutParams
                layoutParams.height = dp2px(400f)
                mDataBind.scrollViewController.layoutParams = layoutParams
                defSelectVisibleView()
            }
        }

        commonCourseAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mCoursePosition = position
                currentFocusType = focus_course
                dynamicChangeScrollView()
            }
        }

        commonKonwledgeAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mKonwledgePosition = position
                currentFocusType = focus_konw
                mDataBind.menuCommonGroup.hideTargetView()
                dynamicChangeScrollView()
            }
        }

        commonDefinitionAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mDefinitionPosition = position
                currentFocusType = focus_definition
                dynamicChangeScrollView()
                if (findFunctionAdapter("清晰度")) {
                    mDataBind.menuSpeedGroup.visibleTargetView()
                }
            }
        }

        commonSpeedAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mSpeedPosition = position
                currentFocusType = focus_speed
                dynamicChangeScrollView()
                if (findFunctionAdapter("字幕")) {
                    mDataBind.menuSubTitleGroup.visibleTargetView()
                }
            }
        }

        commonSubTitleAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mSubTitlePosition = position
                currentFocusType = focus_sub_title
                dynamicChangeScrollView()
            }
        }

        mDataBind.scrollViewController.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            "scrollY = $scrollY ; oldScrollY = $oldScrollY".logE()
        }
    }


    /*动态改变*/
    private fun dynamicChangeScrollView() {
        val layoutParams = mDataBind.scrollViewController.layoutParams
        layoutParams.height = dp2px(500f)
        mDataBind.scrollViewController.layoutParams = layoutParams
    }

    /*默认选中显示的View*/
    private fun defSelectVisibleView() {
        mDataBind.menuCommonGroup.visibleTargetView()
        mDataBind.menuSpeedGroup.visibleTargetView()
        mDataBind.menuDefinitionGroup.visibleTargetView()

        if (findFunctionAdapter("下一个")) {
            mDataBind.menuCourseGroup.visibleTargetView()
        }
        if (findFunctionAdapter("本节知识点")) {
            mDataBind.menuKnowledgeGroup.visibleTargetView()
        }
        if (findFunctionAdapter("字幕")) {
            mDataBind.menuSubTitleGroup.visibleTargetView()
        }
    }

    /**
     * 延迟执行广告
     */
    private var mHideBannerRunnable = Runnable {
        if (mDataBind.superVodPlayerView.playerState == SuperPlayerDef.PlayerState.PAUSE) {
            if (!shortPress) {
                if (mDataBind.superVodPlayerView.windowPlayer.thumbnailRecyclerView.visibility == View.GONE) {//雪碧图不显示的情况下，再去弄这个
                    mDataBind.rlShareCourseLayout.visible()
                } else {
                    mDataBind.rlShareCourseLayout.gone()
                }
            } else {
                mDataBind.rlShareCourseLayout.visible()
            }
        }
    }


    /**
     * 进度
     */
    private fun updateVideoProgress(current: Long) {
        "$current".logE("我是播放器的进度")
        if (current == 0L) { //处理只有0这样的情况
            mViewModel.uploadVideoProgress(mCourseInfo?.code, 1)
        }
        if (mDataBind.superVodPlayerView.playerState == SuperPlayerDef.PlayerState.PAUSE) { // 快速点击的时候，当前视频会暂停，暂停的时候，不去触发打点信息
            mVideoWatchUploadCount = 0
            return
        }

        if (isFirstSeekTo) {
            mVideoWatchUploadCount += 1
            if (mVideoWatchUploadCount >= 6) {

                if (NetworkUtil.isAvailable(this@PlayerActivity)) {//判断是否有网络，如果有网络，就正常上报，没有网络就走库中统计
                    uploadVideoWatch() //命中触发接口
                } else {
                    if (findLocalVideo) {//如果是离线视频，就去上报，否则不去存储
                        uploadOffLineVideoProgress()
                    }
                }
                mVideoWatchUploadCount = 0 // 归零
            }

            if (!mDotHasList) {
                return
            }

            if (mDotHashMap.size <= 0) {
                return
            }

            mDotHashMap.run {
                if (!isFinishing) {
                    this[current]?.let {  //根据当前点位去hashMap 去检索
                        startExecutionEffect(it) //条件满足 去触发动效 暂停视频
                    }
                }
            }
        }
        isFirstSeekTo = true
    }

    /**
     * 签到动效、其他动效 关闭后，重新播放
     */
    override fun onDismiss() {
        // 这里最好有个类型判断，如果是11或者12的，就不用走这个了
        resetPlayerVideo(true)
    }

    override fun onOtherAction(dot_id: String?, answer: String?) {
        val mDeviceID =
            if (!TextUtils.isEmpty(MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID))) {
                MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID).toString()
            } else {
                eventViewModel.appUserInfo.value?.device_id.toString()
            }
        mViewModel.submitExercise(mDeviceID, dot_id, answer)
    }


    /**
     * 执行动画特效
     * 效果类型 1签到效果 2答题效果 3红包效果 4课间休息 11新签到效果 12新答题效果 14新课间休息
     */
    private fun startExecutionEffect(data: VideoDotEntity) {
        if ("11" == data.effect_type || "12" == data.effect_type || "8" == data.effect_type) {
        } else {
            resetPlayerVideo(false)
        }
        if (mVideoEffectsPopupView == null) {
            mVideoEffectsPopupView = VideoEffectsPopupView(activity = this, mDataBind.superVodPlayerView.videoRete, this, data, mCourseInfo?.code!!)
        }

        XPopup.Builder(this)
            .hasShadowBg(false)
            .isDestroyOnDismiss(true)
            .setPopupCallback(object : SimpleCallback() {
                override fun onDismiss(popupView: BasePopupView?) {
                    super.onDismiss(popupView)
                    GlideCacheUtil.getInstance().clearImageMemoryCache(this@PlayerActivity)
                    mVideoEffectsPopupView = null
                }
            })
            .asCustom(mVideoEffectsPopupView)
            .show()
    }


    private fun initIMService() {
        if (!TUILogin.isUserLogined()) {
            mViewModel.getIMInfo()
        }
    }


    /**
     * 用户手动切换清晰度
     */
    private fun changeVideoDefinition() {
        "来切换清晰度了".logE(TAG)
        if (mBottomShowType == 0) {
            mDataBind.superviewLlMoreTipsLayout.gone()

            if (mDataBind.superVodPlayerView.currentSuperPlayerModel.videoQualityList.size > 0) {
                if (!TextUtils.isEmpty(mDataBind.superVodPlayerView.currentSuperPlayerModel.videoQualityList[1].url)) {
                    mDataBind.superVodPlayerView.changeVideoQuality(mDataBind.superVodPlayerView.currentSuperPlayerModel.videoQualityList[1])
                }
            }
            mDefinitionPosition = 1

            CommonViewUtil().changeAdapterSelected(
                commonFunctionsAdapter,
                mDataBind.flController,
                commonDefinitionAdapter,
                ControllerViewState.VIEW_CONTROLLER_DEFINITION,
                mDefinitionPosition
            )
        } else { // 播放下一天
            if (mDataBind.superviewButMoreTips.visibility == View.VISIBLE) {
                mDataBind.superVodPlayerView.playNextVideo()
                otherConfig()
                hideControllerView()
            }
        }
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        "按键了 $keyCode".logD(TAG)
        if (onMyKeyDown(event.keyCode, event)) { // 加一层判断，实现android 9 以及其他的情况
            return true
        }

        when (keyCode) {
            KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME -> {
                return true
            }

            KeyEvent.KEYCODE_DPAD_DOWN -> {
//                if (mDataBind.flController.visibility == View.VISIBLE && currentFocusType == focus_speed) { //触发边界抖动
//                    return CommonAnimationUtil().startPlayerViewAnimationShakeY(
//                        mShakeYAnimation, commonSpeedAdapter, mSpeedPosition
//                    )
//                }

                if (mDataBind.flController.visibility == View.GONE && mDataBind.superVodPlayerView.isPlayInit) {
                    mDataBind.superVodPlayerView.windowPlayer.hide()
                    mDataBind.flController.visible()
                    mDataBind.flController.post {
                        controllerScrollViewUp()

                        CommonViewUtil().controllerViewAction(
                            true,
                            mDataBind.flController,
                            commonFunctionsAdapter.getViewByPosition(0, R.id.rl_common_item_view),
                            ControllerViewState.VIEW_CONTROLLER_COMMON
                        )
                    }
                }

                if (mDataBind.flController.visibility == View.GONE) { //如果没有功能页，触发下方向键，就是加载重试图片
                    if (!TextUtils.isEmpty(mQrCodeUrl)) {
//                        if (mDataBind.rlCustomSignLayout.visibility == View.VISIBLE) {
//                            GlideUtil.loadPic(this@PlayerActivity, mQrCodeUrl, mDataBind.ivQrCode)
//                        } else

                        if (mDataBind.rlShareCourseLayout.visibility == View.VISIBLE) {
                            GlideUtil.loadPic(
                                this@PlayerActivity, mQrCodeUrl, mDataBind.ivShareCourseQrcode
                            )
                        }
                    }
                }
            }

            KeyEvent.KEYCODE_DPAD_UP -> {
                if (mDataBind.flController.visibility == View.VISIBLE && currentFocusType == focus_common) { //关闭控制台
                    hideControllerView()
                    return true
                }

                val stringHashMap = java.util.HashMap<String, Any>()
                stringHashMap["device"] = "${getWireMac()}"
                stringHashMap["store"] = "${eventViewModel.appUserInfo.value?.store_name}"
                if (mDataBind.rlShareCourseLayout.visibility == View.VISIBLE) {
                    stringHashMap["show_channel"] = "扫码购买"
                    stringHashMap["bitmap_load_state"] = "$isBannerLoadSuccess"
                } else {
                    when (type) {
                        1 -> {
                            stringHashMap["show_channel"] = "签到效果"
                        }

                        2 -> {
                            stringHashMap["show_channel"] = "答题效果"
                        }

                        4 -> {
                            stringHashMap["show_channel"] = "课间效果"
                        }
                    }
                    stringHashMap["bitmap_load_state"] = "$isQrCodeLoadSuccess"
                }
                stringHashMap["bitmap_url"] = "$mQrCodeUrl"
                stringHashMap["bitmap_error"] = "$mBitmapError"
                appUMEventObject(UMConstant.KEY_IMAGE_LOAD_ERROR, stringHashMap)
            }

            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (mDataBind.flController.visibility == View.VISIBLE) {
                    return selectCommonView(false)
                }
                if (mDataBind.superVodPlayerView.visibility == View.GONE) {
                    return true
                }
//                if (mDataBind.superVodPlayerView.playerState != SuperPlayerDef.PlayerState.PAUSE) {
//                    mDataBind.superVodPlayerView.onPause()
//                }
                mSubTitleView?.text = ""
                mDataBind.superviewLlMoreTipsLayout.gone()

                event.startTracking()//这个是为了回调onKeyLongPress

                mDataBind.rlShareCourseLayout.gone()

                if (event.repeatCount == 0) {
                    shortPress = true
                }
                if (event.repeatCount > 1) {
                    "快进快退 - 长按了 ${event.repeatCount}".logE(TAG)
                    mDataBind.superVodPlayerView.seekFastTo(true, event.repeatCount)
                    mVideoWatchUploadCount = 0
                }
                return true
            }

            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (mDataBind.flController.visibility == View.VISIBLE) {
                    return selectCommonView(true)
                }
                if (mDataBind.superVodPlayerView.visibility == View.GONE) {
                    return true
                }
//                if (mDataBind.superVodPlayerView.playerState != SuperPlayerDef.PlayerState.PAUSE) {
//                    mDataBind.superVodPlayerView.onPause()
//                }
                mSubTitleView?.text = ""
                mDataBind.superviewLlMoreTipsLayout.gone()

                event.startTracking()//这个是为了回调onKeyLongPress

                mDataBind.rlShareCourseLayout.gone()


                if (event.repeatCount == 0) {
                    "快进快退 - 长按了单次 ${event.repeatCount}".logE(TAG)
                    shortPress = true
                }
                if (event.repeatCount > 1) {
                    "快进快退 - 长按了 ${event.repeatCount}".logE(TAG)
                    mDataBind.superVodPlayerView.seekFastTo(false, event.repeatCount)
                    mVideoWatchUploadCount = 0
                }
                return true
            }

            KeyEvent.KEYCODE_MENU -> {
                goSystemTvBoxSetting(this)
                return true
            }

            KeyEvent.KEYCODE_PROFILE_SWITCH -> return true

            KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_ENTER -> {
                if (mDataBind.superVodPlayerView.visibility == View.GONE) {
                    return true
                }
                if (mDataBind.superviewLlMoreTipsLayout.visibility == View.VISIBLE && mDataBind.superviewButMoreTips.visibility == View.VISIBLE) { // 执行切换清晰度的逻辑
                    changeVideoDefinition()
                    return true
                }
                when (mDataBind.superVodPlayerView.playerState) {
                    SuperPlayerDef.PlayerState.PLAYING -> {
                        mDataBind.superVodPlayerView.onPause()
                        mDataBind.superviewTvTips.gone()
                        mDataBind.superviewTvTips.text = ""
                    }

                    SuperPlayerDef.PlayerState.END -> {
                        playVideo()
                    }

                    SuperPlayerDef.PlayerState.PAUSE -> {
                        mDataBind.rlShareCourseLayout.gone()
                        mDataBind.superVodPlayerView.onResume()
                    }
                }
            }

            KeyEvent.KEYCODE_BACK -> {

                if (mDataBind.flController.visibility == View.VISIBLE) {
                    hideControllerView()
                    return true
                }

                if (mDataBind.rlShareCourseLayout.visibility == View.VISIBLE) {
                    mDataBind.rlShareCourseLayout.visibility = View.GONE
                    uploadADEvent(UMConstant.KEY_QR_CODE, "Back", "消失")
                    return true
                }

                mActionBack = true
                mViewModel.uploadVideoProgress(mCourseInfo?.code, mDataBind.superVodPlayerView.vodProgress)

                eventViewModel.progressEvent.value = mCourseInfo?.code?.let {
                    ProgressNotice(it, "" + mDataBind.superVodPlayerView.vodProgress)
                }
                if (1 == JumpType) {
                    eventViewModel.refreshHistoryList.value = true
                }
                finish()
            }
        }
        return super.onKeyDown(keyCode, event)
    }


    override fun onKeyLongPress(keyCode: Int, event: KeyEvent): Boolean {
        "快进快退 - 长按制定seek 过程".logE(TAG)
        shortPress = false
        return super.onKeyLongPress(keyCode, event)
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent): Boolean {
        if (mDataBind.superVodPlayerView.visibility == View.GONE) {
            return true
        }
        if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
            if (shortPress) {
                "快进快退 - 来单次这里了".logE(TAG)
                if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                    mDataBind.superVodPlayerView.seekToOne(
                        true, (mDataBind.superVodPlayerView.vodProgress - 10).toInt()
                    )
                }
                if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    mDataBind.superVodPlayerView.seekToOne(
                        false, (mDataBind.superVodPlayerView.vodProgress + 10).toInt()
                    )
                }
            } else {
//                mDataBind.superVodPlayerView.windowPlayer.thumbnailAdapter.updateScrollingState(false)
                "快进快退 - 来长按这里了 ${mDataBind.superVodPlayerView.fastFinalProgress}".logE(TAG)
                if (mDataBind.superVodPlayerView.isFastActionZero) {

                    mDataBind.superVodPlayerView.seekToFast(mDataBind.superVodPlayerView.fastFinalProgress)
                    mDataBind.superVodPlayerView.fastCurrentProgress = 0
                    mDataBind.superVodPlayerView.fastFinalProgress = 0

                    mDataBind.superVodPlayerView.setFastCurrentProgressAction(false)
                }
                mDataBind.superVodPlayerView.onResume()
                //隐藏掉banner
                mDataBind.rlShareCourseLayout.gone()
            }
            shortPress = false
            return true
        }
        return super.onKeyUp(keyCode, event)
    }

    /**
     * 多功能界面下区分 左右点击
     */
    private fun selectCommonView(action: Boolean): Boolean {
        when (currentFocusType) {
            focus_common -> { //判断是否到了边界
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonFunctionsAdapter, mCommonPosition, action
                )
            }

            focus_sub_title -> {
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonSubTitleAdapter, mSubTitlePosition, action
                )
            }

            focus_konw -> {
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonKonwledgeAdapter, mKonwledgePosition, action
                )
            }

            focus_definition -> {
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonDefinitionAdapter, mDefinitionPosition, action
                )
            }

            focus_speed -> {
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonSpeedAdapter, mSpeedPosition, action
                )
            }

            focus_course -> {
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonCourseAdapter, mCoursePosition, action
                )
            }
        }
        return false
    }


    /**
     * 统计自定义事件上报
     */
    private fun uploadADEvent(key: String, action: String, pv_state: String) {
        mEventHashMap.clear() //进来之前清楚之前的
        mEventHashMap["action"] = "$action"
        mEventHashMap["store_name"] = "${eventViewModel.appUserInfo.value?.store_name}"
        mEventHashMap["store_id"] = "${eventViewModel.appUserInfo.value?.store_id}"
        mEventHashMap["pv_state"] = "$pv_state"
        appUMEventObject(key, mEventHashMap)
    }

    /**
     * 这个方法会间隔调用
     */
    private fun uploadVideoWatch() {
        mViewModel.uploadVideoStatistics(mCourseInfo?.code)
        //同时上传一下观看记录的情况
        mViewModel.uploadVideoProgress(mCourseInfo?.code, mDataBind.superVodPlayerView.vodProgress)
        //同时再请求一下 检测过期
        mViewModel.checkDevice(false)
    }

    /**
     * 统计播放器初始化时间戳
     * type 1 是播放器统计上报  2 是网速上报
     */
    private fun SuperPlayStatistics(time: Long? = null) {
        "${time}".logD("上报视频初始化")
        if (mDataBind.superVodPlayerView.playerState == SuperPlayerDef.PlayerState.PLAYING && !this.isFinishing) {
            RxHttp.get(NetUrl.MONITOR_URL + NetUrl.VIDEO_STAT).add("xmjz_from", "tv")
                .add("type", "shoupingload")
                .add("xmjz_version", "" + getAppVersion(this)).add("xmjztime", "" + time).asString()
                .subscribe({ s: String ->
                    isUploadFirstSuccess = true
                }) { throwable: Throwable ->
                }
        }
    }


    /**
     * 播放视频
     */
    private fun playVideo() {
        mDataBind.ivLogo.visibility = View.VISIBLE
        mDataBind.llCompany.visibility = View.GONE

        val tasks = TXVodDownloadManager.getInstance().downloadMediaInfoList
        if (tasks.size > 0) {
            "${tasks.size}".logE("本地视频")
            for (i in tasks.indices) {
                if (tasks[i].userName == mCourseInfo?.code) { //title 代表本地的video_code
                    "1 = ${tasks[i].userName}".logE("本地视频")
                    "2 = ${mCourseInfo?.code}".logE("本地视频")
                    "3 = ${tasks[i].isDownloadFinished}".logE("本地视频")

                    if (tasks[i].isDownloadFinished) {
                        "本地视频路径为 = ${tasks[i].playPath}".logE(TAG)
                        spanDebugText("当前视频是本地 \n 课程Code = ${mCourseInfo?.code} \n 课程名称 = ${mCourseInfo?.title_main} \n 本地视频路径为 = ${tasks[i].playPath}")
                        findLocalVideo = true
                        "4 = ${findLocalVideo}".logE("本地视频")
                        playSuperVideo(tasks[i].playPath, mCourseInfo?.code)
                    }
                    break
                }
            }
            if (!findLocalVideo) {
                spanDebugText("当前视频是网络视频 \n 课程Code = ${mCourseInfo?.code} \n 课程名称 = ${mCourseInfo?.title_main}")
                playSuperVideo()
            }
        } else {
            spanDebugText("当前视频是网络视频 \n 课程Code = ${mCourseInfo?.code} \n 课程名称 = ${mCourseInfo?.title_main}")
            playSuperVideo()
        }
    }

    /**
     *  根据腾讯云官方的流程创建视频列表 播放多个视频 此时
     */
    private fun playSuperVideo(localUrl: String? = "", code: String? = "") {
        val superPlayerModelList = ArrayList<SuperPlayerModel>()

        if (commonCourseAdapter.data.size > 0) {
            for (i in commonCourseAdapter.data.indices) {
                val superPlayerModel = SuperPlayerModel()
                val videoQualityList = ArrayList<VideoQuality>()
                val multiURLs = ArrayList<SuperPlayerURL>()
                if (code == commonCourseAdapter.data[i].code) { //如果有本地的视频的话
                    superPlayerModel.url =
                        if (TextUtils.isEmpty(localUrl)) commonCourseAdapter.data[i].video_url else localUrl
                } else {
                    superPlayerModel.url = commonCourseAdapter.data[i].video_url
                }

                videoQualityList.add(
                    VideoQuality(
                        0,
                        "高清",
                        commonCourseAdapter.data[i].hd_video_url_list?.hd
                    )
                )
                multiURLs.add(
                    SuperPlayerURL(
                        commonCourseAdapter.data[i].hd_video_url_list?.hd,
                        "高清"
                    )
                )

                if (!TextUtils.isEmpty(commonCourseAdapter.data[i].hd_video_url_list?.standard)) {
                    multiURLs.add(
                        SuperPlayerURL(
                            commonCourseAdapter.data[i].hd_video_url_list?.standard,
                            "标清"
                        )
                    )
                    videoQualityList.add(
                        VideoQuality(
                            1,
                            "标清",
                            commonCourseAdapter.data[i].hd_video_url_list?.standard
                        )
                    )
                } else {
                    multiURLs.add(
                        SuperPlayerURL(
                            commonCourseAdapter.data[i].hd_video_url_list?.hd,
                            "标清"
                        )
                    )
                    videoQualityList.add(
                        VideoQuality(
                            1,
                            "标清",
                            commonCourseAdapter.data[i].hd_video_url_list?.hd
                        )
                    )
                }

                superPlayerModel.title = commonCourseAdapter.data[i].title_main
                superPlayerModel.appId = ConstantMMVK.DEFAULT_APPID
                superPlayerModel.multiURLs = multiURLs
                superPlayerModel.videoQualityList = videoQualityList
                superPlayerModelList.add(superPlayerModel)
            }
        } else {
            val videoQualityList = ArrayList<VideoQuality>()
            val multiURLs = ArrayList<SuperPlayerURL>()

            videoQualityList.add(VideoQuality(0, "高清", mCourseInfo?.video_url))
            multiURLs.add(SuperPlayerURL(mCourseInfo?.video_url, "高清"))

            if (!TextUtils.isEmpty(mCourseInfo?.hd_video_url_list?.standard)) {
                multiURLs.add(SuperPlayerURL(mCourseInfo?.hd_video_url_list?.standard, "标清"))
                videoQualityList.add(
                    VideoQuality(
                        1,
                        "标清",
                        mCourseInfo?.hd_video_url_list?.standard
                    )
                )
            } else {
                multiURLs.add(SuperPlayerURL(mCourseInfo?.hd_video_url_list?.hd, "标清"))
                videoQualityList.add(VideoQuality(1, "标清", mCourseInfo?.hd_video_url_list?.hd))
            }

            //判断下是否需要显示字幕按钮
            if (!TextUtils.isEmpty(mCourseInfo?.hd_video_subtitle_url)) {
                mDataBind.superVodPlayerView.windowPlayer.vodSubTitleView.visible()
                mDataBind.gridSubTitle.visible()
                mDataBind.tvSubTitle.visible()
                mCourseInfo?.hd_video_subtitle_url?.let { initSubTitle(it) }
            } else {
                mDataBind.superVodPlayerView.windowPlayer.flVodSubTitle.gone()
                mDataBind.superVodPlayerView.windowPlayer.vodSubTitleView.gone()
                deletedFunctionAdapter("字幕")
                mDataBind.gridSubTitle.gone()
                mDataBind.tvSubTitle.gone()
            }

            val superPlayerModelV3 = SuperPlayerModel()
            superPlayerModelV3.title = mCourseInfo?.title_main
            superPlayerModelV3.url =
                if (TextUtils.isEmpty(localUrl)) mCourseInfo?.video_url else localUrl
            superPlayerModelV3.appId = ConstantMMVK.DEFAULT_APPID
            superPlayerModelV3.multiURLs = multiURLs
            superPlayerModelV3.videoQualityList = videoQualityList
            superPlayerModelList.add(superPlayerModelV3)
        }

        mDataBind.superVodPlayerView.playWithModelList(superPlayerModelList, true, currentPlayerPosition) //循环播放列表，并且从第0位开始
        "初始化播放器".logE()
        otherConfig() //视频设置后之后的处理
    }


    /**
     * 显示debug模式下的 可视化的内容
     */
    private fun spanDebugText(text: String? = null, clean: Boolean? = false) {
        if (clean == true) {
            stringBuilder.clear()
        } else {
            stringBuilder.append(text).append("\n")
        }
//        mDataBind.tvDebugContent.text = stringBuilder
    }

    /**
     * 异步下载bitmap 回掉给SuperView
     * 方案A 是一下拿到所有的图
     * 方案B 根据当前的进度条去拿对应的是那条链接
     * (假如有10条，一条url的图片是10*10的 一张图片代表10帧 ，一张大图就是1000秒  假如到了3268  那么就是第四张图，268 再除以10，那么就是26.8 那么当前现实的下标应该就是27)
     */
    private fun executeAsyncTask(imageUrls: List<String>, total: Int) {
        "雪碧图-来执行耗时任务了 ${imageUrls.size}".logE()
        val bitmapPath = Constants.BITMAP_PATH + "${mCourseInfo?.tencent_video_id}/"
        try {
            if (imageUrls.isNullOrEmpty()) {
                return
            }
            mSpristAsyncTask = CoroutineScope(Dispatchers.IO).launch {
                try {
                    //先创建假数据
                    val resultThumbnails = withContext(Dispatchers.IO) {
                        mutableListOf<ThumbnailEntity>().apply {
                            for (i in 0 until total) {
                                add(ThumbnailEntity(null, i * 10))
                            }
                        }
                    }

                    withContext(Dispatchers.IO) {
                        val file = File(bitmapPath)
                        if (file.listFiles() != null && file.listFiles().size >= imageUrls.size) { //本地就有，不需要下载 直接更新UI
                            // 在主线程中更新 UI
                            withContext(Dispatchers.Main) {
                                "雪碧图 -  本地的 --- 给Adapter 先塞塞假数据了 $bitmapPath".logE()
                                mDataBind.superVodPlayerView.changeThumbnail(resultThumbnails, Constants.BITMAP_PATH, bitmapPath, total, imageUrls.size, mDotList)
                            }

                            withContext(Dispatchers.IO) {
                                val deferredList = mutableListOf<Deferred<Bitmap>>()
                                var count = 0
                                for ((key, value) in mDotHashMap) {
                                    if (!TextUtils.isEmpty(value.effect_material?.background) && "8" != value.effect_type && "14" != value.effect_type) {
                                        val url = value.effect_material?.background
                                        val fileName = "app_${value.effect_type}.jpg"
                                        val file = File("${Constants.BITMAP_PATH}$fileName")
                                        if (!file.exists()) {
                                            // 如果本地没有对应的图片文件，使用 Glide 下载并保存到本地
                                            val deferred = async {
                                                Glide.with(this@PlayerActivity)
                                                    .asBitmap()
                                                    .skipMemoryCache(true)
                                                    .diskCacheStrategy(DiskCacheStrategy.NONE) // 禁用磁盘缓存
                                                    .load(url)
                                                    .submit()
                                                    .get()
                                                    .also {
                                                        FileUtil.saveBitmapToFile(it, "${Constants.BITMAP_PATH}$fileName")
                                                        "雪碧图 - 打点本地下载并保存 ${count + 1}".logE()
                                                    }
                                            }
                                            deferredList.add(deferred)
                                        } else {
                                            // 如果本地已经有对应的图片文件，跳过不处理
                                            "雪碧图 - 打点本地已有 ${count + 1}".logE()
                                        }
                                        count++
                                    }
                                }
                                deferredList.awaitAll()
                            }

                            this.cancel()
                        }
                    }

                    "雪碧图 - 先来下载雪碧图了".logE()
                    withContext(Dispatchers.IO) {
                        imageUrls.mapIndexed { index, imageUrl ->
                            async {
                                "雪碧图 - 具体的路径 - $imageUrl".logE()
                                Glide.with(this@PlayerActivity)
                                    .asBitmap()
                                    .skipMemoryCache(true)
                                    .diskCacheStrategy(DiskCacheStrategy.NONE) // 禁用磁盘缓存
                                    .load(imageUrl)
                                    .submit()
                                    .get()
                                    .also {
                                        FileUtil.saveBitmapToFile(it, "$bitmapPath$index.jpg")
                                        "雪碧图 - 本地放入了 ".logE()
                                    }
                                "雪碧图 - 下载完成 ${index + 1}/${imageUrls.size}".logE()
                            }
                        }.awaitAll()
                    }

                    withContext(Dispatchers.IO) {
                        val deferredList = mutableListOf<Deferred<Bitmap>>()
                        var count = 0
                        for ((key, value) in mDotHashMap) {
                            if (!TextUtils.isEmpty(value.effect_material?.background) && "8" != value.effect_type && "14" != value.effect_type) {
                                val url = value.effect_material?.background
                                val fileName = "app_${value.effect_type}.jpg"
                                val file = File("${Constants.BITMAP_PATH}$fileName")
                                if (!file.exists()) {
                                    // 如果本地没有对应的图片文件，使用 Glide 下载并保存到本地
                                    val deferred = async {
                                        Glide.with(this@PlayerActivity)
                                            .asBitmap()
                                            .skipMemoryCache(true)
                                            .diskCacheStrategy(DiskCacheStrategy.NONE) // 禁用磁盘缓存
                                            .load(url)
                                            .submit()
                                            .get()
                                            .also {
                                                FileUtil.saveBitmapToFile(it, "${Constants.BITMAP_PATH}$fileName")
                                                "雪碧图 - 打点本地下载并保存 ${count + 1}".logE()
                                            }
                                    }
                                    deferredList.add(deferred)
                                } else {
                                    // 如果本地已经有对应的图片文件，跳过不处理
                                    "雪碧图 - 打点本地已有 ${count + 1}".logE()
                                }
                                count++
                            }
                        }
                        deferredList.awaitAll()
                    }


                    // 在主线程中更新 UI
                    withContext(Dispatchers.Main) {
                        "雪碧图 - 给View塞数据了".logE()
                        mDataBind.superVodPlayerView.changeThumbnail(resultThumbnails, Constants.BITMAP_PATH, bitmapPath, total, imageUrls.size, mDotList)
                    }

                } catch (e: Exception) {
                    // 处理异常
                    e.message
                    "雪碧图 - 异常了 ${e.message}".logE()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            "雪碧图 - 异常了 ${e.message}".logE()
        }
    }

    // 保存图片的函数
    fun saveImageToFile(file: File, filePath: String): File {
        val imageFile = File(filePath)
        file.copyTo(imageFile, true)
        return imageFile
    }


    override fun onResume() {
        super.onResume()
        mSubTitleView?.resume()
        mInitTime = System.currentTimeMillis()
        if (NetworkUtil.isAvailable(this)) {
            mDataBind.superVodPlayerView.onResume()
        }
    }

    override fun onPause() {
        super.onPause()
        mSubTitleView?.pause()
        mDataBind.superVodPlayerView.onPause()
    }

    override fun onStop() {
        super.onStop()
        mSubTitleView?.stop()
        mDataBind.superVodPlayerView.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        stopAll()
    }

    override fun finish() {
        super.finish()
        stopAll()
    }

    /**
     * 重置超级播放器
     */
    private fun stopSuperPlay() {
        mDataBind.superVodPlayerView.release()
        mDataBind.superVodPlayerView.resetPlayer()
    }

    private fun stopAll() {
        GlideCacheUtil.getInstance().clearImageMemoryCache(this)
        mSpristAsyncTask?.cancel()//停止协程
        mSubTitleView?.destroy()
        stopSuperPlay()
        MediaHelper.stop()
        MediaHelper.release()
        TtsVoiceUtil.getInstance().onPause()
        TtsVoiceUtil.getInstance().onStop()
        finishActivity(this)
    }


}