package com.panda.course.ui.activity.dialog

import android.media.MediaPlayer
import android.os.Bundle
import android.os.Handler
import android.text.TextUtils
import android.view.KeyEvent
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.appContext
import com.panda.course.databinding.ActivityExpandInterfaceBinding
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.service.MultiTaskDownloader
import com.panda.course.ui.adapter.RedPaperSnatchAdapter
import com.panda.course.ui.viewmodel.ExpandInterfaceViewModel
import com.panda.course.util.GlideUtil
import com.panda.course.util.MMKVHelper
import com.panda.course.util.MediaHelper
import com.panda.course.util.QRCodeUtil
import com.panda.course.util.RxTimerUtil
import io.reactivex.rxjava3.disposables.Disposable
import kotlinx.coroutines.*
import okio.ByteString.Companion.encodeUtf8

class ExpandInterfaceActivity :
    BaseDbActivity<ExpandInterfaceViewModel, ActivityExpandInterfaceBinding>() {

    private var mRedPaperNumber = "" //红包的编号
    private var mAnimationCount = 0 //记录红包的走到哪一步了
    private var mRedPaperEntity: RedPaperEntity? = null
    private var mAdapter = RedPaperSnatchAdapter()

    // 倒计时
    private var mCountDown: Disposable? = null

    override fun initView(savedInstanceState: Bundle?) {
        initBundle()
        initRecycler()
    }

    private fun initRecycler() {
        mDataBind.recycler.layoutManager = LinearLayoutManager(this)
        mDataBind.recycler.adapter = mAdapter
    }

    private fun initBundle() {
        intent?.extras?.let {
            mRedPaperNumber = it.getString("number").toString()
            val mDeviceID =
                if (!TextUtils.isEmpty(MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID))) {
                    MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID).toString()
                } else {
                    eventViewModel.appUserInfo.value?.device_id.toString()
                }
            mViewModel.getRedPaper(mRedPaperNumber, mDeviceID)
        }
    }

    override fun initObserver() {
        super.initObserver()
        eventViewModel.listenForIMPushMessages.observe(this, Observer {
            if (it == null) {
                return@Observer
            }
            if ("7" == it.type) {
                mAdapter.addData(
                    RedPaperPeopleEntity(
                        it.service_personnel_name,
                        it.service_personnel_avatar,
                        it.money,
                        it.remain_envelope_num,
                        it.envelope_num,
                        it.envelope_title
                    )
                )
                mDataBind.tvRecyclerEmpty.gone()
                mDataBind.recycler.scrollToPosition(mAdapter.data.size - 1)
            }
        })
    }

    override fun onRequestSuccess() {
        super.onRequestSuccess()
        mViewModel.redPaperEntity.observe(this, Observer {
            if (it == null) {
                finish()
                return@Observer
            }
            mRedPaperEntity = it
            resultAnimation(it)
        })
    }

    //处理动画的逻辑
    private fun resultAnimation(data: RedPaperEntity) {
        if (data.list.isNotEmpty()) {
            startAnimation(data.list[mAnimationCount])
        } else {
            finish()//啥也没有，不用留在这个界面了loadQrImageView
        }
    }

    // 执行动画
    private fun startAnimation(data: RedPaperListEntity) {
        "播放动画 - 1 ".logE()
        stopCountDown()
        when (data.effect_type) {
            "1" -> {
                runOnUiThread {
                    mDataBind.ivRedTitleLogo.gone()
                    mDataBind.tvRedPaperDown.gone()
                    mDataBind.tvRedPaperFirstDown.visible()
                    mDataBind.tvRedPaperStartTips.visible()
                    mDataBind.ivRedPaperQrCode.gone()
                    mDataBind.flRedPaperFinishLayout.gone()
                    mDataBind.flRedPaperQrCodeLayout.gone()
                    mDataBind.flRedPaperRankLayout.gone()
                }

            }

            "2" -> {

                runOnUiThread {
                    mDataBind.ivRedTitleLogo.visible()
                    mDataBind.tvRedPaperFirstDown.gone()
                    mDataBind.tvRedPaperDown.visible()
                    mDataBind.ivRedPaperDriftDown.visible()
                    mDataBind.tvRedPaperStartTips.gone()
                    mDataBind.flRedPaperQrCodeLayout.visible()
                    mDataBind.flRedPaperRankLayout.visible()
                    mDataBind.flRedPaperFinishLayout.gone()
                    mDataBind.ivRedPaperQrCode.visible()
                }
                data.qr_code_content?.let { loadQrImageView(it) }
                //加载摇一摇 没必要每次都走 走一次即可
                GlideUtil.loadGif(appContext, R.drawable.yao1yao, mDataBind.ivRedPaperYao)
                GlideUtil.loadGif(appContext, R.drawable.yao1, mDataBind.ivRedPaperDriftDown)
            }

            "3" -> {
                runOnUiThread {
                    mDataBind.ivRedPaperDriftDown.gone()
                    mDataBind.flRedPaperFinishLayout.visible()
                }
            }
        }
        "播放动画 - 2 ".logE()
        mDataBind.tvRedPaperStartTips.text = data.introduction
        if (mAnimationCount == 0) {
            GlideUtil.loadGif(appContext, R.drawable.test1, mDataBind.ivRedPaperBigBg)
        } else {
            if (!TextUtils.isEmpty(data.background)) {
                //背景图
                GlideUtil.loadPic(appContext, data.background, mDataBind.ivRedPaperBigBg)
            }
        }


        //播放mp3
        data.background_voice?.let { media ->
            //启动异步任务执行
            initMp3(media)
        }

        //制定倒计时的总数
        val mCountDownTimer =
            if (!TextUtils.isEmpty(data.countdown_time)) data.countdown_time!!.toInt() else 10
        "播放动画 - 5 ".logE()
        //执行倒计时
        mCountDown = RxTimerUtil.timer(mCountDownTimer).subscribe { count ->
            runOnUiThread {
                when (mAnimationCount) {
                    0 -> {
                        mDataBind.tvRedPaperFirstDown.text = "${count}s"
                    }

                    1 -> {
                        mDataBind.tvRedPaperDown.text = "${count}s"
                    }

                    else -> {
                        mDataBind.tvRedPaperFinishDown.text = "${count}s"
                    }
                }

            }

            "播放动画 - 7 count $count ".logE()
            if (count <= 0) {//倒计时执行完毕
                mAnimationCount += 1//标记动画位置 进行下一个了
                "播放动画 - 8 =$mAnimationCount".logE()

                if (mAnimationCount >= mRedPaperEntity!!.list.size) {
                    "播放动画 - 10 ".logE()
                    mAnimationCount = 0;
                    finish()
                    return@subscribe
                }
                mDataBind.tvRedPaperDown.postDelayed({
                    "播放动画 - 9 ssss $mAnimationCount".logE()
                    startAnimation(mRedPaperEntity!!.list[mAnimationCount])
                }, 1000)
            }
        }

    }

    /**
     *  只要失败的，就一直请求，直到成功
     */
    private fun loadQrImageView(qrUrl: String) {
        if (TextUtils.isEmpty(qrUrl)) {
            return
        }
        mDataBind.ivRedPaperQrCode.setImageBitmap(QRCodeUtil.getInstance().createQRCode(qrUrl))
//        GlideUtil.loadQrPic(
//            this@ExpandInterfaceActivity,
//            qrUrl,
//            mDataBind.ivRedPaperQrCode,
//            object : GlideUtil.PictureListener {
//                override fun success() {
//                }
//
//                override fun fail(msg: String) {
//                    Handler().post {
//                        loadQrImageView(qrUrl)
//                    }
//                }
//            })
    }

    /**
     * 初始化Mp3
     */
    private fun initMp3(media: String) {
        //本地要是没有就直接去下载
        if (TextUtils.isEmpty(findLocalMp3(media))) {
            val task = DownloadTask(media)
            val suffix: String = media.substring(media.lastIndexOf("."))
            task.localPath =
                externalCacheDir.toString() + "/" + media.encodeUtf8().md5().hex() + suffix
            MultiTaskDownloader.startDownLoadTask(task)
        }
        try {
            CoroutineScope(Dispatchers.Main).launch {
                withContext(Dispatchers.IO) {
                    if (!<EMAIL>) {
                        MediaHelper.playSound(this@ExpandInterfaceActivity, media) { mp: MediaPlayer? -> }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun stopCountDown() {
        mCountDown?.dispose()
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        when (event.keyCode) {
            KeyEvent.KEYCODE_BACK -> {
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onDestroy() {
        super.onDestroy()
        stopCountDown()
        MediaHelper.stop()
        MediaHelper.release()
        finishActivity(this)
    }

}