package com.panda.course.ui.activity

import android.graphics.Color
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.text.TextUtils
import androidx.core.text.bold
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import androidx.core.text.scale
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityOnlineRenewalBinding
import com.panda.course.entity.OnlineRenewalYearEntity
import com.panda.course.ext.eventViewModel
import com.panda.course.ext.gone
import com.panda.course.ext.visible
import com.panda.course.ui.adapter.RowRenewalAdapter
import com.panda.course.ui.adapter.RowSkillAdapter
import com.panda.course.ui.adapter.SkillRecordAdapter
import com.panda.course.ui.viewmodel.OnlineRenewalViewModel
import com.panda.course.util.GlideImageLoader
import com.panda.course.util.GlideUtil
import com.panda.course.util.MMKVHelper
import com.panda.course.util.QRCodeUtil

///在线续费
class OnlineRenewalActivity : BaseDbActivity<OnlineRenewalViewModel, ActivityOnlineRenewalBinding>() {

    ///续费年限
    private var mRowAdapter = RowRenewalAdapter()

    override fun initView(savedInstanceState: Bundle?) {
        mViewModel.getOnlineRenewal()
        mDataBind.recycler.layoutManager = GridLayoutManager(this, 3)
        mDataBind.recycler.adapter = mRowAdapter
    }

    override fun initObserver() {
        mRowAdapter.apply {
            this.setOnViewFocus(OnViewFocus { hasFocus, position, view ->
                if (hasFocus) {
                    mDataBind.ivOnlineRenewalQr.gone()
                    mDataBind.ivOnlineRenewalQr.alpha = 0.1f
                    mDataBind.progress.visible()
                    resultPrice(mRowAdapter.data[position])
                    getQrCode(mRowAdapter.data[position].year)
                }
            })
        }
    }

    private fun resultPrice(data: OnlineRenewalYearEntity) {
        mDataBind.tvRenewalPrice.text = buildSpannedString {
            append("应付金额：")
            bold {
                scale(1.2f) {
                    color(Color.parseColor("#FF0000")) {
                        append(data.pay_money + "元")
                    }
                }
            }
            append("\n")
            append("缴费后到期时间：${data.new_end_date}")
            if (!TextUtils.isEmpty(data.tips)) {
                append("\n")
                append("${data.tips}")
            }
        }

    }

    private fun getQrCode(year: String) {
        val mDeviceID =
            if (!TextUtils.isEmpty(MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID))) {
                MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID).toString()
            } else {
                eventViewModel.appUserInfo.value?.device_id.toString()
            }
        mViewModel.getOnlineRenewalQrCode(mDeviceID, year)
    }

    override fun onRequestSuccess() {
        mViewModel.apply {
            this.onlineRenewalEntity.observe(this@OnlineRenewalActivity, Observer {
                mDataBind.tvRenewalDevices.text = "设备信息：${it.device_mac}"
                mDataBind.tvRenewalDate.text = "当前到期时间：${it.expire_end_date}"
                mRowAdapter.setList(it.product_list)
                mDataBind.recycler.postDelayed({
                    mRowAdapter.getViewByPosition(0, R.id.row_card_view_renewal)?.requestFocus()
                }, 200)
            })

            this.qrCodeEntity.observe(this@OnlineRenewalActivity, Observer {
                mDataBind.progress.gone()
                mDataBind.ivOnlineRenewalQr.setImageBitmap(QRCodeUtil.getInstance().createQRCode(it.qr_code_content))
//                GlideImageLoader.loadImageWithRetry(this@OnlineRenewalActivity, it.qr_code_url, mDataBind.ivOnlineRenewalQr)
                mDataBind.ivOnlineRenewalQr.alpha = 1f
                mDataBind.ivOnlineRenewalQr.visible()
            })
        }
    }
}