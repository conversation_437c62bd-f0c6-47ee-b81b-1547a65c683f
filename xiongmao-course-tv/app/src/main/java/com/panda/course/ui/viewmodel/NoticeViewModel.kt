package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

/**
 * 消息通知相关
 */
class NoticeViewModel : BaseViewModel() {

    //获取列表
    var noticeListEntity = MutableLiveData<NoticeListEntity>()


    var mCourseDetail = MutableLiveData<CourseDetail>()


    fun getNoticeList(page: Int) {
        rxHttpRequest {
            onRequest = {
                noticeListEntity.value = RxHttp.get(NetUrl.GET_NOTICE_LIST)
                    .add("page", page)
                    .add("size", "10")
                    .add("device_code", getSerialNumber())
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).toResponse<NoticeListEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_NOTICE_LIST
        }
    }


    /** 已读消息数量 **/
    fun sendNotice(number: String) {
        rxHttpRequest {
            onRequest = {
                RxHttp.get(NetUrl.READ_SEND_NOTICE)
                    .add("number", number)
                    .add("device_code", getSerialNumber())
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).toResponse<Any>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.READ_SEND_NOTICE
        }
    }


    /**
     * 获取某个课程的详情 只是为了做个校验判断  所以page 传递1
     */
    fun getCourseDetail(code: String?, uuid: String? = null, hideDialog: Boolean = false) {
        rxHttpRequest {
            onRequest = {
                mCourseDetail.value =
                    RxHttp.get(NetUrl.GET_COURSE_DETAIL).add("code", "$code").add("page", "1")
                        .add("size", "" + ConstantMMVK.PAGE_SIZE).add("device_code", getSerialNumber())
                        .add("uuid", "$uuid").add("device_mac", getWireMac()).add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).toResponse<CourseDetail>()
                        .await() // 请求成功，页码+1
            }
            loadingType = if (hideDialog) LoadingType.LOADING_NULL else LoadingType.LOADING_CUSTOM
            requestUrl = NetUrl.GET_COURSE_DETAIL
        }
    }


}