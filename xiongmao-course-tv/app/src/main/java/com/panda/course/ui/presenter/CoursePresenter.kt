package com.panda.course.ui.presenter

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import cn.jiazhengye.panda_home.utils.DateUtil
import com.panda.course.config.ConstantMMVK
import com.panda.course.entity.Surprise
import com.panda.course.ext.toStartActivity
import com.panda.course.ui.activity.ContentActivity
import com.panda.course.ui.activity.WebViewActivity
import com.panda.course.ui.activity.dialog.SurpriseContentActivity
import com.panda.course.ui.activity.dialog.SurpriseDialogActivity
import com.panda.course.util.MMKVHelper

/**
 * 业务逻辑处理
 */
object CoursePresenter {


    /**
     * 首页惊喜活动
     */
    fun resultEverydayPub(
        pub: Surprise, layout: View
    ) {
//        val localDate = MMKVHelper.decodeString(ConstantMMVK.SURPRISE_SHOW_TOMORROW)
//        if (!TextUtils.isEmpty(localDate)) {
//            if (-1 == localDate!!.compareTo(DateUtil().getToDayDate())) { //返回负数 就是本地存的小于当前的日期了
//                resultEverydayPop(pub, layout)
//            }
//        } else { //保存当前的日期进去
//            MMKVHelper.encode(ConstantMMVK.SURPRISE_SHOW_TOMORROW, DateUtil().getToDayDate())
            resultEverydayPop(pub, layout)
//        }
    }

    /**
     * 惊喜活动处理
     */
    private fun resultEverydayPop(pub: Surprise, layout: View) {
        val bundle = Bundle()
        bundle.putString("root_bg", pub.img_url)
        bundle.putParcelable(
            "pub_data", pub
        )
        var showCount = MMKVHelper.decodeInt(ConstantMMVK.SURPRISE_SHOW_COUNT)
        if (showCount != null) {
            if (showCount <= 1) {
                showCount += 1
                layout.postDelayed({
                    toStartActivity(SurpriseDialogActivity::class.java, bundle)
                    MMKVHelper.encode(ConstantMMVK.SURPRISE_SHOW_COUNT, showCount)
                }, 3000)
            }
        } else {
            layout.postDelayed({
                toStartActivity(SurpriseDialogActivity::class.java, bundle)
                MMKVHelper.encode(ConstantMMVK.SURPRISE_SHOW_COUNT, 1)
            }, 3000)
        }
    }


    /**
     * 惊喜活动入口的渠道判断
     */
    fun toSurpriseDetails(mSurprise: Surprise?) {
        val bundle = Bundle()
        mSurprise?.let { s -> //跳转类型 1课程 2活动ID 3网址
            when (s.jump_type) {
                "1" -> {
                    bundle.putString("course_code", s.course_code)
                    toStartActivity(ContentActivity::class.java, bundle)
                }
                "2" -> {
                    bundle.putString("activity_id", s.activity_id)
                    toStartActivity(SurpriseContentActivity::class.java, bundle)
                }
                "3" -> {
                    bundle.putString("jump_url", s.jump_url)
                    toStartActivity(WebViewActivity::class.java, bundle)
                }
            }
        }
    }
}