package com.panda.course.util

import android.app.Activity
import android.content.Context
import android.graphics.drawable.Drawable
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.panda.course.R

object GlideImageLoader {
    //一般只有二维码才会用这个
    fun loadImageWithRetry(context: Activity, imageUrl: String, imageView: ImageView, maxRetries: Int = 5, delayMillis: Long = 1000) {
        if (context.isFinishing) {
            return;
        }
        var retries = 0

        val requestOptions = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.NONE)//不缓存
            .priority(Priority.HIGH)//最高优先级
            .placeholder(R.drawable.icon_placeholder) // Replace with your placeholder drawable resource
            .error(R.drawable.icon_error) // Replace with your error drawable resource
        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)
//                .transition(DrawableTransitionOptions.withCrossFade())
            .listener(object : RequestListener<Drawable?> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any,
                    target: Target<Drawable?>,
                    isFirstResource: Boolean
                ): Boolean {
                    e?.logRootCauses("GlideImageLoader")

                    retries++
                    if (retries <= maxRetries) {
                        // Retry loading the image
                        imageView.postDelayed({
                            loadImageWithRetry(context, imageUrl, imageView)
                        }, delayMillis)
                        return true
                    }
                    return false

                }

                override fun onResourceReady(
                    resource: Drawable?,
                    model: Any,
                    target: Target<Drawable?>,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    // 加载成功时，取消之前的重试任务
                    imageView.removeCallbacks(null)
                    return false
                }
            })
            .into(imageView)
    }

}