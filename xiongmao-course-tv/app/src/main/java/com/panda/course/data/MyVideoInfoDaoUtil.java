package com.panda.course.data;

import com.panda.course.config.App;
import com.panda.course.config.base.Ktx;
import com.panda.course.dao.DataDao;
import com.panda.course.dao.MyVideoInfo;
import com.panda.course.ext.LogExtKt;
import com.panda.course.util.ToastUtil;

import java.util.List;

/**
 * 对数据库的操作
 */
public class MyVideoInfoDaoUtil {


    /**
     * 往库里插入一条数据  每次插入之前 判断该id是否一经存在库了，如果有就更新
     */
    public static void insertDao(MyVideoInfo myVideoInfo) {
        if (DataDao.getDaoSession() != null) {
            List<MyVideoInfo> myVideoInfos = DataDao.getDaoSession().getMyVideoInfoDao().loadAll();
            for (MyVideoInfo info : myVideoInfos) {
                if (info.getCode().equals(myVideoInfo.getCode())) {
                    myVideoInfo.setId(info.getId());
                    myVideoInfo.setWatch_time_long(info.getWatch_time_long() + 5);//找到已经有的数据，每次+5改变内容 记录已经看了5秒了
                    DataDao.getDaoSession().getMyVideoInfoDao().update(myVideoInfo);
                    return;
                }
            }
            DataDao.getDaoSession().getMyVideoInfoDao().insert(myVideoInfo);
        }
    }


    /**
     * 删除一条数据
     */
    public static void deleteItem(MyVideoInfo myVideoInfo) {
        if (DataDao.getDaoSession() != null) {
            DataDao.getDaoSession().getMyVideoInfoDao().delete(myVideoInfo);
        }
    }

    /**
     * 删除全部的数据
     */
    public static void deleteAll() {
        if (DataDao.getDaoSession() != null) {
            DataDao.getDaoSession().getMyVideoInfoDao().deleteAll();
        }
    }

    /**
     * 获取库里现有的数据
     */
    public static List<MyVideoInfo> getVideoDao() {
        if (DataDao.getDaoSession() != null) {
            return DataDao.getDaoSession().getMyVideoInfoDao().loadAll();
        }
        return null;
    }
}
