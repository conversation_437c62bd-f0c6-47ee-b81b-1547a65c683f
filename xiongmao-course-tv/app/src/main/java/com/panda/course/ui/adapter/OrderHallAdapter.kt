package com.panda.course.ui.adapter

import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.entity.OrderHallTypeDetailsListEntity
import com.panda.course.ext.*
import com.panda.course.widget.focus.MyFocusHighlightHelper

class OrderHallAdapter : BaseQuickAdapter<OrderHallTypeDetailsListEntity, BaseViewHolder>(R.layout.item_order_hall) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var onItemFocus: OnViewFocus? = null


    fun setOnViewFocus(onItemFocus: OnViewFocus?) {
        this.onItemFocus = onItemFocus
    }

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight = MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false)
        }
    }

    override fun convert(holder: BaseViewHolder, item: OrderHallTypeDetailsListEntity) {

        holder.setText(R.id.tv_item_order_hall_name, item.aunt_type)
            .setText(R.id.tv_item_order_hall_salary, resultSalary(item.min_salary, item.max_salary))
            .setText(R.id.tv_item_order_hall_time, "时间：${item.work_demands_time_format}")
            .setText(R.id.tv_item_order_hall_remark, "备注：${item.remark}")
            .setText(R.id.tv_item_order_hall_release_name, "${item.nick_name}")
            .setText(R.id.tv_item_order_hall_release_time, "${item.time_format}")

        val worry = holder.getView<ImageView>(R.id.iv_item_order_hall_worry)

        if (item.is_worry) {
            worry.visible()
        } else {
            worry.gone()
        }

        if (item.recruitment_number?.contains("#") == true) {
            holder.getView<TextView>(R.id.tv_item_order_hall_code).visible()
            val split = item.recruitment_number?.split("#".toRegex())?.toTypedArray()
            val s = split?.get(0)
            if (s != null) {
                if (s.length >= 4) {
                    holder.setText(R.id.tv_item_order_hall_code, "编号：$s")
                } else {
                    // 0 代表前面补充0
                    // 4代表长度为4
                    // d 代表参数为正数型
                    val str = String.format("%04d", Integer.valueOf(s))
                    holder.setText(R.id.tv_item_order_hall_code, "编号：$str")

                }
            }
        } else {
            holder.getView<TextView>(R.id.tv_item_order_hall_code).invisible()
        }


        Glide.with(context).asBitmap().load(item.avatar)
            .apply(RequestOptions.bitmapTransform(CircleCrop()))
            .placeholder(R.drawable.def_pic).error(R.drawable.def_pic)
            .into(holder.getView(R.id.iv_item_order_hall_pic))
        val cardView = holder.getView<CardView>(R.id.row_card_view)

        cardView.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->

            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)

            onItemFocus?.onChangeFocus(hasFocus, getItemPosition(item), v)

            // 设置阴影
            cardView.cardElevation = if (hasFocus) px2dp(20f).toFloat() else px2dp(0f).toFloat()

            cardView.setCardBackgroundColor(
                if (hasFocus) ContextCompat.getColor(
                    context, R.color.black
                ) else ContextCompat.getColor(context, R.color.transparent)
            )

            cardView.radius = if (hasFocus) 16f else 0f
        }
    }

    private fun resultSalary(salaryMin: String?, salaryMax: String?): String {
        return if (!TextUtils.isEmpty(salaryMin) && !TextUtils.isEmpty(salaryMax)) {
            try {
                val salaryShowFormat = getSalaryShowFormat(salaryMin!!.toInt(), salaryMax!!.toInt())
                if (!TextUtils.isEmpty(salaryShowFormat)) {
                    salaryShowFormat
                } else {
                    "面议"
                }
            } catch (e: Exception) {
                "面议"
            }
        } else {
            "面议"
        }
    }


    private fun getSalaryShowFormat(min_expected_salary: Int, max_expected_salary: Int): String {
        var salaryShowFormat = ""
        salaryShowFormat = if (min_expected_salary == 0 && max_expected_salary == 0) {
            ""
        } else if (max_expected_salary == 80000) {
            min_expected_salary.toString() + "以上"
        } else if (min_expected_salary == max_expected_salary) {
            min_expected_salary.toString() + "左右"
        } else if (min_expected_salary != 0 && max_expected_salary == 0) {  //说明是自定义
            min_expected_salary.toString() + "元"
        } else {
            "$min_expected_salary-$max_expected_salary"
        }
        return salaryShowFormat
    }

}