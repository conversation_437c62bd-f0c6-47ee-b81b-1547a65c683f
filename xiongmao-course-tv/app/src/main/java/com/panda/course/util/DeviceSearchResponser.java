package com.panda.course.util;

import android.os.Build;
import android.text.TextUtils;


import com.panda.course.config.ConstantMMVK;
import com.panda.course.ext.AppExtKt;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;

/**
 * 用于响应局域网设备搜索
 */
public class DeviceSearchResponser {

    /**
     * 用于设备搜索的端口
     */
    public static final int DEVICE_SEARCH_PORT = 8100;
    /**
     * 用于接收命令的端口
     */
    public static final int COMMAND_RECEIVE_PORT = 60001;
    /**
     * 设备搜索次数
     */
    public static final int SEARCH_DEVICE_TIMES = 3;
    /**
     * 搜索的最大设备数量
     */
    public static final int SEARCH_DEVICE_MAX = 250;
    /**
     * 接收超时时间
     */
    public static final int RECEIVE_TIME_OUT = 1000;

    /**
     * udp数据包前缀
     */
    public static final int PACKET_PREFIX = '$';
    /**
     * udp数据包类型：搜索类型
     */
    public static final int PACKET_TYPE_SEARCH_DEVICE_REQ = 0x10;
    /**
     * udp数据包类型：搜索应答类型
     */
    public static final int PACKET_TYPE_SEARCH_DEVICE_RSP = 0x11;


    private static SearchRespThread searchRespThread;

    /**
     * 启动响应线程，收到设备搜索命令后，自动响应
     */
    public static void open() {
        if (searchRespThread == null) {
            searchRespThread = new SearchRespThread();
            searchRespThread.start();
        }
    }

    /**
     * 停止响应
     */
    public static void close() {
        if (searchRespThread != null) {
            searchRespThread.destory();
            searchRespThread = null;
        }
    }

    private static class SearchRespThread extends Thread {

        DatagramSocket socket;
        volatile boolean openFlag;

        public void destory() {
            if (socket != null) {
                socket.close();
                socket = null;
            }
            openFlag = false;
        }

        @Override
        public void run() {
            try {
                //指定接收数据包的端口
                socket = new DatagramSocket(DEVICE_SEARCH_PORT);
                byte[] buf = new byte[1024];
                DatagramPacket recePacket = new DatagramPacket(buf, buf.length);
                openFlag = true;
                while (openFlag) {
                    socket.receive(recePacket);
                    //校验数据包是否是搜索包
                    if (verifySearchData(recePacket)) {
                        //发送搜索应答包
                        byte[] sendData = packSearchRespData();
                        DatagramPacket sendPack = new DatagramPacket(sendData, sendData.length, recePacket.getSocketAddress());
                        socket.send(sendPack);
                    }
                }
            } catch (IOException e) {
                destory();
            }
        }

        /**
         * 生成搜索应答数据
         * 协议：$(1) + packType(1) + sendSeq(4) + dataLen(1) + [data]
         * packType - 报文类型
         * sendSeq - 发送序列
         * dataLen - 数据长度
         * data - 数据内容
         *
         * @return
         */
        private byte[] packSearchRespData() {
            byte[] data = new byte[1024];
            int offset = 0;
            data[offset++] = PACKET_PREFIX;
            data[offset++] = PACKET_TYPE_SEARCH_DEVICE_RSP;

            // 添加UUID数据
            byte[] uuid = getUuidData();
            data[offset++] = (byte) uuid.length;
            System.arraycopy(uuid, 0, data, offset, uuid.length);
            offset += uuid.length;
            byte[] retVal = new byte[offset];
            System.arraycopy(data, 0, retVal, 0, offset);

            return retVal;
        }

        /**
         * 校验搜索数据是否符合协议规范
         * 协议：$(1) + packType(1) + sendSeq(4) + dataLen(1) + [data]
         * packType - 报文类型
         * sendSeq - 发送序列
         * dataLen - 数据长度
         * data - 数据内容
         */
        private boolean verifySearchData(DatagramPacket pack) {
            if (pack.getLength() < 6) {
                return false;
            }

            byte[] data = pack.getData();
            int offset = pack.getOffset();
            int sendSeq;
            if (data[offset++] != '$' || data[offset++] != PACKET_TYPE_SEARCH_DEVICE_REQ) {
                return false;
            }
            sendSeq = data[offset++] & 0xFF;
            sendSeq |= (data[offset++] << 8) & 0xFF00;
            sendSeq |= (data[offset++] << 16) & 0xFF0000;
            sendSeq |= (data[offset++] << 24) & 0xFF000000;
            if (sendSeq < 1 || sendSeq > SEARCH_DEVICE_TIMES) {
                return false;
            }

            return true;
        }

        /**
         * 获取设备uuid
         *
         * @return
         */
        private byte[] getUuidData() {
            return (Build.PRODUCT + Build.ID + "+" + getDeviceId()).getBytes();
        }

        private String getDeviceId() {
            if (!TextUtils.isEmpty(MMKVHelper.INSTANCE.decodeString(ConstantMMVK.DEVICE_ID))) {
                return MMKVHelper.INSTANCE.decodeString(ConstantMMVK.DEVICE_ID);
            } else {
                if (AppExtKt.getEventViewModel().getAppUserInfo() != null && AppExtKt.getEventViewModel().getAppUserInfo().getValue() != null) {
                    if (!TextUtils.isEmpty(AppExtKt.getEventViewModel().getAppUserInfo().getValue().getDevice_id())) {
                        return AppExtKt.getEventViewModel().getAppUserInfo().getValue().getDevice_id();
                    }
                }
            }
            return "10086";
        }
    }
}
