package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class RedPaperEntity(
    var list: List<RedPaperListEntity> = emptyList(),
) : Parcelable


@Parcelize
data class RedPaperListEntity(
    var effect_type: String? = null,//1休息 2抢红包 3红包结束
    var background: String? = null,//背景图
    var background_voice: String? = null,//背景音乐
    var countdown_time: String? = null,//倒计时秒数
    var qr_code_content: String? = null,//二维码
    var envelope_total_num: String? = null,//红包总数
    var introduction: String? = null,//红包标题
) : Parcelable