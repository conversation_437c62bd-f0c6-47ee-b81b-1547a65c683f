package com.panda.course.ui.adapter;

import static com.panda.course.ext.CommExtKt.getColorExt;

import android.graphics.Color;
import android.media.Image;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.leanback.widget.FocusHighlight;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.request.RequestOptions;
import com.chad.library.adapter.base.BaseSectionQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.panda.course.R;
import com.panda.course.callback.OnViewFocus;
import com.panda.course.config.ConstantMMVK;
import com.panda.course.config.base.Ktx;
import com.panda.course.entity.CourseDetailInfo;
import com.panda.course.entity.DateSection;
import com.panda.course.entity.GraduateListImgEntity;
import com.panda.course.ext.DensityExtKt;
import com.panda.course.util.RoundedCornersTransform;
import com.panda.course.widget.NineOverShootInterPolator;
import com.panda.course.widget.focus.MyFocusHighlightHelper;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Objects;

public class GraduationMapNewAdapter extends BaseSectionQuickAdapter<DateSection, BaseViewHolder> {

    private MyFocusHighlightHelper.BrowseItemFocusHighlight browseItemFocusHighlight;

    private OnViewFocus mOnViewFocus;

    public void setOnItemFocus(OnViewFocus mOnViewFocus) {
        this.mOnViewFocus = mOnViewFocus;
    }

    private final Animation scaleAnimation;


    public GraduationMapNewAdapter(int layoutResId, int sectionHeadResId, List<DateSection> data) {
        super(sectionHeadResId, data);
        setNormalLayout(layoutResId);
        if (browseItemFocusHighlight == null) {
            browseItemFocusHighlight = new MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_LARGE,
                    false);
        }
        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row);
        scaleAnimation.setInterpolator(new NineOverShootInterPolator());
    }

    @Override
    protected void convertHeader(@NotNull BaseViewHolder holder, @NotNull DateSection dateSection) {
        if (dateSection.getObject() instanceof String) {
            holder.setText(R.id.tv_item_graduation_title, (String) dateSection.getObject());
        }
    }

    @Override
    protected void convert(@NotNull BaseViewHolder holder, DateSection dateSection) {
        GraduateListImgEntity item = (GraduateListImgEntity) dateSection.getObject();

        ImageView bigImageView = holder.getView(R.id.iv_item_big_cover);

        Glide.with(getContext()).load(item.getImg_url())
                .apply(new RequestOptions().transform(new CenterCrop(), new RoundedCornersTransform(bigImageView.getContext(), 10f, true, true, true, true))).dontAnimate()
                .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error).into(bigImageView);

        FrameLayout flLayout = holder.getView(R.id.fl_item_layout);
        TextView tvTips = holder.getView(R.id.tv_item_tips);
        ImageView shadow = holder.getView(R.id.iv_item_shadow);


        CardView cardView = holder.getView(R.id.content_card_view);
        cardView.setOnFocusChangeListener((v, hasFocus) -> {
            browseItemFocusHighlight.onItemFocused(v, hasFocus);

            if (mOnViewFocus != null) {
                mOnViewFocus.onChangeFocus(hasFocus, getItemPosition(dateSection), v);
            }

            flLayout.setVisibility(hasFocus ? View.VISIBLE : View.GONE);
            tvTips.setVisibility(hasFocus ? View.VISIBLE : View.GONE);
            shadow.setVisibility(hasFocus ? View.VISIBLE : View.GONE);

            cardView.setCardElevation(hasFocus ? DensityExtKt.px2dp(20f) : DensityExtKt.px2dp(0f));

            cardView.setCardBackgroundColor(hasFocus ? ContextCompat.getColor(getContext(), R.color.black) : ContextCompat.getColor(getContext(), R.color.transparent));


            cardView.setRadius(hasFocus ? 16f : 0f);


            if (hasFocus) {
                cardView.clearAnimation();
                cardView.startAnimation(scaleAnimation);
            } else {
                cardView.clearAnimation();
            }
        });
    }
}

