package com.panda.course.ui.activity

import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import androidx.core.content.ContextCompat
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.data.CommonAnimationUtil
import com.panda.course.data.CommonDataUtil
import com.panda.course.data.CommonViewUtil
import com.panda.course.databinding.ActivityPropagandaBinding
import com.panda.course.entity.Ad
import com.panda.course.entity.ControllerViewState
import com.panda.course.entity.SourceList
import com.panda.course.ext.*
import com.panda.course.ui.adapter.CommonSpecialFunctionsAdapter
import com.panda.course.ui.adapter.MultipleTypesAdapter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.util.MMKVHelper.decodeParcelable
import com.tencent.liteav.demo.superplayer.SuperPlayerDef
import com.tencent.liteav.demo.superplayer.SuperPlayerModel
import com.tencent.liteav.demo.superplayer.SuperPlayerView
import com.tencent.liteav.demo.superplayer.model.entity.VideoQuality
import com.youth.banner.listener.OnPageChangeListener
import java.util.ArrayList

/**
 * 公司宣传片
 */
class PropagandaActivity : BaseDbActivity<CourseViewModel, ActivityPropagandaBinding>() {

    private val TAG = "PropagandaActivity"

    private var adapter: MultipleTypesAdapter? = null
    private var isLoop = false
    private var isPause = false
    private var mCurrentVideoProgress: Long = -1 //记录当前播放的点
    private var mVideoWatchUploadCount: Int = 0
    private var id: String? = ""

    private var mCommonPosition = 0;
    private var mDefinitionPosition = 0;
    private var mSpeedPosition = 0;

    //记录清晰度、倍速
    private var currentSpeedPosition = 0;
    private var currentDefinitionPosition = 0

    /**
     * 常用功能
     */
    private val focus_common = 1

    /**
     * 清晰度
     */
    private val focus_definition = 3

    /**
     * 倍速
     */
    private val focus_speed = 4

    /**
     * 记录当前的焦点在哪里
     */
    private var currentFocusType: Int = focus_common

    // 控制器的adpater
    val commonFunctionsAdapter = CommonSpecialFunctionsAdapter()
    val commonDefinitionAdapter = CommonSpecialFunctionsAdapter()
    val commonSpeedAdapter = CommonSpecialFunctionsAdapter()

    //用来处理快速点击
    var mLongClickTime: Long = 0
    private var isFastClick = false


    override fun initView(savedInstanceState: Bundle?) {
        if (intent != null && intent.extras != null) {
            if (1 == intent.extras!!.getInt("JumpType")) {
                val mAdData: Ad? = intent.extras!!.getParcelable("AD_INFO")
                if (mAdData == null) {
                    finish()
                    return
                }
                id = mAdData.id
                initBanner(mAdData.source_list)
            }
        } else {
            val mAdData = decodeParcelable(ConstantMMVK.COMPANY_PROPAGANDA, Ad::class.java)
            if (mAdData == null) {
                finish()
                return
            }
            id = mAdData.id
            initBanner(mAdData.source_list)
        }

        mDataBind.tvBack.text = changeFontColor(mDataBind.tvBack.text.toString(), 1, 6, Color.YELLOW, true)

        // 设置控制器的recycler
        mDataBind.gridCommon.initPlayRecyclerCommon(this, commonFunctionsAdapter, CommonDataUtil().propagandaCommonFunctions)

        mDataBind.gridDefinition.initPlayRecyclerCommon(this, commonDefinitionAdapter, CommonDataUtil().videoDefinitions)

        mDataBind.gridSpeed.initPlayRecyclerCommon(this, commonSpeedAdapter, CommonDataUtil().videoSpeed)

        mDataBind.menuCommonGroup.setTargetView(mDataBind.gridCommon)
        mDataBind.menuCommonGroup.setTargetViewFocusChangeListener {
            mDataBind.menuTvCommonTitle.setTextColor(if (it) getColor(R.color.white) else getColor(R.color.gray_1))
        }
        mDataBind.menuDefinitionGroup.setTargetView(mDataBind.gridDefinition)
        mDataBind.menuDefinitionGroup.setTargetViewFocusChangeListener {
            mDataBind.menuTvDefinitionTitle.setTextColor(if (it) getColor(R.color.white) else getColor(R.color.gray_1))
        }
        mDataBind.menuSpeedGroup.setTargetView(mDataBind.gridSpeed)
        mDataBind.menuSpeedGroup.setTargetViewFocusChangeListener {
            mDataBind.menuTvSpeedTitle.setTextColor(if (it) getColor(R.color.white) else getColor(R.color.gray_1))
        }

        mDataBind.superplayer.windowPlayer.vodNextView.gone()
    }

    private fun initBanner(mList: List<SourceList>?) { // 处理一下是否单个视频 循环播放
        if (mList == null || mList.size <= 0) {
            finish()
            return
        }
        if (mList != null && mList.size == 1 && mList[0].type == 2) {
            isLoop = true
            playVideo(mList[0], isLoop)
            mDataBind.superplayer.visibility = View.VISIBLE
            initPlayerObserver(0, id)
        }
        ("列表大小" + mList!!.size + "，是否循环 " + isLoop).logE(TAG)
        adapter = MultipleTypesAdapter(this, mList)
        mDataBind.banner.setAdapter(adapter).addBannerLifecycleObserver(this).setUserInputEnabled(false) // 禁止手动滑动
            .setLoopTime(4000) //轮播间隔时间
            .setScrollTime(1000) //轮播滑动的时间
            //                .setPageTransformer(new AlphaPageTransformer());
            //                .setPageTransformer(new DepthPageTransformer());
            //                .setPageTransformer(new RotateDownPageTransformer());
            //                .setPageTransformer(new RotateUpPageTransformer());
            //                .setPageTransformer(new RotateYTransformer());
            //                .setPageTransformer(new ScaleInTransformer());
            //                .setPageTransformer(new ZoomOutPageTransformer());
            .addOnPageChangeListener(object : OnPageChangeListener {
                override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                    "onPageScrolled = $position".logE(TAG)
                    stopVideo(position, id)
                }

                override fun onPageSelected(position: Int) {}
                override fun onPageScrollStateChanged(state: Int) {}
            })
    }

    /**
     * 处理视频方案
     *
     * @param position
     */
    private fun stopVideo(position: Int, id: String? = "") {
        val (type) = mDataBind.banner.adapter.getData(position) as SourceList
        if (type == 1) {
            mDataBind.superplayer.onPause()
            mDataBind.superplayer.visibility = View.GONE
            "图片".logE(TAG)
        } else if (type == 2) {
            "视频 ".logE(TAG)
            mDataBind.superplayer.visibility = View.VISIBLE
            if (!TextUtils.isEmpty(adapter!!.getData(position).source_url)) {
                playVideo(adapter!!.getData(position), false)
                initPlayerObserver(position, id)
            }
        }
    }

    /**
     * 播放视频
     */
    private fun playVideo(data: SourceList?, loop: Boolean) {

        val videoQualityList = ArrayList<VideoQuality>()
        val multiURLs = ArrayList<SuperPlayerModel.SuperPlayerURL>()

        val superPlayerModelV3 = SuperPlayerModel()
        superPlayerModelV3.title = ""
        superPlayerModelV3.url = data?.source_url
        superPlayerModelV3.appId = ConstantMMVK.DEFAULT_APPID

        if (data?.source_url != null) {
            videoQualityList.add(VideoQuality(1, "高清", data.hd_video_url_list?.hd))
            multiURLs.add(SuperPlayerModel.SuperPlayerURL(data.hd_video_url_list?.hd, "高清"))

            if (!TextUtils.isEmpty(data.hd_video_url_list?.standard)) {
                multiURLs.add(
                    SuperPlayerModel.SuperPlayerURL(
                        data.hd_video_url_list?.standard, "标清"
                    )
                )
                videoQualityList.add(VideoQuality(0, "标清", data.hd_video_url_list?.standard))
            } else {
                multiURLs.add(SuperPlayerModel.SuperPlayerURL(data.hd_video_url_list?.hd, "标清"))
                videoQualityList.add(VideoQuality(0, "标清", data.hd_video_url_list?.hd))
            }
        } else {
            videoQualityList.add(VideoQuality(1, "高清", data?.source_url))
            multiURLs.add(SuperPlayerModel.SuperPlayerURL(data?.source_url, "高清"))

            multiURLs.add(SuperPlayerModel.SuperPlayerURL(data?.source_url, "标清"))
            videoQualityList.add(VideoQuality(0, "标清", data?.source_url))
        }


        superPlayerModelV3.multiURLs = multiURLs
        superPlayerModelV3.videoQualityList = videoQualityList
        mDataBind.superplayer.setLoop(loop)
        mDataBind.superplayer.playWithModel(superPlayerModelV3)
    }

    private fun initPlayerObserver(position: Int, id: String? = "") {
        mDataBind.banner.stop()
        mDataBind.superplayer.setPlayerViewCallback(object : SuperPlayerView.OnSuperPlayerViewCallback {

            /**
             * 开始全屏播放
             */
            override fun onStartFullScreenPlay() {
            }

            /**
             * 结束全屏播放
             */
            override fun onStopFullScreenPlay() {
            }

            /**
             * 点击悬浮窗模式下的x按钮
             */
            override fun onClickFloatCloseBtn() {
            }

            /**
             * 点击小播放模式的返回按钮
             */
            override fun onClickSmallReturnBtn() {
            }

            /**
             * 开始悬浮窗播放
             */
            override fun onStartFloatWindowPlay() {
            }

            /**
             * 开始播放回调
             */
            override fun onPlaying() {
                adapter?.getData(position)?.state = 1

            }

            /**
             * 播放暂停
             */
            override fun onPause() {
                adapter?.getData(position)?.state = 2
                mVideoWatchUploadCount = 0
            }

            /**
             * 播放结束
             */
            override fun onPlayEnd() {
                adapter?.getData(position)?.state = 0
                if (!isLoop) {
                    mDataBind.banner.start()
                }
            }

            /**
             * 播放下一节
             */
            override fun playNext() {

            }

            /**
             * 当播放失败的时候回调
             *
             * @param code
             */
            override fun onError(code: Int) {
            }

            /**
             * 更新进度回调
             *
             * @param current
             * @param duration
             */
            override fun updateVideoProgress(current: Long, duration: Long) {
                if (current == duration) { //当前的时间点与总时间点一致，说明播放完成了，没必须继续了
                    return
                }
                if (mCurrentVideoProgress == current) {
                    return
                } else {
                    mCurrentVideoProgress = current
                }
                if (current > 0 && mDataBind.superplayer.playerState == SuperPlayerDef.PlayerState.PAUSE) { //暂停的时候，不去触发打点信息
                    mVideoWatchUploadCount = 0
                    return
                }
                mVideoWatchUploadCount += 1
                if (mVideoWatchUploadCount >= 5) {
                    mViewModel.uploadPropagandaStoreVideoStatistics(id)
                    mVideoWatchUploadCount = 0 // 归零
                }
            }

            /**
             * 下载页面，点击了缓存列表按钮
             */
            override fun onShowCacheListClick() {
            }

            /**
             * 切换清晰度
             */
            override fun onChangeVideoQuality() {
            }
        })

        eventViewModel.appCommonViewState.observe(this, androidx.lifecycle.Observer{
            var mStr = ""
            if (it != null) {
                if (it.channelState == ControllerViewState.VIEW_CONTROLLER_DEFINITION) { //清晰度
                    if (mDefinitionPosition == 0) {
                        mStr = "已成功切换成超清"
                        mDataBind.superplayer.windowPlayer.updateVodDefinition("超清")
                    } else {
                        mStr = "已成功切换成高清"
                        mDataBind.superplayer.windowPlayer.updateVodDefinition("高清")
                    }
                    mDataBind.superviewTvTips.text = changeFontColor(
                        content = mStr,
                        startIndex = 6,
                        endIndex = mStr.length,
                        color = ContextCompat.getColor(this, R.color.green)
                    )
                    mDataBind.superviewTvTips.text.logD(TAG)
                    CommonViewUtil().startTipsAnimation(mDataBind.superviewTvTips)
                } else if (it.channelState == ControllerViewState.VIEW_CONTROLLER_SPEED) { //速度
                    if (mDataBind.superplayer.videoRete == 1.0f) {
                        mDataBind.superviewTvTips.text = changeFontColor(
                            content = "已切换为正常播放倍速",
                            startIndex = 4,
                            endIndex = 10,
                            color = ContextCompat.getColor(this, R.color.green)
                        )
                    } else {
                        mDataBind.superviewTvTips.text = changeFontColor(
                            content = "已切换为${mDataBind.superplayer.videoRete}倍，若倍速未生效或卡顿，请切换至1.0倍",
                            startIndex = 4,
                            endIndex = if (mDataBind.superplayer.videoRete == 1.25f) 9 else 8,
                            color = ContextCompat.getColor(this, R.color.green)
                        )
                    }

                    mDataBind.superplayer.windowPlayer.updateVodSpeed("${mDataBind.superplayer.videoRete}倍")
                    mDataBind.superviewTvTips.text.logD(TAG)
                    CommonViewUtil().startTipsAnimation(mDataBind.superviewTvTips)
                }
            }
        })
    }

    override fun initObserver() { // 常用功能
        commonFunctionsAdapter.setOnItemClickListener { _, _, position ->
            when (position) {
                0 -> {
                    mDataBind.menuCommonGroup.hideTargetView()
                    controllerScrollViewDown()
                    CommonViewUtil().requestFocusDelay(commonDefinitionAdapter, currentDefinitionPosition)
                }
                1 -> {
                    mDataBind.menuCommonGroup.hideTargetView()
                    mDataBind.menuDefinitionGroup.hideTargetView()
                    controllerScrollViewDown()
                    CommonViewUtil().requestFocusDelay(commonSpeedAdapter, currentSpeedPosition)
                }
            }
        }

        // 常用功能 - 倍速
        commonSpeedAdapter.setOnItemClickListener { _, _, position -> // 倍速
            currentSpeedPosition = position
            mDataBind.superplayer.setVideoRete(position) //调整倍速
            CommonViewUtil().changeAdapterSelected(
                commonFunctionsAdapter,
                mDataBind.flController,
                commonSpeedAdapter,
                ControllerViewState.VIEW_CONTROLLER_SPEED,
                position
            )
            onResume()
        }

        // 常用功能 - 清晰度
        commonDefinitionAdapter.setOnItemClickListener { _, _, position -> // 清晰度
            currentDefinitionPosition = position

            if (mDataBind.superplayer.currentSuperPlayerModel.videoQualityList.size > 0) {
                if (!TextUtils.isEmpty(mDataBind.superplayer.currentSuperPlayerModel.videoQualityList[position].url)) {
                    mDataBind.superplayer.changeVideoQuality(mDataBind.superplayer.currentSuperPlayerModel.videoQualityList[position])
                }
            }

            CommonViewUtil().changeAdapterSelected(
                commonFunctionsAdapter,
                mDataBind.flController,
                commonDefinitionAdapter,
                ControllerViewState.VIEW_CONTROLLER_DEFINITION,
                position
            )
            onResume()
        }

        commonFunctionsAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mCommonPosition = position
                currentFocusType = focus_common
                mDataBind.scrollViewController.scrollTo(0, 0)
                defSelectVisibleView()
            }
        }

        commonDefinitionAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mDefinitionPosition = position
                currentFocusType = focus_definition
                controllerScrollViewDown()
            }
        }
        commonSpeedAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mSpeedPosition = position
                currentFocusType = focus_speed
                controllerScrollViewDown()
            }
        }
    }

    /*默认选中显示的View*/
    private fun defSelectVisibleView() {
        mDataBind.menuCommonGroup.visibleTargetView()
        mDataBind.menuSpeedGroup.visibleTargetView()
        mDataBind.menuDefinitionGroup.visibleTargetView()
    }

    override fun onResume() {
        super.onResume()
        mDataBind.superplayer.onResume()
    }

    override fun onStart() {
        super.onStart()
        mDataBind.banner.start()
    }

    override fun onStop() {
        super.onStop()
        mDataBind.banner.stop()
        mDataBind.superplayer.onPause()
    }

    override fun onPause() {
        super.onPause()
        mDataBind.superplayer.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        "onDestroy".logE(TAG)
        mDataBind.banner.destroy()
        mDataBind.superplayer.release()
        mDataBind.superplayer.resetPlayer()
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {

            KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_BACK -> {
                if (mDataBind.flController.visibility == View.VISIBLE) {
                    hideControllerView()
                    return true
                }
                finish()
            }
            KeyEvent.KEYCODE_DPAD_UP -> {
                if (mDataBind.flController.visibility == View.VISIBLE && currentFocusType == focus_common) {
                    hideControllerView()
                    return true
                }
            }
            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (mDataBind.flController.visibility == View.GONE && mDataBind.superplayer.visibility == View.VISIBLE) {
                    mDataBind.flController.visible()
                    mDataBind.flController.post {
                        CommonViewUtil().controllerViewAction(
                            true,
                            mDataBind.flController,
                            commonFunctionsAdapter.getViewByPosition(0, R.id.rl_common_item_view),
                            ControllerViewState.VIEW_CONTROLLER_COMMON
                        )
                    }
                }
            }
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER -> {
                if (mDataBind.superplayer.visibility == View.VISIBLE) {
                    if (mDataBind.superplayer.playerState == SuperPlayerDef.PlayerState.PLAYING) {
                        mDataBind.superplayer.onPause()
                    } else if (mDataBind.superplayer.playerState == SuperPlayerDef.PlayerState.PAUSE) {
                        mDataBind.superplayer.onResume()
                    }
                } else {
                    isPause = if (isPause) {
                        mDataBind.banner.start()
                        false
                    } else {
                        mDataBind.banner.stop()
                        true
                    }
                }
            }

            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (mDataBind.flController.visibility == View.VISIBLE) {
                    return selectCommonView(false)
                }
                if (mDataBind.superplayer.visibility == View.GONE) {
                    return true
                }
                if (event.repeatCount > 1) {
                    isFastClick = true
                    mDataBind.superplayer.seekFastTo(true, event.repeatCount)
                } else {
                    isFastClick = false
                    mDataBind.superplayer.seekToOne(
                        true, (mDataBind.superplayer.vodProgress - 10).toInt()
                    )
                }
                return true
            }
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (mDataBind.flController.visibility == View.VISIBLE) {
                    return selectCommonView(true)
                }
                if (mDataBind.superplayer.visibility == View.GONE) {
                    return true
                }
                if (event.repeatCount > 1) {
                    isFastClick = true
                    mDataBind.superplayer.seekFastTo(false, event.repeatCount)
                } else {
                    isFastClick = false
                    mDataBind.superplayer.seekToOne(
                        false, (mDataBind.superplayer.vodProgress + 10).toInt()
                    )
                }
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }


    override fun onKeyUp(keyCode: Int, event: KeyEvent): Boolean {
        if (mDataBind.superplayer.visibility == View.GONE) {
            return true
        }
        if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
            val diff = System.currentTimeMillis() - mLongClickTime
            if (diff > 2000 && isFastClick) { // 长按
                isFastClick = false
                mDataBind.superplayer.seekToFast(mDataBind.superplayer.fastFinalProgress)
                mDataBind.superplayer.fastCurrentProgress = 0
                mDataBind.superplayer.fastFinalProgress = 0
                onResume()
                return true
            }
        }
        return super.onKeyUp(keyCode, event)
    }

    private fun hideControllerView() {
        mDataBind.flController.gone()
    }

    /**
     * 控制器滑动到最底部
     */
    private fun controllerScrollViewDown() {
        mDataBind.scrollViewController.post {
            mDataBind.scrollViewController.smoothScrollBy(0, mDataBind.scrollViewController.measuredHeight)
        }
    }

    /**
     * 多功能界面下区分 左右点击
     */
    private fun selectCommonView(action: Boolean): Boolean {
        when (currentFocusType) {
            focus_common -> { //判断是否到了边界
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonFunctionsAdapter, mCommonPosition, action
                )
            }
            focus_definition -> {
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonDefinitionAdapter, mDefinitionPosition, action
                )
            }
            focus_speed -> {
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonSpeedAdapter, mSpeedPosition, action
                )
            }
        }
        return false
    }
}