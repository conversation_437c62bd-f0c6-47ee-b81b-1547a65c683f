package com.panda.course.dao;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Generated;

@Entity
public class MyVideoInfo {
    @Id(autoincrement = true)
    private Long id;

    private String code;//记录的code
    private long watch_time_long;//记录当前观看的时常
    private long watch_time;//记录当前观看的时刻
    private String ts;//时间戳
    private String watch_date;//观看的日期 格式2022-12-15
    private String version;//记录版本号
    @Generated(hash = 1023386070)
    public MyVideoInfo(Long id, String code, long watch_time_long, long watch_time,
            String ts, String watch_date, String version) {
        this.id = id;
        this.code = code;
        this.watch_time_long = watch_time_long;
        this.watch_time = watch_time;
        this.ts = ts;
        this.watch_date = watch_date;
        this.version = version;
    }
    @Generated(hash = 48029299)
    public MyVideoInfo() {
    }
    public Long getId() {
        return this.id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public String getCode() {
        return this.code;
    }
    public void setCode(String code) {
        this.code = code;
    }
    public long getWatch_time_long() {
        return this.watch_time_long;
    }
    public void setWatch_time_long(long watch_time_long) {
        this.watch_time_long = watch_time_long;
    }
    public long getWatch_time() {
        return this.watch_time;
    }
    public void setWatch_time(long watch_time) {
        this.watch_time = watch_time;
    }
    public String getTs() {
        return this.ts;
    }
    public void setTs(String ts) {
        this.ts = ts;
    }
    public String getWatch_date() {
        return this.watch_date;
    }
    public void setWatch_date(String watch_date) {
        this.watch_date = watch_date;
    }
    public String getVersion() {
        return this.version;
    }
    public void setVersion(String version) {
        this.version = version;
    }


}
