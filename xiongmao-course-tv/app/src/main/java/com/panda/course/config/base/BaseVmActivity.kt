package com.panda.course.config.base

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.lifecycle.ViewModelProvider
import com.kingja.loadsir.core.LoadService
import com.kingja.loadsir.core.LoadSir
import com.panda.course.R
import com.panda.course.config.App
import com.panda.course.ext.*
import com.panda.course.ext.dismissLoadingExt
import com.panda.course.ext.getVmClazz
import com.panda.course.ext.showLoadingExt
import com.panda.course.network.LoadStatusEntity
import com.panda.course.network.LoadingDialogEntity
import com.panda.course.network.LoadingType
import com.panda.course.receiver.NetBroadcastReceiver
import com.panda.course.state.BaseEmptyCallback
import com.panda.course.state.BaseErrorCallback
import com.panda.course.state.BaseLoadingCallback
import com.panda.course.util.NetworkUtil
import com.umeng.analytics.MobclickAgent


abstract class BaseVmActivity<VM : BaseViewModel> : BaseInitActivity(), BaseIView {

    //界面状态管理者
    lateinit var uiStatusManger: LoadService<*>

    //当前Activity绑定的 ViewModel
    lateinit var mViewModel: VM

    //toolbar 这个可替换成自己想要的标题栏
    private var mTitleBarView: View? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_base)
        addActivity(this) //生成ViewModel
        mViewModel = createViewModel() //初始化 status View
        initStatusView(savedInstanceState) //注册界面响应事件
        addLoadingUiChange(mViewModel) //初始化绑定observer
        initObserver() //初始化请求成功方法
        onRequestSuccess() //初始化绑定点击方法
        onBindViewClick() // 注册网络监听
    }

    private fun initStatusView(savedInstanceState: Bundle?) {
        findViewById<FrameLayout>(R.id.baseContentView).addView(
            if (dataBindView == null) LayoutInflater.from(this).inflate(layoutId, null) else dataBindView
        )
        uiStatusManger = LoadSir.getDefault()
            .register(if (getLoadingView() == null) findViewById<FrameLayout>(R.id.baseContentView) else getLoadingView()!!) {
                onLoadRetry()
            }
        findViewById<FrameLayout>(R.id.baseContentView).post {
            initView(savedInstanceState)
        }
    }

    /**
     * 初始化view
     */
    abstract fun initView(savedInstanceState: Bundle?)

    /**
     * 创建观察者
     */
    open fun initObserver() {}

    /**
     * 创建viewModel
     */
    private fun createViewModel(): VM {
        return ViewModelProvider(this).get(getVmClazz(this))
    }

    /**
     * 是否隐藏 标题栏 默认显示
     */
    open fun showToolBar(): Boolean {
        return true
    }

    /**
     * 点击事件方法 配合setOnclick()拓展函数调用，做到黄油刀类似的点击事件
     */
    open fun onBindViewClick() {}

    /**
     * 注册 UI 事件 监听请求时的回调UI的操作
     */
    fun addLoadingUiChange(viewModel: BaseViewModel) {
        viewModel.loadingChange.run {
            loading.observe(this@BaseVmActivity) {
                when (it.loadingType) {
                    LoadingType.LOADING_DIALOG -> {
                        if (it.isShow) {
                            showLoading(it)
                        } else {
                            dismissLoading(it)
                        }
                    }
                    LoadingType.LOADING_CUSTOM -> {
                        if (it.isShow) {
                            showCustomLoading(it)
                        } else {
                            dismissCustomLoading(it)
                        }
                    }
                    LoadingType.LOADING_XML -> {
                        if (it.isShow) {
                            showLoadingUi()
                        }
                    }
                }
            }
            showEmpty.observe(this@BaseVmActivity) {
                onRequestEmpty(it)
            }
            showError.observe(this@BaseVmActivity) { //如果请求错误 并且loading类型为 xml 那么控制界面显示为错误布局
                if (it.loadingType == LoadingType.LOADING_XML) {
                    showErrorUi(it.errorMessage)
                }
                onRequestError(it)
            }
            showSuccess.observe(this@BaseVmActivity) {
                showSuccessUi()
            }
        }
    }

    /**
     * 请求列表数据为空时 回调
     * @param loadStatus LoadStatusEntity
     */
    override fun onRequestEmpty(loadStatus: LoadStatusEntity) {
        showEmptyUi()
    }

    /**
     * 请求接口失败回调，如果界面有请求接口，需要处理错误业务，请实现它 乳沟不实现那么 默认吐司错误消息
     * @param loadStatus LoadStatusEntity
     */
    override fun onRequestError(loadStatus: LoadStatusEntity) {
//        loadStatus.errorMessage.toast()
    }

    /**
     * 请求成功的回调放在这里面 没干啥就是取了个名字，到时候好找
     */
    override fun onRequestSuccess() {}

    /**
     * 空界面，错误界面 点击重试时触发的方法，如果有使用 状态布局的话，一般子类都要实现
     */
    override fun onLoadRetry() {}

    /**
     * 显示 成功状态界面
     */
    override fun showSuccessUi() {
        uiStatusManger.showSuccess()
    }

    /**
     * 显示 错误 状态界面
     * @param errMessage String
     */
    override fun showErrorUi(errMessage: String) {
        uiStatusManger.showCallback(BaseErrorCallback::class.java)
    }

    /**
     * 显示 空数据 状态界面
     */
    override fun showEmptyUi() {
        uiStatusManger.showCallback(BaseEmptyCallback::class.java)
    }

    /**
     * 显示 loading 状态界面
     */
    override fun showLoadingUi() {
        uiStatusManger.showCallback(BaseLoadingCallback::class.java)
    }

    /**
     * 显示自定义loading 在请求时 设置 loadingType类型为LOADING_CUSTOM 时才有效 可以根据setting中的requestCode判断
     * 具体是哪个请求显示该请求自定义的loading
     * @param setting LoadingDialogEntity
     */
    override fun showCustomLoading(setting: LoadingDialogEntity) {
        showLoadingExt(setting.loadingMessage)
    }

    /**
     * 隐藏自定义loading 在请求时 设置 loadingType类型为LOADING_CUSTOM 时才有效 可以根据setting中的requestCode判断
     * 具体是哪个请求隐藏该请求自定义的loading
     * @param setting LoadingDialogEntity
     */
    override fun dismissCustomLoading(setting: LoadingDialogEntity) {
        dismissLoadingExt()
    }

    override fun showLoading(setting: LoadingDialogEntity) {
        showLoadingExt(setting.loadingMessage)
    }

    override fun dismissLoading(setting: LoadingDialogEntity) {
        dismissLoadingExt()
    }

    override fun onDestroy() {
        super.onDestroy()
        dismissLoadingExt()
        removeActivity(this)
        ("$currentActivity").logD("生命周期")
    }

    override fun finish() {
        super.finish()
        dismissLoadingExt()
        ("$currentActivity").logD("生命周期")
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        ("$currentActivity").logD("生命周期")
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        ("$currentActivity").logD("生命周期")
    }

    override fun onRestart() {
        super.onRestart()
        ("$currentActivity").logD("生命周期")
    }

    override fun onStart() {
        super.onStart()
        ("$currentActivity").logD("生命周期")
    }

    override fun onResume() {
        super.onResume()
        ("$currentActivity").logD("生命周期")
        MobclickAgent.onResume(this)
    }

    override fun onPause() {
        super.onPause()
        ("$currentActivity").logD("生命周期")
        MobclickAgent.onPause(this)
    }

    override fun onStop() {
        super.onStop()
        ("$currentActivity").logD("生命周期")
    }


}