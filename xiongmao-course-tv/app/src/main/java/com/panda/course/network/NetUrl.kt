package com.panda.course.network

import rxhttp.wrapper.annotation.DefaultDomain
import rxhttp.wrapper.annotation.Domain


object NetUrl {

    /**
     * 用来记录当前是那个环境
     * @see 0 正式环境
     * @see 1 测试环境
     * @see 2 开发环境
     */
    open var DEF_ADDRESS = 0

    @JvmField
    @DefaultDomain
    open var DEF_URL = "https://appapi.jiazhengye.cn/"
//    open var DEF_URL = "https://appapi-test.jiazhengye.cn/"
//    open var DEF_URL = "https://appapi-dev.jiazhengye.cn/"


    // 改正式域名的情况下，要把IM的切成线上


    // 其他域名
    @Domain(name = "monitor", className = "monitor")
    const val MONITOR_URL = "https://monitor.jiazhengye.cn/"

    @Domain(name = "BASE_URL") //正式域名
    const val BASE_URL = "https://appapi.jiazhengye.cn/"

    @Domain(name = "DEV_TEST_URL") //测试域名
    const val DEV_TEST_URL = "https://appapi-test.jiazhengye.cn/"

    @Domain(name = "DEV_URL") //开发域名
    const val DEVELOPERS_URL = "https://appapi-dev.jiazhengye.cn/"


    //获取二维码
    const val QR_CODE = "/tv/getLoginQrcode"


    //刷新二维码状态
    const val LOGIN_INFO = "/tv/getUserLoginInfo"

    //获取用户数据
    const val GET_USER_INFO = "/tvV3/user/getCurrentUserInfo"

    //获取观看记录等等
    const val RECOMMEND = "/tv/index"

    // 录播课
    const val COURSE_LIST = "/tv/findRecordCourseList"

    // 录播课详情
    const val GET_COURSE_DETAIL = "/tv/getRecordCourseDetail"

    //退出登录
    const val LOG_OUT = "/tv/logout"

    // 更新版本
    const val GET_VERSION = "/tv/getVersion"

    //上传时长
    const val UPLOAD_PROGRESS = "/tv/updateVideoWatchLog"

    //校验设备有效期
    const val CHECK_DEVICE = "/tv/checkDevice"

    //获取分类列表
    const val ROW_LIST = "/tvV3/course/getCategoryList"

    //全国排行榜单接口
    const val TOP_LIST = "/tv/findCountryTopList"

    //门店榜单排行
    const val STORE_TOP_LIST = "/tv/findStoreTopList"

    //统计观看时长
    const val COURSE_STAT = "/tv/courseStat"

    //统计播放器加载时长
    const val VIDEO_STAT = "/training/courses"

    //课程结业考试二维码
    const val EXAM_QR_CODE = "/tv/getFinalExamQrCode"

    //获取师生互动二维码
    const val TEACHER_QR_CODE = "/tv/getTeacherInteractiveQrCode"

    //课程练习生成二维码
    const val EXERCISE_QR_CODE = "/tv/getExerciseQrCode"

    //打点信息
    const val FIND_VIDEO_DOT = "/tv/findVideoDotList"

    // 签到二维码
    const val SIGN_QR_CODE = "/tv/getSignQrCode"

    //获取练习列表
    const val DOT_EXERCISE_LIST = "/tv/findVideoDotExerciseList"

    //获取练习列表
    const val FIND_SIGN_LIST = "/tv/findSignList"

    //毕业表彰列表
    const val GRADUATION_LIST = "/tvV3/graduationPhoto/getList"

    //获取设备
    const val DEVICE_EXPIREDATE = "/tv/getDeviceExpireDate"

    //获取IM
    const val GET_IM_INFO = "/tv/getImInfo"

    //获取分享课程二维码
    const val SHARE_COURSE_QR_CODE = "/tvV3/getShareCourseQrCode"

    //观看时长统计接口
    const val STORE_VIDEO = "/tvV3/statStoreVideo"

    //获取缓存视频下载列表
    const val CACHE_DOWNLOADER_LIST = "/tvV3/video/getDownloadList"

    //预置所有打点信息
    const val CACHE_ALL_DOT = "/tvv3/course/getAllVideoDot"

    const val CACHE_ALL_DOT_RESOURCE = "/tvV3/video/getDotEffectList"

    //上报视频的状态
    const val UPLOAD_VIDEO_STATE = "/tvV3/video/saveDownloadSchedule"

    //获取是否有惊喜活动
    const val SURPRISE_ACTIVITY_AD = "/tvV3/activityAd/getOne"

    //获取是否有惊喜活动的列表
    const val SURPRISE_ACTIVITY_LIST = "/tvV3/activity/getReceiveRewardList"

    //获取是否有惊喜活动的界面内容
    const val SURPRISE_ACTIVITY_ALL = "/tvV3/activity/getAll"

    //查询客户是否有看客奖励
    const val TODAY_IS_FINISHED = "/tvV3/storeActivityTask/todayIsFinished"

    //获取课程表
    const val GET_SCHEDULE_IMG = "/tvV3/course/getSchedule"

    //获取测试网速的节点
    const val NET_WORK_SPEED = "/tvV3/netSpeed/test"

    //OTA升级
    const val OTA_UPDATE = "/tvv3/version/getOtaOne"

    //获取该课程的知识点
    const val KNOWLEDGE_POINTS = "/tvv3/video/getKnowledgeAll"

    //获取消息未读数量
    const val UN_READ_NOTICE = "/tvV3/notice/getUnReadTotal"

    //已读消息数量
    const val READ_SEND_NOTICE = "/tvV3/notice/setRead"

    //获取课程互动列表
    const val GET_POST_LIST = "/tvV3/course/getPostList"

    //获取消息列表
    const val GET_NOTICE_LIST = "/tvV3/notice/getList"

    //查看是否接受消息
    const val CHECK_ACCEPT_NOTICE = "/tvV3/notice/checkAcceptNotice"

    //更新设备版本信息
    const val UPDATE_DEVICE_VERSION = "/tvV3/version/update"

    //离线观看上报的接口
    const val OFFLINE_COURSE_WATCH = "/tvV3/stat/offlineCourseWatchTime"

    //宣传片上传时刻
    const val STORE_WATCH_TIME = "/tvV3/stat/storeMediaWatchTime"

    //获取所有岗位测评技能
    const val AUNT_SKILL_ALL = "/tvV3/auntSkillPost/getAll"

    //获取所有岗位测评技能
    const val SKILL_RECORD_LIST = "/tvV3/auntSkillPost/getRecordList"

    //获取技能测评二维码
    const val SKILL_RECORD_QR_CODE = "/tvV3/auntSkillPost/getShareQrCode"

    //获取反馈信息内容
    const val GET_FEEDBACK_SETTING = "/tvv3/feedback/getSetting"

    //获取帮助列表
    const val HELP_QUESTION_LIST = "/tvv3/help/getAll"

    //反馈提交
    const val FEED_BACK_CREATE = "/tvv3/feedback/create"

    //获取七牛token
    const val GET_QINNIU_TOKEN = "/data/getQiniuToken"

    //获取红包特效
    const val GET_RED_PAPER = "/tvV3/aiRedEnvelope/getEffectSource"

    //获取红包的升级提醒
    const val GET_RED_PAPER_SETTING = "/tvV3/aiRedEnvelope/getSetting"

    //发红包接口
    const val RED_PAPER_CREATE = "/tvV3/aiRedEnvelope/create"

    //业态在线续费
    const val ONLINE_RENEWAL = "/tvV3/device/getAllReNewProduct"

    //业态续费二维码
    const val ONLINE_RENEWAL_QR_CODE = "/tvV3/device/getReNewQrCodeUrl"

    //遥控器答题提交
    const val SUBMIT_EXERCISE = "/tvV3/device/submitExercise"

    //获取雪碧图
    const val GET_IMAGE_SPRITE = "/tvV3/video/getImageSpriteInfo"

    //订单大厅-分类列表
    const val GET_ORDER_HALL_ALL = "/tvV3/recruitmentOrder/getAllType"

    //订单大厅-详情
    const val GET_ORDER_HALL_DETIATIL = "/tvV3/recruitmentOrder/getList"

    //优秀排行榜
    const val EXCELLENT_RANK = "/tvV3/stat/getStudentExerciseMonthRank"

    //门店结业练习排行榜接口
    const val EXAM_STORE_LIST = "/tv/findStoreTopList"

    //获取某个学生的结业考试结果
    const val ONE_FINAL_EXAM = "/tvV3/exercise/getOneFinalExam"


    //直播课列表
    const val LIVE_LIST = "/tvV3/aiLiveCourse/getList"

    //直播课时列表
    const val LIVE_LIST_HOURS = "/tvV3/aiLiveCourse/getVideoList"

    //获取 直播二维码
    const val GET_LIVE_QR_CODE = "/tvV3/aiLiveCourse/getJoinRoomQrCode"

    //获取弹幕
    const val GET_BARRAGE = "/tvV3/aiLiveCourse/getBarrageList"

    //获取参加码
    const val JOIN_CODE = "/tvV3/aiLiveCourse/getJoinCode"

    //获取pdf 课节
    const val HOUR_DETAILS = "/tvv3/course/getCoursewareList"

    //课件row
    const val CLASS_VIDEO_LIST = "/tvV3/video/getCoursewareVideoList"
}
