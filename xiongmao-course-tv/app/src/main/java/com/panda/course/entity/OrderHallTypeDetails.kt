package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class OrderHallTypeDetailsEntity(
    var list: List<OrderHallTypeDetailsListEntity> = emptyList(),
) : Parcelable


@Parcelize
data class OrderHallTypeDetailsListEntity(
    var uuid: String? = null,
    var aunt_type: String? = null,
    var remark: String? = null,
    var min_salary: String? = null,
    var avatar: String? = null,
    var max_salary: String? = null,
    var create_time: String? = null,
    var recruitment_number: String? = null,
    var salary_unit: String? = null,
    var work_demands_time_format: String? = null,
    var time_format: String? = null,
    var nick_name: String? = null,
    var is_worry: Boolean = false,
    var user_mobile: String? = null,
) : Parcelable