package com.panda.course.ui.adapter

import android.graphics.Color
import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight.ZOOM_FACTOR_XSMALL
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.LiveCourseEntity
import com.panda.course.ext.getColorExt
import com.panda.course.ext.px2dp
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper
import com.panda.course.widget.focus.MyFocusHighlightHelper.ZOOM_FACTOR_XXSMALL

class LiveListAdapter : BaseQuickAdapter<LiveCourseEntity, BaseViewHolder>(R.layout.item_live_list) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var onItemFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null

    fun setOnViewFocus(onItemFocus: OnViewFocus?) {
        this.onItemFocus = onItemFocus
    }

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(ZOOM_FACTOR_XXSMALL, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()
    }

    override fun convert(holder: BaseViewHolder, item: LiveCourseEntity) {

        val aiStatus = holder.getView<TextView>(R.id.tv_ai_status)

        val title = holder.getView<TextView>(R.id.tv_item_live_title)
        val sub = holder.getView<TextView>(R.id.tv_item_live_sub)
        val subTimer = holder.getView<TextView>(R.id.tv_item_live_timer)

        val layout = holder.getView<RelativeLayout>(R.id.rl_item_live_list_layout)

        val cardView = holder.getView<CardView>(R.id.card_item_view_class_hours)

        val coverImageView = holder.getView<ImageView>(R.id.item_iv_course_cover)

        title.text = item.title_main
        sub.text = item.course_introduction
        subTimer.text = "直播时间：${item.examination_start_time} 至 ${item.examination_end_time}"
        aiStatus.text = item.course_status_name

        Glide.with(context).load(item.course_cover_image).apply(
            RequestOptions().transform(
                CenterCrop(), RoundedCornersTransform(context, 10f, rightTop = false, rightBottom = false)
            )
        ).dontAnimate().placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error).into(coverImageView)


        val drawableIng = ContextCompat.getDrawable(context, R.drawable.icon_live_ing)
        val drawableNoIng = ContextCompat.getDrawable(context, R.drawable.icon_live_no_ing)
        drawableIng?.setBounds(0, 0, drawableIng.intrinsicWidth, drawableIng.intrinsicHeight)
        drawableNoIng?.setBounds(0, 0, drawableNoIng.intrinsicWidth, drawableNoIng.intrinsicHeight)

        when (item.course_status) {
            "1" -> {
                aiStatus.setCompoundDrawables(drawableIng, null, null, null)
                aiStatus.setTextColor(getColorExt(R.color.green))
                aiStatus.compoundDrawablePadding = 16
            }

            else -> {
                aiStatus.setCompoundDrawables(drawableNoIng, null, null, null)
                aiStatus.setTextColor(getColorExt(R.color.blue))
                aiStatus.compoundDrawablePadding = 16
            }

        }

        // 隐藏的代码是为了需求变更使用、之前是view放大，现在是View 选中状态
        cardView.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->

            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)

            onItemFocus?.onChangeFocus(hasFocus, getItemPosition(item), v)

            layout.setBackgroundResource(if (hasFocus) R.color.white else R.color.black1)
            sub.setTextColor(if (hasFocus) Color.parseColor("#25333D") else Color.parseColor("#ffffff"))
            title.setTextColor(if (hasFocus) Color.parseColor("#25333D") else Color.parseColor("#ffffff"))


            // 设置阴影
            cardView.cardElevation = if (hasFocus) px2dp(10f).toFloat() else px2dp(0f).toFloat()

            cardView.setCardBackgroundColor(
                if (hasFocus) ContextCompat.getColor(
                    context, R.color.black
                ) else ContextCompat.getColor(context, R.color.transparent)
            )

            if (hasFocus) {
                cardView.clearAnimation()
                cardView.startAnimation(scaleAnimation)
            } else {
                cardView.clearAnimation()
            }

            cardView.radius = if (hasFocus) 16f else 0f
        }
    }


}