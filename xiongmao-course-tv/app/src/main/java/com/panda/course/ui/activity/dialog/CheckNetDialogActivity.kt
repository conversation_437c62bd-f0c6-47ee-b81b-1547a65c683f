package com.panda.course.ui.activity.dialog

import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.webkit.ValueCallback
import android.webkit.WebSettings
import androidx.core.content.ContextCompat
import com.panda.course.R
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityCheckNetWorkBinding
import com.panda.course.entity.TestPointEntity
import com.panda.course.ext.*
import com.panda.course.network.check.core.Speedtest
import com.panda.course.network.check.core.config.SpeedtestConfig
import com.panda.course.network.check.core.config.TelemetryConfig
import com.panda.course.network.check.core.serverSelector.TestPoint
import com.panda.course.ui.viewmodel.NetWorkViewModel
import com.panda.course.util.NetworkUtil
import com.panda.course.util.RxTimerUtil
import io.reactivex.rxjava3.disposables.Disposable
import org.json.JSONException
import org.json.JSONObject
import rxhttp.wrapper.param.RxHttp
import java.lang.reflect.Constructor
import java.lang.reflect.Field
import java.lang.reflect.Method
import java.util.*
import kotlin.math.roundToInt


/**
 * 监测网络
 */
class CheckNetDialogActivity : BaseDbActivity<NetWorkViewModel, ActivityCheckNetWorkBinding>() {

    private val TAG = "CheckNetDialogActivity"

    companion object {
        private var speedtest: Speedtest? = null
    }

    //记录当前网速
    private var mDownLoadSpeed = 0.0

    private var mDownLoadSpeedNoFormat = 0.0

    private var isTaskFinish = false

    var taskFinish: Disposable? = null
    var task: Disposable? = null

    var lastDlText: String? = null
    var lastUlText: String? = null

    var threshold = 0  // dlText 和 ulText 连续相等的阈值


    override fun onCreate(savedInstanceState: Bundle?) {
        if (getCurrentAndroid9()) {
            hookWebView()
        }
        super.onCreate(savedInstanceState)
    }

    /**
     * 初始化view
     */
    override fun initView(savedInstanceState: Bundle?) {
        setDialogWindowParams(window, 0.7f)

        mDataBind.webView.loadUrl("https://test.ustc.edu.cn/")
        val webSettings: WebSettings = mDataBind.webView.getSettings()
        webSettings.javaScriptEnabled = true
        webSettings.allowContentAccess = true
        webSettings.databaseEnabled = true
        webSettings.domStorageEnabled = true
        webSettings.setAppCacheEnabled(true)
        webSettings.useWideViewPort = true
        webSettings.loadWithOverviewMode = true



        task = RxTimerUtil.interval(2000).subscribe {
            mDataBind.webView.evaluateJavascript("document.getElementById('dlText').innerHTML", ValueCallback<String> { content ->
                // content 即为获取到的 div 内容
                "dlText content: $content".logE(TAG)
                if (content == null || content == "null") {
                    pageError();
                    return@ValueCallback
                }
                if (lastDlText == content) {
                    threshold++
                    if (threshold >= 3) {
                        threshold = 0
                        task?.dispose()
                        task = RxTimerUtil.interval(2000).subscribe {
                            mDataBind.webView.evaluateJavascript("document.getElementById('ulText').innerHTML", ValueCallback<String> { content ->
                                // content 即为获取到的 div 内容
                                "ulText content: $content".logE(TAG)
                                if (lastUlText == content) {
                                    threshold++
                                    if (threshold >= 3) {
                                        task?.dispose()
                                        isTaskFinish = true
                                        pageFinish()
                                    }
                                } else {
                                    lastUlText = content
                                }
                                mDataBind.tvNetUploadSpeed.text = getString(R.string.net_check_upload, "${content.replace("\"", "")}")
                            }
                            )
                        }
                    }
                } else {
                    lastDlText = content
                    val num = content.replace("\"", "")
                    num.notTextNull({
                        mDownLoadSpeedNoFormat = if (num != "...") num.toDouble() else 0.0
                        mDownLoadSpeed = convertValues(if (num != "...") num.toDouble() else 0.0)
                    }, {
                        mDownLoadSpeedNoFormat = 0.0
                        mDownLoadSpeed = 0.0
                    })
                }
                mDataBind.tvNetDownloadSpeed.text = getString(R.string.net_check_download, "${content.replace("\"", "")}")
            }
            )
        }

        taskFinish = RxTimerUtil.interval(1000 * 90).subscribe { //如果2分钟之后还没有检测出数据
            if (!isTaskFinish) {
                pageError()
            }
        }


        if (NetworkUtil.isAvailable(this@CheckNetDialogActivity)) {
//            mViewModel.getNetPoint()
        } else {
            getString(R.string.sys_net_no).toast()
            pageError()
        }


        RxHttp.get("https://api-v3.speedtest.cn/ip").asString().subscribe({ strber: String ->
            "请求回来的内容=== $strber".logE(TAG)
            if (strber != null) {
                try {
                    val json = JSONObject(strber)
                    val jsonBase = JSONObject(json.getString("data"))
                    "请求回来的内容=== " + jsonBase.getString("country").logE(TAG)
                    "请求回来的内容=== " + jsonBase.getString("ip").logE(TAG)
                    mDataBind.tvIpAddress.text = jsonBase.getString("country") + " " + jsonBase.getString("city") + " " + jsonBase.getString("operator") + "  ${jsonBase.getString("ip")}"
                } catch (e: JSONException) {
                    e.printStackTrace()
                }
            }
        }) { throwable: Throwable ->
        }
    }


    override fun initObserver() {
        super.initObserver()
        eventViewModel.monitorNetWork.observe(this, androidx.lifecycle.Observer {
            if (!it) {
                pageError()
            }
        })
    }

    /**
     * 界面错误的情况
     */
    private fun pageError() {
        mDataBind.tvNetCheckEndTips.text = getString(R.string.sys_net_no)
        mDataBind.tvNetUploadSpeed.text = getString(R.string.net_check_upload, "0")
        mDataBind.tvNetDownloadSpeed.text = getString(R.string.net_check_download, "0")
        mDataBind.llNetCheckIngLayout.gone()
        mDataBind.llNetCheckEndLayout.visible()
    }

    /**
     * 检测完成
     */
    private fun pageFinish() {
        mDataBind.tvTopBack.visible()
        mDataBind.butBack.gone()
        mDataBind.llNetCheckIngLayout.gone()
        mDataBind.llNetCheckEndLayout.visible()
        resultNetLevel(mDownLoadSpeed.toInt())
    }

    /**
     * 启动网速测试
     */
    private fun startCheckSpeed(testPoint: TestPoint) {
        speedtest?.setSelectedServer(testPoint)
        speedtest?.start(object : Speedtest.SpeedtestHandler() {
            override fun onDownloadUpdate(dl: Double, progress: Double) {
                "onDownloadUpdate = $dl".logE(TAG)
                runOnUiThread {
                    mDownLoadSpeedNoFormat = dl
                    mDownLoadSpeed = convertValues(dl)
                    if (!TextUtils.isEmpty(format(dl))) {
                        mDataBind.tvNetDownloadSpeed.text = "下载速度：${format(dl).replace("-", "")} Mbps"
                    } else {
                        mDataBind.tvNetDownloadSpeed.text = "下载速度：${format(dl).replace("-", "")} Mbps"
                    }
                }
            }

            override fun onUploadUpdate(ul: Double, progress: Double) {
                "onUploadUpdate = $ul".logE(TAG)
                runOnUiThread {
                    if (!TextUtils.isEmpty(format(ul))) {
                        mDataBind.tvNetUploadSpeed.text = "上传速度：${format(ul).replace("-", "")} Mbps"
                    } else {
                        mDataBind.tvNetUploadSpeed.text = "上传速度：${format(ul).replace("-", "")} Mbps"
                    }
                }
            }

            override fun onPingJitterUpdate(ping: Double, jitter: Double, progress: Double) {
                "onPingJitterUpdate".logE(TAG)
            }

            override fun onIPInfoUpdate(ipInfo: String) {
                "onIPInfoUpdate".logE(TAG)
            }

            override fun onTestIDReceived(id: String, shareURL: String) {
                "onTestIDReceived".logE(TAG)
            }

            override fun onEnd() {
                "onEnd".logE(TAG)
                isTaskFinish = true
                runOnUiThread { pageFinish() }
            }

            override fun onCriticalFailure(err: String) {
                "onCriticalFailure".logE(TAG)
                runOnUiThread { pageError() }
            }
        })
    }

    /**
     * 获取当前网速
     * 1Mbps=1024Kbps=1024/8KBps=128KB/s，1Mbps的下载速率理论值是128KB/s
     */
    private fun resultNetLevel(level: Int) {
        "$mDownLoadSpeed".logE("获取的网速")
        var mStr = ""
        when {
            level <= 128 -> {
                mStr = getString(R.string.net_check_speed_128)
                mDataBind.tvNetCheckEndStatus.text = getString(R.string.net_check_speed_128_text)
                mDataBind.tvNetCheckEndStatus.setTextColor(getColorExt(R.color.red))
            }

            level <= 2560 -> {
                mStr = getString(R.string.net_check_speed_512)
                mDataBind.tvNetCheckEndStatus.text = getString(R.string.net_check_speed_512_text)
                mDataBind.tvNetCheckEndStatus.setTextColor(getColorExt(R.color.red))
            }

            level <= 6400 -> {
                mStr = getString(R.string.net_check_speed_6400)
                mDataBind.tvNetCheckEndStatus.text = getString(R.string.net_check_speed_6400_text)
                mDataBind.tvNetCheckEndStatus.setTextColor(getColorExt(R.color.orange))
            }

            else -> {
                mStr = getString(R.string.net_check_speed)
                mDataBind.tvNetCheckEndStatus.text = getString(R.string.net_check_speed_text)
                mDataBind.tvNetCheckEndStatus.setTextColor(getColorExt(R.color.green))
            }
        }
        mDataBind.tvNetCheckEndTips.text = changeFontColor(
            content = mStr,
            startIndex = if (level <= 128) mStr.length - 7 else mStr.length - 6,
            endIndex = mStr.length - 3,
            color = ContextCompat.getColor(this, R.color.green)
        )
    }


    override fun onBindViewClick() {
        mDataBind.butBack.setOnClickListener {
            finish()
        }
    }

    /**
     * 转换值
     */
    private fun convertValues(number: Double): Double {
        return if (number == 0.0) {
            0.0
        } else {
            (number * 1024) / 8
        }
    }

    private fun format(d: Double): String {
        var l: Locale? = null
        l = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            resources.configuration.locales[0]
        } else {
            resources.configuration.locale
        }
        if (d < 10) return String.format(l, "%.2f", d)
        return if (d < 100) String.format(l, "%.1f", d) else "" + d.roundToInt()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_BACK -> {
                finish()
                return true
            }

            KeyEvent.KEYCODE_MENU -> {
                goSystemTvBoxSetting(this)
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            task?.dispose()
            taskFinish?.dispose()
            speedtest?.abort()
        } catch (t: Throwable) {
            t.message
        }
    }

}