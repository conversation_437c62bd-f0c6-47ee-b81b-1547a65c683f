package com.panda.course.ui.adapter

import android.graphics.Color
import android.graphics.Typeface
import android.text.TextUtils
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.entity.GraduateStudentListImgEntity


class GraduateStringAdapter(var color: String? = "", var typeFace: Typeface) : BaseQuickAdapter<GraduateStudentListImgEntity, BaseViewHolder>(R.layout.item_map_string) {

    override fun convert(holder: BaseViewHolder, item: GraduateStudentListImgEntity) {
        holder.setText(R.id.tv_item_map_title, item.service_true_name)
        if (!TextUtils.isEmpty(color)) {
            holder.getView<TextView>(R.id.tv_item_map_title).setTextColor(Color.parseColor(color))
        } else {
            holder.getView<TextView>(R.id.tv_item_map_title).setTextColor(Color.RED)
        }
        holder.getView<TextView>(R.id.tv_item_map_title).typeface = typeFace
    }


}