package com.panda.course.ui.viewmodel

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import com.panda.course.util.MMKVHelper
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class LiveModel : BaseViewModel() {

    var liveListEntity = MutableLiveData<LiveListCourseEntity>()
    var liveBarrageEntity = MutableLiveData<LiveBarrageEntity>()

    var hourListEntity = MutableLiveData<LiveListCourseHourEntity>()


    var joinEntity = MutableLiveData<QRCodeEntity>()

    var liveQrEntity = MutableLiveData<QRCodeEntity>()


    /**
     * 获取live列表
     */
    fun getLiveList(course_status: String? = null) {
        rxHttpRequest {
            onRequest = {
                liveListEntity.value = RxHttp.get(NetUrl.LIVE_LIST)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("course_status", course_status)
                    .toResponse<LiveListCourseEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_CUSTOM
            requestUrl = NetUrl.LIVE_LIST
        }
    }


    /**
     * 获取课时列表
     */
    fun getLiveHoursList(code: String? = null) {
        rxHttpRequest {
            onRequest = {
                hourListEntity.value = RxHttp.get(NetUrl.LIVE_LIST_HOURS)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("code", "" + code)
                    .toResponse<LiveListCourseHourEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_CUSTOM
            requestUrl = NetUrl.LIVE_LIST_HOURS
        }
    }


    /**
     * 获取视频二维码
     */
    fun getLiveInfo(code: String) {
        val mDeviceID =
            if (!TextUtils.isEmpty(MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID))) {
                MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID).toString()
            } else {
                eventViewModel.appUserInfo.value?.device_id.toString()
            }
        rxHttpRequest {
            onRequest = {
                liveQrEntity.value = RxHttp.get(NetUrl.GET_LIVE_QR_CODE)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("code", code)
                    .add("tv_device_id", "" + mDeviceID)
                    .toResponse<QRCodeEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_LIVE_QR_CODE
        }
    }


    /**
     * 获取弹幕
     */
    fun getBarrage() {
        rxHttpRequest {
            onRequest = {
                liveBarrageEntity.value = RxHttp.get(NetUrl.GET_BARRAGE)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<LiveBarrageEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_BARRAGE
        }
    }


    /**
     * 获取参加码
     */
    fun getLiveQrCode(code: String? = "", course_code: String? = "") {

        rxHttpRequest {
            onRequest = {
                joinEntity.value = RxHttp.get(NetUrl.JOIN_CODE)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("code", code)
                    .add("course_code", course_code)
                    .toResponse<QRCodeEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.JOIN_CODE
        }
    }

}