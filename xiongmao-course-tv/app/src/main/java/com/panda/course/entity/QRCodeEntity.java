package com.panda.course.entity;

import java.io.Serializable;

public class QRCodeEntity implements Serializable {
    private String code;
    private String img_url;
    private String device_id;
    private String teacher_name;
    private String teacher_pic;
    private String join_code;

    private String qr_code_content;

    public void setQr_code_content(String qr_code_content) {
        this.qr_code_content = qr_code_content;
    }

    public String getQr_code_content() {
        return qr_code_content;
    }

    public void setTeacher_name(String teacher_name) {
        this.teacher_name = teacher_name;
    }

    public String getTeacher_name() {
        return teacher_name;
    }

    public void setTeacher_pic(String teacher_pic) {
        this.teacher_pic = teacher_pic;
    }

    public String getTeacher_pic() {
        return teacher_pic;
    }

    public void setDevice_id(String device_id) {
        this.device_id = device_id;
    }

    public String getDevice_id() {
        return device_id;
    }

    public void setImg_url(String img_url) {
        this.img_url = img_url;
    }

    public String getImg_url() {
        return img_url;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setJoin_code(String join_code) {
        this.join_code = join_code;
    }

    public String getJoin_code() {
        return join_code;
    }
}
