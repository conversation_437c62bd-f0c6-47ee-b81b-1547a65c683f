package com.panda.course.ui.activity

import android.app.Activity
import android.content.Context
import android.net.ConnectivityManager
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityPhoneRemoteBinding
import com.panda.course.ext.eventViewModel
import com.panda.course.ext.getAppVersion
import com.panda.course.ext.logE
import com.panda.course.ui.viewmodel.SettingModel
import com.panda.course.util.MMKVHelper
import com.panda.course.util.NetworkUtil


class PhoneRemoteActivity : BaseDbActivity<SettingModel, ActivityPhoneRemoteBinding>() {

    override fun initView(savedInstanceState: Bundle?) {
//        if (eventViewModel.deviceExpireDate.value != null) {
//            eventViewModel.deviceExpireDate.value?.let {
//                if (it == null) {
//                    return@let
//                }
//                if (TextUtils.isEmpty(it.expire_start_date) && TextUtils.isEmpty(it.expire_end_date)) {
//                    mDataBind.tvSettingInfo.text = "AI课版本：V${getAppVersion(this)}\n设备不存在"
//                } else {
//                    mDataBind.tvSettingInfo.text = "AI课版本：V${getAppVersion(this)}${if (!TextUtils.isEmpty(getDeviceId())) "      设备ID：${getDeviceId()}" else ""}\n服务日期至：${it?.expire_end_date}"
//                }
//            }
//        } else {
//            mDataBind.tvSettingInfo.text = "AI课版本：V${getAppVersion(this)}${if (!TextUtils.isEmpty(getDeviceId())) "      设备ID：${getDeviceId()}" else ""}"
//        }
    }


    fun getWIFISSID(activity: Activity): String? {
        val wifiMgr = getSystemService(Context.WIFI_SERVICE) as WifiManager
        val wifiState = wifiMgr.wifiState
        val info = wifiMgr.connectionInfo
        val wifiId = info?.ssid

        "${wifiId?.toString()}".logE("拿到的Wi-Fi信息")

        val ssid = ""
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.O || Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val mWifiManager = (activity.applicationContext.getSystemService(WIFI_SERVICE) as WifiManager)
            val info = mWifiManager.connectionInfo
            "${info.toString()}".logE("拿到的Wi-Fi信息")
            return if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                info.ssid
            } else {
                info.ssid.replace("\"", "")
            }
        } else if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O_MR1) {
            val connManager = (activity.applicationContext.getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager)
            val networkInfo = connManager.activeNetworkInfo
            if (networkInfo?.isConnected == true) {
                if (networkInfo.extraInfo != null) {
                    return networkInfo.extraInfo.replace("\"", "")
                }
            }
        }
        return ssid
    }

    private fun getDeviceId(): String? {
        if (!TextUtils.isEmpty(MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID))) {
            return MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID)
        } else {
            if (eventViewModel.appUserInfo.value != null) {
                if (!TextUtils.isEmpty(eventViewModel.appUserInfo.value!!.device_id)) {
                    return eventViewModel.appUserInfo.value!!.device_id
                }
            }
        }
        return null
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (onMyKeyDown(keyCode, event)) { // 加一层判断，实现android 9 以及其他的情况
            return true
        }
        return super.onKeyDown(keyCode, event)
    }
}