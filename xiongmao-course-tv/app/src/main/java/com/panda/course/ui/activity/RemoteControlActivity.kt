package com.panda.course.ui.activity

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothDevice.ACTION_PAIRING_REQUEST
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import androidx.annotation.RequiresApi
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.databinding.ActivityRemoteControlBinding
import com.panda.course.ext.finishActivity
import com.panda.course.ext.finishActivitys
import com.panda.course.ext.logE
import com.panda.course.util.AnimationUtils
import com.panda.course.util.ClsUtils
import com.panda.course.util.HidConncetUtil


class RemoteControlActivity : BaseDbActivity<BaseViewModel, ActivityRemoteControlBinding>() {


    private val mBluetoothAdapter: BluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
    private val mBluetoothDevice: BluetoothDevice? = null
    private var bluetoothReceiver: BluetoothReceiver? = null
    private val isConnected = false
    private var hidConnectUtil = HidConncetUtil(this)


    @RequiresApi(Build.VERSION_CODES.M)
    override fun initView(savedInstanceState: Bundle?) {
        mDataBind.ivHello.startAnimation(AnimationUtils.getShowAlphaAnimation(5000))

        hidConnectUtil.getHidConncetList {
            "$it".logE()
        }

        bluetoothReceiver = BluetoothReceiver(mBluetoothAdapter, hidConnectUtil)
        val intentFilter = IntentFilter()
        intentFilter.addAction(BluetoothDevice.ACTION_FOUND)
        intentFilter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED)
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED)
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECT_REQUESTED)
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED)
        intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
        intentFilter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED)
        intentFilter.addAction(ACTION_PAIRING_REQUEST)

        intentFilter.addAction("android.bluetooth.input.profile.action.CONNECTION_STATE_CHANGED")
        registerReceiver(bluetoothReceiver, intentFilter)

        if (mBluetoothAdapter != null) {
            if (mBluetoothAdapter.isEnabled) {
                mBluetoothAdapter.startDiscovery();
                "mBluetoothAdapter.startDiscovery".logE()
            } else {
                mBluetoothAdapter.enable();
                "mBluetoothAdapter.enable".logE()
            }
        }
    }


    class BluetoothReceiver(var mBluetoothAdapter: BluetoothAdapter, var conncetUtil: HidConncetUtil) : BroadcastReceiver() {
        var pin = "1234"
        override fun onReceive(context: Context, intent: Intent) {
            val action: String = intent.action.toString()
            val device: BluetoothDevice
            // 搜索发现设备时，取得设备的信息；注意，这里有可能重复搜索同一设备
            if (BluetoothDevice.ACTION_FOUND == action) {
                //这里就是我们扫描到的蓝牙设备
                device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)!!
                val pin = intent.getIntExtra("android.bluetooth.device.extra.PAIRING_KEY", 1234)

                "${device.address} + ${device.name}".logE("扫描出来的")
//                if (device.address.contains("54:03:84")) {
//
//                    "bond = ${device.bondState} , pin = $pin".logE()
//                    if (device.bondState == BluetoothDevice.BOND_NONE) {
//
//                        if (mBluetoothAdapter.isDiscovering) {
//                            mBluetoothAdapter.cancelDiscovery()
//                        }
//                        ClsUtils.createBond(device::class.java, device)
//                    }
//
//                }
            } else if (BluetoothDevice.ACTION_BOND_STATE_CHANGED == action) {
//                device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
//                when (device.bondState) {
//                    BluetoothDevice.BOND_BONDING -> {
//                        "正在配对......${device.name}".logE()
//                    }
//                    BluetoothDevice.BOND_BONDED -> {
//                        "完成配对".logE()
//                        finishActivitys(RemoteControlActivity::class.java)
//                    }
//                    BluetoothDevice.BOND_NONE -> {
//                        "取消配对....".logE()
//                    }
//                    else -> {
//                    }
//                }
            } else if (action == ACTION_PAIRING_REQUEST) {
//                device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
//                //1.确认配对
//                ClsUtils.setPairingConfirmation(device::class.java, device, true)
//                //2.终止有序广播
//                "isOrderedBroadcast:$isOrderedBroadcast,isInitialStickyBroadcast:$isInitialStickyBroadcast".logE()
//                abortBroadcast() //如果没有将广播终止，则会出现一个一闪而过的配对框。
//                //3.调用setPin方法进行配对...
//                val ret = ClsUtils.setPin(device::class.java, device, pin)
            }
        }

        fun intToBytes(value: Int): ByteArray? {
            val src = ByteArray(4)
            src[3] = (value shr 24 and 0xFF).toByte()
            src[2] = (value shr 16 and 0xFF).toByte()
            src[1] = (value shr 8 and 0xFF).toByte()
            src[0] = (value and 0xFF).toByte()
            return src
        }
    }




}