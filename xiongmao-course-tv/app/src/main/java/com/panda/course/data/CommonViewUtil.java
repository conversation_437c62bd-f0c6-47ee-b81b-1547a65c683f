package com.panda.course.data;

import android.view.View;
import android.view.animation.Animation;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.panda.course.R;
import com.panda.course.entity.CommonFunctions;
import com.panda.course.entity.ControllerViewState;
import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.LogExtKt;
import com.panda.course.ui.adapter.CommonFunctionsAdapter;
import com.panda.course.ui.adapter.CommonSpecialFunctionsAdapter;
import com.panda.course.util.AnimationUtils;
import com.tencent.liteav.demo.superplayer.SuperPlayerView;

public class CommonViewUtil {

    private final int delayTime = 1000;

    /**
     * 视频播放页控制台延迟执行焦点
     */
    public void requestFocusDelay(BaseQuickAdapter mAdapter, int position, int delayTime) {
        if (mAdapter != null) {
            View view = mAdapter.getViewByPosition(position, R.id.rl_common_item_view);
            if (view != null) {
                view.requestFocus(delayTime);
            }
        }
    }


    /**
     * 视频播放页控制台延迟执行焦点
     */
    public void requestFocusDelay(BaseQuickAdapter mAdapter, int position) {
        if (mAdapter != null) {
            View view = mAdapter.getViewByPosition(position, R.id.rl_common_item_view);
            if (view != null) {
                view.requestFocus(delayTime);
            }
        }
    }

    /**
     * 改变adapter的内容
     * current 是区分某个 1 分辨率 2 倍速
     * position 是点击选择的那个
     */
    public void changeAdapterSelected(CommonSpecialFunctionsAdapter mAdapter, View controllerView, CommonSpecialFunctionsAdapter currentAdapter, int current, int position) {
        int index = 0;
        if (currentAdapter != null && currentAdapter.getData() != null && currentAdapter.getData().size() > 0) {
            for (int i = 0; i < currentAdapter.getData().size(); i++) {
                if (currentAdapter.getData().get(i).isSeleced()) {
                    index = i;
                }
                currentAdapter.getData().get(i).setSeleced(false);
            }
            currentAdapter.getData().get(position).setSeleced(true);
            // 刷新单个item
            currentAdapter.notifyItemChanged(index);
            currentAdapter.notifyItemChanged(position);

            if (current == ControllerViewState.VIEW_CONTROLLER_DEFINITION) {
                for (int i = 0; i < mAdapter.getData().size(); i++) {
                    if (mAdapter.getData().get(i).getTitle().contains("清晰度")) {
                        mAdapter.getData().get(i).setContent(currentAdapter.getData().get(position).getContent());
                        mAdapter.notifyDataSetChanged();
                        break;
                    }
                }
            } else if (current == ControllerViewState.VIEW_CONTROLLER_SPEED) {
                for (int i = 0; i < mAdapter.getData().size(); i++) {
                    if (mAdapter.getData().get(i).getTitle().contains("倍速")) {
                        mAdapter.getData().get(i).setContent(currentAdapter.getData().get(position).getContent());
                        mAdapter.notifyDataSetChanged();
                        break;
                    }
                }
            } else if (current == ControllerViewState.VIEW_CONTROLLER_SUBTITLE) {
                for (int i = 0; i < mAdapter.getData().size(); i++) {
                    if (mAdapter.getData().get(i).getTitle().contains("字幕")) {
                        mAdapter.getData().get(i).setContent(currentAdapter.getData().get(position).getContent());
                        mAdapter.notifyDataSetChanged();
                        break;
                    }
                }
            }
            hideControllerView(controllerView, current);
        }
    }

    /**
     * 控制台的显示于隐藏
     * action 是展示还是隐藏
     */
    public void controllerViewAction(boolean action, View view, View requestView, int channelState) {
        if (view != null) {
            view.clearAnimation();
            if (action) {
                if (view.getVisibility() == View.GONE) {
                    view.setVisibility(View.VISIBLE);
                }
                view.startAnimation(AnimationUtils.getShowAlphaAnimation(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                        AppExtKt.getEventViewModel().getAppCommonViewState().postValue(new ControllerViewState(channelState, false));
                        if (requestView != null) {
                            requestView.requestFocus();
                        }
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {

                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                }));
            } else {
                if (view.getVisibility() == View.VISIBLE) {
                    view.setVisibility(View.GONE);
                }
                view.clearAnimation();
                view.startAnimation(AnimationUtils.getHiddenAlphaAnimation(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {

                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        AppExtKt.getEventViewModel().getAppCommonViewState().postValue(new ControllerViewState(channelState, true));
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                }));
            }
        }
    }

    /**
     * 降低复杂度  单独抽出一个方法
     *
     * @param controllerView
     */
    public void hideControllerView(View controllerView) {
        controllerViewAction(false, controllerView, null, ControllerViewState.VIEW_CONTROLLER_COMMON);
    }

    /**
     * 降低复杂度  单独抽出一个方法
     *
     * @param controllerView
     */
    public void hideControllerView(View controllerView, int channelState) {
        controllerViewAction(false, controllerView, null, channelState);
    }


    /**
     * 操作后的tips 嵌套动画去隐藏View
     */
    public void startTipsAnimation(TextView mView) {
        if (mView != null) {
            mView.setVisibility(View.VISIBLE);
            mView.startAnimation(AnimationUtils.getBottomAnimation(2000, new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    mView.setVisibility(View.GONE);
                    mView.startAnimation(AnimationUtils.getHiddenAlphaAnimation(3000, new Animation.AnimationListener() {
                        @Override
                        public void onAnimationStart(Animation animation) {

                        }

                        @Override
                        public void onAnimationEnd(Animation animation) {
                            mView.setText("");
                        }

                        @Override
                        public void onAnimationRepeat(Animation animation) {

                        }
                    }));
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            }));
        }
    }

}
