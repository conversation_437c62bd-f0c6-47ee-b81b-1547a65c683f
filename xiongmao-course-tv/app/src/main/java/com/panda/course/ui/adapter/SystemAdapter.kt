package com.panda.course.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.config.base.Ktx
import com.panda.course.entity.SurpriseListContentEntity
import com.panda.course.entity.SurpriseListEntity
import com.panda.course.entity.SystemInfo
import com.panda.course.ext.gone
import com.panda.course.ext.visible
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper
import com.youth.banner.adapter.BannerAdapter

class SystemAdapter : BaseQuickAdapter<SystemInfo, BaseViewHolder>(R.layout.item_sys_info) {


    private var currentOutputMode = ""

    override fun convert(holder: BaseViewHolder, item: SystemInfo) {
        holder.setText(R.id.tv_item_title, item.name).setText(R.id.tv_item_time, item.content)

        if (currentOutputMode.contains(item.name)) {
            holder.getView<ImageView>(R.id.iv_item_status).visible()
        } else {
            holder.getView<ImageView>(R.id.iv_item_status).gone()
        }

        holder.getView<LinearLayout>(R.id.rl_common_item_view).setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                holder.getView<View>(R.id.view_item_sys).gone()
                holder.getView<TextView>(R.id.tv_item_time).setTextColor(ContextCompat.getColor(context, R.color.white))
            } else {
                holder.getView<View>(R.id.view_item_sys).visible()
                holder.getView<TextView>(R.id.tv_item_time).setTextColor(ContextCompat.getColor(context, R.color.base_color_body))
            }
        }
    }

    fun updateItem(mode: String) {
        currentOutputMode = mode
        notifyDataSetChanged()
    }


}