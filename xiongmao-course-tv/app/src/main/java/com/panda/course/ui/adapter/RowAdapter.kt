package com.panda.course.ui.adapter

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.RowStringEntity
import com.panda.course.ext.dp2px
import com.panda.course.ext.px2dp
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.RoundCornerImageView
import com.panda.course.widget.focus.MyFocusHighlightHelper
import com.panda.course.widget.lable.LabelImageView

class RowAdapter : BaseQuickAdapter<RowStringEntity, BaseViewHolder>(R.layout.item_row) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var onItemFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null

    fun setOnViewFocus(onItemFocus: OnViewFocus?) {
        this.onItemFocus = onItemFocus
    }

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()
    }

    override fun convert(holder: BaseViewHolder, item: RowStringEntity) {

        val imageViewISNew = holder.getView<ImageView>(R.id.iv_item_is_new)
        imageViewISNew.visibility = if ("1" == item.is_new) View.VISIBLE else View.GONE

        val view = holder.getView<RoundCornerImageView>(R.id.iv_item_row)

        Glide.with(context).load(item.cover_url).diskCacheStrategy(DiskCacheStrategy.RESOURCE) //磁盘缓存模式
            .apply(RequestOptions().transform(CenterCrop(), RoundedCornersTransform(context, 20f)))
            .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error).into(view)


        val cardView = holder.getView<CardView>(R.id.row_card_view)

        when {
            getItemPosition(item) == 0 -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(100f))
                layoutParams.setMargins(dp2px(20f), dp2px(20f), dp2px(20f), dp2px(0f))
                cardView.layoutParams = layoutParams
            }
            getItemPosition(item) == (data.size - 1) -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(100f))
                layoutParams.setMargins(dp2px(20f), dp2px(20f), dp2px(20f), dp2px(20f))
                cardView.layoutParams = layoutParams
            }
            else -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(100f))
                layoutParams.setMargins(dp2px(20f), dp2px(20f), dp2px(20f), dp2px(0f))
                cardView.layoutParams = layoutParams
            }
        }

        view.setOnClickListener {
            onItemFocus?.onChangeFocus(true, getItemPosition(item), view)
        }

        // 隐藏的代码是为了需求变更使用、之前是view放大，现在是View 选中状态
        view.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->

            mBrowseItemFocusHighlight?.onItemFocused(cardView, hasFocus)

            onItemFocus?.onChangeFocus(hasFocus, getItemPosition(item), view)

            // 设置阴影
            cardView.cardElevation = if (hasFocus) px2dp(20f).toFloat() else px2dp(0f).toFloat()

            cardView.setCardBackgroundColor(
                if (hasFocus) ContextCompat.getColor(
                    context, R.color.black
                ) else ContextCompat.getColor(context, R.color.transparent)
            )

            if (hasFocus) {
                cardView.clearAnimation()
                cardView.startAnimation(scaleAnimation)
            } else {
                cardView.clearAnimation()
            }

            cardView.radius = if (hasFocus) 16f else 0f
        }
    }


}