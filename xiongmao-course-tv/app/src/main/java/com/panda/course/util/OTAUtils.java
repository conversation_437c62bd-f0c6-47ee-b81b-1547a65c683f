package com.panda.course.util;

import android.content.Context;
import android.os.RecoverySystem;

import com.panda.course.ext.LogExtKt;

import java.io.File;
import java.io.IOException;
import java.security.GeneralSecurityException;

/**
 * OTA 升级
 */
public class OTAUtils {

    private static String TAG = "OTAUtils";

    /**
     * 需要把下载的成功的ota.zip 放到内部存储的recovery 中 不然升级的时候会失败
     *
     * @param context
     * @param file
     * @return
     */
    public static boolean installOtaPackageAuto(final Context context, File file) {

//        String internalPath = "/data/recovery/ota.zip";
//        String cmd = "cat " + file.getAbsolutePath() + " > " + internalPath;
//
//        String res = Tools.shell(cmd, false);
//        LogExtKt.logE("shell: " + res, TAG);
//
//        File otaPackageFile = new File(internalPath);
//
//        try {
//            RecoverySystem.verifyPackage(otaPackageFile, null, null);
//        } catch (IOException e) {
//            e.printStackTrace();
//            return false;
//        } catch (GeneralSecurityException e) {
//            e.printStackTrace();
//            return false;
//        }
//
//        try {
//            RecoverySystem.installPackage(context, otaPackageFile);
//        } catch (IOException e) {
//            e.printStackTrace();
//            return false;
//        }
        return true;
    }
}
