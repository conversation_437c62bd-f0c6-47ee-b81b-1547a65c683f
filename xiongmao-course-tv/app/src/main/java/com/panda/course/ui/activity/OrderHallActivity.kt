package com.panda.course.ui.activity

import android.os.Bundle
import android.os.Handler
import android.view.KeyEvent
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.*
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityOrderHallBinding
import com.panda.course.ext.*
import com.panda.course.ui.adapter.OrderHallAdapter
import com.panda.course.ui.viewmodel.OrderHallViewModel
import com.panda.course.widget.AutoSlideGridLayoutManager
import io.reactivex.rxjava3.disposables.Disposable
import java.util.*

class OrderHallActivity : BaseDbActivity<OrderHallViewModel, ActivityOrderHallBinding>() {


    private var mAdapter = OrderHallAdapter()

    private var page = 1


    private var auntType = ""

    /**
     * 默认分类
     */
    private val focus_row = 1

    /**
     * 全部技能测评
     */
    private val focus_child = 2


    /**
     * 记录当前的焦点在哪里
     */
    private var currentFocusType: Int = focus_row

    private var autoSlideGridLayoutManager: AutoSlideGridLayoutManager? = null


    override fun initView(savedInstanceState: Bundle?) {
        autoSlideGridLayoutManager = AutoSlideGridLayoutManager(this, 2)

        mDataBind.recyclerView.adapter = mAdapter
        mDataBind.recyclerView.itemAnimator = DefaultItemAnimator()
        mDataBind.recyclerView.layoutManager = autoSlideGridLayoutManager
        mViewModel.getOrderHallAllType()
        mViewModel.getOrderHallList(page, auntType)
    }

    override fun initObserver() {

        mAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                currentFocusType = focus_child
            }

            if (hasFocus && mAdapter.data.size >= ConstantMMVK.PAGE_SIZE_18 && position == mAdapter.data.size - 1 || position == mAdapter.data.size - 2) {
                page++
                mViewModel.getOrderHallList(page, auntType)
            }
        }

        mDataBind.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            var mDy = 0
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                mDy += dy
//                "mDy = $mDy 总高度 = ${mDataBind.recyclerView.measuredHeight}".logE("来来回回")
                if (mDy == 0) { //到顶了
                    autoSlideGridLayoutManager?.setBottom(false)
                }
            }
        })
    }


    override fun onRequestSuccess() {
        mViewModel.orderHallTypeListEntity.observe(this, Observer {
            if (it.work_total == 0) {
                mDataBind.tvOrderHallTotal.gone()
                mDataBind.tvSubtitle.gone()
                return@Observer
            }
            mDataBind.tvOrderHallTotal.text = "岗位数量：${it.work_total}"
            startAutoScroll()
        })

        mViewModel.detailsListEntity.observe(this, Observer {
            if (1 == page) {
                if (it.list.isNotEmpty()) {
                    mDataBind.emptyLayout.gone()
                    mDataBind.recyclerView.smoothScrollToPosition(0)
                    mAdapter.setList(it.list)
                    requestOne()
                } else {
                    mDataBind.emptyLayout.visible()
                }
            } else {
                mAdapter.addData(it.list)
            }
        })
    }

    fun requestOne() {
        Handler().postDelayed({
            if (mAdapter.data.size > 0) {
//                val mView = mAdapter.getViewByPosition(0, R.id.row_card_view)
//                mView?.requestFocus()
            }
        }, 100)
    }


    override fun onDestroy() {
        super.onDestroy()
        autoDisposable?.dispose()
    }

    //记录距离
    private var distance = 0
    var autoDisposable: Disposable? = null

    /**
     * 改变recyclerView
     */
    private fun scrollToRecyclerViewHeight() {
//        "距离 = $distance  绘制高度 ${mDataBind.recyclerView.measuredHeight}".logE("来来回回")
        distance += 1
        mDataBind.recyclerView.smoothScrollBy(0, distance)
    }

    private fun startAutoScroll() {
//        autoDisposable = RxTimerUtil.interval(200).subscribe {
//            scrollToRecyclerViewHeight()
//        } as Disposable
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo() // 宣传片

        if (onMyKeyDown(keyCode, event)) { // 加一层判断，实现android 9 以及其他的情况
            return true
        }

        autoSlideGridLayoutManager?.stopPageUp()

        return super.onKeyDown(keyCode, event)
    }
}