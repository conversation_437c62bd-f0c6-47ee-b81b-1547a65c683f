package com.panda.course.ext

import com.panda.course.config.ConstantMMVK
import com.panda.course.util.MMKVHelper
import com.tencent.qcloud.tuicore.util.MD5Utils
import okio.ByteString.Companion.encodeUtf8
import java.io.File
import java.util.*

/**
 * 查询是否有mp3
 * 如果return 是false 那么播放在线的，并且重新下载
 * @return
 */
fun findLocalMp3(mUrl: String): String? {
    try {
        val md5 = mUrl.encodeUtf8().md5().hex()
        val mRootFile = File(ConstantMMVK.OTA_ZIP_PATH)
        val mFileArr = mRootFile.listFiles()
        val mLocalState = MMKVHelper.decodeBoolean(md5)
        if (mFileArr != null && mFileArr.isNotEmpty()) {
            for (i in mFileArr.indices) {
                if (mFileArr[i].name.contains(md5) && mLocalState == true) { //如果找到相同的那么返回true 还有路径
                    return mFileArr[i].absolutePath
                }
            }
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return null
}