package com.panda.course.ui.adapter

import android.os.Build
import android.text.TextUtils
import android.view.View
import android.webkit.*
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.entity.FeedBackQuestionEntity
import com.panda.course.entity.FeedBackQuestionListEntity
import com.panda.course.entity.NoticeListEntityEntity
import com.panda.course.ext.gone
import com.panda.course.ext.px2dp
import com.panda.course.ext.visible
import com.panda.course.util.RichTextUtils
import com.panda.course.widget.focus.MyFocusHighlightHelper


class FeedBackQuestionAdapter : BaseQuickAdapter<FeedBackQuestionEntity, BaseViewHolder>(R.layout.item_feedback_question_list) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var mOnViewFocus: OnViewFocus? = null


    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight = MyFocusHighlightHelper.BrowseItemFocusHighlight(MyFocusHighlightHelper.ZOOM_FACTOR_XXXXSMALL, false)
        }
    }

    fun setOnItemFocus(onItemFocus: OnViewFocus?) {
        this.mOnViewFocus = onItemFocus
    }

    override fun convert(holder: BaseViewHolder, item: FeedBackQuestionEntity) {

        holder.setText(R.id.tv_item_feedback_list_title, item.title)


        val titleLayout = holder.getView<LinearLayout>(R.id.ll_feedback_title_layout)

        val contentView = holder.getView<TextView>(R.id.tv_item_feedback_list_content)

        val cardView = holder.getView<CardView>(R.id.row_card_feedback_list_view)


        RichTextUtils.showRichHtmlWithImageContent(contentView, item.content)


        // 隐藏的代码是为了需求变更使用、之前是view放大，现在是View 选中状态
        cardView.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->

            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)

            mOnViewFocus?.onChangeFocus(hasFocus, getItemPosition(item), v)

            // 设置阴影
            cardView.cardElevation = if (hasFocus) px2dp(20f).toFloat() else px2dp(0f).toFloat()

            cardView.setCardBackgroundColor(if (hasFocus) ContextCompat.getColor(context, R.color.black) else ContextCompat.getColor(context, R.color.transparent))

            cardView.radius = if (hasFocus) 16f else 0f

            titleLayout.setBackgroundResource(if (hasFocus) R.drawable.base_border_selecter_top_selecter else R.color.transparent)


            if (hasFocus) {
                contentView.visible()
                cardView.clearAnimation()
            } else {
                contentView.gone()
                cardView.clearAnimation()
            }
        }
    }


}