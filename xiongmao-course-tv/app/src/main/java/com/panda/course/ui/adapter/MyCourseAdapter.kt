package com.panda.course.ui.adapter

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.CourseListEntity
import com.panda.course.ext.*
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.RoundCornerImageView
import com.panda.course.widget.focus.MyFocusHighlightHelper
import com.panda.course.widget.lable.LabelImageView
import org.w3c.dom.Text


/**
 * DiskCacheStrategy.NONE：表示不缓存任何内容。
 * DiskCacheStrategy.SOURCE：表示只缓存原始图片。
 * DiskCacheStrategy.RESULT：表示只缓存转换过后的图片（默认选项）。
 * DiskCacheStrategy.ALL ：表示既缓存原始图片，也缓存转换过后的图片。
 */
class MyCourseAdapter : BaseQuickAdapter<CourseListEntity, BaseViewHolder>(R.layout.item_course) {

    private var ellipsis = 95

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var mOnViewFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(MyFocusHighlightHelper.ZOOM_FACTOR_XXXSMALL, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()

    }


    fun setOnItemFocus(onItemFocus: OnViewFocus?) {
        this.mOnViewFocus = onItemFocus
    }

    override fun convert(holder: BaseViewHolder, item: CourseListEntity) {

        val imageViewISNew = holder.getView<ImageView>(R.id.iv_item_is_new)
        imageViewISNew.visibility = if ("1" == item.is_new) View.VISIBLE else View.GONE

        val imageView = holder.getView<ImageView>(R.id.item_iv_course_cover)
        val tvDesc = holder.getView<TextView>(R.id.tv_item_desc)

        holder.setText(R.id.tv_item_title, "${item.teacher_name}")

        Glide.with(context).asBitmap().load(item.course_cover_image).diskCacheStrategy(DiskCacheStrategy.RESOURCE) //磁盘缓存模式
            .placeholder(R.drawable.icon_placeholder)
            .apply(
                RequestOptions().transform(
                    CenterCrop(), RoundedCornersTransform(context, 20f, rightTop = false, rightBottom = false, leftTop = true, leftBottom = true)
                )
            ).into(imageView)


        if (!TextUtils.isEmpty(item.create_time)) {
            if (item.course_introduction.length > ellipsis) {
                tvDesc.text = "${item.course_introduction.subSequence(0, ellipsis)}..."
            } else {
                tvDesc.text = "${item.course_introduction}"
            }
            holder.setText(R.id.tv_item_date, "更新时间：${item.create_time}")
        } else {
            tvDesc.text = "${item.course_introduction}"
            holder.setText(R.id.tv_item_date, "")
        }

        val ivPlay = holder.getView<ImageView>(R.id.iv_item_play)


        val cardView = holder.getView<CardView>(R.id.card_view)


        when {
            getItemPosition(item) == 0 -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(150f))
                layoutParams.setMargins(dp2px(14f), dp2px(30f), dp2px(20f), dp2px(20f))
                cardView.layoutParams = layoutParams
            }

            getItemPosition(item) == (data.size - 1) -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(150f))
                layoutParams.setMargins(dp2px(14f), dp2px(10f), dp2px(20f), dp2px(20f))
                cardView.layoutParams = layoutParams
            }

            else -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(150f))
                layoutParams.setMargins(dp2px(14f), dp2px(10f), dp2px(20f), dp2px(20f))
                cardView.layoutParams = layoutParams
            }
        }



        cardView.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->
            mBrowseItemFocusHighlight?.onItemFocused(cardView, hasFocus)
            mOnViewFocus?.onChangeFocus(hasFocus, getItemPosition(item), cardView)
            ivPlay.visibility = if (hasFocus) View.VISIBLE else View.GONE

            cardView.cardElevation = if (hasFocus) dp2px(20f).toFloat() else dp2px(0f).toFloat()

            cardView.setCardBackgroundColor(
                if (hasFocus) ContextCompat.getColor(
                    context, R.color.black
                ) else ContextCompat.getColor(context, R.color.transparent)
            )
            cardView.radius = if (hasFocus) 16f else 0f


            if (hasFocus) {
                cardView.clearAnimation()
                cardView.startAnimation(scaleAnimation)
            } else {
                cardView.clearAnimation()
            }


        }

    }


}