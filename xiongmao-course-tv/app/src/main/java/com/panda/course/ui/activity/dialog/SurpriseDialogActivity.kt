package com.panda.course.ui.activity.dialog

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.view.KeyEvent
import com.panda.course.R
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.data.CommonAnimationUtil
import com.panda.course.databinding.ActivityRankingBinding
import com.panda.course.databinding.ActivitySurpriseBinding
import com.panda.course.entity.Surprise
import com.panda.course.ext.appUMEvent
import com.panda.course.ext.notTextNull
import com.panda.course.ext.setDialogWindowParams
import com.panda.course.ext.toStartActivity
import com.panda.course.ui.activity.ContentActivity
import com.panda.course.ui.activity.NoticeActivity
import com.panda.course.ui.activity.WebViewActivity
import com.panda.course.ui.presenter.CoursePresenter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.util.GlideUtil

/**
 * 惊喜活动
 */
class SurpriseDialogActivity : BaseDbActivity<CourseViewModel, ActivitySurpriseBinding>() {

    private var mSurprise: Surprise? = null

    private var jump_type = ""

    /**
     * 初始化view
     */
    override fun initView(savedInstanceState: Bundle?) {
        setDialogWindowParams(window, 1.0f)
        intent.extras?.apply {
            getString("root_bg").notTextNull {
                GlideUtil.loadPic(this@SurpriseDialogActivity, it, mDataBind.ivSurprise)
            }
            jump_type = getString("jump_type").toString()
            getParcelable<Surprise>("pub_data")?.let {
                mSurprise = it
            }
        }

        appUMEvent(UMConstant.HOME_AD_DETAILS)
    }

    override fun onBindViewClick() {
        super.onBindViewClick()
        mDataBind.tvSurpriseOk.setOnClickListener {
            if ("notice" == jump_type) {
                toStartActivity(NoticeActivity::class.java)
            } else {
                CoursePresenter.toSurpriseDetails(mSurprise)
            }
            finish()

            //            val bundle = Bundle()
            //            mSurprise?.let { s -> //跳转类型 1课程 2活动ID 3网址
            //                when (s.jump_type) {
            //                    "1" -> {
            //                        bundle.putString("course_code", s.course_code)
            //                        toStartActivity(ContentActivity::class.java, bundle)
            //                    }
            //                    "2" -> {
            //                        bundle.putString("activity_id", s.activity_id)
            //                        toStartActivity(SurpriseContentActivity::class.java, bundle)
            //                    }
            //                    "3" -> {
            //                        bundle.putString("jump_url", s.jump_url)
            //                        toStartActivity(WebViewActivity::class.java, bundle)
            //                    }
            //                }
            //            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_DPAD_RIGHT, KeyEvent.KEYCODE_DPAD_LEFT -> {
                CommonAnimationUtil().startShakeAnimation(false, mDataBind.tvSurpriseOk)
            }
            KeyEvent.KEYCODE_DPAD_UP, KeyEvent.KEYCODE_DPAD_DOWN -> {
                CommonAnimationUtil().startShakeAnimation(true, mDataBind.tvSurpriseOk)
            }
        }
        return super.onKeyDown(keyCode, event)
    }
}