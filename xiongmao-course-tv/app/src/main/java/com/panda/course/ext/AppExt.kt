package com.panda.course.ext

import android.app.Activity
import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.wifi.WifiManager
import android.os.Build
import android.provider.Settings
import android.text.TextUtils
import com.panda.course.callback.EventViewModel
import com.panda.course.config.App
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.Ktx
import com.panda.course.config.base.appContext
import com.panda.course.ui.activity.CourseActivity
import com.panda.course.ui.activity.MainActivity
import com.panda.course.util.Constants
import com.panda.course.util.MMKVHelper
import com.umeng.analytics.MobclickAgent
import okhttp3.internal.and
import java.io.*
import java.lang.reflect.Method
import java.net.*
import java.security.MessageDigest
import java.util.*


val eventViewModel: EventViewModel by lazy { App.eventViewModelInstance }


/**
 * 获取设备的SN
 */
fun getSerialNumber(): String? {
    val insideNumber = "UT30_31"
    var serial: String? = null
    try {
        val c = Class.forName("android.os.SystemProperties")
        val get: Method = c.getMethod("get", String::class.java)
        serial = get.invoke(c, "ro.serialno") as String?
    } catch (e: Exception) {
        e.printStackTrace()
    }

    // 第一次的时候存写死的
    if (ConstantMMVK.INSIDE_RELEASE) {
        MMKVHelper.encode(ConstantMMVK.INSIDE_NUMBER, insideNumber)
    }

    val mmkvNumber = MMKVHelper.decodeString(ConstantMMVK.INSIDE_NUMBER)


    if (!TextUtils.isEmpty(mmkvNumber)) {
        if (mmkvNumber!!.contains(insideNumber)) {
            MMKVHelper.encode(ConstantMMVK.INSIDE_NUMBER, insideNumber)
            return mmkvNumber
        }
    } else {
        MMKVHelper.encode(ConstantMMVK.INSIDE_NUMBER, "$serial")
        return serial
    }
    return serial
}

/**
 * 获取设备的Android ID
 */
fun getAndroidId(): String? {
    try {
        return Settings.Secure.getString(appContext.contentResolver, Settings.Secure.ANDROID_ID)
    } catch (ex: java.lang.Exception) {
        ex.printStackTrace()
    }
    return ""
}


fun getLocalInetAddress(): InetAddress? {
    var ip: InetAddress? = null
    try { //列举
        val en_netInterface = NetworkInterface.getNetworkInterfaces()
        while (en_netInterface.hasMoreElements()) { //是否还有元素
            val ni = en_netInterface.nextElement() as NetworkInterface //得到下一个元素
            val en_ip = ni.inetAddresses //得到一个ip地址的列举
            while (en_ip.hasMoreElements()) {
                ip = en_ip.nextElement()
                ip = if (!ip!!.isLoopbackAddress && ip.hostAddress.indexOf(":") == -1) break else null
            }
            if (ip != null) {
                break
            }
        }
    } catch (e: SocketException) {
        e.printStackTrace()
    }
    return ip
}

/**
 * 获取当前的ip
 */
fun getIpAddressForInterfaces(): String? {
    val interfaceName = "eth0"
    try {
        val enNetworkInterface = NetworkInterface.getNetworkInterfaces() //获取本机所有的网络接口
        while (enNetworkInterface.hasMoreElements()) {  //判断 Enumeration 对象中是否还有数据
            val networkInterface = enNetworkInterface.nextElement() //获取 Enumeration 对象中的下一个数据
            if (!networkInterface.isUp) { // 判断网口是否在使用
                continue
            }
            if (interfaceName != networkInterface.displayName) { // 网口名称是否和需要的相同
                continue
            }
            val enInetAddress = networkInterface.inetAddresses //getInetAddresses 方法返回绑定到该网卡的所有的 IP 地址。
            while (enInetAddress.hasMoreElements()) {
                val inetAddress = enInetAddress.nextElement()
                if (inetAddress is Inet4Address) {  //判断是否未ipv4
                    return inetAddress.getHostAddress()
                }
            }
        }
    } catch (e: java.lang.Exception) {
        e.printStackTrace()
    }
    return "0.0.0.0"
}


//得到MAC地址
fun getWireMac(): String? {
    val insideMac = "A1:B2:C3:D4:F5"
    var strMacAddress: String? = null
    try {
        val b = NetworkInterface.getByName("eth0").hardwareAddress
        val buffer = StringBuffer()
        for (i in b.indices) {
            if (i != 0) {
                buffer.append(':')
            }
            println("b:" + (b[i] and 0xFF))
            val str = Integer.toHexString(b[i] and 0xFF)
            buffer.append(if (str.length == 1) "0$str" else str)
        }
        strMacAddress = buffer.toString().toUpperCase()
    } catch (e: java.lang.Exception) {
        e.printStackTrace()
    }

    if (ConstantMMVK.INSIDE_RELEASE) {
        MMKVHelper.encode(ConstantMMVK.INSIDE_MAC, insideMac)
    }

    val mmkvMAC = MMKVHelper.decodeString(ConstantMMVK.INSIDE_MAC)
    return if (!TextUtils.isEmpty(mmkvMAC) && ConstantMMVK.INSIDE_RELEASE) {
        mmkvMAC
    } else {
        strMacAddress
    }

    //    return "A1:B2:C3:D4:F5"
    //    return virtualMac()
}


/**
 * 根据规则生成固定模式随机的Mac地址
 */
fun virtualMac(): String {
    val stringBuilder = java.lang.StringBuilder()
    stringBuilder.append(Constants.MAC).append(":")
    var x = 0
    while (x <= 3) {
        stringBuilder.append((Math.random() * 26 + 'A'.toDouble()).toChar().toString().replace("\"", ""))
            .append((Math.random() * 10).toInt()).append(":")
        x += 1
    }
    return stringBuilder.take(stringBuilder.length - 1).toString()
}

/**
 * 对密码进行加密
 */
fun md5Digest(password: String): String? {
    return try {
        val digest: MessageDigest = MessageDigest.getInstance("MD5")
        val bytes: ByteArray = digest.digest(password.toByteArray())
        val sb = java.lang.StringBuilder()
        for (b in bytes) {
            val c = b and 0xff //负数转换成正数
            val result = Integer.toHexString(c) //把十进制的数转换成十六进制的书
            if (result.length < 2) {
                sb.append(0) //让十六进制全部都是两位数
            }
            sb.append(result)
        }
        sb.toString() //返回加密后的密文
    } catch (ex: java.lang.Exception) {
        ex.printStackTrace()
        ""
    }
}


/**
 * 获取CPU序列号
 *
 * @return CPU序列号(16位) 读取失败为"0000000000000000"
 */
fun getCPUSerial(): String? {
    var str = ""
    var strCPU = ""
    var cpuAddress = "0000000000000000"
    try { // 读取CPU信息
        val pp = Runtime.getRuntime().exec("cat/proc/cpuinfo")
        val ir = InputStreamReader(pp.inputStream)
        val input = LineNumberReader(ir) // 查找CPU序列号
        for (i in 1..99) {
            str = input.readLine()
            if (str != null) { // 查找到序列号所在行
                if (str.indexOf("Serial") > -1) { // 提取序列号
                    strCPU = str.substring(str.indexOf(":") + 1, str.length) // 去空格
                    cpuAddress = strCPU.trim { it <= ' ' }
                    break
                }
            } else { // 文件结尾
                break
            }
        }
    } catch (ex: Exception) { // 赋予默认值
        ex.printStackTrace()
        "${ex.message} 唯一标识".logE()
    }
    return cpuAddress
}


/**
 * 获取当前进程的名称，默认进程名称是包名
 */
val currentProcessName: String?
    get() {
        val pid = android.os.Process.myPid()
        val mActivityManager = appContext.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        for (appProcess in mActivityManager.runningAppProcesses) {
            if (appProcess.pid == pid) {
                return appProcess.processName
            }
        }
        return null
    }

/**
 * 获取packageName
 */
fun getPackageNameName(context: Context): String {
    try {
        val pi = context.packageManager.getPackageInfo(context.packageName, 0)
        return pi.packageName
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
    }
    return ""
}

/**
 * 获取versionName
 */
fun getAppVersion(context: Context): String {
    try {
        val pi = context.packageManager.getPackageInfo(context.packageName, 0)
        return pi.versionName
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
    }
    return ""
}

/**
 * 判断是否是android 9
 */

fun getCurrentAndroid9(): Boolean {
    return android.os.Build.VERSION.SDK_INT == 28
}

/**
 * 跳转系统的WIFI界面
 */
fun goSystemWIFISetting(context: Context) {
    val wifiSettingsIntent = Intent("android.settings.WIFI_SETTINGS")
    wifiSettingsIntent.action = Settings.ACTION_WIFI_SETTINGS
    context.startActivity(wifiSettingsIntent)
}


/**
 * 跳转系统设置界面
 */
fun goSystemTvBoxSetting(context: Context) {
    val wifiSettingsIntent = Intent("android.settings.Settings")
    wifiSettingsIntent.action = Settings.ACTION_SETTINGS
    context.startActivity(wifiSettingsIntent)
}

/**
 * 跳转系统蓝牙
 */
fun goSystemBluetoothSetting(context: Context) {
    val intent = Intent("android.settings.BLUETOOTH_SETTINGS")
    intent.action = Settings.ACTION_BLUETOOTH_SETTINGS
    if (intent.resolveActivity(context.packageManager) != null) {
        context.startActivity(intent)
    }
}

/**
 *  获取PackageManagerAPK的信息
 */
fun getAPKPackageInfo(context: Context, path: String?): Boolean {
    try {
        val packageManager: PackageManager = context.packageManager
        val packageInfo: PackageInfo? = packageManager.getPackageArchiveInfo(path.toString(), PackageManager.GET_ACTIVITIES)
        if (packageInfo != null) {
            return isUpdate(packageInfo.versionName, getAppVersion(context))
        }
    } catch (ignore: Throwable) {
        ignore.message
    }
    return false
}


fun isUpdate(newVer: String, appVer: String): Boolean {
    if (newVer == "") return false
    val newVerS = newVer.split(".")
    val appVerS = appVer.split(".")
    val maxLength = if (newVerS.size > appVerS.size) newVerS.size else appVerS.size
    for (index in 0 until maxLength) {
        if ((if (newVerS.size == index) 0 else newVerS[index].toInt()) > (if (appVerS.size == index) 0 else appVerS[index].toInt())) {
            return true
        }
    }
    return false
}


/**
 * 获取当前机型
 */
fun getDeviceModel(): String {
    return try {
        URLEncoder.encode(Build.MODEL, "utf-8")
    } catch (e: java.lang.Exception) {
        "other"
    }
}

/**
 * 获取当前安卓系统版本
 */
fun getDeviceSystemInt(): String? {
    return android.os.Build.VERSION.RELEASE
}

/**
 * 无线电固件版本号
 */
fun getDeviceRadio(): String {
    return getSysValueFromProp("ro.build.description")
}

fun getDeviceRadioFirmware(): String {
    return getSysValueFromProp("ro.product.firmware")
}


/**
 * 获取当前机型唯一id
 */
fun getDeviceID(): String {
    return try {
        Build.ID
    } catch (e: java.lang.Exception) {
        "100200300400500600700800900"
    }
}


/**
 * 通过反射获取系统build.prop的参数
 */
fun getSysValueFromProp(key: String): String {
    var value = ""
    var classType: Class<*>? = null
    try {
        classType = Class.forName("android.os.SystemProperties")
        //拿到get方法，此方法传入一个String类型参数。
        val getMethod = classType.getDeclaredMethod("get", *arrayOf<Class<*>>(String::class.java))
        // 键值persist.sys.sb.hide，其他的也可以。
        value = getMethod.invoke(classType, *arrayOf<Any>(key)) as String
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return value
}

/**
 * 通过反射改变系统build.prop的参数
 */
private fun setSysValueToProp() {
    var classType: Class<*>? = null
    try {
        classType = Class.forName("android.os.SystemProperties")
        //拿到set方法，此方法传入两个String类型参数,一个是键，一个是设定的值。
        val getMethod = classType.getDeclaredMethod("set", *arrayOf<Class<*>>(String::class.java, String::class.java))
        // 对persist.sys.sb.hide这一项设定值为1。
        getMethod.invoke(classType, *arrayOf("persist.sys.sb.hide", "1"))
    } catch (e: Exception) {
        e.printStackTrace()
    }
}


fun getIPAddress(): String? {
    val info: NetworkInfo? =
        (appContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager).activeNetworkInfo
    if (info != null && info.isConnected) {
        when {
            info.type === ConnectivityManager.TYPE_MOBILE -> { //当前使用2G/3G/4G网络
                try { //Enumeration<NetworkInterface> en=NetworkInterface.getNetworkInterfaces();
                    val en = NetworkInterface.getNetworkInterfaces()
                    while (en.hasMoreElements()) {
                        val intf = en.nextElement()
                        val enumIpAddr = intf.inetAddresses
                        while (enumIpAddr.hasMoreElements()) {
                            val inetAddress = enumIpAddr.nextElement()
                            if (!inetAddress.isLoopbackAddress && inetAddress is Inet4Address) {
                                return inetAddress.hostAddress
                            }
                        }
                    }
                } catch (e: SocketException) {
                    e.printStackTrace()
                }
            }
            info.type === ConnectivityManager.TYPE_WIFI -> { //当前使用无线网络
                return getLocalIpAddress() //得到IPV4地址
            }
            info.type === ConnectivityManager.TYPE_ETHERNET -> { //有线网络
                try { // 获取本地设备的所有网络接口
                    val enumerationNi = NetworkInterface.getNetworkInterfaces()
                    while (enumerationNi.hasMoreElements()) {
                        val networkInterface = enumerationNi.nextElement()
                        val interfaceName = networkInterface.displayName
                        ("网络名字$interfaceName").logE() // 如果是有线网卡
                        if (interfaceName == "eth0") {
                            val enumIpAddr = networkInterface.inetAddresses
                            while (enumIpAddr.hasMoreElements()) { // 返回枚举集合中的下一个IP地址信息
                                val inetAddress = enumIpAddr.nextElement() // 不是回环地址，并且是ipv4的地址
                                if (!inetAddress.isLoopbackAddress && inetAddress is Inet4Address) {
                                    (inetAddress.hostAddress + "   网络名字").logE()
                                    return inetAddress.hostAddress
                                }
                            }
                        }
                    }
                } catch (e: SocketException) {
                    e.printStackTrace()
                }
            }
        }
    } else { //当前无网络连接,请在设置中打开网络
    }
    return null
}

/**
 * 获取当前网关
 */
fun getGateWay(): String {
    val arr: Array<String>
    try {
        val process = Runtime.getRuntime().exec("ip route list table 0")
        val `in` = BufferedReader(InputStreamReader(process.inputStream))
        val string = `in`.readLine()
        arr = string.split("\\s+".toRegex()).toTypedArray()
        return arr[2]
    } catch (e: IOException) {
        e.printStackTrace()
    }
    return "0.0.0.0"
}

/**
 * 获取子网掩码
 * */
fun getIpAddressMaskForInterfaces(): String? {
    val interfaceName = "eth0"
    try {
        //获取本机所有的网络接口
        val networkInterfaceEnumeration = NetworkInterface.getNetworkInterfaces()
        //判断 Enumeration 对象中是否还有数据
        while (networkInterfaceEnumeration.hasMoreElements()) {

            //获取 Enumeration 对象中的下一个数据
            val networkInterface = networkInterfaceEnumeration.nextElement()
            if (!networkInterface.isUp && interfaceName != networkInterface.displayName) {

                //判断网口是否在使用，判断是否时我们获取的网口
                continue
            }
            for (interfaceAddress in networkInterface.interfaceAddresses) {
                if (interfaceAddress.address is Inet4Address) {

                    //仅仅处理ipv4
                    //获取掩码位数，通过 calcMaskByPrefixLength 转换为字符串
                    return calcMaskByPrefixLength(interfaceAddress.networkPrefixLength.toInt())
                }
            }
        }
    } catch (e: SocketException) {
        e.printStackTrace()
    }
    return "0.0.0.0"
}

/*通过子网掩码的位数计算子网掩码*/
private fun calcMaskByPrefixLength(length: Int): String? {
    val mask = -0x1 shl 32 - length
    val partsNum = 4
    val bitsOfPart = 8
    val maskParts = IntArray(partsNum)
    val selector = 0x000000ff
    for (i in maskParts.indices) {
        val pos = maskParts.size - 1 - i
        maskParts[pos] = mask shr i * bitsOfPart and selector
    }
    var result = ""
    result = result + maskParts[0]
    for (i in 1 until maskParts.size) {
        result = result + "." + maskParts[i]
    }
    return result
}

/**
 * 获取当前ip地址
 *
 * @param context
 * @return
 */
fun getLocalIpAddress(): String? {
    val wifiManager = appContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    val wifiInfo = wifiManager.connectionInfo
    "${wifiInfo.toString()}".logE("网络内容")
    val i = wifiInfo.ipAddress
    return int2ip(i)
}

/**
 * 将ip的整数形式转换成ip形式
 *
 * @param ipInt
 */
fun int2ip(ipInt: Int): String {
    val sb = StringBuilder()
    sb.append(ipInt and 0xFF).append(".")
    sb.append(ipInt shr 8 and 0xFF).append(".")
    sb.append(ipInt shr 16 and 0xFF).append(".")
    sb.append(ipInt shr 24 and 0xFF)
    return sb.toString()
}


fun compareVersion(v1: String, v2: String): Int {
    if (v1 == v2) {
        return 0
    }
    val version1 = v1.split("\\.".toRegex()).toTypedArray()
    val version2 = v2.split("\\.".toRegex()).toTypedArray()
    var index = 0
    val minLen = Math.min(version1.size, version2.size)
    var diff: Long = 0
    while (index < minLen && version1[index].toLong() - version2[index].toLong().also {
            diff = it
        } == 0L) index++
    return if (diff == 0L) {
        for (i in index until version1.size) if (version1[i].toLong() > 0) return 1
        for (i in index until version2.size) if (version2[i].toLong() > 0) return -1
        0
    } else {
        if (diff > 0) 1 else -1
    }
}

private val activityList = LinkedList<Activity>()

//app当前显示的Activity
val currentActivity: Activity? get() = if (activityList.isNullOrEmpty()) null else activityList.last


/**
 * 找出当前是否有这个activity
 */
fun findActivity(activity: Activity): Boolean {
    activityList.forEach {
        if (it == activity) {
            return true
        }
    }
    return false
}


fun findActivity(cls: Class<*>): Boolean {
    activityList.forEach {
        if (it.javaClass == cls) {
            return true
        }
    }
    return false
}

/**
 * 添加Activity入栈
 * @param activity Activity
 */
fun addActivity(activity: Activity) {
    activityList.add(activity)
}

/**
 * 关闭Activity出栈
 * @param activity Activity
 */
fun finishActivity(activity: Activity) {
    if (!activity.isFinishing) {
        activity.finish()
    }
    activityList.remove(activity)
}

/**
 * 从栈移除activity 不会finish
 * @param activity Activity
 */
fun removeActivity(activity: Activity) {
    activityList.remove(activity)
}

/**
 * 关闭Activity出栈
 * @param cls Class<*>
 */
fun finishActivitys(cls: Class<*>) {
    if (activityList.isNullOrEmpty()) return
    val index = activityList.indexOfFirst { it.javaClass == cls }
    if (index == -1) return
    if (!activityList[index].isFinishing) {
        activityList[index].finish()
    }
    activityList.removeAt(index)
}


/**
 * 关闭所有的Activity 除非了CourseActivity
 */
fun finishOtherActivity() {
    activityList.forEach {
        if (currentActivity !is CourseActivity) {
            if (!it.isFinishing && it !is CourseActivity) {
                it.finish()
            }
        }
    }
}

/**
 * 关闭所有的Activity 除非了MainActivity
 */
fun finishOtherMainActivity() {
    activityList.forEach {
        if (currentActivity !is MainActivity) {
            if (!it.isFinishing) {
                it.finish()
            }
        }
    }
}

/**
 * 关闭所有的Activity 全部出栈
 */
fun finishAllActivity() {
    activityList.forEach {
        if (!it.isFinishing) {
            it.finish()
        }
    }
    activityList.clear()
}


fun String?.notTextNull(notNullAction: (value: String) -> Unit) {
    if (this != null && this != "" && this != "null") {
        notNullAction.invoke(this)
    }
}

fun String?.notTextNull(notNullAction: (value: String) -> Unit, nullAction: () -> Unit) {
    if (this != null && this != "" && this != "null") {
        notNullAction.invoke(this)
    } else {
        nullAction.invoke()
    }
}

fun Any?.notNull(notNullAction: () -> Unit, nullAction: () -> Unit) {
    if (this != null) {
        notNullAction.invoke()
    } else {
        nullAction.invoke()
    }
}


/***
 * 友盟统计自定义事件
 */
fun appUMEventObject(eventId: String, hashMap: HashMap<String, Any>) {
    MobclickAgent.onEventObject(Ktx.app, eventId, hashMap)
}

/***
 * 友盟统计 单次上报
 */
fun appUMEvent(eventId: String) {
    MobclickAgent.onEvent(Ktx.app, eventId)
}