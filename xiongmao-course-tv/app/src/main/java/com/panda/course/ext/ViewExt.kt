package com.panda.course.ext

import android.app.Activity
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.noober.background.drawable.DrawableCreator
import com.panda.course.R
import com.panda.course.config.base.appContext
import com.panda.course.entity.CommonFunctions
import com.panda.course.ui.activity.PlayerActivity
import com.panda.course.ui.adapter.CommonFunctionsAdapter
import com.panda.course.ui.adapter.CommonSpecialFunctionsAdapter

/**
 * 监听View的状态设置颜色
 *
 * @param b
 * @return
 */
fun changeButtonColor(b: Boolean): Drawable? {
    var drawable: Drawable? = null
    drawable = if (b) {
        DrawableCreator.Builder().setCornersRadius(dp2pt(100f).toFloat())
            .setSolidColor(ContextCompat.getColor(appContext, R.color.green)).build()
    } else {
        DrawableCreator.Builder().setCornersRadius(dp2pt(100f).toFloat())
            .setSolidColor(ContextCompat.getColor(appContext, R.color.base_bg)).build()
    }
    return drawable
}


/**
 * 设置字体颜色
 */
fun changeFontColor(
    content: String, startIndex: Int, endIndex: Int, color: Int, isBold: Boolean = true
): SpannableStringBuilder {
    val spannable = SpannableStringBuilder(content)
    spannable.setSpan(ForegroundColorSpan(color), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
    if (isBold) {
        spannable.setSpan(StyleSpan(Typeface.BOLD), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
    }
    return spannable
}




fun RecyclerView.initPlayRecyclerCommon(
    activity: AppCompatActivity, mAdapter: CommonSpecialFunctionsAdapter, arr: ArrayList<CommonFunctions>
) {
    val manger = LinearLayoutManager(activity)
    manger.orientation = LinearLayoutManager.HORIZONTAL
    this.layoutManager = manger
    this.adapter = mAdapter
    mAdapter.setList(arr)
}



/**
 * 设置view显示
 */
fun View.visible() {
    visibility = View.VISIBLE
}


/**
 * 设置view占位隐藏
 */
fun View.invisible() {
    visibility = View.INVISIBLE
}

/**
 * 根据条件设置view显示隐藏 为true 显示，为false 隐藏
 */
fun View.visibleOrGone(flag: Boolean) {
    visibility = if (flag) {
        View.VISIBLE
    } else {
        View.GONE
    }
}

/**
 * 根据条件设置view显示隐藏 为true 显示，为false 隐藏
 */
fun View.visibleOrInvisible(flag: Boolean) {
    visibility = if (flag) {
        View.VISIBLE
    } else {
        View.INVISIBLE
    }
}

/**
 * 显示传入的view集合
 */
fun visibleViews(vararg views: View?) {
    views.forEach {
        it?.visible()
    }
}

/**
 * 隐藏传入的view集合
 */
fun goneViews(vararg views: View?) {
    views.forEach {
        it?.gone()
    }
}

/**
 * 设置view隐藏
 */
fun View.gone() {
    visibility = View.GONE
}

/**
 * 公用的tips
 * */
fun Activity.getEmptyView(tips: String? = "暂无数据", colorId: Int? = R.color.white): View {
    val mEmptyView = layoutInflater.inflate(R.layout.layout_empty, null)
    mEmptyView.findViewById<AppCompatTextView>(R.id.loading_empty_string).text =
        if (TextUtils.isEmpty(tips)) "暂无领取记录" else tips
    colorId?.let {
        mEmptyView.findViewById<AppCompatTextView>(R.id.loading_empty_string)
            .setTextColor(ContextCompat.getColor(this, it))
    }
    return mEmptyView
}

fun Fragment.getEmptyView(tips: String? = "暂无数据", colorId: Int? = R.color.white): View {
    val mEmptyView = layoutInflater.inflate(R.layout.layout_empty, null)
    mEmptyView.findViewById<AppCompatTextView>(R.id.loading_empty_string).text =
        if (TextUtils.isEmpty(tips)) "暂无领取记录" else tips
    colorId?.let {
        mEmptyView.findViewById<AppCompatTextView>(R.id.loading_empty_string)
            .setTextColor(ContextCompat.getColor(appContext, it))
    }
    return mEmptyView
}