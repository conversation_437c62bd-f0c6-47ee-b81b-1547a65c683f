package com.panda.course.config;

import android.content.Context;

import com.panda.course.ext.CommExtKt;
import com.panda.course.ext.LogExtKt;
import com.tencent.rtmp.TXLiveBase;
import com.tencent.rtmp.TXLiveBaseListener;

public class TXCSDKService {
    private static final String TAG = "TXCSDKService";
    // 如何获取License? 请参考官网指引 https://cloud.tencent.com/document/product/454/34750
    private static final String licenceUrl = "https://license.vod2.myqcloud.com/license/v2/1300467987_1/v_cube.license";
    private static final String licenseKey = "23758024f42ee6b64fd3358e027d75aa";

    public static boolean INIT_TX_BASE = false;

    private TXCSDKService() {
    }

    /**
     * NetWatcher
     * 初始化腾讯云相关sdk。
     * SDK 初始化过程中可能会读取手机型号等敏感信息，需要在用户同意隐私政策后，才能获取。
     *
     * @param appContext The application context.
     */
    public static void init(Context appContext) {
        TXLiveBase.getInstance().setLicence(appContext, licenceUrl, licenseKey);
//        TXLiveBase.getInstance().setLicence(appContext, CommExtKt.readFileFromAssets("TXLiveSDKLicense.json"), licenseKey);
        TXLiveBase.setListener(new TXLiveBaseListener() {
            @Override
            public void onUpdateNetworkTime(int errCode, String errMsg) {
                if (errCode != 0) {
                    TXLiveBase.updateNetworkTime();
                }
            }

            @Override
            public void onLicenceLoaded(int result, String reason) {
                super.onLicenceLoaded(result, reason);
                if (result == 0) {
                    INIT_TX_BASE = true;
                }
                LogExtKt.logE("初始化状态" + reason, "腾讯播放器");
            }
        });
        TXLiveBase.updateNetworkTime();

        // 短视频licence设置
    }
}
