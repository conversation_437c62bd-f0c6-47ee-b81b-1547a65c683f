package com.panda.course.ui.fragment

import android.os.Bundle
import androidx.lifecycle.Observer
import com.panda.course.R
import com.panda.course.config.base.BaseDbFragment
import com.panda.course.databinding.ActivitySkillBinding
import com.panda.course.databinding.ActivitySkillFragmentBinding
import com.panda.course.ext.getEmptyView

import com.panda.course.ui.adapter.SkillAdapter
import com.panda.course.ui.viewmodel.SkillViewModel

class SkillFragment : BaseDbFragment<SkillViewModel, ActivitySkillFragmentBinding>() {


    override fun initView(savedInstanceState: Bundle?) {

    }

    override fun onRequestSuccess() {

    }
}