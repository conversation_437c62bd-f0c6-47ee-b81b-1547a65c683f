package com.panda.course.ui.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.orient.tea.barragephoto.adapter.BarrageAdapter;
import com.panda.course.R;
import com.panda.course.entity.BarrageData;
import com.panda.course.util.GlideUtil;

import java.util.Random;

public class CustomBarrageViewHolder extends BarrageAdapter.BarrageViewHolder<BarrageData> {

    private ImageView mHeadView;
    private TextView mContent;

    private Random random = new Random();

    private String[] strings = {"给老师点赞👍", "老师讲的真好👍", "老师辛苦啦👍", "感谢老师👍",
            "老师讲的通俗易懂，非常实用👍!", "确实受益匪浅！这么优质的课程！点赞点赞👍👍!", "老师非常用心，细心，感谢👍", "感谢老师的课程指导，让我们在工作中不断提高自己的知识👍!",
            "非常好，学到了方法👍", "老师讲的很好，声动，形性，易懂👍👍👍"};

    public CustomBarrageViewHolder(View itemView) {
        super(itemView);
        mHeadView = itemView.findViewById(R.id.iv_barrage_pic);
        mContent = itemView.findViewById(R.id.tv_barrage_name);
    }

    @Override
    protected void onBind(BarrageData data) {
        mContent.setText(data.getContent() + "\n" + (strings[random.nextInt(strings.length)]));
        GlideUtil.loadPicRound(mHeadView.getContext(), data.getIconUrl(), mHeadView);
    }
}