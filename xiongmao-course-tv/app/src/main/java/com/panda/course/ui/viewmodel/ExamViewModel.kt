package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class ExamViewModel : BaseViewModel() {

    // 测评记录
    var recordListEntity = MutableLiveData<ExamRankEntity>()

    // 获取详细的内容
    var classResultEntity = MutableLiveData<ExamClassResultEntity>()

    /**
     * 获取排行
     */
    fun getExamRankAll(status: String, code: String?, page: Int) {
        rxHttpRequest {
            onRequest = {
                recordListEntity.value = RxHttp.get(NetUrl.EXAM_STORE_LIST)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("status", status)
                    .add("code", code)
                    .add("page", "$page")
                    .add("size", ConstantMMVK.PAGE_SIZE)
                    .toResponse<ExamRankEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.EXAM_STORE_LIST
        }
    }


    /**
     * 获取全部的技能测评二维码
     */
    fun getExamResult(number: String) {
        rxHttpRequest {
            onRequest = {
                classResultEntity.value = RxHttp.get(NetUrl.ONE_FINAL_EXAM)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("number", number)
                    .toResponse<ExamClassResultEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.ONE_FINAL_EXAM
        }
    }

}