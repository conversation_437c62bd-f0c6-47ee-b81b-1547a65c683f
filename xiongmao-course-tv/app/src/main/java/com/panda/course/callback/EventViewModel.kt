package com.panda.course.callback

import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*


/**
 * APP全局的ViewModel，可以在这里发送全局通知替代EventBus，LiveDataBus等
 */
class EventViewModel : BaseViewModel() {

    //全局收藏，在任意一个地方收藏或取消收藏，监听该值的界面都会收到消息
    val progressEvent = EventLiveData<ProgressNotice>()

    // 刷新历史记录
    val refreshHistoryList = EventLiveData<Boolean>()

    // 监听网络
    val monitorNetWork = EventLiveData<Boolean>()

    // 监听用户网速上报后台 2分钟一次的
    val netWorkSpeed = EventLiveData<String>()

    // 监听是否需要升级
    val appUpdate = EventLiveData<UpdateInfo>()

    // 监听缓存数据
    val appCacheVideo = EventLiveData<CacheVideoList>()

    // 用户信息
    val appUserInfo = EventLiveData<UserEntity>()

    // 监听用户闲时操作
    val appUserFreeTime = EventLiveData<Boolean>()

    // 监听视频播放-常用功能View的显示隐藏
    val appCommonViewState = EventLiveData<ControllerViewState>()

    // 控制活动View 是否显示
    val surpriseToggle = EventLiveData<Surprise>()

    // wifi断开的状态
    val wifiState = EventLiveData<Boolean>()

    val wifiConnectState = EventLiveData<String>()

    // OTA升级
    val appOTAUpdate = EventLiveData<Boolean>()

    // Home键盘的监听
    val appHomeClick = EventLiveData<Boolean>()

    // 全局监听头像的替换
    val appUserHead = EventLiveData<Boolean>()

    //记录消息通知的数量
    val noticeCount = EventLiveData<Int>()

    //停止服务
    val appDownloadVideoState = EventLiveData<Boolean>()

    //用户设备信息
    val deviceExpireDate = EventLiveData<DeviceExpireDate>()

    // 监听遥控器是否连接成功
    val appRemote = EventLiveData<Boolean>()

    //监听抢红包
    val listenForIMPushMessages = EventLiveData<VideoDynamicEffect>()

    // 整点报时
    val hourReporting = EventLiveData<Boolean>()
}