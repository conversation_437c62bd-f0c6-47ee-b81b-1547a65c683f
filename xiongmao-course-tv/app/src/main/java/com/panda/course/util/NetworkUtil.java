package com.panda.course.util;

import android.app.Activity;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.panda.course.config.base.Ktx;
import com.panda.course.ext.LogExtKt;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class NetworkUtil {
    /**
     * 没有连接网络
     */
    public static final int NETWORK_NONE = -1;

    /**
     * 有网络
     */
    public static final int NETWORK_CONNECTED = 2;


    public static final String TAG = NetworkUtil.class.getSimpleName();
    public static volatile boolean isActivityConnected = true;
    public static volatile boolean isConnected = true;
    private static ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
    public static final String WIFI_NET_NAME = "WIFI";
    public static final String PHONE_NET_NAME = "蜂窝网络";
    public static final String LINE_NET_NAME = "以太网";
    public static final String NONE_NET_NAME = "其他网络";

    public static int getConnectivityStatus(Context context) {
        if (context == null) {
            return NETWORK_NONE;
        }

        // 得到连接管理器对象
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return NETWORK_NONE;
        }
        //获取所有网络连接的信息
        Network[] networks = connectivityManager.getAllNetworks();
        if (networks == null) {
            return NETWORK_NONE;
        }
        if (networks.length > 0) {
            //通过循环将网络信息逐个取出来
            for (Network network : networks) {
                //获取ConnectivityManager对象对应的NetworkInfo对象
                NetworkInfo networkInfo = connectivityManager.getNetworkInfo(network);
                if (networkInfo != null) {
                    if (networkInfo.isConnected() && isAvailable(context)) {//如果有连接的
                        return NETWORK_CONNECTED;
                    }
                }
            }
        }
        return NETWORK_NONE;
    }


    /**
     * 检查当前网络是否可用
     *
     * @return
     */

    public static boolean isAvailable(Context context) {
        if (context == null) {
            return false;
        }
        // 获取手机所有连接管理对象（包括对wi-fi,net等连接的管理）
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

        if (connectivityManager == null) {
            return false;
        } else {
            // 获取NetworkInfo对象
            NetworkInfo[] networkInfo = connectivityManager.getAllNetworkInfo();
            if (networkInfo == null) {
                return false;
            }
            if (networkInfo.length > 0) {
                for (NetworkInfo info : networkInfo) {
                    if (info != null && info.getState() != null) {
//                        LogExtKt.logE(info.getState(), "网络类型 state=");
//                        LogExtKt.logE(info.getTypeName(), "网络类型 typeName=");
                        // 判断当前网络状态是否为连接状态
                        if (info.getState() == NetworkInfo.State.CONNECTED) {
//                            LogExtKt.logE(info.getTypeName(), "网络类型 CONNECTED=");
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }






    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static List<NetworkCapabilities> getAllNetworkCapabilities() {
        ConnectivityManager connectivityManager = (ConnectivityManager) Ktx.app.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        Network[] networks = connectivityManager.getAllNetworks();
        List<NetworkCapabilities> list = new ArrayList<>();
        for (Network network : networks) {
            NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(network);
            list.add(capabilities);
        }
        return list;
    }

    public static String getNetworkName(int transportType) {
        if (transportType == NetworkCapabilities.TRANSPORT_WIFI) {
            return WIFI_NET_NAME;
        } else if (transportType == NetworkCapabilities.TRANSPORT_CELLULAR) {
            return PHONE_NET_NAME;
        } else if (transportType == NetworkCapabilities.TRANSPORT_ETHERNET) {
            return LINE_NET_NAME;
        } else {
            return NONE_NET_NAME;
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static int getTransportType(NetworkCapabilities networkCapabilities) {
        if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
            return NetworkCapabilities.TRANSPORT_WIFI;
            //post(NetType.WIFI);
        } else if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
            return NetworkCapabilities.TRANSPORT_CELLULAR;
            //post(NetType.CMWAP);
        } else if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) {
            return NetworkCapabilities.TRANSPORT_ETHERNET;
            //post(NetType.AUTO);
        }
        return -1;
    }

    //以太网连上但不能上外网时，程序已经绑定了蜂窝网通道上网，此方法判断是否实际使用蜂窝网上网
    public static boolean isRealCellular() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean activityCellular = isActivityCellular();
            if (activityCellular) {
                return true;
            }
            return !isActivityConnected;
        }
        return true;
    }

    //判断激活的网（默认网络）是否4G网
    @RequiresApi(api = Build.VERSION_CODES.M)
    public static boolean isActivityCellular() {
        boolean isCellular = (getTransportType() == NetworkCapabilities.TRANSPORT_CELLULAR);
        return isCellular;
    }

    public static boolean isCellular(int transportType) {
        boolean isCellular = (transportType == NetworkCapabilities.TRANSPORT_CELLULAR);
        return isCellular;
    }

    //判断激活的网（默认网络）是否4G网
    @RequiresApi(api = Build.VERSION_CODES.M)
    public static boolean isActivityWifi() {
        boolean isWifi = (getTransportType() == NetworkCapabilities.TRANSPORT_WIFI);
        return isWifi;
    }

    public static boolean isWifi(int transportType) {
        boolean isWifi = (transportType == NetworkCapabilities.TRANSPORT_WIFI);
        return isWifi;
    }

    //判断激活的网（默认网络）是否4G网
    @RequiresApi(api = Build.VERSION_CODES.M)
    public static boolean isActivityEthernet() {
        boolean isEthernet = (getTransportType() == NetworkCapabilities.TRANSPORT_ETHERNET);
        return isEthernet;
    }

    public static boolean isEthernet(int transportType) {
        boolean isEthernet = (transportType == NetworkCapabilities.TRANSPORT_ETHERNET);
        return isEthernet;
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    public static int getTransportType() {
        int transportType = -2;
        ConnectivityManager connectivityManager = (ConnectivityManager) Ktx.app.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        Network activeNetwork = connectivityManager.getActiveNetwork();
        if (activeNetwork == null) {
            isConnected = false;
        } else {
            NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
            if (networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                isConnected = true;
                transportType = getTransportType(networkCapabilities);
            } else {
                isConnected = false;
            }
        }
        return transportType;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static void requestNetwork() {
        final ConnectivityManager connectivityManager = (ConnectivityManager) Ktx.app.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkRequest.Builder builder = new NetworkRequest.Builder();
        builder.addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
        builder.addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR);
        NetworkRequest networkRequest = builder.build();
        ConnectivityManager.NetworkCallback networkCallback = new ConnectivityManager.NetworkCallback() {
            @Override
            public void onAvailable(Network network) {
                if (Build.VERSION.SDK_INT >= 23) {
                    connectivityManager.bindProcessToNetwork(network);
                } else {
                    // 23后这个方法舍弃了
                    ConnectivityManager.setProcessDefaultNetwork(network);
                }
            }
        };
        final boolean[] chanelFlag = {true};
        executorService.scheduleWithFixedDelay(() -> {
            try {
                //网络都没连接直接返回
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (isActivityCellular()) {
                        return;
                    }
                }
                if (!isConnected) {
                    return;
                }
                isActivityConnected = ping();
                if (isActivityConnected) {
                    if (chanelFlag[0]) {
                        return;
                    }
                    chanelFlag[0] = true;
                    //1.以太网络可上外网时改为默认优先级上网
                    if (Build.VERSION.SDK_INT >= 23) {
                        connectivityManager.bindProcessToNetwork(null);
                    } else {
                        ConnectivityManager.setProcessDefaultNetwork(null);
                    }
                    connectivityManager.unregisterNetworkCallback(networkCallback);
                } else if (chanelFlag[0]) {
                    //2.以太网络不可上外网时自动切换蜂窝网
                    connectivityManager.requestNetwork(networkRequest, networkCallback);
                    chanelFlag[0] = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
//                LogFileUtils.exceptionToFile(e);
            }
        }, 1, 5, TimeUnit.SECONDS);
    }

    // PING命令 使用新进程使用默认网络 不会使用 networkCallback 绑定的通道  用来判断以太网或者WiFi是否可上外网非常不错
    public static boolean ping() {
        Runtime runtime = Runtime.getRuntime();
        try {
            Process p = runtime.exec("ping -c 1 -W 1 www.baidu.com");
            int ret = p.waitFor();
            Log.i(TAG, "Process:" + ret);
            return ret == 0;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
