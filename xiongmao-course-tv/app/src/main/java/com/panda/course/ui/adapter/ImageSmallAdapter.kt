package com.panda.course.ui.adapter

import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.entity.PictureGroup
import com.panda.course.ext.toStartActivity
import com.panda.course.ui.activity.OtherPlayActivity
import com.panda.course.ui.activity.dialog.GraduateDialogActivity
import com.panda.course.util.GlideUtil
import com.panda.course.util.ToastUtil
import com.panda.course.widget.focus.MyFocusHighlightHelper

/**
 * 小图展示
 */
class ImageSmallAdapter : BaseQuickAdapter<PictureGroup, BaseViewHolder>(R.layout.item_image_small) {
    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null
    private var bundle: Bundle? = null

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight = MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_NONE, false)
        }
        if (bundle == null) {
            bundle = Bundle()
        }
    }

    override fun convert(holder: BaseViewHolder, item: PictureGroup) {
        val frameLayout = holder.getView<FrameLayout>(R.id.fl_item_image_small)
        val imageView = holder.getView<ImageView>(R.id.iv_item_image_small)
        GlideUtil.loadPicRound(context, item.picture_url, imageView, 10)

        frameLayout.setOnClickListener {
            bundle?.let { it1 ->
                val mGroup = ArrayList<String>()
                if (1 == item.type) {
                    for (i in data.indices) {
                        data[i].picture_url?.let { it2 -> mGroup.add(it2) }
                        it1.putStringArrayList("data_urls", mGroup)
                        it1.putInt("position", getItemPosition(item))
                        it1.putString("jump_type", "interactive")
                    }
                    toStartActivity(GraduateDialogActivity::class.java, it1)
                } else {//2视频
                    for (i in data.indices) {
                        data[i].video_url?.let { it2 -> mGroup.add(it2) }
                        it1.putStringArrayList("data_urls", mGroup)
                        it1.putInt("position", getItemPosition(item))
                        it1.putString("jump_type", "interactive")
                    }
                    toStartActivity(OtherPlayActivity::class.java, it1)
                }
            }
        }

        frameLayout.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->
            mBrowseItemFocusHighlight?.onItemFocused(frameLayout, hasFocus)
            if (hasFocus) {
                frameLayout.setBackgroundResource(R.drawable.shape_image_stroke)
            } else {
                frameLayout.setBackgroundResource(R.color.transparent)
            }
        }
    }


}