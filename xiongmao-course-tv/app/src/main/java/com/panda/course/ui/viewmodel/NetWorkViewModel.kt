package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.CourseImgEntity
import com.panda.course.entity.QRCodeEntity
import com.panda.course.entity.TestPointEntity
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class NetWorkViewModel : BaseViewModel() {

    // 登录二维码
    var testPointEntity = MutableLiveData<TestPointEntity>()

    /**
     * 获取courseImg
     */
    fun getNetPoint() {
        rxHttpRequest {
            onRequest = {
                testPointEntity.value = RxHttp.get(NetUrl.NET_WORK_SPEED).add("device_code", getSerialNumber())
                    .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                    .add("firmware", getDeviceRadioFirmware())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).toResponse<TestPointEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.NET_WORK_SPEED
        }
    }
}