package com.panda.course.ui.adapter

import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.entity.InteractiveListEntityEntity
import com.panda.course.entity.PictureGroup
import com.panda.course.ext.*
import com.panda.course.util.GlideUtil
import com.panda.course.widget.focus.MyFocusHighlightHelper


class InteractiveAdapter : BaseQuickAdapter<InteractiveListEntityEntity, BaseViewHolder>(R.layout.item_interactive_list) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var mOnViewFocus: OnViewFocus? = null

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight = MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_NONE, false)
        }
    }

    fun setOnItemFocus(onItemFocus: OnViewFocus?) {
        this.mOnViewFocus = onItemFocus
    }

    private var mSelect: RelativeLayout? = null

    override fun convert(holder: BaseViewHolder, item: InteractiveListEntityEntity) {

        holder.setText(R.id.iv_item_interactive_name, item.service_personnel_name)
            .setText(R.id.iv_item_interactive_time, item.create_time)
            .setText(R.id.iv_item_interactive_store, item.store_name)
            .setText(R.id.iv_item_interactive_title, item.txt_content)

        val mTeacherReply = holder.getView<TextView>(R.id.iv_item_interactive_content)


        if (!TextUtils.isEmpty(item.txt_content)) {
            holder.getView<TextView>(R.id.iv_item_interactive_title).visible()
        } else {
            holder.getView<TextView>(R.id.iv_item_interactive_title).gone()
        }


        if (!item.teacher_comment.isNullOrEmpty()) {
            val stringBuffer = StringBuffer()
            for (i in item.teacher_comment!!.indices) {
                stringBuffer.append("老师回复：${item.teacher_comment!![i].txt_content}")
                if (i != item.teacher_comment!!.size - 1) {
                    stringBuffer.append("\n")
                }
            }
            mTeacherReply.text = stringBuffer.toString()

            //判断老师回复的字数是否超过3行，超过显示查看更多按钮
//            if (stringBuffer.toString().length > 66) {
//                mTeacherReply1.visible()
//                mTeacherReply1.text = stringBuffer.toString().substring(66, stringBuffer.toString().length)
//            } else {
//                mTeacherReply1.gone()
//            }
        } else {
            mTeacherReply.visible()
//            mTeacherReply1.gone()
            mTeacherReply.text = "老师暂无回复"
        }

        //大于3行就显示更多
//        if (mTeacherReply.length() >= 99) {
//            mLookTeacherReply.visible()
//        } else {
//            mLookTeacherReply.gone()
//        }

        GlideUtil.loadPicRound(context, item.avatar, holder.getView(R.id.iv_item_interactive_pic))


        val cardView = holder.getView<RelativeLayout>(R.id.row_card_interactive_list_view)

        val recycler = holder.getView<RecyclerView>(R.id.recycler_item_interactive)
        val mPicAdapter = ImageSmallAdapter()
        recycler.layoutManager = GridLayoutManager(context, 3)
        recycler.adapter = mPicAdapter

        if (!item.picture_url.isNullOrEmpty()) {
            recycler.visible()
            val mGroup = ArrayList<PictureGroup>()
            for (i in item.picture_url!!.indices) {
                mGroup.add(PictureGroup(type = 1, picture_url = item.picture_url!![i]))
            }
            mPicAdapter.setList(mGroup)
        } else if (!TextUtils.isEmpty(item.video_url)) {
            recycler.visible()
            val mGroup = ArrayList<PictureGroup>()
            item.video_url?.let { mGroup.add(PictureGroup(type = 2, picture_url = item.video_cover_url, video_url = it)) }
            mPicAdapter.setList(mGroup)
        } else {
            mPicAdapter.setList(null)
            recycler.gone()
        }


        //监听上下左右的键
        cardView.setOnKeyListener { v, keyCode, event ->
            if (event.repeatCount != 0) {
                return@setOnKeyListener true
            }

            if (0 == getItemPosition(item) && cardView.isFocused && keyCode == KeyEvent.KEYCODE_DPAD_UP) {
                if (mPicAdapter.data.size > 0) {
                    requestItemFocus(mPicAdapter)
                }
                cardView.clearAnimation()
                cardView.startAnimation(mShakeYAnimation)
                return@setOnKeyListener true
            }

            if (data.size - 1 == getItemPosition(item) && cardView.isFocused && keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                requestOnKey(cardView, mPicAdapter)
                cardView.clearAnimation()
                cardView.startAnimation(mShakeYAnimation)
                return@setOnKeyListener true
            }
            if (cardView.isFocused && keyCode == KeyEvent.KEYCODE_DPAD_DOWN || keyCode == KeyEvent.KEYCODE_DPAD_UP) {
                requestOnKey(cardView, mPicAdapter)
            }
            false
        }


        // 隐藏的代码是为了需求变更使用、之前是view放大，现在是View 选中状态
        cardView.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->

            mBrowseItemFocusHighlight?.onItemFocused(cardView, hasFocus)
            mOnViewFocus?.onChangeFocus(hasFocus, getItemPosition(item), cardView)

            if (hasFocus) {
                if (0 == getItemPosition(item)) {
                    mSelect?.background = ContextCompat.getDrawable(context, R.drawable.base_border_unselecter)
                    requestItemFocus(mPicAdapter)
                    mSelect = cardView
                }
                cardView.background = ContextCompat.getDrawable(context, R.drawable.base_border_selecter)
            }
        }
    }


    private fun requestOnKey(cardView: RelativeLayout, mPicAdapter: ImageSmallAdapter) {
        if (mSelect == null) {
            mSelect = cardView
        } else {
            mSelect?.background = ContextCompat.getDrawable(context, R.drawable.base_border_unselecter)
            mSelect = null
            mSelect = cardView
        }
        if (mPicAdapter.data.size > 0) {
            requestItemFocus(mPicAdapter)
        } else {
            mSelect?.background = ContextCompat.getDrawable(context, R.drawable.base_border)
        }
    }

    /**
     * 获取textview某行内容
     */
    fun getTextLineContent(textView: TextView?, line: Int, src: String?): String {
        var result: String = ""
        if (textView == null || src.isNullOrEmpty()) {
            return result
        }
        "$line--line-->${textView.lineCount}".logE()
        if (line > textView.lineCount) {
            return result
        }
        val layout = textView.layout
        val sb = StringBuilder(src)
        "--start-${layout.getLineStart(line)}----end---${layout.getLineEnd(line)}".logE()
        return sb.subSequence(layout.getLineStart(line), layout.getLineEnd(line)).toString()
    }

    /**
     * 让子item获取焦点
     */
    fun requestItemFocus(mPicAdapter: ImageSmallAdapter) {
        val mView = mPicAdapter.getViewByPosition(0, R.id.fl_item_image_small)
        mView?.requestFocus(100)
    }
}