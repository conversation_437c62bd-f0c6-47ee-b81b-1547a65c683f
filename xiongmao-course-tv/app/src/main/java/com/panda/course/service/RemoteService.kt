package com.panda.course.service

import android.app.Notification
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.panda.course.R
import com.panda.course.receiver.RemoteReceiver
import com.panda.course.ui.activity.CourseActivity
import com.panda.course.util.tvremote.NetUtil

/**
 * 代码可以直接用，这是UDP的方式 与RemoteReceiver配套
 */
class RemoteService : Service() {

    companion object {
        const val NOTIFICATION_ID = 1
    }

    override fun onBind(p0: Intent?): IBinder? {
        return null
    }

    override fun onCreate() {
        super.onCreate()

        if (!NetUtil.isRunning()) {
            NetUtil.init()
        }

        val builder = Notification.Builder(this)
        val intent = PendingIntent.getActivity(this, 0, Intent(this, CourseActivity::class.java), 0)
        builder.setContentIntent(intent)
        builder.setSmallIcon(R.mipmap.ic_launcher)
        builder.setTicker(javaClass.simpleName)
        builder.setContentTitle(javaClass.simpleName)
        builder.setContentText(javaClass.simpleName)
        val notification = builder.build()

        startForeground(NOTIFICATION_ID, notification)
    }

    override fun onDestroy() {
        super.onDestroy()
        stopForeground(true)
        val intent = Intent(RemoteReceiver.ACTION_DESTROY)
        sendBroadcast(intent)
    }

}