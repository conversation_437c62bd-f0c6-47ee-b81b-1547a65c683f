package com.panda.course.ui.activity

import android.bluetooth.BluetoothDevice
import android.os.Bundle
import android.view.KeyEvent
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.databinding.ActivityBleBinding
import com.panda.course.ext.logE
import java.lang.String


class BleActivity : BaseDbActivity<BaseViewModel, ActivityBleBinding>() {


    override fun initView(savedInstanceState: Bundle?) {

    }


    override fun initObserver() {

    }




}