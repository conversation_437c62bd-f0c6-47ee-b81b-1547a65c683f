package com.panda.course.ui.activity.dialog

import android.graphics.Bitmap
import android.os.Bundle
import android.view.KeyEvent
import android.view.animation.Animation
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityCourseImgBinding
import com.panda.course.ext.getScreenHeight
import com.panda.course.ext.gone
import com.panda.course.ext.toast
import com.panda.course.ext.visible
import com.panda.course.ui.adapter.ImageBigAdapter
import com.panda.course.ui.viewmodel.CourseImgViewModel
import com.panda.course.util.AnimationUtils
import com.panda.course.util.GlideUtil
import com.panda.course.util.NetworkUtil
import com.panda.course.widget.ScrollLinearLayoutManager
import com.youth.banner.indicator.CircleIndicator


class CourseImgDialogActivity : BaseDbActivity<CourseImgViewModel, ActivityCourseImgBinding>() {

    private var mAdapter = ImageBigAdapter()

    override fun initView(savedInstanceState: Bundle?) {
        intent.extras?.apply {
            getString("code")?.apply {
                mViewModel.getCourseImg(code = this)
            }
        }
        if (!NetworkUtil.isAvailable(this)) {
            "当前无网络，请检查当前网络情况是否正常".toast()
            finish()
        }
        mDataBind.recyclerView.layoutManager = ScrollLinearLayoutManager(this)
        mDataBind.recyclerView.adapter = mAdapter


        mDataBind.progressBar.postDelayed({
            mDataBind.flProgress.gone()
            taskButton()
        }, 2000)
    }

    override fun onRequestSuccess() {
        super.onRequestSuccess()
        mViewModel.courseImgEntity.observe(this, androidx.lifecycle.Observer {
            it.schedule_pic?.let { url ->
                val mList = ArrayList<String>()
                mList.add(url)
                mAdapter.setList(mList)
            }
        })
    }

    /**
     * 执行runnable
     */
    private var mHideButtonRunnable = Runnable {
        mDataBind.butBack.clearAnimation()
        mDataBind.butBack.startAnimation(
            AnimationUtils.getHiddenAlphaAnimation(1000, object : Animation.AnimationListener {

                override fun onAnimationStart(animation: Animation?) {}

                override fun onAnimationEnd(animation: Animation?) {
                    mDataBind.butBack.gone()
                }

                override fun onAnimationRepeat(animation: Animation?) {}
            })
        )
    }


    /**
     * 3秒后隐藏这个控制器
     */
    private fun taskButton() {
        mDataBind.butBack.visible()
        mDataBind.butBack.removeCallbacks(mHideButtonRunnable)
        mDataBind.butBack.postDelayed(mHideButtonRunnable, 3000)
    }


    //记录距离
    private var distance = 0

    /**
     * 改变recyclerView
     */
    private fun scrollToRecyclerViewHeight(up: Boolean) {
        if (up) {
            if (distance != 0) {
                mDataBind.recyclerView.smoothScrollBy(0, -distance)
            }
        } else {
            if (distance == 0) {
                distance = getScreenHeight() / 6
            }
            mDataBind.recyclerView.smoothScrollBy(0, distance)
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_DPAD_UP -> {
                scrollToRecyclerViewHeight(true)
            }
            KeyEvent.KEYCODE_DPAD_DOWN -> {
                scrollToRecyclerViewHeight(false)
            }
        }
        return super.onKeyDown(keyCode, event)
    }
}