package com.panda.course.ui.adapter

import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.RowEntity
import com.panda.course.entity.RowStringEntity
import com.panda.course.ext.*
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper

class AnswerAdapter : BaseQuickAdapter<RowStringEntity, BaseViewHolder>(R.layout.item_row_answer) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var onItemFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null


    private var answers = ArrayList<String>()

    fun setOnViewFocus(onItemFocus: OnViewFocus?) {
        this.onItemFocus = onItemFocus
    }

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(MyFocusHighlightHelper.ZOOM_FACTOR_XXSMALL, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()
    }

    override fun convert(holder: BaseViewHolder, item: RowStringEntity) {

        val imageView = holder.getView<ImageView>(R.id.iv_item_answer_row)
        val tag = holder.getView<ImageView>(R.id.iv_item_answer_tag)

        holder.getView<TextView>(R.id.tv_item_answer_index).text = "${(getItemPosition(item) + 1)}"


        Glide.with(context).load(item.cover_url).diskCacheStrategy(DiskCacheStrategy.RESOURCE) //磁盘缓存模式
            .apply(RequestOptions().transform(CenterCrop(), RoundedCornersTransform(context, 20f)))
            .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error).into(imageView)


        imageView.setOnClickListener {
            tag.gone()
            if (answers.contains((getItemPosition(item) + 1).toString())) {
                onItemFocus?.onChangeFocus(true, getItemPosition(item), imageView)
                item.isCorrect = true
                tag.setImageResource(R.drawable.icon_answer_ok)
                tag.visible()
            } else {
                onItemFocus?.onChangeFocus(false, getItemPosition(item), imageView)
                tag.setImageResource(R.drawable.icon_answer_error)
                tag.visible()
                imageView.alpha = 0.5f
                tag.postDelayed({
                    tag.gone()
                    imageView.alpha = 1.0f
                }, 1500)

            }
        }


        // 隐藏的代码是为了需求变更使用、之前是view放大，现在是View 选中状态
        imageView.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->
            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
            imageView.setBackgroundResource(if (hasFocus) R.drawable.shape_image_answer_stroke else 0)

            if (hasFocus) {
                imageView.clearAnimation()
                imageView.startAnimation(scaleAnimation)
            } else {
                imageView.clearAnimation()
            }
        }
    }


    fun updateAnswerList(answers: List<String>) {
        this.answers = ArrayList(answers)
    }

}