package com.panda.course.ui.activity

import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import androidx.lifecycle.Observer
import com.panda.course.R
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityNoticeBinding
import com.panda.course.ext.*
import com.panda.course.network.LoadStatusEntity
import com.panda.course.ui.adapter.NoticeListAdapter
import com.panda.course.ui.adapter.NoticeRowAdapter
import com.panda.course.ui.viewmodel.NoticeViewModel
import com.panda.course.util.NetworkUtil
import java.lang.reflect.Constructor
import java.lang.reflect.Field
import java.lang.reflect.Method


class NoticeActivity : BaseDbActivity<NoticeViewModel, ActivityNoticeBinding>() {

    private val TAG = "NoticeActivity"


    private val mRowAdapter = NoticeRowAdapter()
    private val mAdapter = NoticeListAdapter()
    private var page = 1
    private var mCourseCode = ""

    override fun initView(savedInstanceState: Bundle?) {
        setDialogWindowParams(window, 1.0f, 1.0f)
        initRecyclerView()
        mViewModel.getNoticeList(page)
        appUMEvent(UMConstant.HOME_NOTICE_MESSAGE)
    }

    private fun initRecyclerView() {
//        mDataBind.recyclerNoticeRow.adapter = mRowAdapter
        mDataBind.recyclerNotice.adapter = mAdapter
//        mDataBind.recyclerNotice.layoutManager = CenterLayoutManager(this, LinearLayoutManager.VERTICAL, false)

        mDataBind.empty.loadingEmptyString.text = getString(R.string.empty_text)
    }


    override fun onBindViewClick() {
    }

    override fun initObserver() {

        // 监听当前网络变化
        eventViewModel.monitorNetWork.observe(this, Observer {
            if (this == currentActivity) {
                if (it) {
                    if (NetworkUtil.isAvailable(this)) {
                        dismissLoadingNetWorkExt()
                        onLoadRetry()
                    }
                } else {
                    showNetDialog { onLoadRetry() }
                }
            }
        })

        mAdapter.setOnItemChildClickListener { _, view, position ->
            if (view.id == R.id.row_card_notice_list_view) {
                if (!TextUtils.isEmpty(mAdapter.data[position].course_code.toString())) {
                    mCourseCode = mAdapter.data[position].course_code.toString()
                    mViewModel.getCourseDetail(mAdapter.data[position].course_code, eventViewModel.appUserInfo.value?.uuid, false)
                }
            }
        }

        mAdapter.setOnItemFocus { hasFocus, position, _ ->
            if (hasFocus) {
                if ("0" == mAdapter.data[position].is_read) {
                    mAdapter.data[position].number?.let {
                        mAdapter.data[position].is_read = "1"
                        mViewModel.sendNotice(it)
                    }
                }
                if (mAdapter.data.size != mViewModel.noticeListEntity.value?.total ?: 10) {
                    page++
                    mViewModel.getNoticeList(page)
                }
                //检查是否还有未读的item 同步给首页
                checkReadStatus()
            }
        }
    }

    private fun checkReadStatus() {
        var readCount = 0
        for (i in mAdapter.data.indices) {
            if ("0" == mAdapter.data[i].is_read) {
                readCount += 1
            }
        }
        eventViewModel.noticeCount.value = readCount
    }

    override fun onRequestSuccess() {
        mViewModel.noticeListEntity.observe(this, Observer {
            if (it.list.isNullOrEmpty()) {
                return@Observer
            }
            mDataBind.empty.stateEmptyLinear.gone()
            if (1 == it.page) {
                mAdapter.setList(it.list)
            } else {
                mAdapter.addData(it.list)
            }
        })

        mViewModel.mCourseDetail.observe(this, androidx.lifecycle.Observer {
            if (it == null || it.video_list.isNullOrEmpty()) {
                showMessageDialog("课程已下架，请打开熊猫系统App-AI课-课程配置，操作上架，才可观看课程。")
                return@Observer
            }
            val bundle = Bundle()
            bundle.putString("course_code", mCourseCode)
            bundle.putString("uuid", eventViewModel.appUserInfo.value?.uuid)
            toStartActivity(ContentActivity::class.java, bundle)
        })
    }

    override fun onRequestError(loadStatus: LoadStatusEntity) {
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        if (keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE || keyCode == KeyEvent.KEYCODE_HOME) {
            return true
        }

        if (keyCode == KeyEvent.KEYCODE_DPAD_UP || keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
            if (event.repeatCount != 0) {
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }


}