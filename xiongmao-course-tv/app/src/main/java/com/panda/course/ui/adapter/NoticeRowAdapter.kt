package com.panda.course.ui.adapter

import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.CourseListEntity
import com.panda.course.entity.NoticeRowEntity
import com.panda.course.ext.*
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper
import www.linwg.org.lib.LCardView


class NoticeRowAdapter : BaseQuickAdapter<NoticeRowEntity, BaseViewHolder>(R.layout.item_notice_row) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var mOnViewFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight = MyFocusHighlightHelper.BrowseItemFocusHighlight(MyFocusHighlightHelper.ZOOM_FACTOR_XXSMALL, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()

    }

    fun setOnItemFocus(onItemFocus: OnViewFocus?) {
        this.mOnViewFocus = onItemFocus
    }

    override fun convert(holder: BaseViewHolder, item: NoticeRowEntity) {

        val view = holder.getView<TextView>(R.id.iv_item_notice_row)
        val cardView = holder.getView<CardView>(R.id.row_card_notice_view)

        view.text = item.name

        when {
            getItemPosition(item) == 0 -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(120f))
                layoutParams.setMargins(dp2px(20f), dp2px(20f), dp2px(20f), dp2px(0f))
                cardView.layoutParams = layoutParams
            }
            getItemPosition(item) == (data.size - 1) -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(120f))
                layoutParams.setMargins(dp2px(20f), dp2px(20f), dp2px(20f), dp2px(20f))
                cardView.layoutParams = layoutParams
            }
            else -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(120f))
                layoutParams.setMargins(dp2px(20f), dp2px(20f), dp2px(20f), dp2px(0f))
                cardView.layoutParams = layoutParams
            }
        }

        view.setOnClickListener {
            mOnViewFocus?.onChangeFocus(true, getItemPosition(item), view)
        }

        // 隐藏的代码是为了需求变更使用、之前是view放大，现在是View 选中状态
        view.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->

            mBrowseItemFocusHighlight?.onItemFocused(cardView, hasFocus)

            mOnViewFocus?.onChangeFocus(hasFocus, getItemPosition(item), view)

            // 设置阴影
            cardView.cardElevation = if (hasFocus) px2dp(20f).toFloat() else px2dp(0f).toFloat()

            cardView.setCardBackgroundColor(
                if (hasFocus) ContextCompat.getColor(
                    context, R.color.black
                ) else ContextCompat.getColor(context, R.color.transparent)
            )

            if (hasFocus) {
                cardView.clearAnimation()
                cardView.startAnimation(scaleAnimation)
            } else {
                cardView.clearAnimation()
            }

            cardView.radius = if (hasFocus) 16f else 0f
        }
    }


}