package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

/**
 * 消息通知相关
 */
class InteractionViewModel : BaseViewModel() {

    // 排行榜
    var topList = MutableLiveData<TopList>()
    var interactiveListEntity = MutableLiveData<InteractiveListEntity>()


    /**
     * 获取榜单数据
     */
    fun getTopList(code: String, type: Int) {
        rxHttpRequest {
            onRequest = {
                topList.value = RxHttp.get(NetUrl.STORE_TOP_LIST)
                    .add("code", code)
                    .add("device_code", getSerialNumber())
                    .add("status", type).add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).toResponse<TopList>().await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.STORE_TOP_LIST
        }
    }

    /**
     * 获取榜单数据
     */
    fun getAllTopList(code: String) {
        rxHttpRequest {
            onRequest = {
                topList.value = RxHttp.get(NetUrl.TOP_LIST)
                    .add("code", code)
                    .add("status", "3")//获取历史数据
                    .add("device_code", getSerialNumber())
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).toResponse<TopList>().await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.TOP_LIST
        }
    }


    /**
     * 获取师生互动的列表
     */
    fun getInteractiveList(page: Int, code: String) {
        rxHttpRequest {
            onRequest = {
                interactiveListEntity.value = RxHttp.get(NetUrl.GET_POST_LIST)
                    .add("page", page)
                    .add("size", "100")
                    .add("is_teacher_reply", "1")
                    .add("course_code", code)
                    .add("device_code", getSerialNumber())
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).toResponse<InteractiveListEntity>().await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_POST_LIST
        }
    }


}