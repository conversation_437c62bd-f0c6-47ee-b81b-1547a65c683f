package com.panda.course.ui.activity

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import com.droidlogic.app.OutputModeManager
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.databinding.ActivityHdmiBinding
import com.panda.course.entity.SystemInfo
import com.panda.course.ext.*
import com.panda.course.ui.adapter.SystemAdapter
import com.panda.course.util.MMKVHelper

class HdmiActivity : BaseDbActivity<BaseViewModel, ActivityHdmiBinding>() {

    private var mAdapter = SystemAdapter()
    private var mOutputModeManager: OutputModeManager? = null

    override fun initView(savedInstanceState: Bundle?) {
        //display  mode
        mOutputModeManager = OutputModeManager(this)
        mOutputModeManager?.highestMatchResolution.logE()
//        val list = mOutputModeManager?.hdmiSupportList
//        "$list".logE()
        //480i60hz,576i50hz,480p60hz,576p50hz,720p60hz,1080i60hz,1080p60hz,720p50hz*,1080i50hz,1080p50hz
//        mOutputModeManager.setBestMode("1080p50hz") //设置显示模式分辨率  //1080p50hz
        mDataBind.recycler.setColumnNumbers(1)
        mDataBind.recycler.adapter = mAdapter
        mAdapter.setList(getListData())
        mOutputModeManager?.currentOutputMode?.let { mAdapter.updateItem(it) }

        "${mOutputModeManager?.hdmiSupportList}".logE()

        mDataBind.tvCurrentMode.text = getString(R.string.sys_hdmi_crurren, "${mOutputModeManager?.currentOutputMode}")

    }

    override fun onBindViewClick() {
        super.onBindViewClick()

        mAdapter.setOnItemClickListener { _, _, position ->
            "点击的${mAdapter.data[position].name}".logE()
            mAdapter.updateItem(mAdapter.data[position].name)
            mOutputModeManager?.setBestMode(mAdapter.data[position].name.replace(getString(R.string.sys_hdmi_auto), ""))
            mDataBind.tvCurrentMode.text = getString(R.string.sys_hdmi_crurren, "${mOutputModeManager?.currentOutputMode}")
            MMKVHelper.encode(ConstantMMVK.HDMI_SCREEN, mAdapter.data[position].name.replace(getString(R.string.sys_hdmi_auto), ""))
        }
    }

    private fun getListData(): ArrayList<SystemInfo> {
        val arrayList = ArrayList<SystemInfo>()
        arrayList.add(SystemInfo(getString(R.string.sys_hdmi_auto) + mOutputModeManager?.highestMatchResolution, ""))

        if (!mOutputModeManager?.hdmiSupportList.isNullOrEmpty()) {
            val hdmiArr = mOutputModeManager?.hdmiSupportList?.split(",")
            for (i in hdmiArr!!.indices) {
                if (!TextUtils.isEmpty(hdmiArr[i])) {
                    arrayList.add(SystemInfo(hdmiArr[i], ""))
                }
            }
        }
//        arrayList.add(SystemInfo("自动：1080p50hz", ""))
//        arrayList.add(SystemInfo("480i60hz", ""))
//        arrayList.add(SystemInfo("576i50hz", ""))
//        arrayList.add(SystemInfo("480p60hz", ""))
//        arrayList.add(SystemInfo("576p50hz", ""))
//        arrayList.add(SystemInfo("720p60hz", ""))
//        arrayList.add(SystemInfo("1080i60hz", ""))
//        arrayList.add(SystemInfo("1080p60hz", ""))
//        arrayList.add(SystemInfo("720p50hz*", ""))
//        arrayList.add(SystemInfo("1080i50hz", ""))
//        arrayList.add(SystemInfo("1080p50hz", ""))

        return arrayList
    }


}