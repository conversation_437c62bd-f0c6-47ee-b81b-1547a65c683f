package com.panda.course.ui.activity

import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.os.RecoverySystem
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.view.View
import androidx.annotation.RequiresApi
import com.alibaba.fastjson.JSON
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.signature.ObjectKey
import com.mallotec.reb.localeplugin.utils.LocaleHelper
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivitySplashLoginBinding
import com.panda.course.entity.QRCodeEntity
import com.panda.course.entity.UserEntity
import com.panda.course.ext.*
import com.panda.course.receiver.HomeReceiver
import com.panda.course.receiver.NetBroadcastReceiver
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.util.MMKVHelper
import com.panda.course.util.MMKVHelper.decodeString
import com.panda.course.util.MMKVHelper.encode
import com.panda.course.util.NetworkUtil
import com.panda.course.util.QRCodeUtil
import com.panda.course.util.RxTimerUtil
import com.umeng.analytics.MobclickAgent
import com.umeng.commonsdk.stateless.UMSLEnvelopeBuild.mContext
import io.reactivex.rxjava3.disposables.Disposable
import java.io.File
import java.io.IOException
import java.util.Locale


/**
 * 1.默认获取登录的二维码
 * 2.二维码失效时间1m
 * 3.每间隔3s去刷新是否登录接口
 * 4.保存数据,关闭界面，销毁定时器等
 */
class MainActivity : BaseDbActivity<CourseViewModel, ActivitySplashLoginBinding>(),
    NetBroadcastReceiver.NetStateChangeObserver {
    private val TAG = "SplashLogin"
    private var qrId: String? = null
    private var qrDisposable: Disposable? = null //1分钟刷新二维码的定时
    private var qrRefreshDisposable: Disposable? = null //3s刷新二维码状态

    private var isJumpCourse: Boolean = false //记录是否需要跳转到course再获取信息

    private val mHomeReceiver = HomeReceiver()


    override fun getLoadingView(): View {
        return mDataBind.ivSplashLogin
    }

    // SDK_INT 28 是android 9 android 9的情况去连接WIFI
    override fun onCreate(savedInstanceState: Bundle?) {
        LocaleHelper.getInstance().language(Locale.ROOT).apply(this)

        "当前版本号---${android.os.Build.VERSION.SDK_INT}".logE(TAG)
        if (getCurrentAndroid9()) {
//            JumpWifi()
        }
        finishOtherMainActivity()
        val info = decodeString(ConstantMMVK.USER_INFO)
        "local = ${info}".logD(TAG)
        if (!TextUtils.isEmpty(info)) { // 如果不等于null 并且login_states true
            eventViewModel.appUserInfo.value = JSON.parseObject(info, UserEntity::class.java)
            eventViewModel.appUserInfo.value?.let {
                JumpMain()
            }
        } else {
            val token = decodeString(ConstantMMVK.TOKEN)
            "token = ${token}".logD(TAG)
            token.notTextNull {
                isJumpCourse = true
                JumpMain()
            }
        }

        super.onCreate(savedInstanceState)
        NetBroadcastReceiver.registerReceiver(this, this)
        val filter = IntentFilter(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)
        registerReceiver(mHomeReceiver, filter)
    }

    @RequiresApi(Build.VERSION_CODES.N)
    override fun initView(savedInstanceState: Bundle?) {
        mViewModel.getQrCode(getDeviceID(), getLocalIpAddress())
        ("唯一标识-->" + getAndroidId()).logE(TAG)
        ("唯一标识-->" + getWireMac()).logE(TAG)
        ("唯一标识-->" + getSerialNumber()).logE(TAG)
        ("唯一标识-->" + NetworkUtil.isAvailable(this)).logE(TAG)
        ("唯一标识-->" + getCacheDir().absolutePath).logE(TAG)
    }

    override fun onResume() {
        super.onResume()
        onLoadRetry()
    }

    override fun onLoadRetry() {
        mViewModel.getOTAInfo()
        mViewModel.getQrCode(getDeviceID(), getLocalIpAddress())
        if (qrDisposable != null) {
            qrDisposable?.dispose()
        }
        qrDisposable = RxTimerUtil.interval((1000 * 60 * 60).toLong()).subscribe {
            if (currentActivity == this) {
                mViewModel.getQrCode(getDeviceID(), getLocalIpAddress())
            } else {
                closeQrDisposable()
            }
        }
    }


    override fun onRequestSuccess() {
        mViewModel.qrCodeEntity.observe(this, androidx.lifecycle.Observer { data: QRCodeEntity ->
            if (data == null) {
                return@Observer
            }
            qrId = data.code
            encode(ConstantMMVK.DEVICE_ID, data.device_id)
            "${data.qr_code_content} 这是内容".logE()
            mDataBind.ivSplashLogin.setImageBitmap(QRCodeUtil.getInstance().createQRCode(data.qr_code_content));
            refreshQrLogin()
        })
        mViewModel.userEntity.observe(this, androidx.lifecycle.Observer { userEntity: UserEntity ->
            if (userEntity == null) {
                return@Observer
            }
            saveUserInfo(userEntity = userEntity)
        })

        mViewModel.mOTAUpdateInfo.observe(this, androidx.lifecycle.Observer {
            if (it == null) {
                eventViewModel.appOTAUpdate.value = false
                return@Observer
            }
            eventViewModel.appOTAUpdate.value = true
        })

//        mViewModel.deviceExpireDate.observe(this, androidx.lifecycle.Observer {
//            if (it == null) {
//                return@Observer
//            }
//            eventViewModel.deviceExpireDate.value = it
//        })
    }

    private fun saveUserInfo(userEntity: UserEntity) {
        if (userEntity.login_status) {
            eventViewModel.appUserInfo.value = userEntity
            MobclickAgent.onProfileSignIn(getWireMac())
            closeDisposableAll() //保存信息到本地
            encode(ConstantMMVK.DEVICE_ID, userEntity.device_id)
            encode(ConstantMMVK.TOKEN, userEntity.token)
            encode(ConstantMMVK.USER_INFO, JSON.toJSONString(userEntity))
            "登录成功 ${userEntity.toString()}".logD(TAG)
            "登录成功 ${JSON.toJSONString(userEntity)}".logD(TAG)
            JumpMain()
        }
    }

    private fun refreshQrLogin() {
        if (qrRefreshDisposable != null) {
            qrRefreshDisposable?.dispose()
        }
        qrRefreshDisposable = RxTimerUtil.interval(3000).subscribe {
            if (currentActivity == this) {
                "${getWireMac()} | ${getSerialNumber()}".logE("测试内容")
                if (!TextUtils.isEmpty(qrId)) {
                    Log.e("二维码界面l", "重新刷新状态了")
                    mViewModel.getRefreshLoginStatus(qrId!!)
                }
            } else {
                closeQrRefreshDisposable()
            }
        }
    }

    private fun closeQrDisposable() {
        if (qrDisposable != null) {
            qrDisposable!!.dispose()
            qrDisposable = null
        }
    }

    private fun closeQrRefreshDisposable() {
        if (qrRefreshDisposable != null) {
            qrRefreshDisposable!!.dispose()
            qrRefreshDisposable = null
        }
    }

    private fun closeDisposableAll() {
        closeQrDisposable()
        closeQrRefreshDisposable()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_MENU -> {
                goSystemTvBoxSetting(this)
                return true
            }

            KeyEvent.KEYCODE_BACK -> {
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun JumpMain() {
        val bundle = Bundle()
        bundle.putBoolean("isJumpCourse", isJumpCourse)
        toStartActivity(CourseActivity::class.java, bundle)
        finishActivity(this)
    }

    private fun JumpWifi() {
        val bundle = Bundle()
        bundle.putBoolean("isJumpCourse", isJumpCourse)
        toStartActivity(SettingsActivity::class.java, bundle)
        finishActivity(this)
    }

    private fun JumpFirstRemote() {
        val bundle = Bundle()
        toStartActivity(RemoteControlActivity::class.java, bundle)
        finishActivity(this)
    }


    override fun onDestroy() {
        super.onDestroy()
        closeDisposableAll()
        unregisterReceiver(mHomeReceiver)
        NetBroadcastReceiver.unRegisterReceiver(this, this)
    }

    override fun onNetWorkConnect() {
        if (NetworkUtil.isAvailable(this)) {
            dismissLoadingNetWorkExt()
            onLoadRetry()
        }
    }

    override fun onDisconnect() {
        if (this == currentActivity) {
            showNetDialog { onLoadRetry() }
        }
    }
}