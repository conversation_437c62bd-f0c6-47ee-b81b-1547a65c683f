package com.panda.course.widget.popup

import android.content.Context
import com.lxj.xpopup.core.DrawerPopupView
import com.panda.course.R
import com.panda.course.callback.OnVideoEffectsListener

/**
 * 这里主要做视频的控制器交互效果
 * 还没想好怎么拆出来
 */
class VideoControllerPopupView(context: Context, var listener: OnVideoEffectsListener?) :
    DrawerPopupView(context) {

    override fun getImplLayoutId() = R.layout.popup_video_effects


    override fun onCreate() {
        super.onCreate()
    }

    override fun onDismiss() {
        super.onDismiss()
        //处理一下变量的处理
        listener?.onDismiss()
    }
}