package com.panda.course.ui.activity.dialog

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.view.KeyEvent
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.panda.course.R
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.data.CommonDataUtil
import com.panda.course.databinding.ActivityCheckNetWorkBinding
import com.panda.course.databinding.ActivityMaketingBinding
import com.panda.course.entity.SurpriseListContentEntity
import com.panda.course.ext.getEmptyView
import com.panda.course.ext.logE
import com.panda.course.ext.setDialogWindowParams
import com.panda.course.ui.adapter.MarketingAdapter
import com.panda.course.ui.viewmodel.CourseViewModel

class MarketingDialogActivity : BaseDbActivity<CourseViewModel, ActivityMaketingBinding>() {

    private val marketingAdapter = MarketingAdapter()

    /**
     * 初始化view
     */
    override fun initView(savedInstanceState: Bundle?) {
        setDialogWindowParams(window, 0.7f)
        mViewModel.getSurpriseActivityAdList()
        mDataBind.recyclerView.layoutManager = LinearLayoutManager(this)
        mDataBind.recyclerView.adapter = marketingAdapter
        marketingAdapter.setEmptyView(getEmptyView(tips = "暂无领取记录", R.color.black))
    }


    override fun onRequestSuccess() {
        super.onRequestSuccess()
        mViewModel.surpriseListEntity.observe(this, androidx.lifecycle.Observer{
            marketingAdapter.setList(it.list)
            "${marketingAdapter.data.size}".logE("大小")

            mDataBind.recyclerView.postDelayed({
                if (marketingAdapter.data.size > 0) {
                    val mView = marketingAdapter.getViewByPosition(0, R.id.rl_common_item_view)
                    mView?.requestFocus()
                }
            }, 200)
        })
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        if (keyCode == KeyEvent.KEYCODE_ENTER || keyCode == KeyEvent.KEYCODE_DPAD_CENTER || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE
            || keyCode == KeyEvent.KEYCODE_HOME || keyCode == KeyEvent.KEYCODE_PROFILE_SWITCH) {
            onLoadRetry()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }
}