package com.panda.course.ui.activity.wifi

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.view.KeyEvent
import com.panda.course.R
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.databinding.ActivityNetDetailsBinding
import com.panda.course.ext.*

class NetDetailsActivity : BaseDbActivity<BaseViewModel, ActivityNetDetailsBinding>() {

    override fun initView(savedInstanceState: Bundle?) {
        intent?.extras?.apply {
            getString("wifi_type")?.let { wifi_type ->
                if (wifi_type == "1") {
                    getString("wifi_name").notTextNull {
                        mDataBind.tvWifiName.text = it
                    }
                } else {
                    mDataBind.tvWifiName.text = "有线网络"
                    mDataBind.butDisWifi.gone()
                }
            }
        }

        //网关、子网掩码、ip地址
        mDataBind.tvNetDetailsIp.text = "${getIPAddress()}"
        mDataBind.tvNetDetailsGateway.text = "${getGateWay()}"
        mDataBind.tvNetDetailsSubnetMask.text = "${getIpAddressMaskForInterfaces()}"
    }


    override fun onBindViewClick() {
        super.onBindViewClick()
        mDataBind.butDisWifi.setOnClickListener {  // 通知上层关闭当前的wifi 然后重新去整理
            eventViewModel.wifiState.value = true
            finish()
        }
    }




}