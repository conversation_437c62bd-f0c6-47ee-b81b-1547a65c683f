package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class IndexEntity(
    var history_list: List<CourseDetailInfo>? = null,
    var ad: List<Ad>? = emptyList(),
    var live_course_list: List<LiveCourseEntity>? = emptyList(),
    var store_log: String? = null,
    var aunt_skill_pic: String? = null,
    var honor_pic: String? = null,
    var is_ai_live_course: Boolean = false,
    var recruitment_order_pic: String? = null,
    var student_ranking_pic: String? = null,
) : Parcelable


@Parcelize
data class Ad(
    // 1是默认 2不是
    var is_default: String? = null,
    var id: String? = null,
    var source_type: String? = null,
    var cover_url: String? = null,
    var effects_time: String? = null,
    var media_name: String? = null,
    var source_list: List<SourceList>? = null,
) : Parcelable


/**
 * 自定义state 类型
 * 0 停止播放
 * 1 开始播放
 * 2 暂停播放
 */
@Parcelize
data class SourceList(
    var type: Int = 1,
    var source_url: String? = null,
    var cover_url: String? = null,
    var state: Int,
    var hd_video_url_list: CourseHDUrlListInfo?,
) : Parcelable