package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class OrderHallViewModel : BaseViewModel() {

    // 测评课程
    var orderHallTypeListEntity = MutableLiveData<OrderHallTypeEntity>()

    // 测评记录
    var detailsListEntity = MutableLiveData<OrderHallTypeDetailsEntity>()


    /**
     * 获取全部的技能测评
     */
    fun getOrderHallAllType() {
        rxHttpRequest {
            onRequest = {
                orderHallTypeListEntity.value = RxHttp.get(NetUrl.GET_ORDER_HALL_ALL)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("is_total", "1")
                    .toResponse<OrderHallTypeEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_ORDER_HALL_ALL
        }
    }

    /**
     * 获取全部的技能测评记录
     */
    fun getOrderHallList(page: Int, aunt_type: String? = null) {
        rxHttpRequest {
            onRequest = {
                detailsListEntity.value = RxHttp.get(NetUrl.GET_ORDER_HALL_DETIATIL)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("page", "$page")
                    .add("size", "" + ConstantMMVK.PAGE_SIZE)
                    .add("aunt_type", aunt_type)
                    .toResponse<OrderHallTypeDetailsEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_ORDER_HALL_DETIATIL
        }
    }


}