package com.panda.course.ui.adapter

import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.*
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.CourseDetailInfo
import com.panda.course.entity.GraduateListImgEntity
import com.panda.course.ext.dp2px
import com.panda.course.ext.gone
import com.panda.course.ext.px2dp
import com.panda.course.ext.visible
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper

class GraduationMapAdapter : BaseQuickAdapter<GraduateListImgEntity, BaseViewHolder>(R.layout.item_graduation_map) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var mOnViewFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null


    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false)
        }
        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()
    }

    fun setOnItemFocus(onItemFocus: OnViewFocus?) {
        this.mOnViewFocus = onItemFocus
    }

    override fun convert(holder: BaseViewHolder, item: GraduateListImgEntity) {
        val bigImageView = holder.getView<ImageView>(R.id.iv_item_big_cover)



        Glide.with(context).load(item.img_url)
            .apply(RequestOptions().transform(CenterCrop(), RoundedCornersTransform(context, 10f))).dontAnimate()
            .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error).into(bigImageView)


        val cardView = holder.getView<CardView>(R.id.content_card_view)
        val flLayout = holder.getView<FrameLayout>(R.id.fl_item_layout)
        val tvTips = holder.getView<TextView>(R.id.tv_item_tips)

        when {
            getItemPosition(item) % 2 == 0 -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(154f))
                layoutParams.setMargins(dp2px(0f), dp2px(20f), dp2px(20f), dp2px(0f))
                cardView.layoutParams = layoutParams
            }

            else -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(154f))
                layoutParams.setMargins(dp2px(0f), dp2px(20f), dp2px(20f), dp2px(0f))
                cardView.layoutParams = layoutParams
            }
        }


        cardView.setOnFocusChangeListener { v, hasFocus ->
            mBrowseItemFocusHighlight?.onItemFocused(cardView, hasFocus)

            flLayout.visibility = if (hasFocus) View.VISIBLE else View.GONE
            tvTips.visibility = if (hasFocus) View.VISIBLE else View.GONE

            mOnViewFocus?.onChangeFocus(hasFocus, getItemPosition(item), cardView)

            cardView.cardElevation = if (hasFocus) px2dp(10f).toFloat() else px2dp(0f).toFloat()
            cardView.setCardBackgroundColor(
                if (hasFocus) ContextCompat.getColor(
                    context, R.color.black
                ) else ContextCompat.getColor(context, R.color.transparent)
            )
            cardView.radius = if (hasFocus) 16f else 0f

            if (hasFocus) {
                cardView.startAnimation(scaleAnimation)
            } else {
                cardView.clearAnimation()
            }
        }
    }
}