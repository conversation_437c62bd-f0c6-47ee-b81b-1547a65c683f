package com.panda.course.widget.popup

import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.media.MediaPlayer
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.ArrayMap
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lxj.xpopup.impl.FullScreenPopupView
import com.noober.background.drawable.DrawableCreator
import com.orient.tea.barragephoto.adapter.BarrageAdapter
import com.orient.tea.barragephoto.ui.BarrageView
import com.panda.course.R
import com.panda.course.callback.OnVideoEffectsListener
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.ConstantMMVK
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.NetUrl
import com.panda.course.service.MultiTaskDownloader
import com.panda.course.ui.activity.PlayerActivity
import com.panda.course.ui.adapter.AnswerAdapter
import com.panda.course.ui.adapter.CustomBarrageViewHolder
import com.panda.course.ui.adapter.VideoSignAdapter
import com.panda.course.ui.adapter.VideoSignNewAdapter
import com.panda.course.util.*
import com.panda.course.widget.LikeAnimView
import com.tencent.liteav.demo.superplayer.SuperPlayerModel
import com.tencent.liteav.demo.superplayer.SuperPlayerView
import com.tencent.liteav.demo.superplayer.model.entity.VideoQuality
import com.tencent.qcloud.tuicore.util.ScreenUtil
import io.reactivex.rxjava3.disposables.Disposable
import kotlinx.coroutines.*
import okio.ByteString.Companion.encodeUtf8
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.asResponse
import java.util.*
import kotlin.collections.ArrayList


/**
 * 这里主要做视频的动态效果
 * 1.签到、答题、课间休息(需要播放视频)
 * 2.需要针对不同的动效模版进行不同的展示
 * 3.做好关键点的统计上报
 * 4.处理好对变量，数据的销毁、避免内存泄漏
 * 5.以及弹幕效果
 * 6.有个问题，如果播放过程中，去了设置界面，怎么去处理？？？回调方法？
 * ##是否 需要做 资源主动预下载的操作？？？
 */
class VideoEffectsPopupView(var activity: PlayerActivity, var videoRete: Float, var listener: OnVideoEffectsListener?, var data: VideoDotEntity, var courseCode: String) : FullScreenPopupView(activity) {


    // 倒计时
    private var mCountDown: Disposable? = null

    //记录语音播报的点
    private var mTextVoiceHashMap = ArrayMap<String, TextList>()

    //签到人员的Adapter
    private var mSignAdapter = VideoSignAdapter()
    private var mSignNewAdapter = VideoSignNewAdapter()

    // 检测IM的数量 列表会用到
    private var IM_COUNT = 1

    //短暂记录倒计时的情况
    private var mCountDownTimer: Int = 0

    private var rlRoot: RelativeLayout? = null
    private var rlLayout: RelativeLayout? = null
    private var rlNewLayout: RelativeLayout? = null
    private var ivRlLayout: ImageView? = null
    private var ivQrCode: ImageView? = null
    private var ivNewQrCode: ImageView? = null
    private var ivNewLayout: ImageView? = null
    private var ivPerson: ImageView? = null
    private var tvTimer: TextView? = null
    private var tvNewTimer: TextView? = null
    private var tvNewTimer1: TextView? = null
    private var tvTimerContent: TextView? = null
    private var tvNewTips: TextView? = null
    private var recyclerView: RecyclerView? = null
    private var recyclerViewNew: RecyclerView? = null
    private var likeAnimView: LikeAnimView? = null
    private var likeAnimView1: LikeAnimView? = null
    private var tvNumber: TextView? = null
    private var tvNewNumber: TextView? = null
    private var superPlayer: SuperPlayerView? = null
    private var barrageView: BarrageView? = null
    private var tvNewTitle: TextView? = null

    //新答题动效
    private var tvNewAnswerTitle: TextView? = null
    private var rlNewAnswerLayout: RelativeLayout? = null
    private var ivNewAnswerLayout: ImageView? = null
    private var tvNewAnswerTimer: TextView? = null
    private var tvNewAnswerTimer1: TextView? = null
    private var tvNewAnswerResult: TextView? = null
    private var mRecyclerAnswer: RecyclerView? = null
    //新答题动效

    //答题
    private var mAnswerAdapter = AnswerAdapter()

    //弹幕的内容
    private var mBarrAgeAdapter: BarrageAdapter<BarrageData>? = null

    private val FORWARD_REWIND_TIME_INTERVAL = 100L

    private val mHandler = Handler(Looper.getMainLooper())

    private val mRunnable: Runnable = object : Runnable {
        override fun run() {
            initAction()
        }
    }

    override fun getImplLayoutId() = R.layout.popup_video_effects

    override fun onCreate() {
        super.onCreate()
        initView()
        initObserver()
        initDanmuKu()
        mHandler.postDelayed(mRunnable, FORWARD_REWIND_TIME_INTERVAL)
    }


    private fun initView() {
        rlRoot = findViewById(R.id.rl_root_main)
        rlLayout = findViewById(R.id.rl_dialog_custom_sign_layout)
        rlNewLayout = findViewById(R.id.rl_new_layout)
        ivRlLayout = findViewById(R.id.iv_dialog_custom_sign_layout)
        tvTimer = findViewById(R.id.tv_dialog_sign_timer)
        tvTimerContent = findViewById(R.id.tv_dialog_content)
        tvNewTips = findViewById(R.id.tv_new_tips)
        ivPerson = findViewById(R.id.iv_dialog_cat)
        ivQrCode = findViewById(R.id.iv_dialog_qr_code)
        ivNewLayout = findViewById(R.id.iv_new_layout)
        recyclerView = findViewById(R.id.recycler_dialog)
        tvNumber = findViewById(R.id.tv_dialog_sign_number)
        likeAnimView = findViewById(R.id.live_dialog_view)
        likeAnimView1 = findViewById(R.id.live_dialog_view1)
        superPlayer = findViewById(R.id.superplayer_dialog)
        barrageView = findViewById(R.id.danmuku_dialog_view)
        recyclerViewNew = findViewById(R.id.recycler_new)
        tvNewTitle = findViewById(R.id.tv_new_title)
        tvNewNumber = findViewById(R.id.tv_new_number)
        tvNewTimer = findViewById(R.id.tv_new_timer)
        tvNewTimer1 = findViewById(R.id.tv_new_timer1)
        ivNewQrCode = findViewById(R.id.iv_new_qrcode)

        tvNewAnswerTitle = findViewById(R.id.tv_new_answer_title)
        rlNewAnswerLayout = findViewById(R.id.rl_new_answer_layout)
        ivNewAnswerLayout = findViewById(R.id.iv_new_answer_layout)
        tvNewAnswerTimer = findViewById(R.id.tv_new_answer_timer)
        tvNewAnswerTimer1 = findViewById(R.id.tv_new_answer_timer1)
        tvNewAnswerResult = findViewById(R.id.tv_new_answer_result)
        mRecyclerAnswer = findViewById(R.id.recycler_new_answer)

        mSignAdapter = VideoSignAdapter()
        recyclerView?.adapter = mSignAdapter
        recyclerView?.layoutManager = LinearLayoutManager(activity)

        //新的列表
        recyclerViewNew?.adapter = mSignNewAdapter
        recyclerViewNew?.layoutManager = LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false)

        //新答题效果
        mAnswerAdapter = AnswerAdapter()
        mRecyclerAnswer?.adapter = mAnswerAdapter
        mRecyclerAnswer?.layoutManager = GridLayoutManager(context, 2)
//        mRecyclerAnswer?.addItemDecoration(SpaceItemDecoration(2, px2dp(40f), true))
    }


    //监听一些view
    private fun initObserver() { //监听Im的消息推送
        eventViewModel.listenForIMPushMessages.observe(activity, Observer {
            try {
                "收到IM推送的消息 = $it".logE()
                if (it == null) {
                    return@Observer
                }
                if (!isShow) {
                    "不显示了，拦截 = $it".logE()
                    return@Observer
                }
                when (it.type) {
                    "1" -> {
                        it.index = "$IM_COUNT"
                        IM_COUNT += 1
                        mSignAdapter.addData(it)
                        likeAnimView?.addFavor()
                        peopleCounting()
                    }
                    "2" -> {
                        data.id?.let { it1 -> getExerciseListCode(courseCode, it1, false) }
                        likeAnimView?.addFavor()
                    }
                    "11" -> {
                        likeAnimView1?.addFavor()
                        IM_COUNT += 1
                        mSignNewAdapter.addData(it)
                        peopleCounting()
                    }
                    "12" -> {
                        likeAnimView1?.addFavor()
                        data.id?.let { it1 -> getExerciseListCode(courseCode, it1, false) }
                    }
                    "30" -> {//这里是弹幕
                        addDanmu(it)
                    }
                }

            } catch (e: Exception) {
                e.printStackTrace()
            }
        })
    }

    //效果类型 1签到效果 2答题效果 3红包效果 4课间休息 8是新答题效果 11新签到效果 12新答题效果 14新课间休息
    private fun initAction() {
        try {
            data.let { data ->
                "进来这里了 3- data = ${data}".logE()
                tvTimerContent?.text = ""
                mSignAdapter.data.clear()
                mSignAdapter.notifyDataSetChanged()
                IM_COUNT = 1
                mSignAdapter.setViewBg(data.effect_material?.right_act_bg_color, data.effect_material?.right_act_color)

                when (data.effect_type) {
                    "1" -> {
                        GlideUtil.loadPic(context, data.effect_material?.background, ivRlLayout)//加载背景图
                        GlideUtil.loadGif(context, data.effect_material?.human, ivPerson)
                        getExerciseListCode(courseCode, data.id.toString(), true)
                        getQrCode(NetUrl.SIGN_QR_CODE, false)
                        visibleViews(rlLayout, tvNumber, ivQrCode, recyclerView, ivPerson, tvTimerContent)
                        goneViews(superPlayer, rlNewLayout, rlNewAnswerLayout)
                    }
                    "2" -> {
                        GlideUtil.loadPic(context, data.effect_material?.background, ivRlLayout)//加载背景图
                        GlideUtil.loadGif(context, data.effect_material?.human, ivPerson)
                        getQrCode(NetUrl.EXERCISE_QR_CODE, false)
                        getExerciseListCode(courseCode, data.id.toString(), false)
                        visibleViews(rlLayout, tvNumber, ivQrCode, recyclerView, ivPerson, tvTimerContent)
                        goneViews(superPlayer, rlNewLayout, rlNewAnswerLayout)
                    }
                    "4" -> {
                        GlideUtil.loadPic(context, data.effect_material?.background, ivRlLayout)//加载背景图
                        visibleViews(rlLayout, ivRlLayout)
                        goneViews(tvNumber, ivQrCode, recyclerView, tvTimerContent, ivPerson, superPlayer, rlNewLayout, rlNewAnswerLayout)
                    }
                    "8" -> {
                        if (!TextUtils.isEmpty(data.effect_material?.background)) {
                            GlideUtil.loadPic(context, data.effect_material?.background, ivNewAnswerLayout)//加载背景图
                        }
                        goneViews(rlLayout, rlNewLayout, superPlayer)
                        visibleViews(rlNewAnswerLayout)
                        tvNewAnswerTitle?.text = data.effect_material?.question?.title
//                        val datas = ArrayList<RowStringEntity>()
                        for (url in data.effect_material?.question?.options!!) {
//                            datas.add(RowStringEntity(url))
                            tvNewAnswerTitle?.postDelayed({
                                mAnswerAdapter.addData(RowStringEntity(url))
                            }, 800)
                        }

                        tvNewAnswerTitle?.postDelayed({
                            data.effect_material?.question?.answer?.let { initAnswerClick(it) }
                        }, 1000)
//                        mAnswerAdapter.setList(datas)
//                        data.effect_material?.question?.answer?.let { initAnswerClick(it) }
                    }
                    "11" -> {
                        GlideUtil.loadPic(context, data.effect_material?.background, ivNewLayout)//加载背景图
                        getExerciseListCode(courseCode, data.id.toString(), true)
                        getQrCode(NetUrl.SIGN_QR_CODE, true)
                        tvNewTips?.text = "微信扫码签到"
                        tvNewTitle?.text = "签到"
                        visibleViews(rlNewLayout)
                        goneViews(rlLayout, superPlayer, rlNewAnswerLayout)
                    }
                    "12" -> {
                        GlideUtil.loadPic(context, data.effect_material?.background, ivNewLayout)//加载背景图
                        getExerciseListCode(courseCode, data.id.toString(), false)
                        getQrCode(NetUrl.EXERCISE_QR_CODE, true)
                        tvNewTips?.text = "微信扫码答题"
                        tvNewTitle?.text = "答题"
                        visibleViews(rlNewLayout)
                        goneViews(rlLayout, superPlayer, rlNewAnswerLayout)
                    }
                    "14" -> {//这里可能要播放视频
                        rlRoot?.setBackgroundResource(R.color.base_bg3)
                        goneViews(rlLayout, rlNewLayout, rlNewAnswerLayout)
                        visibleViews(superPlayer)
                        if (data.effect_material?.background?.contains(".mp4") == true || data.effect_material?.background?.contains(".m3u8") == true) {
                            if (!TextUtils.isEmpty(data.effect_material?.background)) {
                                startPlaySuperView(data.effect_material?.background!!)
                            } else {
                                dismiss()
                            }
                        } else {
                            GlideUtil.loadPic(context, data.effect_material?.background, ivRlLayout)//加载背景图
                        }
                    }
                }


                if ("1" == data.effect_type || "2" == data.effect_type) {
                    ivPerson?.clearAnimation()
                    ivPerson?.startAnimation(AnimationUtils.getAlphaAnimation(0f, 1f, 5000)) //渐变动画
                } else {
                    ivPerson?.clearAnimation()
                }

                data.effect_material?.countdown_color?.notTextNull {
                    if (it.contains("#")) {
                        if ("11" == data.effect_type || "12" == data.effect_type) {
                            tvNewTimer?.setTextColor(Color.parseColor(it))
                            tvNewTimer1?.setTextColor(Color.parseColor(it))
                        } else if ("8" == data.effect_type) {
                            tvNewAnswerTimer?.setTextColor(Color.parseColor(it))
                            tvNewAnswerTimer1?.setTextColor(Color.parseColor(it))
                        } else {
                            tvTimer?.setTextColor(Color.parseColor(it))
                        }
                    } else {
                        if ("11" == data.effect_type || "12" == data.effect_type) {
                            tvNewTimer?.setTextColor(Color.parseColor("#$it"))
                            tvNewTimer1?.setTextColor(Color.parseColor("#$it"))
                        } else if ("8" == data.effect_type) {
                            tvNewAnswerTimer?.setTextColor(Color.parseColor("#$it"))
                            tvNewAnswerTimer1?.setTextColor(Color.parseColor("#$it"))
                        } else {
                            tvTimer?.setTextColor(Color.parseColor("#$it"))
                        }
                    }
                }
                if (!TextUtils.isEmpty(data.effect_material?.stat_count_color)) {
                    if ((data.effect_material?.stat_count_color!!).contains("#")) {
                        if ("11" == data.effect_type || "12" == data.effect_type) {
                            tvNewNumber?.setTextColor(Color.parseColor(data.effect_material?.stat_count_color))
                        } else {
                            tvNumber?.setTextColor(Color.parseColor(data.effect_material?.stat_count_color))
                        }
                    } else {
                        if ("11" == data.effect_type || "12" == data.effect_type) {
                            tvNewNumber?.setTextColor(Color.parseColor("#${data.effect_material?.stat_count_color}"))
                        } else {
                            tvNumber?.setTextColor(Color.parseColor("#${data.effect_material?.stat_count_color}"))
                        }
                    }
                }

                if (!TextUtils.isEmpty(data.effect_material?.stat_count_bg_color)) {
                    data.effect_material?.stat_count_bg_color?.notTextNull {
                        val drawable: Drawable = DrawableCreator.Builder().setCornersRadius(ScreenUtil.dip2px(20f).toFloat())
                            .setSolidColor(Color.parseColor(if (it.contains("#")) it else "#$it"))
                            .build()
                        tvNumber?.background = drawable
                    }
                }

                data.effect_material?.background_voice?.notTextNull { initMp3(it) }//循环播放mp3

                resultTextList(data)//找出字体

                data.effect_time?.notTextNull { time ->
                    mCountDownTimer = 0//重置状态
                    rlRoot?.postDelayed({
                        countdownDynamicEffects()
                    }, 1000)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    //答题的点击
    private fun initAnswerClick(answer: List<String>) {
        mAnswerAdapter.updateAnswerList(answer)

        mAnswerAdapter.setOnViewFocus(OnViewFocus { hasFocus, position, view ->
            tvNewAnswerResult?.text = if (hasFocus) "恭喜您答对啦" else "答错了，请重新答题"
            tvNewAnswerResult?.setTextColor(if (hasFocus) getColorExt(R.color.green) else Color.RED)
            listener?.onOtherAction(data.id, (position + 1).toString())
        })
        mRecyclerAnswer?.postDelayed({
            val mView = mAnswerAdapter.getViewByPosition(0, R.id.iv_item_answer_row)
            mView?.requestFocus()
        }, 200)

    }


    /**
     * 执行倒计时
     */
    private fun countdownDynamicEffects() {
        if (mCountDown != null) {
            mCountDown?.dispose()
        }
        data.effect_time?.notTextNull { time ->
            if (mCountDownTimer == 0) {
                mCountDownTimer = time.toInt()
            }
        }

        mCountDown = RxTimerUtil.timer(mCountDownTimer, videoRete).subscribe { count ->
            mCountDownTimer = count
            if ("11" == data.effect_type || "12" == data.effect_type) {
                tvNewTimer?.text = "${count}秒"
            } else if ("8" == data.effect_type) {
                tvNewAnswerTimer?.text = "${count}秒"
            } else {
                tvTimer?.text = "${count}s"
            }
            mTextVoiceHashMap["$count"]?.let { startPlayVoice(it) }
            if (count <= 0) {
                mCountDownTimer = 0
                dismiss()
                return@subscribe
            }
        }
    }

    /**
     * 处理语音播报的内容
     * text = 1 voice = 1 同时播报
     * 否则只播报其中之一
     */
    private fun startPlayVoice(it: TextList) {
        if (isShow) {
            if ("1" == it.is_txt && "1" == it.is_voice) {
                TtsVoiceUtil.getInstance().startPlay(it.content)
                tvTimerContent?.text = it.content
            } else if ("1" == it.is_txt) {
                tvTimerContent?.text = it.content
            } else if ("1" == it.is_voice) {
                TtsVoiceUtil.getInstance().startPlay(it.content)
                tvTimerContent?.text = ""
            }
        }
    }


    /**
     * 筛选出这个点位下的、具体要博报的文字
     */
    private fun resultTextList(data: VideoDotEntity) {
        mTextVoiceHashMap.clear()
        data.effect_time?.notTextNull { time -> // 保存text点
            if (data.effect_material != null && data.effect_material!!.txt_list != null) {
                for (j in data.effect_material?.txt_list?.indices!!) {
                    data.effect_material!!.txt_list[j].let {
                        if (!TextUtils.isEmpty(it.act_time)) {
                            mTextVoiceHashMap["${time.toInt() - it.act_time!!.toInt()}"] = it
                        }
                    }
                }
            }
        }
    }


    private fun addDanmu(effect: VideoDynamicEffect) {
        mBarrAgeAdapter?.add(BarrageData(effect.service_personnel_name, effect.avatar, 0, 1))
    }

    //弹幕的配置
    private fun initDanmuKu() {
        val options = BarrageView.Options()
            .setGravity(BarrageView.GRAVITY_FULL) // 设置弹幕的位置
            .setInterval(250) // 设置弹幕的发送间隔
            .setSpeed(300, 0) // 设置速度和波动值
            .setModel(BarrageView.MODEL_COLLISION_DETECTION) // 设置弹幕生成模式
            .setRepeat(1) // 循环播放 默认为1次 -1 为无限循环
            .setClick(false) // 设置弹幕是否可以点击

        barrageView?.setOptions(options)

        // 设置适配器 第一个参数是点击事件的监听器
        mBarrAgeAdapter = object : BarrageAdapter<BarrageData>(null, context) {
            override fun onCreateViewHolder(root: View?, type: Int): BarrageViewHolder<BarrageData> {
                return CustomBarrageViewHolder(root)
            }

            override fun getItemLayout(t: BarrageData?) = R.layout.barrage_item_normal
        }

        barrageView?.setAdapter(mBarrAgeAdapter)
    }

    /**
     * 课间休息模式，播放视频
     */
    private fun startPlaySuperView(url: String) {
        val videoQualityList = ArrayList<VideoQuality>()
        val multiURLs = ArrayList<SuperPlayerModel.SuperPlayerURL>()

        val superPlayerModel = SuperPlayerModel()
        superPlayerModel.title = ""
        superPlayerModel.url = url
        superPlayerModel.appId = ConstantMMVK.DEFAULT_APPID

        videoQualityList.add(VideoQuality(1, "高清", url))
        multiURLs.add(SuperPlayerModel.SuperPlayerURL(url, "高清"))

        multiURLs.add(SuperPlayerModel.SuperPlayerURL(url, "标清"))
        videoQualityList.add(VideoQuality(0, "标清", url))

        superPlayerModel.multiURLs = multiURLs
        superPlayerModel.videoQualityList = videoQualityList
        superPlayer?.setLoop(true)
        superPlayer?.playWithModel(superPlayerModel)
    }

    /**
     * 获取签到｜答题的人数内容
     */
    fun getExerciseListCode(code: String? = null, dot_id: String, isSign: Boolean) {
        RxHttp.get(if (isSign) NetUrl.FIND_SIGN_LIST else NetUrl.DOT_EXERCISE_LIST)
            .add("code", code)
            .add("dot_id", dot_id)
            .add("device_mac", getWireMac())
            .add("device_id", getAndroidId()).add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).asResponse<VideoSignOrDynamicList>()
            .subscribe { fillSignData(it, isSign) }
    }

    /**
     * 联网获取 处理一下数量
     */
    private fun fillSignData(it: VideoSignOrDynamicList, isSign: Boolean) {
        tvNumber?.postDelayed({ //需要对做个延迟 因为延迟不加载的情况
            mSignAdapter.data.clear()
            mSignAdapter.notifyDataSetChanged()
            IM_COUNT = 1
            if ("11" == data.effect_type || "12" == data.effect_type) {
                mSignNewAdapter.setList(it.list)
                peopleCounting()
            } else {
                if (it.list.isNotEmpty()) {
                    for (i in it.list.indices) {
                        it.list[i].index = "$IM_COUNT"
                        mSignAdapter.addData(it.list[i])
                        IM_COUNT += 1
                    }
                }
                peopleCounting()
            }
        }, 200)

    }

    //做个规整
    private fun peopleCounting() {
        if ("11" == data.effect_type || "12" == data.effect_type) {
            if ("11" == data.effect_type) {
                tvNewNumber?.text = "签到人数：${mSignNewAdapter.data.size}"
            } else if ("12" == data.effect_type) {
                tvNewNumber?.text = "作答人数：${mSignNewAdapter.data.size}"
            }
            mSignNewAdapter.notifyItemChanged(mSignNewAdapter.data.size - 1)
            recyclerViewNew?.scrollToPosition(mSignNewAdapter.data.size - 1)
        } else if ("1" == data.effect_type || "2" == data.effect_type) {
            if ("1" == data.effect_type) {
                tvNumber?.text = "签到人数：${mSignAdapter.data.size}"
            } else if ("2" == data.effect_type) {
                tvNumber?.text = "作答人数：${mSignAdapter.data.size}"
            }
            mSignAdapter.notifyItemChanged(mSignAdapter.data.size - 1)
            recyclerView?.scrollToPosition(mSignAdapter.data.size - 1)
        }
    }


    /**
     * 获取签到二维码
     */
    private fun getQrCode(url: String, isNew: Boolean) {
        RxHttp.get(url).add("code", courseCode)
            .add("dot_id", data.id)
            .add("device_mac", getWireMac())
            .add("device_id", getAndroidId())
            .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
            .asResponse<QRCodeEntity>().subscribe { data ->
                if (isNew) {
                    ivNewQrCode?.post {
                        ivNewQrCode?.let {
//                            GlideImageLoader.loadImageWithRetry(context as Activity, data.qr_code_url, it)
                            it.setImageBitmap(QRCodeUtil.getInstance().createQRCode(data.qr_code_content))
                        }
                    }
                } else {
                    ivQrCode?.post {
                        ivQrCode?.let {
//                            GlideImageLoader.loadImageWithRetry(context as Activity, data.qr_code_url, it)
                            it.setImageBitmap(QRCodeUtil.getInstance().createQRCode(data.qr_code_content))
                        }
                    }

                }
            }
    }


    /**
     * 关闭所有的定时器
     */
    private fun closeDisposeAll() {
        if (mCountDown != null) {
            mCountDown?.dispose()
            mCountDown = null
        }
    }


    /**
     * 初始化Mp3
     */
    private fun initMp3(media: String) {
        //本地要是没有就直接去下载
        if (TextUtils.isEmpty(findLocalMp3(media))) {
            val task = DownloadTask(media)
            val suffix: String = media.substring(media.lastIndexOf("."))
            task.localPath = context.externalCacheDir.toString() + "/" + media.encodeUtf8().md5().hex() + suffix
            MultiTaskDownloader.startDownLoadTask(task)
        }
        CoroutineScope(Dispatchers.Main).launch {
            withContext(Dispatchers.IO) {
                MediaHelper.playSound(context, media) { mp: MediaPlayer? -> }
            }
        }
    }

    override fun beforeDismiss() {
        super.beforeDismiss()
        closeDisposeAll()
        TtsVoiceUtil.getInstance().onPause()
        TtsVoiceUtil.getInstance().onStop()
        MediaHelper.stop()
        MediaHelper.release()
        superPlayer?.release()
        superPlayer?.resetPlayer()
        barrageView?.destroy()
        mHandler.removeCallbacksAndMessages(null)
        listener?.onDismiss()
    }
}