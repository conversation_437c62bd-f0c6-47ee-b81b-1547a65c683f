package com.panda.course.ui.activity.dialog

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.view.KeyEvent
import androidx.lifecycle.Observer
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.panda.course.R
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityQrSkillBinding
import com.panda.course.databinding.ActivityTeacherStudentInteractionBinding

import com.panda.course.entity.AuntSkillRecordListEntity

import com.panda.course.ext.*
import com.panda.course.ui.adapter.InteractiveAdapter
import com.panda.course.ui.adapter.SkillRecordAdapter
import com.panda.course.ui.viewmodel.InteractionViewModel
import com.panda.course.ui.viewmodel.SkillViewModel
import com.panda.course.util.QRCodeUtil
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.util.RxTimerUtil
import io.reactivex.rxjava3.disposables.Disposable

class QrSkillActivity : BaseDbActivity<SkillViewModel, ActivityQrSkillBinding>() {

    private var mCourseUuid = ""

    private var page = 1

    private var mAdapter = SkillRecordAdapter(false)

    private var mTopDisposable: Disposable? = null //用来循环当天的数据


    override fun onLoadRetry() {

        intent.extras?.apply {
            getString("course_uuid").notTextNull {
                mCourseUuid = it

                mViewModel.getAuntSkillRecord(page, mCourseUuid)

                mTopDisposable = RxTimerUtil.interval(3000).subscribe {
                    mViewModel.getAuntSkillRecord(page, mCourseUuid)
                }

                mViewModel.getAuntSkillQrCode(it)
            }
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        setDialogWindowParams(window, 1.0f, 1.0f)

        onLoadRetry()

        mDataBind.recyclerTask.adapter = mAdapter
        mDataBind.recyclerTask.setColumnNumbers(1)


    }

    override fun initObserver() {
        mAdapter.setOnItemClickListener { adapter, view, position ->
            toStartActivity(SkillRecordActivity::class.java, Bundle().apply {
                putInt("position", position)

                val arr = ArrayList<AuntSkillRecordListEntity>()
                for (i in mAdapter.data.indices) {
                    arr.add(mAdapter.data[i])
                }
                putParcelableArrayList("record", arr)
            })
        }
    }

    override fun onRequestSuccess() {
        mViewModel.recordListEntity.observe(this, Observer {
            mAdapter.setList(it.list)
            mAdapter.notifyDataSetChanged()
            if (mAdapter.data.size > 0) {
                mDataBind.tvEmpty.gone()
            }
        })

        mViewModel.qrCodeEntity.observe(this, Observer {
            if (it == null) {
                return@Observer
            }
            Glide.with(this@QrSkillActivity).asBitmap().load(QRCodeUtil.getInstance().createQRCode(it.qr_code_content)).apply(
                RequestOptions().transform(
                    CenterCrop(),
                    RoundedCornersTransform(this@QrSkillActivity, 6f, rightBottom = false, leftBottom = false)
                )
            ).into(mDataBind.ivPopupQrTeacherCode)

        })

    }

    /**
     * 销毁门店的定时请求
     */
    private fun closeStoreDispose() {
        mTopDisposable?.dispose()
        mTopDisposable = null
    }

    override fun onDestroy() {
        super.onDestroy()
        closeStoreDispose()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        if (keyCode == KeyEvent.KEYCODE_ENTER || keyCode == KeyEvent.KEYCODE_DPAD_CENTER || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE
            || keyCode == KeyEvent.KEYCODE_HOME || keyCode == KeyEvent.KEYCODE_PROFILE_SWITCH
        ) {
            return true
        }

        return super.onKeyDown(keyCode, event)
    }
}
