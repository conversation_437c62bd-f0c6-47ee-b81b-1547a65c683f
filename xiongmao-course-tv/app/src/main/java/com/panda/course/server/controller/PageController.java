package com.panda.course.server.controller;

import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.LogExtKt;
import com.panda.course.ui.activity.MainActivity;
import com.panda.course.util.tvremote.NetUtil;
import com.yanzhenjie.andserver.RequestHandler;
import com.yanzhenjie.andserver.util.HttpRequestParser;

import org.apache.httpcore.HttpException;
import org.apache.httpcore.HttpRequest;
import org.apache.httpcore.HttpResponse;
import org.apache.httpcore.entity.StringEntity;
import org.apache.httpcore.protocol.HttpContext;

import java.io.IOException;
import java.util.Map;

//注册Page控制台

public class PageController implements RequestHandler {

    @Override
    public void handle(HttpRequest request, HttpResponse response, HttpContext context) throws HttpException, IOException {
        Map<String, String> params = HttpRequestParser.parseParams(request);
        // Request params.
        int keyCode = Integer.parseInt(params.get("keyCode"));
        //处理一下版本 Home按键
        LogExtKt.logE("收到消息了" + System.currentTimeMillis(), "服务端接收到消息");
        if (AppExtKt.getCurrentAndroid9() && keyCode == 3) {
            if (AppExtKt.getCurrentActivity() instanceof MainActivity) {
                AppExtKt.finishOtherMainActivity();
            } else {
                AppExtKt.finishOtherActivity();
            }
        } else {
            NetUtil.INSTANCE.sendKeyDownUpSync(keyCode);
        }
        StringEntity stringEntity = new StringEntity("成功了 + " + keyCode, "utf-8");
        response.setEntity(stringEntity);
    }
}
