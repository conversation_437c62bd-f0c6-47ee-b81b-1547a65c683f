package com.panda.course.entity;

public class RowStringEntity {


    private String id;
    private String type_name;
    private String cover_url;
    private String content_style;
    private boolean correct;
    private String is_new;

    public void setIs_new(String is_new) {
        this.is_new = is_new;
    }

    public String getIs_new() {
        return is_new;
    }

    public void setContent_style(String content_style) {
        this.content_style = content_style;
    }

    public String getContent_style() {
        return content_style;
    }

    public RowStringEntity(String cover_url) {
        this.cover_url = cover_url;
    }

    public void setCorrect(boolean correct) {
        this.correct = correct;
    }

    public boolean isCorrect() {
        return correct;
    }

    public void setCover_url(String cover_url) {
        this.cover_url = cover_url;
    }

    public String getCover_url() {
        return cover_url;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType_name() {
        return type_name;
    }

    public void setType_name(String type_name) {
        this.type_name = type_name;
    }
}
