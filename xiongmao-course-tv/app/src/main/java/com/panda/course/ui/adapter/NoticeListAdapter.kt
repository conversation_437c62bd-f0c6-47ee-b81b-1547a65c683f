package com.panda.course.ui.adapter

import android.os.Build
import android.text.TextUtils
import android.view.View
import android.webkit.*
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.entity.NoticeListEntityEntity
import com.panda.course.ext.gone
import com.panda.course.ext.px2dp
import com.panda.course.ext.visible
import com.panda.course.util.RichTextUtils
import com.panda.course.widget.focus.MyFocusHighlightHelper


class NoticeListAdapter : BaseQuickAdapter<NoticeListEntityEntity, BaseViewHolder>(R.layout.item_notice_list) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var mOnViewFocus: OnViewFocus? = null


    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight = MyFocusHighlightHelper.BrowseItemFocusHighlight(MyFocusHighlightHelper.ZOOM_FACTOR_XXXXSMALL, false)
        }
        addChildClickViewIds(R.id.but_item_notice_look, R.id.row_card_notice_list_view)
    }

    fun setOnItemFocus(onItemFocus: OnViewFocus?) {
        this.mOnViewFocus = onItemFocus
    }

    override fun convert(holder: BaseViewHolder, item: NoticeListEntityEntity) {

        holder.setText(R.id.tv_item_notice_list_title, item.title)
            .setText(R.id.tv_item_notice_list_time, item.create_time)


        val mWebView = holder.getView<TextView>(R.id.tv_item_notice_list_content)
//        mWeb.loadDataWithBaseURL("", item.msg, "text/html", "utf-8", null)
        RichTextUtils.showRichHtmlWithImageContent(mWebView, item.msg)


        val mStatus = holder.getView<TextView>(R.id.tv_item_notice_list_status)

        val button = holder.getView<Button>(R.id.but_item_notice_look)
        val frameLayout = holder.getView<FrameLayout>(R.id.ll_item_notice_list_content)
        val titleLayout = holder.getView<LinearLayout>(R.id.ll_notice_title_layout)

        val cardView = holder.getView<CardView>(R.id.row_card_notice_list_view)


        if ("0" == item.is_read) { //判断是否会有阅读的状态
            mStatus.visible()
        } else {
            mStatus.gone()
        }


        // 隐藏的代码是为了需求变更使用、之前是view放大，现在是View 选中状态
        cardView.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->
            mBrowseItemFocusHighlight?.onItemFocused(cardView, hasFocus)
            mOnViewFocus?.onChangeFocus(hasFocus, getItemPosition(item), cardView)

            // 设置阴影
            cardView.cardElevation = if (hasFocus) px2dp(20f).toFloat() else px2dp(0f).toFloat()

            cardView.setCardBackgroundColor(if (hasFocus) ContextCompat.getColor(context, R.color.black) else ContextCompat.getColor(context, R.color.transparent))

            cardView.radius = if (hasFocus) 16f else 0f

            titleLayout.setBackgroundResource(if (hasFocus) R.drawable.base_border_selecter_top_selecter else R.color.transparent)

            if (hasFocus) {
                if (TextUtils.isEmpty(item.course_code)) {
                    button.gone()
                } else {
                    button.visible()
                }
                frameLayout.visible()
                cardView.clearAnimation()
            } else {
                button.gone()
                frameLayout.gone()
                cardView.clearAnimation()
            }
        }
    }


}