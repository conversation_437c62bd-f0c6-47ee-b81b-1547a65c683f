package com.panda.course.ui.activity

import android.app.Dialog
import android.os.Bundle
import android.os.Handler
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.widget.Button
import androidx.databinding.DataBindingUtil
import androidx.leanback.widget.FocusHighlight
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import com.alibaba.fastjson.JSON
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.data.CommonAnimationUtil
import com.panda.course.data.CommonDataUtil
import com.panda.course.data.MyVideoInfoDaoUtil
import com.panda.course.databinding.ActivityContentBinding
import com.panda.course.databinding.ActivityContentHeaderBinding
import com.panda.course.databinding.HeaderExamRankAnswerBinding
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.ui.activity.dialog.*
import com.panda.course.ui.adapter.MyCourseContentAdapter
import com.panda.course.ui.presenter.CoursePresenter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.util.*
import com.panda.course.widget.focus.MyFocusHighlightHelper


class ContentActivity : BaseDbActivity<CourseViewModel, ActivityContentBinding>() {

    private var TAG = "ContentActivity"

    private var mAdapter = MyCourseContentAdapter()

    private var mCourseCode: String? = null

    private var mTeacherName: String? = null
    private var mTeacherUrl: String? = null

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null


    private var courseDetail: CourseDetail? = null

    private var mCourseName: String? = null

    // 区分内容
    private var isHideTeacher = false

    private var mPosition = 0

    private var course_cover_url = ""

    private var shouldShowCount = 0

    /**
     * 默认分类
     */
    private val focus_header = 1

    /**
     * 分类课程
     */
    private val focus_course = 2

    /**
     * 底部其他
     */
    private val focus_bottom = 3


    private var currentFocusType: Int = focus_header


    private var homeHeaderView: ActivityContentHeaderBinding? = null


    override fun onLoadRetry() {
        super.onLoadRetry()
        if (NetworkUtil.isAvailable(this)) {
            dismissLoadingNetWorkExt()
        }
        shouldShowCount = 0
        requestCourseDetail()
    }

    override fun initView(savedInstanceState: Bundle?) {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false)
        }
        mViewModel.updateAppVersion()
        locationData()

        mViewModel.getNoticeTotal(is_notice_content = "1", null)

        val header = View.inflate(this, R.layout.activity_content_header, null)
        header.findViewById<Button>(R.id.but_course).setOnClickListener {
            val bundle = Bundle()
            bundle.putString("code", "$mCourseCode")
            toStartActivity(CourseImgDialogActivity::class.java, bundle)
            appUMEvent(UMConstant.DETAILS_CLASS_SCHEDULE_CARD)
        }

//        header.findViewById<Button>(R.id.but_course_courseware).setOnClickListener {
//            val bundle = Bundle()
//            bundle.putString("course_code", "" + mCourseCode)
//            toStartActivity(PdfChooseActivity::class.java, bundle)
//            appUMEvent(UMConstant.COURSE_COURSEWARE)
//        }
//
//        header.findViewById<Button>(R.id.but_course_courseware).setOnFocusChangeListener { v, hasFocus ->
//            if (hasFocus) {
//                currentFocusType = focus_header
//            }
//        }
        header.findViewById<Button>(R.id.but_course).setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                currentFocusType = focus_header
            }
        }


        homeHeaderView = DataBindingUtil.bind(header)
        mAdapter.addHeaderView(header)

        mDataBind.recycler.layoutManager = GridLayoutManager(this, 2)
        mDataBind.recycler.adapter = mAdapter
        mAdapter.setEmptyView(R.layout.layout_empty)

        intent.extras?.let {
            mCourseCode = it.getString("course_code")
            course_cover_url = it.getString("course_cover_url").toString()
            requestCourseDetail()
        }

        // 监听行为
        eventViewModel.progressEvent.observe(this, Observer {
            for (i in 0..mAdapter.data.size) {
                if (mAdapter.data[i].code == it.code) {
                    "${mAdapter.data[i].video_watch_time}".logE("接收到的状态")
                    mAdapter.data[i].video_watch_time = it.progress
                    "${it.progress}".logE("接收到的状态")
                    return@Observer
                }
            }
        })

        // 监听当前网络变化
        eventViewModel.monitorNetWork.observe(this, Observer {
            if (this == currentActivity) {
                if (it) {
                    if (NetworkUtil.isAvailable(this)) {
                        dismissLoadingNetWorkExt()
                        onLoadRetry()
                    }
                } else {
                    showNetDialog { onLoadRetry() }
                }
            }
        })

        // 消息通知的监听，实现无焦点的情况下，执行跑马灯的效果
        mDataBind.baseHead.llNoticeLayout.setOnFocusChangeListener { v, hasFocus ->
            if (!hasFocus) {
                if (!TextUtils.isEmpty(mViewModel.noticeEntity.value?.notice?.title)) {
                    mDataBind.baseHead.tvNotice.text = mViewModel.noticeEntity.value?.notice?.title
                }
            }
        }

        requestVideo()
        uploadOfflineCourse()
    }

    /* 上传本地数据库的内容 */
    private fun uploadOfflineCourse() {
        val list = MyVideoInfoDaoUtil.getVideoDao()
        if (!list.isNullOrEmpty()) {
            "${list}".logE(TAG)
            "${MyVideoInfoDaoUtil.getVideoDao().size}".logE(TAG)
            JSON.toJSONString(list).logE(TAG)
            mViewModel.uploadOfflineCourse(JSON.toJSONString(list))
        }
    }

    /**
     * 请求课时列表
     */
    private fun requestCourseDetail(isRefresh: Boolean = true) {
        if (mCourseCode != null) {
            mViewModel.getCourseDetail(isRefresh, mCourseCode!!, eventViewModel.appUserInfo.value?.uuid, true)
        }
    }

    /**
     * 上报缓存在本地的视频
     */
    private fun requestVideo() {
        if (TXVodUtil.getInstance().uploadTXVideo().size > 0) {
            mViewModel.uploadVideoDownLoadState(TXVodUtil.getInstance().uploadTXVideo())
        }
    }

    /**
     * 获取本地数据
     */
    private fun locationData() {
        eventViewModel.appUserInfo.value?.let {
            mDataBind.baseHead.llUserLayout.visibility = View.VISIBLE
            mDataBind.baseHead.tvHeadStoreName.text = it.store_name
//            mDataBind.baseHead.tvHeadAppVersionCode.text = "V${getAppVersion(this)}"
            GlideUtil.loadPicRound(this, it.store_log, mDataBind.baseHead.ivHeadPic)
        }

        eventViewModel.surpriseToggle.value.let {
            if (it != null) {
                GlideUtil.loadGif(this, it.icon_img_url, mDataBind.baseHead.ivSurprise)
                mDataBind.baseHead.tvSurprise.text = it.title
                mDataBind.baseHead.llSurpriseLayout.visible()
            } else {
                mDataBind.baseHead.llSurpriseLayout.gone()
            }
        }
    }

    override fun onBindViewClick() {
        mDataBind.tvFinalCourseware.setOnClickListener {
            val bundle = Bundle()
            bundle.putString("course_code", "" + mCourseCode)
            toStartActivity(PdfChooseActivity::class.java, bundle)
            appUMEvent(UMConstant.COURSE_COURSEWARE)
        }
        mDataBind.baseHead.llNoticeLayout.setOnClickListener {
            toStartActivity(NoticeActivity::class.java)
        }
        mDataBind.baseHead.llSurpriseLayout.setOnClickListener {
            CoursePresenter.toSurpriseDetails(eventViewModel.surpriseToggle.value)
        }
        mDataBind.baseHead.llUpdateLayout.setOnClickListener {
            appUMEvent(UMConstant.HOME_HELP_FEEDBACK)
            toStartActivity(FeedbackActivity::class.java)
        }
        mDataBind.baseHead.llUserLayout.setOnClickListener {
            toStartActivity(UserOutDialogActivity::class.java)
        }
        mDataBind.baseHead.llSettingLayout.setOnClickListener {
            appUMEvent(UMConstant.HOME_SETTING)
            toStartActivity(SettingsActivity::class.java)
        }
        mAdapter.apply {
            setOnItemClickListener { _, _, position ->
                mPosition = position
                mViewModel.checkDevice()
            }

            setOnItemFocus { hasFocus, position, _ ->
                if (hasFocus) {
                    mPosition = position
                    currentFocusType = focus_course
                }
            }
        }

        mDataBind.tvFinalExamPic.setOnClickListener {
            mCourseCode?.let {
                isHideTeacher = false
                mViewModel.getClassQrCode(it, ConstantMMVK.POPUP_EXAMINATION)
                appUMEvent(UMConstant.GRADUATE)
            }
        }
        mDataBind.tvTeacherInteractivePic.setOnClickListener {
            mCourseCode?.let {
                isHideTeacher = true
                mViewModel.getClassQrCode(it, ConstantMMVK.POPUP_TEACHER)
                appUMEvent(UMConstant.TEACHER_INTERACTION)
            }
        }
        mDataBind.tvGraduationPhotoPic.setOnClickListener {
            appUMEvent(UMConstant.GRADUATE_COMMEND)
            val bundle = Bundle()
            bundle.putString("mCourseName", "" + mCourseName)
            bundle.putString("course_code", "" + mCourseCode)
            toStartActivity(GraduationMapActivity::class.java, bundle)
        }

        mDataBind.tvFinalCourseware.setOnFocusChangeListener { v, hasFocus ->
            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
            if (hasFocus) {
                currentFocusType = focus_bottom
            }
        }

        mDataBind.tvFinalExamPic.setOnFocusChangeListener { v, hasFocus ->
            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
            if (hasFocus) {
                currentFocusType = focus_bottom
            }
        }
        mDataBind.tvTeacherInteractivePic.setOnFocusChangeListener { v, hasFocus ->
            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
            if (hasFocus) {
                currentFocusType = focus_bottom
            }
        }
        mDataBind.tvGraduationPhotoPic.setOnFocusChangeListener { v, hasFocus ->
            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
            if (hasFocus) {
                currentFocusType = focus_bottom
            }
        }

        viewsFocus(
            mDataBind.baseHead.llNoticeLayout, mDataBind.baseHead.llSurpriseLayout,
            mDataBind.baseHead.llUpdateLayout, mDataBind.baseHead.llUserLayout, mDataBind.baseHead.llSettingLayout
        )


    }

    /**
     * 显示传入的view集合
     */
    fun viewsFocus(vararg views: View?) {
        views.forEach {
            it?.setOnFocusChangeListener { v, hasFocus ->
                if (hasFocus) {
                    currentFocusType = focus_header
                }
            }
        }
    }

    /**
     * 默认焦点
     */
    fun openUserLayoutFocusable(): Boolean {
        return if (homeHeaderView?.butCourse?.visibility == View.VISIBLE && mPosition == 1 && homeHeaderView?.butCourse?.isFocused == false && currentFocusType != focus_header) {
            homeHeaderView?.butCourse?.requestFocus()
            true
        } else if (homeHeaderView?.butCourse?.visibility == View.GONE) {
            if (mPosition == 0) {
                mDataBind.baseHead.llUserLayout.requestFocus()
            } else {
                mDataBind.baseHead.llSettingLayout.requestFocus()
            }
            false
        } else if (homeHeaderView?.butCourse?.isFocused == true) {
            mDataBind.baseHead.llSettingLayout.requestFocus()
            false
        } else {
            mDataBind.baseHead.llUserLayout.requestFocus()
            false
        }
    }


    override fun onRequestSuccess() {
        mViewModel.apply { // 课程详情

            //离线数据上报,如果成功
            uploadOffline.observe(this@ContentActivity, androidx.lifecycle.Observer {
                MyVideoInfoDaoUtil.deleteAll()
            })


            mCourseDetail.observe(this@ContentActivity, androidx.lifecycle.Observer { data: CourseDetail ->
                dismissLoadingNetWorkExt()
                courseDetail = data

                eventViewModel.appUserInfo.value?.let {
                    if (!TextUtils.isEmpty(it.store_log) && !TextUtils.isEmpty(data.store_log)) {
                        if (it.store_log != data.store_log) {
                            eventViewModel.appUserInfo.value?.store_log = data.store_log
                            locationData()
                            eventViewModel.appUserHead.value = true
                        }
                    }
                }

                course_cover_url = data.course_cover_image
                mCourseName = data.title_main
                homeHeaderView?.tvContentTeacher?.text = "${data.teacher_name}"
                homeHeaderView?.tvContentDesc?.text = "${data.course_introduction}"
                data.teacher_name?.let { mAdapter.setTeacherName(it) }

                data.schedule_pic.notTextNull { homeHeaderView?.butCourse?.visible() }

                "${data.video_list?.size}".logD("测试内容")

                MMKVHelper.encode(mCourseCode!!, JSON.toJSONString(data))

                mAdapter.setList(data.video_list)

                addAdapterItem(data.has_course_ware, data.final_exam_pic, data.teacher_interactive_pic, data.graduation_photo_pic)

                requestOne()
            })

            // 二维码
            qrCodeEntity.observe(this@ContentActivity, androidx.lifecycle.Observer {
                if (it == null) {
                    return@Observer
                }
                if (!TextUtils.isEmpty(it.qr_code_content)) {
                    val bundle = Bundle()
                    bundle.putBoolean("isHideTeacher", isHideTeacher)
                    bundle.putString("course_detail_code", mCourseCode)
                    bundle.putString("qr_code_url", it.qr_code_content)
                    bundle.putString("mCourseName", mCourseDetail.value?.title_main)
                    if (isHideTeacher) {//如果是师生互动，那么就增加头像跟名称
                        bundle.putSerializable("qr_code_info", it)
                    }
                    toStartActivity(if (!isHideTeacher) RankingDialogActivity::class.java else TeacherStudentInteractionActivity::class.java, bundle)
                }
            }) // 检测日期
            checkDevice.observe(this@ContentActivity, androidx.lifecycle.Observer {
                if (mAdapter.data[mPosition].video_url?.contains(".mp4") == true) {
                    "该视频源异常".toast()
                    return@Observer
                }
                appUMEvent(UMConstant.COURSE_DETAILS_VIDEO)
                val bundle = Bundle()
                mAdapter.data[mPosition].course_cover_url = course_cover_url
                "${mAdapter.data[mPosition].course_cover_url}".logE("封面图")
                bundle.putParcelable("course_detail", mAdapter.data[mPosition])
                bundle.putString("store_logo", courseDetail?.store_log)
                bundle.putString("course_detail_code", mCourseCode)
                bundle.putString("course_status", "" + courseDetail?.course_status)
                if (!TextUtils.isEmpty(courseDetail?.final_exam_pic)) { //结业考试图片
                    bundle.putBoolean("isExam", true)
                }
                toStartActivity(PlayerActivity::class.java, bundle)

            })
            checkTipsDevice.observe(this@ContentActivity, androidx.lifecycle.Observer {
                showMessageDialog(it)
            })

            checkCodeDevice.observe(this@ContentActivity, androidx.lifecycle.Observer {
                if (it == 2000) {
                    isPropagandaVideo = false
                }
            })

            mUpdateInfo.observe(this@ContentActivity, androidx.lifecycle.Observer { updateInfo ->
                if (updateInfo == null) {
                    eventViewModel.appUpdate.value = null
                    return@Observer
                }
                eventViewModel.appUpdate.value = updateInfo

                toStartActivity(UpdateDialogActivity::class.java)
            })

            //类似于活动展示弹窗
            noticeEntity.observe(this@ContentActivity, androidx.lifecycle.Observer {
                if (it == null) {
                    return@Observer
                }
                if (!TextUtils.isEmpty(it.notice?.title)) {
                    mDataBind.baseHead.tvNotice.text = mViewModel.noticeEntity.value?.notice?.title
                }
                if (it.show_total != null) {
                    if (it.show_total!! >= 99) {
                        mDataBind.baseHead.tvNoticeNumber.visible()
                        mDataBind.baseHead.tvNoticeNumber.text = "99+"
                    } else if (it.show_total!! > 0) {
                        mDataBind.baseHead.tvNoticeNumber.visible()
                        mDataBind.baseHead.tvNoticeNumber.text = "${it.show_total!!}"
                    }
                } else {
                    mDataBind.baseHead.tvNoticeNumber.gone()
                }

            })


        }
    }


    /**
     * 增加课时item
     */
    private fun addAdapterItem(has_course_ware: String, final_exam_pic: String, teacher_interactive_pic: String, graduation_photo_pic: String) {

        if (!TextUtils.isEmpty(final_exam_pic)) { //结业考试图片
            mDataBind.tvFinalExamPic.visible()
//            GlideUtil.loadPic(this, final_exam_pic, mDataBind.tvFinalExamPic)
            shouldShowCount += 1
//            video_list.add(CommonDataUtil().createCourseDetailInfo(final_exam_pic, ConstantMMVK.POPUP_EXAMINATION))
        } else {
            mDataBind.tvFinalExamPic.gone()
        }
        if (!TextUtils.isEmpty(teacher_interactive_pic)) { // 师生互动图片
//            GlideUtil.loadPic(this, teacher_interactive_pic, mDataBind.tvTeacherInteractivePic)
//            video_list.add(CommonDataUtil().createCourseDetailInfo(teacher_interactive_pic, ConstantMMVK.POPUP_TEACHER))
            mDataBind.tvTeacherInteractivePic.visible()
            shouldShowCount += 1
        } else {
            mDataBind.tvTeacherInteractivePic.gone()
        }
        if (!TextUtils.isEmpty(graduation_photo_pic)) { // 毕业勋章
            mDataBind.tvGraduationPhotoPic.visible()
            shouldShowCount += 1
//            GlideUtil.loadPic(this, graduation_photo_pic, mDataBind.tvGraduationPhotoPic)
//            video_list.add(CommonDataUtil().createCourseDetailInfo(graduation_photo_pic, ConstantMMVK.POPUP_GRADUATION))
        } else {
            mDataBind.tvGraduationPhotoPic.gone()
        }


        if ("1" == has_course_ware) {
            mDataBind.tvFinalCourseware.visible()
            shouldShowCount += 1
        } else {
            mDataBind.tvFinalCourseware.gone()
        }


        if (TextUtils.isEmpty(has_course_ware) && TextUtils.isEmpty(final_exam_pic) && TextUtils.isEmpty(teacher_interactive_pic) && TextUtils.isEmpty(graduation_photo_pic)) {
            mDataBind.llLayout.gone()
        } else {

            when (shouldShowCount) {
                1 -> { //说明显示了1个View，那么就需要打开2个占位
                    mDataBind.view1.visible()
                    mDataBind.view2.visible()
                    mDataBind.view3.visible()
                }

                2 -> {
                    mDataBind.view1.visible()
                    mDataBind.view2.gone()
                    mDataBind.view3.gone()
                }

                3, 4 -> {
                    mDataBind.view1.gone()
                    mDataBind.view2.gone()
                    mDataBind.view3.gone()
                }
            }

            mDataBind.llLayout.visible()

        }


    }


    private fun requestOne() {
        Handler().postDelayed({
            val mView = mAdapter.getViewByPosition(1, R.id.content_card_view)
            mView?.requestFocus()
        }, 10)
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        Log.e("获取界面焦点", "===当前获取焦点的View===${window.decorView.findFocus()}")
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {

            KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_MENU -> {
                goSystemTvBoxSetting(this)
                return true
            }

            KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                return true
            }

            KeyEvent.KEYCODE_DPAD_UP -> {
                if (mDataBind.baseHead.llUserLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llUserLayout)
                    return true
                }
                if (mDataBind.baseHead.llNoticeLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llNoticeLayout)
                    return true
                }
                if (mDataBind.baseHead.llSurpriseLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llSurpriseLayout)
                    return true
                }
                if (mDataBind.baseHead.llUpdateLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llUpdateLayout)
                    return true
                }

                if (mDataBind.baseHead.llSettingLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llSettingLayout)
                    return true
                }

                if (mPosition == 0 || mPosition == 1) {
                    val isAtTop = !mDataBind.recycler.canScrollVertically(-1)
                    if (!isAtTop) {
                        mDataBind.recycler.scrollToPosition(0)
                    }
                    return openUserLayoutFocusable()
                }
            }


            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (mPosition % 2 == 0 && currentFocusType == focus_course) {
                    return true
                }
                if (mDataBind.baseHead.llSettingLayout.isFocused) {
                    return false
                }
                if (mDataBind.baseHead.llUserLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(false, mDataBind.baseHead.llUserLayout)
                    return true
                }
                if (mDataBind.tvFinalCourseware.visibility == View.VISIBLE && mDataBind.tvFinalCourseware.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(false, mDataBind.tvFinalCourseware)
                    return true
                }
                if (mDataBind.tvFinalCourseware.visibility == View.GONE
                    && mDataBind.tvFinalExamPic.isFocused && mDataBind.tvFinalExamPic.visibility == View.VISIBLE
                ) {
                    CommonAnimationUtil().startShakeAnimation(false, mDataBind.tvFinalExamPic)
                    return true
                }
                if (mDataBind.tvFinalCourseware.visibility == View.GONE
                    && mDataBind.tvFinalExamPic.visibility == View.GONE && mDataBind.tvTeacherInteractivePic.isFocused
                ) {
                    CommonAnimationUtil().startShakeAnimation(false, mDataBind.tvTeacherInteractivePic)
                    return true
                }
                if (mDataBind.tvFinalCourseware.visibility == View.GONE
                    && mDataBind.tvFinalExamPic.visibility == View.GONE && mDataBind.tvTeacherInteractivePic.visibility == View.GONE && mDataBind.tvGraduationPhotoPic.isFocused
                ) {
                    CommonAnimationUtil().startShakeAnimation(false, mDataBind.tvGraduationPhotoPic)
                    return true
                }
            }

            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (homeHeaderView?.butCourse?.isFocused == true) {
                    CommonAnimationUtil().startShakeAnimation(false, homeHeaderView?.butCourse)
                    return true
                }
                if (mPosition % 2 == 1 && currentFocusType == focus_course) {
                    return true
                }
                if (mPosition == mAdapter.data.size - 1 && currentFocusType == focus_course) {
                    return true
                }
                if (mDataBind.baseHead.llSettingLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(false, mDataBind.baseHead.llSettingLayout)
                    return true
                }
            }
        }
        return super.onKeyDown(keyCode, event)
    }


}