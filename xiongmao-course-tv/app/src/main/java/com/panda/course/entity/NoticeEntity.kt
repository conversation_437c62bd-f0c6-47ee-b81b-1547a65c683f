package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class NoticeEntity(
    var total: Int? = null,
    var show_total: Int? = null,
    var notice: NoticeEntityEntity? = null,
) : Parcelable


@Parcelize
data class NoticeEntityEntity(
    var is_popup: String? = null,
    var popup_start_time: String? = null,
    var popup_end_time: String? = null,
    var number: String? = null,
    var img_url: String? = null,
    var title: String? = null,
) : Parcelable