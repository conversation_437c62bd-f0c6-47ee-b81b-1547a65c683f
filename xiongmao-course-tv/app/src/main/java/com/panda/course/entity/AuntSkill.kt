package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class AuntSkillEntity(
    var record_total: Int? = null,
    var list: List<AuntSkillListEntity> = emptyList(),
) : Parcelable


@Parcelize
data class AuntSkillListEntity(
    var uuid: String? = null,
    var title: String? = null,
    var min_salary: String? = null,
    var max_salary: String? = null,
    var intro: String? = null,
) : Parcelable