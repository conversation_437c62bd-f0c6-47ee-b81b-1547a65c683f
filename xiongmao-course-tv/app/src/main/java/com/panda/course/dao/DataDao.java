package com.panda.course.dao;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;

public class DataDao {


    private static final String DB_NAME = "xm_offline_video.db";

    private static DaoSession mDaoSession;

    public static void initGreenDao(Context context) {
        DaoMaster.DevOpenHelper helper = new DaoMaster.DevOpenHelper(context, DB_NAME);
        SQLiteDatabase db = helper.getWritableDatabase();
        DaoMaster daoMaster = new DaoMaster(db);
        mDaoSession = daoMaster.newSession();
    }

    public static DaoSession getDaoSession() {
        return mDaoSession;
    }

}
