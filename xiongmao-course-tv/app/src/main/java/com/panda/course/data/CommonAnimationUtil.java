package com.panda.course.data;

import android.view.View;
import android.view.animation.Animation;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.panda.course.R;
import com.panda.course.ext.CommExtKt;
import com.panda.course.util.ToastUtil;

public class CommonAnimationUtil {

    /**
     * 执行功能页View的边界动画
     */
    public boolean startPlayerViewAnimationShake(Animation animation, BaseQuickAdapter mAdapter, int position, boolean action) {
        if (mAdapter != null) {
            if (mAdapter.getData().size() == 1) {
                startViewAnimation(animation, mAdapter, position);
                return true;
            }
            if (position == 0) {
                startViewAnimation(animation, mAdapter, position);
            } else if (position >= 1 && position == (mAdapter.getData().size() - 1)) {
                startViewAnimation(animation, mAdapter, position);
                return action;
            }
        }
        return false;
    }

    /**
     * 执行功能页View的边界动画
     */
    private void startViewAnimation(Animation animation, BaseQuickAdapter mAdapter, int position) {
        if (mAdapter != null) {
            View view = mAdapter.getViewByPosition(position, R.id.rl_common_item_view);
            if (view != null) {
                view.clearAnimation();
                view.startAnimation(animation);
            }
        }
    }

    /**
     * 执行功能页View的边界动画 下方向
     */
    public boolean startPlayerViewAnimationShakeY(Animation animation, BaseQuickAdapter mAdapter, int position) {
        if (mAdapter != null) {
            View view = mAdapter.getViewByPosition(position, R.id.rl_common_item_view);
            if (view != null) {
                view.clearAnimation();
                view.startAnimation(animation);
            }
        }
        return true;
    }


    /**
     * 动画效果
     */
    public void startShakeAnimation(boolean actionY, View mView) {
        mView.clearAnimation();
        mView.startAnimation(actionY ? CommExtKt.getMShakeYAnimation() : CommExtKt.getMShakeAnimation());
    }
}
