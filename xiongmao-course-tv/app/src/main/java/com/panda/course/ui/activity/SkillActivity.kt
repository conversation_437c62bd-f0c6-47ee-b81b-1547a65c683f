package com.panda.course.ui.activity

import android.os.Bundle
import android.os.Handler
import android.view.KeyEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import com.panda.course.R
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivitySkillBinding
import com.panda.course.entity.AuntSkillRecordListEntity
import com.panda.course.ext.*
import com.panda.course.ui.activity.dialog.QrSkillActivity
import com.panda.course.ui.activity.dialog.SkillRecordActivity
import com.panda.course.ui.adapter.RowSkillAdapter
import com.panda.course.ui.adapter.SkillAdapter
import com.panda.course.ui.adapter.SkillRecordAdapter
import com.panda.course.ui.viewmodel.SkillViewModel

class SkillActivity : BaseDbActivity<SkillViewModel, ActivitySkillBinding>() {

    private var mRowAdapter = RowSkillAdapter()
    private var mSkillAdapter = SkillAdapter()
    private var mSkillRecordAdapter = SkillRecordAdapter(true)

    private var mRowView: View? = null

    private var page = 1

    /**
     * 默认分类
     */
    private val focus_row = 1

    /**
     * 全部技能测评
     */
    private val focus_skill_all = 2

    /**
     * 测评记录
     */
    private val focus_skill_record = 3

    /**
     * 记录当前的焦点在哪里
     */
    private var currentFocusType: Int = focus_row

    override fun initView(savedInstanceState: Bundle?) {
        initRecyclerView()
        onLoadRetry()
    }

    override fun onLoadRetry() {
        mViewModel.getAuntSkillAll()
        requestOne()
    }

    private fun initRecyclerView() {
        mDataBind.recyclerRow.adapter = mRowAdapter


        val mStrList = ArrayList<String>()
        mStrList.add("技能测评")
        mStrList.add("测评记录")
        mRowAdapter.setList(mStrList)


        mSkillAdapter.setEmptyView(getEmptyView(tips = "暂无技能测评", R.color.white))

        mDataBind.recyclerChild.adapter = mSkillAdapter
        mDataBind.recyclerChild.setColumnNumbers(1)


        mDataBind.recyclerRecord.adapter = mSkillRecordAdapter
        mDataBind.recyclerRecord.setColumnNumbers(2)
    }


    fun requestOne() {
        Handler().postDelayed({
            if (mRowAdapter.data.size > 0) {
                val mView = mRowAdapter.getViewByPosition(0, R.id.tv_item_row_skill)
                mView?.requestFocus()

            }
        }, 100)
    }

    override fun initObserver() {
        mRowAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mRowView = view
                page = 1
                if (0 == position) {
                    mDataBind.recyclerChild.visible()
                    mDataBind.recyclerRecord.gone()
                    mViewModel.getAuntSkillAll()
                } else {
                    mDataBind.recyclerRecord.backToTop()
                    mDataBind.recyclerRecord.visible()
                    mDataBind.recyclerChild.gone()
                    mViewModel.getAuntSkillRecord(page)
                }
            } else {
                mRowView?.background = ContextCompat.getDrawable(this@SkillActivity, R.drawable.base_border_unselecter)
            }

            currentFocusType = focus_row
        }


        mSkillAdapter.apply {
            setOnViewFocus { hasFocus, position, view ->
                if (hasFocus && currentFocusType != focus_skill_all) {
                    mRowView?.background = ContextCompat.getDrawable(this@SkillActivity, R.drawable.base_border_selecter)
                    currentFocusType = focus_skill_all
                }
            }
            setOnItemClickListener { adapter, view, position ->
                toStartActivity(QrSkillActivity::class.java, Bundle().apply {
                    putString("course_uuid", mSkillAdapter.data[position].uuid)
                })
            }
        }


        mSkillRecordAdapter.setOnItemClickListener { adapter, view, position ->
            toStartActivity(SkillRecordActivity::class.java, Bundle().apply {
                putInt("position", position)

                val arr = ArrayList<AuntSkillRecordListEntity>()
                for (i in mSkillRecordAdapter.data.indices) {
                    arr.add(mSkillRecordAdapter.data[i])
                }
                putParcelableArrayList("record", arr)
            })
        }

        mSkillRecordAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus && currentFocusType != focus_skill_record) {
                mRowView?.background = ContextCompat.getDrawable(this@SkillActivity, R.drawable.base_border_selecter)
                currentFocusType = focus_skill_record
            }

            if (hasFocus && position == mSkillRecordAdapter.data.size - 1) {
                page++
                mViewModel.getAuntSkillRecord(page)
            }
        }
    }


    override fun onRequestSuccess() {
        mViewModel.auntSkillListEntity.observe(this, Observer {
            if (it == null) {
                return@Observer
            }

            mRowAdapter.data[1] = "测评记录(${it.record_total})"
            mRowAdapter.notifyItemChanged(1) //更新记录
            mSkillAdapter.setList(it.list)


        })


        mViewModel.recordListEntity.observe(this, Observer {
            if (it == null) {
                return@Observer
            }
            page == it.page
            if (1 == page) {
                mSkillRecordAdapter.setList(it.list)
            } else {
                mSkillRecordAdapter.addData(it.list)
            }
        })


    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo() // 宣传片

        if (onMyKeyDown(keyCode, event)) { // 加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (currentFocusType == focus_row) {
                    mRowView?.background = ContextCompat.getDrawable(this@SkillActivity, R.drawable.base_border)
                }
            }
        }

        return super.onKeyDown(keyCode, event)
    }

}