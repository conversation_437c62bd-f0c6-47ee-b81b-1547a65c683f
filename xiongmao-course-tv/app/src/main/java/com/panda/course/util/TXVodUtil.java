package com.panda.course.util;

import com.panda.course.entity.VideoDownloadInfo;
import com.panda.course.ext.LogExtKt;
import com.tencent.rtmp.downloader.TXVodDownloadManager;
import com.tencent.rtmp.downloader.TXVodDownloadMediaInfo;

import java.util.ArrayList;
import java.util.List;

public class TXVodUtil {


    private static TXVodUtil instance;//单例模式 双重检查锁定

    public static TXVodUtil getInstance() {
        if (instance == null) {
            synchronized (TXVodUtil.class) {
                if (instance == null) {
                    instance = new TXVodUtil();
                }
            }
        }
        return instance;
    }


    /**
     * 关闭所有下载视频的任务
     */
    public void stopAllVideoDownload() {
//        LogExtKt.logE("腾讯云停止任务", "DownloadVideoService");
        List<TXVodDownloadMediaInfo> videos = TXVodDownloadManager.getInstance().getDownloadMediaInfoList();
        if (videos != null && videos.size() > 0) {
            for (TXVodDownloadMediaInfo info : videos) {
                if (!info.isDownloadFinished()) { // 没有完成的，都去stop
                    TXVodDownloadManager.getInstance().stopDownload(info);
                }
            }
        }
    }


    /**
     * 删除指定的任务
     */
    public void deleteTXVideo(String videoUrl) {
        if (TXVodDownloadManager.getInstance().getDownloadMediaInfoList() != null && TXVodDownloadManager.getInstance().getDownloadMediaInfoList().size() > 0) {
            for (int i = 0; i < TXVodDownloadManager.getInstance().getDownloadMediaInfoList().size(); i++) {
                if (TXVodDownloadManager.getInstance().getDownloadMediaInfoList().get(i).getUrl().equals(videoUrl)) {
                    TXVodDownloadManager.getInstance().deleteDownloadMediaInfo(TXVodDownloadManager.getInstance().getDownloadMediaInfoList().get(i));
                    break;
                }
            }
        }
    }

    /**
     * 删除全部的任务
     */
    public void deleteAllTXVideo() {
        if (TXVodDownloadManager.getInstance().getDownloadMediaInfoList() != null && TXVodDownloadManager.getInstance().getDownloadMediaInfoList().size() > 0) {
            for (int i = 0; i < TXVodDownloadManager.getInstance().getDownloadMediaInfoList().size(); i++) {
                TXVodDownloadManager.getInstance().deleteDownloadMediaInfo(TXVodDownloadManager.getInstance().getDownloadMediaInfoList().get(i));
            }
        }
    }

    /**
     * 统计上报本地视频的信息
     */
    public List<VideoDownloadInfo> uploadTXVideo() {
        List<VideoDownloadInfo> uploads = new ArrayList<>();
        for (TXVodDownloadMediaInfo info : TXVodDownloadManager.getInstance().getDownloadMediaInfoList()) {
            uploads.add(new VideoDownloadInfo(info.getUserName(), String.valueOf(info.getProgress() * 1.0f * 100)));
        }
        return uploads;
    }
}
