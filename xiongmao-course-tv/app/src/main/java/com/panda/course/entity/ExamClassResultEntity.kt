package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ExamClassResultEntity(
    val course_title: String? = null,
    val score: String? = null,
    val score_level: String? = null,
    val service_personnel_name: String? = null,
    val score_ratio_one: String? = null,
    val score_ratio_two: String? = null,
    val score_ratio_three: String? = null,
    val score_ratio_four: String? = null,
    var list: List<ExamClassResultListEntity> = emptyList(),
) : Parcelable


@Parcelize
data class ExamClassResultListEntity(
    var id: String? = null,
    var title: String? = null,
    var content: String? = null,
    var type: String? = null,
    var answer: String? = null,
    var my_answer: String? = null,
    var analyse: String? = null,
    var iscorrect: String? = null,
    var title_source: String? = null,
    var is_image: String? = null,
    var title_source_type: String? = null,
) : Parcelable