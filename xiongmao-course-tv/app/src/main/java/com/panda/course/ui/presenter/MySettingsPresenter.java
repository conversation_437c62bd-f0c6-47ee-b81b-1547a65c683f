package com.panda.course.ui.presenter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.leanback.widget.Presenter;

import com.panda.course.R;
import com.panda.course.entity.SettingInfo;

public class MySettingsPresenter extends Presenter {

    private Context mContext;

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_app_settings, parent, false);
//        view.setOnFocusChangeListener((v, hasFocus) -> {
//            v.findViewById(R.id.tv_app_name).setSelected(hasFocus);
//        });
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        if (item instanceof SettingInfo) {
            ViewHolder vh = (ViewHolder) viewHolder;
            if (vh != null) {
                vh.ivLogo.setImageResource(((SettingInfo) item).getLogo());
                vh.tvName.setText(((SettingInfo) item).getName());
                vh.tvSubName.setText(((SettingInfo) item).getSubContent());
                vh.tvStatusName.setText(((SettingInfo) item).getStatusName());
            }
        }
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public class ViewHolder extends Presenter.ViewHolder {

        private TextView tvName;
        private TextView tvSubName;
        private TextView tvStatusName;
        private ImageView ivLogo;
        private ImageView ivLogoStatus;

        public ViewHolder(View view) {
            super(view);
            tvName = view.findViewById(R.id.tv_item_setting_name);
            tvSubName = view.findViewById(R.id.tv_item_setting_sub_name);
            tvStatusName = view.findViewById(R.id.tv_item_setting_status_name);
            ivLogo = view.findViewById(R.id.iv_item_setting_logo);
            ivLogoStatus = view.findViewById(R.id.iv_item_setting_logo_status);
        }
    }

}
