package com.panda.course.util;

import android.Manifest;
import android.app.Activity;
import android.app.Fragment;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.Priority;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.request.transition.Transition;
import com.panda.course.R;
import com.panda.course.config.UMConstant;
import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.LogExtKt;

import java.io.File;
import java.util.HashMap;
import java.util.concurrent.ExecutionException;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import org.jetbrains.annotations.NotNull;

/**
 * DiskCacheStrategy.ALL ： 使用DATA和RESOURCE缓存远程数据，仅使用RESOURCE来缓存本地数据。
 * DiskCacheStrategy.NONE： 表示不缓存任何内容。
 * DiskCacheStrategy.DATA： 在将原始数据写入磁盘缓存。
 * DiskCacheStrategy.RESOURCE： 表示只缓存转换过后的图片
 * DiskCacheStrategy.AUTOMATIC ： 根据原始图片数据和资源编码策略来自动选择磁盘缓存策略。
 * 缓存机制根据业务需求自行替换
 */

public class GlideUtil {
    public static void loadPic(Activity activity, int resID, ImageView imageView) {
        if (activity != null && !activity.isDestroyed()) {
            try {
                Glide.with(activity)
                        .load(resID)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error)
                        .dontAnimate().diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                        .into(imageView);
            } catch (Exception ignored) {
            }
        } else {
            Log.i("Tag", "Picture loading failed,activity is null");
        }
    }

    public static void loadPic(Activity activity, String resID, ImageView imageView, int placeholder) {
        if (activity != null && !activity.isDestroyed()) {
            try {
                Glide.with(activity)
                        .load(resID)
                        .placeholder(placeholder)
                        .dontAnimate()
                        .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).into(imageView);
            } catch (Exception ignored) {
            }
        } else {
            Log.i("Tag", "Picture loading failed,activity is null");
        }
    }

    public static void loadPicNoCache(Activity activity, String resID, ImageView imageView) {
        if (activity != null && !activity.isDestroyed()) {
            try {
                Glide.with(activity)
                        .load(resID)
                        .dontAnimate().into(imageView);
            } catch (Exception ignored) {
            }
        } else {
            Log.i("Tag", "Picture loading failed,activity is null");
        }
    }


    /**
     * 对图片进行重试策略
     */
    public static void loadRetryPic(Activity activity, String url, ImageView imageView, final int retryCount) {
        if (activity != null && !activity.isDestroyed()) {
            try {
                Glide.with(activity)
                        .asBitmap()
                        .load(url)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                        .listener(new RequestListener<Bitmap>() {
                            @Override
                            public boolean onLoadFailed(@Nullable @org.jetbrains.annotations.Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                                return false;
                            }
                        })
                        .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                        .into(imageView);
            } catch (Exception e) {
            }
        }
    }


    public static void loadQrPic(Activity activity, String url, ImageView imageView, PictureListener listener) {
        if (activity != null) {
            try {
                Glide.with(activity)
                        .asBitmap()
                        .load(url)
                        .skipMemoryCache(false)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                        .diskCacheStrategy(DiskCacheStrategy.NONE)//自动缓存
                        .apply(new RequestOptions()
                                .priority(Priority.IMMEDIATE))
                        .listener(new RequestListener<Bitmap>() {

                            @Override
                            public boolean onLoadFailed(@Nullable @org.jetbrains.annotations.Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                                if (listener != null) {
                                    if (e != null) {
                                        listener.fail(e.getMessage());
                                    }
                                }
                                LogExtKt.logE("onLoadFailed=" + e.getMessage(), "Glide");

                                HashMap<String, Object> stringHashMap = new HashMap<>();
                                stringHashMap.put("device", AppExtKt.getWireMac());
                                if (e != null && !TextUtils.isEmpty(e.getMessage())) {
                                    stringHashMap.put("failed_msg", e.getMessage());
                                }
                                AppExtKt.appUMEventObject(UMConstant.KEY_IMAGE_LOAD_ERROR, stringHashMap);
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                                LogExtKt.logE("onResourceReady=" + resource.getHeight(), "Glide");
                                LogExtKt.logE("onResourceReady=" + resource.getWidth(), "Glide");
                                if (listener != null) {
                                    listener.success();
                                }
                                return false;
                            }
                        })
                        .into(imageView);
            } catch (Exception e) {
                if (e != null && listener != null) {
                    listener.fail(e.getMessage());
                }
            }
        } else {
            ToastUtil.show("图片加载失败");
            if (listener != null) {
                listener.fail("图片加载失败");
            }
            Log.i("Tag", "Picture loading failed,activity is null");
        }
    }

    public static void loadPic(Activity activity, String url, ImageView imageView) {
        if (activity != null) {
            try {
                Glide.with(activity)
                        .asBitmap()
                        .load(url)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                        .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)//自动缓存
                        .into(imageView);
            } catch (Exception ignored) {
            }
        } else {
            ToastUtil.show("图片加载失败");
            Log.i("Tag", "Picture loading failed,activity is null");
        }
    }

    public static void loadPreloadPic(Activity activity, String url) {
        if (activity != null) {
            try {
                Glide.with(activity)
                        .load(url)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                        .diskCacheStrategy(DiskCacheStrategy.ALL)//自动缓存
                        .preload();
            } catch (Exception ignored) {
                ToastUtil.show("图片加载失败");
            }
        } else {
            ToastUtil.show("图片加载失败");
            Log.i("Tag", "Picture loading failed,activity is null");
        }
    }


    public static void loadPicRound(Context context, Bitmap bitmap, ImageView imageView, int degree) {
        if (context != null) {
            try {
                Glide.with(context).
                        load(bitmap)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片

                        .transform(new GlideRoundTransform((int) DisplayUtil.getPxFromDp(context, degree)))
                        .into(imageView);
            } catch (Exception ignored) {

            }
        } else {
            Log.i("Tag", "Picture loading failed,activity is null");
        }
    }

    public static void loadPicRound(Activity activity, String url, ImageView imageView, int degree) {
        if (activity != null && !activity.isDestroyed()) {
            try {
                Glide.with(activity).
                        load(url)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片

                        .transform(new GlideRoundTransform((int) DisplayUtil.getPxFromDp(activity, degree)))
                        .into(imageView);
            } catch (Exception ignored) {

            }
        } else {
            Log.i("Tag", "Picture loading failed,activity is null");
        }
    }

    public static void loadPicRound(Activity activity, int resId, ImageView imageView, int degree) {
        if (activity != null && !activity.isDestroyed()) {
            try {
                Glide.with(activity)
                        .load(resId)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片

                        .transform(new GlideRoundTransform((int) DisplayUtil.getPxFromDp(activity, degree)))
                        .into(imageView);
            } catch (Exception ignored) {
            }
        } else {
            Log.i("Tag", "Picture loading failed,activity is null");
        }
    }

    public static void getUrlCacheBitmapWithHW(final Context context, final String url, int width, int height, int degree, final GetUrlCacheBitmapListener listner) {
        if (context == null) {
            return;
        }
        if (!TextUtils.isEmpty(url)) {
            Glide.with(context)
                    .asBitmap()
                    .load(url)
                    .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                    .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                    .into(new CustomTarget<Bitmap>(width, height) {
                        @Override
                        public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                            if (listner != null) {
                                if (degree != 0) {
                                    listner.loadBitmap(bitmpRound(resource, (int) DisplayUtil.getPxFromDp(context, degree)));
                                } else {
                                    listner.loadBitmap(resource);
                                }
                            }
                        }

                        @Override
                        public void onLoadCleared(@Nullable Drawable placeholder) {

                        }
                    });
        }
    }

    private static Bitmap bitmpRound(Bitmap mbitmap, float index) {
        Bitmap bitmap = Bitmap.createBitmap(mbitmap.getWidth(), mbitmap.getHeight(), Bitmap.Config.ARGB_4444);
        Canvas canvas = new Canvas(bitmap);
        Paint paint = new Paint();
        paint.setAntiAlias(true);

        Rect rect = new Rect(0, 0, mbitmap.getWidth(), mbitmap.getHeight());
        RectF rectF = new RectF(rect);

        canvas.drawARGB(0, 0, 0, 0);
        canvas.drawRoundRect(rectF, index, index, paint);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));

        canvas.drawBitmap(mbitmap, rect, rect, paint);
        return bitmap;
    }

    public static void getUrlCacheBitmapForShareThumbnail(final Context context, final String url, final GetUrlCacheBitmapListener listner) {
        if (context == null) {
            return;
        }
        if (!TextUtils.isEmpty(url)) {
            Glide.with(context)
                    .asBitmap()
                    .load(url)
                    .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片

                    .into(new CustomTarget<Bitmap>() {
                        @Override
                        public void onLoadFailed(@Nullable Drawable errorDrawable) {
                            super.onLoadFailed(errorDrawable);
                            if (listner != null) {
                                listner.loadBitmap(null);
                            }
                        }

                        @Override
                        public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                            if (listner != null) {
                                listner.loadBitmap(resource);
                            }
                        }

                        @Override
                        public void onLoadCleared(@Nullable Drawable placeholder) {

                        }
                    });
        }
    }

    /**
     * 加载第二秒的帧数作为封面
     * url就是视频的地址
     */
    public static void loadCover(ImageView imageView, String url, Context context) {
        imageView.setScaleType(ImageView.ScaleType.CENTER);
//        imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        Glide.with(context)
                .setDefaultRequestOptions(
                        new RequestOptions()
                                .frame(2000000)
                                .centerCrop()
//                                .error(R.mipmap.eeeee)//可以忽略
//                                .placeholder(R.mipmap.ppppp)//可以忽略
                )
                .load(url)
                .into(imageView);
    }

    public static void loadPic(Fragment fragment, int resID, ImageView imageView) {
        if (fragment != null && fragment.getActivity() != null) {
            try {
                Glide.with(fragment).load(resID).placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                        .dontAnimate().diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).into(imageView);
            } catch (Exception ignored) {
            }
        } else {
        }
    }

    public static void loadPic(Fragment fragment, String url, ImageView imageView) {
        if (fragment != null && fragment.getActivity() != null) {
            try {
                Glide.with(fragment).load(url).placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                        .thumbnail(0.2f).diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).dontAnimate().into(imageView);
            } catch (Exception ignored) {
            }
        } else {
        }
    }

    public static void loadPicRound(Fragment fragment, String url, ImageView imageView, int degree) {
        if (fragment != null && fragment.getActivity() != null) {
            try {
                Glide.with(fragment).
                        load(url)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片

                        .transform(new GlideRoundTransform((int) DisplayUtil.getPxFromDp(fragment.getActivity(), degree)))
                        .into(imageView);
            } catch (Exception ignored) {
            }
        } else {
        }
    }

    public static void loadPicRound(Fragment fragment, int resId, ImageView imageView, int degree) {
        if (fragment != null && fragment.getActivity() != null) {
            try {
                Glide.with(fragment).
                        load(resId)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片

                        .transform(new GlideRoundTransform((int) DisplayUtil.getPxFromDp(fragment.getActivity(), degree)))
                        .into(imageView);
            } catch (Exception ignored) {
            }
        } else {
        }
    }

    public static void loadPic(Context context, int resID, ImageView imageView) {
        if (context != null) {
            try {
                Glide.with(context).load(resID).placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                        .dontAnimate().diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).into(imageView);
            } catch (Exception ignored) {
            }
        } else {
        }
    }

    public static void loadPic(Context context, String url, ImageView imageView) {
        if (context != null) {
            try {
                Glide.with(context).load(url).placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                        .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).thumbnail(0.2f).dontAnimate().into(imageView);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
        }
    }

    public static void loadPic(Context context, String url, ImageView imageView, @DrawableRes int resourceId) {
        if (context != null) {
            try {
                Glide.with(context).load(url).placeholder(resourceId).diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).thumbnail(0.2f).dontAnimate().into(imageView);
            } catch (Exception e) {

            }
        } else {
        }
    }


    public static void loadPicNoHolder(Context context, String url, ImageView imageView) {
        if (context != null) {
            try {
                Glide.with(context).load(url).thumbnail(0.2f).diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).dontAnimate().into(imageView);
            } catch (Exception e) {
            }
        } else {
        }
    }

    /**
     * 显示圆图
     *
     * @param context
     * @param url
     * @param imageView
     */
    public static void loadPicRound(Context context, String url, ImageView imageView) {
        if (context != null) {
            try {
                Glide.with(context).load(url).diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                        .placeholder(R.drawable.icon_placeholder_round) //设置状态图片
                        .error(R.drawable.icon_placeholder_round)
                        .apply(RequestOptions.bitmapTransform(new CircleCrop())).into(imageView);
            } catch (Exception e) {
            }
        } else {
        }
    }

    public static void loadPicRound(Context context, Bitmap bitmap, ImageView imageView) {
        if (context != null) {
            try {
                Glide.with(context).load(bitmap).diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                        .placeholder(R.drawable.icon_placeholder_round) //设置状态图片
                        .error(R.drawable.icon_placeholder_round)
                        .apply(RequestOptions.bitmapTransform(new CircleCrop())).into(imageView);
            } catch (Exception e) {
            }
        } else {
        }
    }

    public static void loadPicRound(Context context, String url, ImageView imageView, int degree) {
        if (context != null) {
            try {
                Glide.with(context).load(url)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_placeholder) //设置状态图片
                        .diskCacheStrategy(DiskCacheStrategy.DATA)
                        .transform(new CenterCrop(), new RoundedCorners((int) DisplayUtil.getPxFromDp(context, degree)))
                        .into(imageView);
            } catch (Exception e) {
            }
        } else {
        }
    }

    public static void loadPicRound(Context context, String url, ImageView imageView, int place_holder, int degree) {
        if (context != null) {
            try {
                Glide.with(context).
                        load(url)
                        .placeholder(place_holder)
                        .transform(new GlideRoundTransform((int) DisplayUtil.getPxFromDp(context, degree)))
                        .into(imageView);
            } catch (Exception e) {

            }
        } else {
        }
    }


    public static void loadPicRound(Context context, int resId, ImageView imageView, int degree) {
        if (context != null) {
            try {
                Glide.with(context).
                        load(resId)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                        .transform(new GlideRoundTransform((int) DisplayUtil.getPxFromDp(context, degree)))
                        .into(imageView);
            } catch (Exception ignored) {
            }
        } else {
        }
    }


    public static void loadPicRound(Context context, String url, ImageView imageView, Float radius, Boolean leftTop, Boolean rightTop, Boolean leftBottom, Boolean rightBottom) {
        if (context != null) {
            try {
                Glide.with(context).
                        load(url)
                        .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error) //设置状态图片
                        .transform(new RoundedCornersTransform(context, radius, leftTop, rightTop, leftBottom, rightBottom))
                        .into(imageView);
            } catch (Exception ignored) {
            }
        } else {
        }
    }

    public static void loadGif(Context context, String url, ImageView imageView) {
        Glide.with(context)
                .asGif()
                .load(url)
                .into(imageView);
    }

    public static void loadGif(Context context, int res, ImageView imageView) {
        Glide.with(context)
                .asGif()
                .load(res)
                .into(imageView);
    }


    public static void downImage(Context context, String mUrl, PictureDownloadListener listener) {
        Glide.with(context)
                .downloadOnly()
                .load(mUrl)
                .listener(new RequestListener<File>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<File> target, boolean isFirstResource) {
                        if (listener != null) {
                            listener.fail(e.getMessage());
                        }
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(File resource, Object model, Target<File> target, DataSource dataSource, boolean isFirstResource) {
                        if (listener != null) {
                            listener.success(getDiskBitmap(resource.getAbsolutePath()));
                        }
                        return false;
                    }
                })
                .preload();
    }

    /**
     * 根据路径获取bitmap
     *
     * @param pathString
     * @return
     */
    private static Bitmap getDiskBitmap(String pathString) {
        Bitmap bitmap = null;
        try {
            File file = new File(pathString);
            if (file.exists()) {
                bitmap = BitmapFactory.decodeFile(pathString);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bitmap;
    }


    public interface PictureListener {
        void success();

        void fail(String msg);
    }

    public interface PictureDownloadListener {
        void success(Bitmap bitmap);

        void fail(String msg);
    }
}
