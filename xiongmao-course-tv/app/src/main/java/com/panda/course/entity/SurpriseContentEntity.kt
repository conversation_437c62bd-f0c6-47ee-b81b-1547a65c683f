package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
data class SurpriseContentEntity(
    var list: List<SurpriseContentListEntity> = emptyList(),
    var daily_task_calendar: List<SurpriseContentDailyEntity> = emptyList(),
    var tips: String = "",
    var finished_total: String = "",
    var activity: ActivityEntity? = null,
) : Parcelable

@Parcelize
data class ActivityEntity(
    var img_url: String = "",
    var tips: String = "",
    var task_finished_bg: String = "",
    var task_un_finished_bg: String = "",
) : Parcelable