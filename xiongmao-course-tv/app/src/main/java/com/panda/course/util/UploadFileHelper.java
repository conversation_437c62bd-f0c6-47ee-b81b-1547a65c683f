package com.panda.course.util;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.panda.course.ext.AppExtKt;
import com.panda.course.network.NetUrl;
import com.qiniu.android.common.FixedZone;
import com.qiniu.android.http.ResponseInfo;
import com.qiniu.android.storage.Configuration;
import com.qiniu.android.storage.UpCompletionHandler;
import com.qiniu.android.storage.UploadManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;

public class UploadFileHelper {

    private static String TAG = "UploadFileHelper";
    private static final String QINIU_HOST_NAME = NetUrl.BASE_URL;

    static UploadFileHelper uploadFileHelper;


    UploadManager uploadManager;


    //压缩文件存放路径
//    public static final String STOREPATH = Environment.getExternalStorageDirectory().getAbsolutePath() + File.separator + "Android/data/com.panda.course/files/log/";
    public static final String STOREPATH = Constants.LOG_PATH;

    boolean isUploading = false;

    private UploadFileHelper() {
        Configuration config = new Configuration.Builder()
                .connectTimeout(90)              // 链接超时。默认90秒
                .useHttps(false)                  // 是否使用https上传域名
                .useConcurrentResumeUpload(true) // 使用并发上传，使用并发上传时，除最后一块大小不定外，其余每个块大小固定为4M，
                .concurrentTaskCount(3)          // 并发上传线程数量为3
                .responseTimeout(90)             // 服务器响应超时。默认90秒
//                .recorder(recorder)              // recorder分片上传时，已上传片记录器。默认null
//                .recorder(recorder, keyGen)      // keyGen 分片上传时，生成标识符，用于片记录器区分是那个文件的上传记录
                .zone(FixedZone.zone2)           // 设置区域，不指定会自动选择。指定不同区域的上传域名、备用域名、备用IP。
                .build();
        // 重用uploadManager。一般地，只需要创建一个uploadManager对象
        uploadManager = new UploadManager(config);
    }

    public static UploadFileHelper getInstance() {
        if (uploadFileHelper == null) {
            uploadFileHelper = new UploadFileHelper();
        }
        return uploadFileHelper;
    }


    /**
     * 上传文件
     *
     * @param filePath 文件路径
     * @param listener 文件上传回调
     */
    public void uploadFile(String token, String filePath, UploadFileListener listener) {
        startUploadFile(token, -1, listener, getFileByPath(getAllDataFileName(filePath)));
    }


    /**
     * 上传文件
     *
     * @param requestCode 文件上传回调对应码
     * @param files       文件
     * @param listener    监听回调
     */
    private void startUploadFile(String token, int requestCode, UploadFileListener listener, File... files) {
        if (!isUploading) {
            isUploading = true;
        } else {
            if (listener != null) {
                listener.uploadFailed("当前正在执行上传任务", requestCode);
            }
            return;
        }
        if (!TextUtils.isEmpty(token)) {
            uploadFileQiNiu(requestCode, listener, token, files);
        } else {
            ToastUtil.show("上传失败，稍后再尝试");
        }
    }


    /**
     * 路径转换为文件
     *
     * @param filePath
     * @return
     */
    private File[] getFileByPath(ArrayList<String> filePath) {
        File[] file = new File[filePath.size()];
        for (int i = 0; i < filePath.size(); i++) {
            file[i] = new File(filePath.get(i));
        }
        return file;
    }


    /**
     * 上传文件,指定请求号
     *
     * @param requestCode 请求号
     * @param files       本地文件
     * @param listener    监听回调
     */
    private void uploadFileQiNiu(int requestCode, UploadFileListener listener, String token, File... files) {
        ArrayList<String> fielUrls = new ArrayList<>();
        Log.e(TAG, "uploadFileQiNiu start " + files.length);
        for (int i = 0; i < files.length; i++) {
            final int index = i;
            Thread thread = new Thread() {
                @Override
                public void run() {
                    try {
                        uploadFileQiniu(files[index], token, new UpQiniuListener() {
                            @Override
                            public void uploadSuccess(String url) {
                                Log.e(TAG, "uploadFileQiNiu end " + index);
                                fielUrls.add(url);
//                                if (fielUrls.size() == files.length && isUploading) {
//                                    Log.e(TAG, "Upload Success");  //正确的：{"hash":"Ft-886ksoUqzTBhiUewWITF_mcnB","key":"14e357c7239b439188065d507b25bd1f.amr"}
//                                    if (listener != null) {
//                                        listener.uploadSuccess(fielUrls.size() == 1 ? fielUrls.get(0) : new Gson().toJson(fielUrls), requestCode);
//                                    }
//                                    isUploading = false;
//                                }
                                if (files.length == fielUrls.size()) {
                                    if (listener != null) {
                                        listener.uploadSuccess(fielUrls, requestCode);
                                    }
                                    isUploading = false;
                                }
                            }

                            @Override
                            public void uploadFailed(String err) {
                                Log.e(TAG, "Upload Fail");
                                //如果失败，这里可以把info信息上报自己的服务器，便于后面分析上传错误原因
                                if (listener != null) {
                                    listener.uploadFailed(err, requestCode);
                                }
                                isUploading = false;

                            }
                        });
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            };
            thread.start();
        }
    }

    /**
     * 上传文件,指定请求号
     *
     * @param file     本地文件
     * @param listener 监听回调
     */
    public void uploadFileQiniu(File file, String token, UpQiniuListener listener) {
        String name = file.getName();
        DateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
        String filename = "tencent/" + (simpleDateFormat.format(new Date()).replaceAll("/", "-") + "-" + AppExtKt.getWireMac()) + "/" + name;
        uploadManager.put(file, filename, token, new UpCompletionHandler() {
            @Override
            public void complete(String key, ResponseInfo info, JSONObject response) {  //response ={"error":"incorrect region, please use up-z2.qiniup.com"}
                //res包含hash、key等信息，具体字段取决于上传策略的设置  回调中key:36d5895cba64403faf8b3ef8ea7c8cb0.amr
                Log.e(TAG, "Upload info" + info);
                Log.e(TAG, "Upload info" + getKeyUrl(response));

                if (info.isOK()) {
                    listener.uploadSuccess(getKeyUrl(response));
                    Log.e(TAG, "uploadSuccess" + getKeyUrl(response));
                    //http://q349xx31l.bkt.clouddn.com/14e357c7239b439188065d507b25bd1f.amr
                } else {
                    //如果失败，这里可以把info信息上报自己的服务器，便于后面分析上传错误原因
                    listener.uploadFailed(info.error);
                }

            }
        }, null);
    }


    // 七牛完整地址
    private static String getUrl(String key) {
        return QINIU_HOST_NAME + key;
    }

    private static String getKeyUrl(JSONObject response) {
        if (response == null) {
            return null;
        }
        String url = null;
        try {
            url = QINIU_HOST_NAME + String.valueOf(response.get("key"));
        } catch (JSONException e) {
            Log.e(TAG, "获取七牛返回的Url" + e);
        }
        return url;
    }

    private static String getUrl(JSONObject response) {
        if (response == null) {
            return null;
        }
        String url = null;
        try {
            url = QINIU_HOST_NAME + String.valueOf(response.get("hash"));
        } catch (JSONException e) {
            Log.e(TAG, "获取七牛返回的Url" + e);
        }
        return url;
    }


    /**
     * 获取路径下的全部文件进行上传
     *
     * @param folderPath
     * @return
     */
    public ArrayList<String> getAllDataFileName(String folderPath) {
        ArrayList<String> fileList = new ArrayList<>();
        File file = new File(folderPath);
        File[] tempList = file.listFiles();
        for (int i = 0; i < tempList.length; i++) {
            if (tempList[i].isFile()) {
                if (tempList[i].getAbsolutePath() != null) {
                    fileList.add(tempList[i].getAbsolutePath());
                }
            }
        }
        return fileList;
    }

    public interface UpQiniuListener {
        void uploadSuccess(String url);

        void uploadFailed(String msg);
    }

    public interface UploadFileListener {
        void uploadSuccess(ArrayList<String> arr, int uploadRequestCode);

        void uploadFailed(String message, int requestCode);
    }
}
