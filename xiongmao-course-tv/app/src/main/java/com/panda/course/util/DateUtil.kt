package cn.jiazhengye.panda_home.utils

import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 时间处理的工具类
 */
open class DateUtil {


    /**
     * 获取当前手机系统的日期
     */
    fun getToDayDate(): String {
        var simpleDateFormat = SimpleDateFormat("yyyy-MM-dd");// HH:mm:ss
        //获取当前时间
        var date = Date(System.currentTimeMillis());
        return simpleDateFormat.format(date)
    }

    /**
     * 获取当前手机系统月份
     */
    fun getMonthDate(): String {
        var simpleDateFormat = SimpleDateFormat("yyyy-MM")
        //获取当前时间
        var date = Date(System.currentTimeMillis());
        return simpleDateFormat.format(date)
    }

    /**
     * 获得指定日期的前一天
     * @param specifiedDay
     * @return
     * @throws Exception
     */
    open fun getSpecifiedDayBefore(specifiedDay: String?, num: Int): Date {
        val c: Calendar = Calendar.getInstance()
        var date: Date? = null
        try {
            date = SimpleDateFormat("yy-MM-dd").parse(specifiedDay)
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        c.time = date
        val day: Int = c.get(Calendar.DATE)
        c.set(Calendar.DATE, day - num)
        return c.time
    }

    /**
     * 获得指定日期的后一天
     * @param specifiedDay
     * @return
     */
    open fun getSpecifiedDayAfter(specifiedDay: String?, num: Int): Date {
        val c: Calendar = Calendar.getInstance()
        var date: Date? = null
        try {
            date = SimpleDateFormat("yy-MM-dd").parse(specifiedDay)
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        c.time = date
        val day: Int = c.get(Calendar.DATE)
        c.set(Calendar.DATE, day + num)
        return c.time
    }


    /**
     * 根据日期获取星期几
     */
    open fun getWeek(date: Date?): String? {
        val sdf = SimpleDateFormat("E")
        return sdf.format(date)
    }


    /**
     * 将秒数转换为hh:mm:ss的格式
     *
     * @param second
     * @return
     */
    open fun formattedTime(second: Long): String? {
        val formatTime: String
        val h: Long
        val m: Long
        val s: Long
        h = second / 3600
        m = second % 3600 / 60
        s = second % 3600 % 60
        formatTime = if (h == 0L) {
            asTwoDigit(m) + ":" + asTwoDigit(s)
        } else {
            asTwoDigit(h) + ":" + asTwoDigit(m) + ":" + asTwoDigit(s)
        }
        return formatTime
    }

    protected fun asTwoDigit(digit: Long): String {
        var value = ""
        if (digit < 10) {
            value = "0"
        }
        value += digit.toString()
        return value
    }

}