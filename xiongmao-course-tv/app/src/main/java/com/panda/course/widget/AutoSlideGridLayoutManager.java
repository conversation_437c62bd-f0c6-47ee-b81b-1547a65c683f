package com.panda.course.widget;


import android.content.Context;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.panda.course.ext.LogExtKt;

public class AutoSlideGridLayoutManager extends GridLayoutManager {
    private static final String TAG = AutoSlideGridLayoutManager.class.getName();
    private final Handler handler = new Handler();
    private RecyclerView recyclerView;
    private boolean isLayout = true; //是否自动计算子布局数
    private boolean isPageUp = true; //是否自动翻页
    private int pageUpTime = 5000;//翻页间隔时间
    private int pageReStartUpTime = 10000;//重新开始的时间

    public AutoSlideGridLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public AutoSlideGridLayoutManager(Context context, int spanCount) {
        super(context, spanCount);
    }

    public AutoSlideGridLayoutManager(Context context, int spanCount, int orientation, boolean reverseLayout) {
        super(context, spanCount, orientation, reverseLayout);
    }

    public void setLayout(boolean layout) {
        isLayout = layout;
    }

    public void setPageUp(boolean pageUp) {
        isPageUp = pageUp;
        if (!isPageUp && pageUp) {
            startPageUp();
        }
    }

//    public void setPageUpTime(int pageUpTime) {
//        this.pageUpTime = pageUpTime;
//    }

    //停止自动滚动
    public void stopPageUp() {
        handler.removeCallbacks(pageUpRunnable);
        handler.postDelayed(pageUpRunnable, pageReStartUpTime);
    }

    public void startPageUp() {
        handler.removeCallbacks(pageUpRunnable);
        handler.postDelayed(pageUpRunnable, pageUpTime);
    }

    @Override
    public void onItemsChanged(RecyclerView recyclerView) {
        super.onItemsChanged(recyclerView);
        this.recyclerView = recyclerView;
        startPageUp();
    }

    private Runnable pageUpRunnable = new Runnable() {
        @Override
        public void run() {
            if (pageUpTime <= 0) return;
            if (!isPageUp) return;
            pageUp();
            startPageUp();
        }
    };

    @Override
    public void onLayoutCompleted(RecyclerView.State state) {
        super.onLayoutCompleted(state);
        if (!isLayout) return;
        //这里使用handler的原因在于先让主代码执行等 系统本身函数执行完成再进行计算
        /**
         * 比较忙没有研究源码 这里我更愿意理解为 UI绘制需要时间直接调用calculate函数会在UI没有绘制完成的时候取计算span数 导致获取到错误的宽度等信息
         * post不跨线程的情况下 单线程是从上至下执行使用post会让当前run的执行塞到现在任务的尾端 //我的理解望大佬指教
         */
        handler.post(updateCount);
    }

    private final Runnable updateCount = new Runnable() {
        @Override
        public void run() {
            calculate();
        }
    };

    /**
     * 动态计算行数
     */
    public void calculate() {
        if (getChildCount() == 0) return;
        View view = getChildAt(0);
        RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) view.getLayoutParams();
        int count;
        if (getOrientation() == RecyclerView.VERTICAL) {
            int childWidth = view.getWidth() + params.leftMargin + params.rightMargin;
            if (childWidth <= 0) return;
            count = getWidth() / childWidth;
        } else {
            int childHeight = view.getHeight() + params.topMargin + params.bottomMargin;
            if (childHeight <= 0) return;
            count = getHeight() / childHeight;
        }
        if (count == getSpanCount()) return;
        if (count == 0) count = 1;
        setSpanCount(count);
    }

    private int slideCountHeight;
    private RollPositionListener positionListener;

    public void setPositionListener(RollPositionListener positionListener) {
        this.positionListener = positionListener;
    }

    private void pageUp() {
        if (recyclerView == null) {
            try {
                throw new Exception("如果要使用翻页功能则不能直接加载数据，应通过notifyDataSetChanged 把数据更新过去");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (getOrientation() != RecyclerView.VERTICAL) {
            try {
                throw new Exception("翻页只暂时只支持垂直翻页，不支持水平翻页。");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (recyclerView == null) return;
        if (recyclerView.getScrollState() != 0) return;
        if (getItemCount() == 0 || getChildCount() == 0 || getSpanCount() == 0) return;
        View childAt = getChildAt(0);
        RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) childAt.getLayoutParams();
        int height = childAt.getHeight() + params.topMargin + params.bottomMargin;
        int itemSize = findLastVisibleItemPosition() - findFirstVisibleItemPosition() + 1;
        //针对显示时候  虽然 item在屏幕显示但显示不完整 时候 把最后一行再显示一次
        int rowItemSize;
        if (getSpanCount() < itemSize) {
            rowItemSize = itemSize / getSpanCount() - 1;
        } else {
            rowItemSize = itemSize / getSpanCount(); //如果不需要重复显示 只保留这一行
        }
        //在这里做一下处理，如果到最后了，那么反方向减少
        int slideHeight = 0;
        if (!isBottom) { //没到底走这个
            slideCountHeight += slideHeight = height * rowItemSize;
            LogExtKt.logE("我计算了 - " + slideCountHeight, "自动翻滚");
        }
        int countHeight = height * getItemCount() / getSpanCount();
        if ((countHeight - slideCountHeight) <= 0) {
            isBottom = true;
//            slideHeight = 0 - countHeight;
//            slideCountHeight = 0;
//            if (positionListener != null) {
//                positionListener.onRollToBottom();
//            }
        } else if (rowItemSize == 0) {
//            if (positionListener != null) {
//                positionListener.onRollToBottom();
//            }

        }

        if (isBottom) {//说明到底了
            slideHeight = -886;
            slideCountHeight = slideCountHeight - slideHeight;
        }

//        LogExtKt.logE("slideHeight - " + slideHeight + " ； countHeight - " + countHeight + " ； slideCountHeight - " + slideCountHeight, "自动翻滚");
        recyclerView.smoothScrollBy(0, slideHeight);
    }


    public void setBottom(boolean bottom) {
        isBottom = bottom;
        slideCountHeight = 0;
    }

    private boolean isBottom = false;


    public interface RollPositionListener {
        void onRollToBottom();
    }
}