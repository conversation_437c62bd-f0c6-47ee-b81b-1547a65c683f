package com.panda.course.ui.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.panda.course.R;

public class ImageHolder extends RecyclerView.ViewHolder {
    public ImageView imageView;
    public TextView tv_date;
    public TextView mContent;
    public TextView mName;
    public TextView tips;
    public RecyclerView studentContent;

    public ImageHolder(@NonNull View view) {
        super(view);
        this.imageView = (ImageView) view.findViewById(R.id.image);
        this.tv_date = (TextView) view.findViewById(R.id.tv_date);
        this.mName = itemView.findViewById(R.id.tv_store_name);
        this.mContent = itemView.findViewById(R.id.tv_store_content);
        this.tips = itemView.findViewById(R.id.tv_store_tip1);
        this.studentContent = itemView.findViewById(R.id.tv_store_student);
    }
}