package com.panda.course.ui.adapter

import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.App
import com.panda.course.config.base.Ktx
import com.panda.course.entity.CommonFunctions
import com.panda.course.entity.RowStringEntity
import com.panda.course.ext.dp2px
import com.panda.course.ext.gone
import com.panda.course.ext.px2dp
import com.panda.course.ext.visible
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper

class CommonFunctionsAdapter : BaseQuickAdapter<CommonFunctions, BaseViewHolder>(R.layout.item_common) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var onItemFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null

    fun setOnViewFocus(onItemFocus: OnViewFocus?) {
        this.onItemFocus = onItemFocus
    }

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()
    }

    override fun convert(holder: BaseViewHolder, item: CommonFunctions) {

        holder.setText(R.id.tv_item_title, item.title)
        holder.setText(R.id.tv_item_content, item.content)
        holder.setText(R.id.tv_item_pure_title, item.content)

        if (item.showType == CommonFunctions.VIEW_TEXT) {
            holder.getView<TextView>(R.id.tv_item_title).gone()
            holder.getView<TextView>(R.id.tv_item_content).gone()
            holder.getView<ImageView>(R.id.iv_item_arrow).gone()
            holder.getView<TextView>(R.id.tv_item_pure_title).visible()
        } else if (item.showType == CommonFunctions.VIEW_DEF) {
            holder.getView<TextView>(R.id.tv_item_title).visible()
            holder.getView<TextView>(R.id.tv_item_content).visible()
            holder.getView<ImageView>(R.id.iv_item_arrow).visible()
            holder.getView<TextView>(R.id.tv_item_pure_title).gone()
        }


        holder.getView<TextView>(R.id.tv_item_pure_title).setTextColor(
            if (item.isSeleced) ContextCompat.getColor(
                context, R.color.green
            ) else ContextCompat.getColor(context, R.color.white)
        )


        val view = holder.getView<RelativeLayout>(R.id.rl_common_item_view)


        view.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->

            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)

            onItemFocus?.onChangeFocus(hasFocus, getItemPosition(item), view)

            if (item.showType == CommonFunctions.VIEW_DEF) {
                holder.getView<TextView>(R.id.tv_item_content).setTextColor(
                    if (hasFocus) ContextCompat.getColor(context, R.color.white) else ContextCompat.getColor(
                        context, R.color.green
                    )
                )
                if (hasFocus) {
                    holder.getView<ImageView>(R.id.iv_item_arrow).gone()
                } else {
                    holder.getView<ImageView>(R.id.iv_item_arrow).visible()
                }
            } else {
                holder.getView<ImageView>(R.id.iv_item_arrow).gone()
            }


            if (hasFocus && item.isSeleced) {
                holder.getView<TextView>(R.id.tv_item_pure_title).setTextColor(
                    ContextCompat.getColor(context, R.color.white)
                )
            } else {
                holder.getView<TextView>(R.id.tv_item_pure_title).setTextColor(
                    if (item.isSeleced) ContextCompat.getColor(
                        context, R.color.green
                    ) else ContextCompat.getColor(context, R.color.white)
                )
            }


            if (hasFocus) {
                view.clearAnimation()
                view.startAnimation(scaleAnimation)
            } else {
                view.clearAnimation()
            }
        }
    }


}