package com.panda.course.ui.adapter;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.Interpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.leanback.widget.FocusHighlight;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.request.RequestOptions;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseSectionQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.noober.background.drawable.DrawableCreator;
import com.panda.course.R;
import com.panda.course.callback.OnViewFocus;
import com.panda.course.config.ConstantMMVK;
import com.panda.course.config.base.Ktx;
import com.panda.course.entity.CourseDetailInfo;
import com.panda.course.entity.DateSection;
import com.panda.course.ext.DensityExtKt;
import com.panda.course.util.GlideUtil;
import com.panda.course.util.RoundedCornersTransform;
import com.panda.course.widget.NineOverShootInterPolator;
import com.panda.course.widget.focus.MyFocusHighlightHelper;
import com.tencent.liteav.demo.superplayer.entity.KnowledgePointsListEntity;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Method;
import java.util.List;

import cn.jiazhengye.panda_home.utils.DateUtil;

import static com.tencent.qcloud.tuicore.util.ScreenUtil.dip2px;

public class CommonKonwledgeAdapter extends BaseQuickAdapter<KnowledgePointsListEntity, BaseViewHolder> {

    private MyFocusHighlightHelper.BrowseItemFocusHighlight browseItemFocusHighlight;

    private OnViewFocus mOnViewFocus;

    public void setOnViewFocus(OnViewFocus mOnViewFocus) {
        this.mOnViewFocus = mOnViewFocus;
    }

    private final Animation scaleAnimation;
    private RecyclerView recyclerView = null;

    private String mCode = "";

    private Drawable select;
    private Drawable unselect;

    public CommonKonwledgeAdapter() {
        super(R.layout.item_common_konwledge);
        if (browseItemFocusHighlight == null) {
            browseItemFocusHighlight = new MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL,
                    false);
        }
        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row);
        scaleAnimation.setInterpolator(new NineOverShootInterPolator());

        select = new DrawableCreator.Builder()
                .setCornersRadius(dip2px(10), dip2px(10), dip2px(0), dip2px(0))
                .setSolidColor(Color.parseColor("#80000000"))
                .build();
        unselect = new DrawableCreator.Builder()
                .setCornersRadius(dip2px(10), dip2px(10), dip2px(0), dip2px(0))
                .setSolidColor(Color.parseColor("#ffffff"))
                .build();
    }

    @Override
    protected void convert(@NotNull BaseViewHolder holder, KnowledgePointsListEntity info) {

        TextView textView = holder.getView(R.id.tv_item_history_title);
        TextView tvDate = holder.getView(R.id.tv_item_history_date);

        Glide.with(getContext())
                .asBitmap()
                .placeholder(R.drawable.icon_placeholder)
                .error(R.drawable.icon_error)
                .apply(new RequestOptions().transform(new CenterCrop(),
                        new RoundedCornersTransform(getContext(), 20f, true, false, true, false)))
                .load(info.getImg_url())
                .into((ImageView) holder.getView(R.id.item_iv_course_cover));


        tvDate.setText(info.getDot_time().substring(0, info.getEnd_dot_time().lastIndexOf(":")) + " — " +
                info.getEnd_dot_time().substring(0, info.getEnd_dot_time().lastIndexOf(":")));

        textView.setText(info.getTitle());


        LinearLayout relativeLayout = holder.getView(R.id.rl_common_item_view);
        relativeLayout.setOnFocusChangeListener((v, hasFocus) -> {
            browseItemFocusHighlight.onItemFocused(v, hasFocus);

//            textView.setTextColor(hasFocus ? Color.parseColor("#25333D") : Color.parseColor("#ffffff"));

            if (mOnViewFocus != null) {
                mOnViewFocus.onChangeFocus(hasFocus, getItemPosition(info), v);
            }

            if (hasFocus) {
                relativeLayout.clearAnimation();
                relativeLayout.startAnimation(scaleAnimation);
            } else {
                relativeLayout.clearAnimation();
            }

            if (hasFocus) {
                if (recyclerView != null) {
                    int[] amount = getScrollAmount(recyclerView, v);//计算需要滑动的距离
                    //滑动到指定距离
                    scrollToAmount(recyclerView, amount[0], amount[1]);
                }
            }
        });

        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(DensityExtKt.dp2px(400), DensityExtKt.dp2px(115));
        if (getItemPosition(info) == getData().size() - 1) {
            layoutParams.setMargins(DensityExtKt.dp2px(16), DensityExtKt.dp2px(16), DensityExtKt.dp2px(16), DensityExtKt.dp2px(16));
        } else {
            layoutParams.setMargins(DensityExtKt.dp2px(16), DensityExtKt.dp2px(16), DensityExtKt.dp2px(0), DensityExtKt.dp2px(16));
        }
        relativeLayout.setLayoutParams(layoutParams);
    }


    /**
     * 播放的那个课
     *
     * @param mCode
     */
    public void setPlayCode(String mCode) {
        this.mCode = mCode;
    }

    public void setRecycler(RecyclerView recyclerView) {
        this.recyclerView = recyclerView;
    }

    //根据坐标滑动到指定距离
    private void scrollToAmount(RecyclerView recyclerView, int dx, int dy) {
        //如果没有滑动速度等需求，可以直接调用这个方法，使用默认的速度
        recyclerView.smoothScrollBy(dx, dy);
    }

    /**
     * 计算需要滑动的距离,使焦点在滑动中始终居中
     *
     * @param recyclerView
     * @param view
     */
    private int[] getScrollAmount(RecyclerView recyclerView, View view) {
        int[] out = new int[2];
        final int parentLeft = recyclerView.getPaddingLeft();
        final int parentTop = recyclerView.getPaddingTop();
        final int parentRight = recyclerView.getWidth() - recyclerView.getPaddingRight();
        final int childLeft = view.getLeft() + 0 - view.getScrollX();
        final int childTop = view.getTop() + 0 - view.getScrollY();

        final int dx = childLeft - parentLeft - ((parentRight - view.getWidth()) / 2);//item左边距减去Recyclerview不在屏幕内的部分，加当前Recyclerview一半的宽度就是居中

        final int dy = childTop - parentTop - (parentTop - view.getHeight()) / 2;//同上
        out[0] = dx;
        out[1] = dy;
        return out;

    }
}
