package com.panda.course.entity;

import com.orient.tea.barragephoto.model.DataSource;

/**
 * 弹幕数据
 * <p>
 * Created by wa<PERSON><PERSON><PERSON> on 2019/3/22.
 */

@SuppressWarnings("ALL")
public class BarrageData implements DataSource {

    private String content;
    private String iconUrl;
    private int type;
    private int pos;

    public BarrageData(String content, String iconUrl, int type, int pos) {
        this.content = content;
        this.iconUrl = iconUrl;
        this.type = type;
        this.pos = pos;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public int getPos() {
        return pos;
    }

    public void setPos(int pos) {
        this.pos = pos;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    public int getType() {
        return type;
    }
}
