package com.panda.course.ui.activity

import android.os.Bundle
import android.os.Handler
import android.view.KeyEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.panda.course.R
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.data.CommonAnimationUtil
import com.panda.course.databinding.ActivityLiveListBinding
import com.panda.course.entity.LiveCourseEntity
import com.panda.course.ext.*
import com.panda.course.ui.activity.dialog.QrSkillActivity
import com.panda.course.ui.activity.dialog.UserOutDialogActivity
import com.panda.course.ui.adapter.LiveListAdapter
import com.panda.course.ui.adapter.RowSkillAdapter
import com.panda.course.ui.presenter.CoursePresenter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.ui.viewmodel.LiveModel
import com.panda.course.util.GlideUtil
import com.panda.course.util.SpaceItemDecoration

class LiveListActivity : BaseDbActivity<LiveModel, ActivityLiveListBinding>() {

    private var mRowAdapter = RowSkillAdapter()

    private var mAdapter = LiveListAdapter()

    private var mRowView: View? = null

    private var page = 1

    private var mRowPosition = 0

    /**
     * 默认分类
     */
    private val focus_row = 1

    /**
     * 剩下的直播课
     */
    private val focus_live = 2


    /**
     * 记录当前的焦点在哪里
     */
    private var currentFocusType: Int = focus_row


    private var course_status = "1"

    override fun initView(savedInstanceState: Bundle?) {

        val mStrList = ArrayList<String>()
        mStrList.add("开课中")
        mStrList.add("待开课")
        mRowAdapter.setList(mStrList)

        "${mRowAdapter.data.size}".logE()

        mDataBind.rowRecycler.layoutManager = LinearLayoutManager(this)

        mDataBind.rowRecycler.adapter = mRowAdapter

        mAdapter.setEmptyView(getEmptyView(tips = "暂无直播", R.color.white))

        mDataBind.recycler.adapter = mAdapter

        mDataBind.recycler.layoutManager = LinearLayoutManager(this)

        mDataBind.recycler.addItemDecoration(SpaceItemDecoration(1, px2dp(60f), true))

        requestOne()
    }


    override fun onLoadRetry() {
        mViewModel.getLiveList(course_status)
    }

    override fun initObserver() {
        mRowAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                if (0 == position) {
                    appUMEvent(UMConstant.AI_LEAN_LIVE)
                } else {
                    appUMEvent(UMConstant.AI_LEAN_NOT_LIVE)
                }
                mRowView = view
                page = 1
                course_status = if (position == 0) {
                    "1"
                } else {
                    "2"
                }
                onLoadRetry()
            } else {
                mRowView?.background = ContextCompat.getDrawable(this@LiveListActivity, R.drawable.base_border_unselecter)
            }
            mRowPosition = position
            currentFocusType = focus_row
        }


        mAdapter.apply {
            setOnViewFocus { hasFocus, position, view ->
                if (hasFocus && currentFocusType != focus_live) {
                    mRowView?.background = ContextCompat.getDrawable(this@LiveListActivity, R.drawable.base_border_selecter)
                    currentFocusType = focus_live
                }
            }
            setOnItemClickListener { adapter, view, position ->
                toStartActivity(LiveClassHourActivity::class.java, Bundle().apply {
                    putString("code", mAdapter.data[position].code)
                })
            }
        }
    }

    override fun onRequestSuccess() {
        super.onRequestSuccess()
        mViewModel.liveListEntity.observe(this, Observer {
            mAdapter.setList(it.list)
//            Handler().postDelayed({
//                if (mAdapter.data.size > 0) {
//                    val mView = mAdapter.getViewByPosition(0, R.id.card_item_view_class_hours)
//                    mView?.requestFocus()
//                }
//            }, 100)
        })
    }

    fun requestOne() {
        Handler().postDelayed({
            if (mRowAdapter.data.size > 0) {
                val mView = mRowAdapter.getViewByPosition(mRowPosition, R.id.row_card_view)
                mView?.requestFocus()
            }
        }, 100)
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo() // 宣传片

        if (onMyKeyDown(keyCode, event)) { // 加一层判断，实现android 9 以及其他的情况
            return true
        }

        when (keyCode) {

            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (currentFocusType == focus_row) {
                    mRowView?.background = ContextCompat.getDrawable(this@LiveListActivity, R.drawable.base_border)
                }
                requestOne()
                return true
            }

            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (mAdapter.data.size <= 0) {
                    return true
                }
            }
        }

        return super.onKeyDown(keyCode, event)
    }

}