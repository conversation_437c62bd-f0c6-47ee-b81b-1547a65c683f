package com.panda.course.ui.activity

import android.os.Bundle
import android.text.TextUtils

import androidx.lifecycle.Observer

import android.view.KeyEvent

import com.droidlogic.app.OutputModeManager
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivitySettingsBinding
import com.panda.course.entity.SettingInfo
import com.panda.course.ext.*
import com.panda.course.ui.activity.dialog.CheckNetDialogActivity
import com.panda.course.ui.activity.dialog.UpdateDialogActivity
import com.panda.course.ui.activity.wifi.NetDetailsActivity
import com.panda.course.ui.adapter.SettingsAdapter
import com.panda.course.ui.viewmodel.SettingModel
import com.panda.course.util.Constants
import com.panda.course.util.MMKVHelper
import com.panda.course.util.MMKVHelper.decodeString
import com.panda.course.util.NetworkUtil
import com.panda.course.util.UploadFileHelper
import com.tencent.qcloud.tuicore.util.FileUtil
import java.io.File


class SettingsActivity : BaseDbActivity<SettingModel, ActivitySettingsBinding>() {

    private var mAdapter = SettingsAdapter()

    private var mOutputModeManager: OutputModeManager? = null


    override fun initView(savedInstanceState: Bundle?) {
        if (getCurrentAndroid9()) { //只有新版本的盒子才会去做这个处理
            mOutputModeManager = OutputModeManager(this)
        }

        initRecyclerView()
//        mViewModel.getDeviceExpireDate()
    }

    override fun initObserver() {
        if (eventViewModel.deviceExpireDate.value != null) {
            eventViewModel.deviceExpireDate.value?.let {
                if (it == null) {
                    return@let
                }
                if (TextUtils.isEmpty(it.expire_start_date) && TextUtils.isEmpty(it.expire_end_date)) {
                    mDataBind.tvSettingInfo.text = getString(R.string.settings_current, "${getAppVersion(this)}\n设备不存在", "", "")

                } else {
                    mDataBind.tvSettingInfo.text = getString(R.string.settings_current, "${getAppVersion(this)}", "${if (!TextUtils.isEmpty(getDeviceId())) "      设备ID：${getDeviceId()}" else ""}", "\n服务日期至：${it?.expire_end_date}")
                }
            }
        } else {
            mDataBind.tvSettingInfo.text = getString(R.string.settings_current, "${getAppVersion(this)}", "：${if (!TextUtils.isEmpty(getDeviceId())) "      设备ID：${getDeviceId()}" else ""}", "")
        }


        mViewModel.qiniuInfo.observe(this, Observer {
            //七牛上传
            if (it != null) {
                UploadFileHelper.getInstance().uploadFile(it.token, UploadFileHelper.STOREPATH, object : UploadFileHelper.UploadFileListener {

                    override fun uploadSuccess(arr: java.util.ArrayList<String>?, uploadRequestCode: Int) {
                        getString(R.string.sys_tips_commit).toast()
                        FileUtil.deleteFile(Constants.LOG_PATH)
                    }

                    override fun uploadFailed(message: String?, requestCode: Int) {
                        getString(R.string.sys_tips_commit).toast()
                    }
                })
            }
        })
    }


    private fun getDeviceId(): String? {
        if (!TextUtils.isEmpty(decodeString(ConstantMMVK.DEVICE_ID))) {
            return decodeString(ConstantMMVK.DEVICE_ID)
        } else {
            if (eventViewModel.appUserInfo.value != null) {
                if (!TextUtils.isEmpty(eventViewModel.appUserInfo.value!!.device_id)) {
                    return eventViewModel.appUserInfo.value!!.device_id
                }
            }
        }
        return null
    }

    private fun initRecyclerView() {

        "${NetworkUtil.getNetworkName(NetworkUtil.getTransportType())}".logE()

        mDataBind.recycler.setColumnNumbers(4)
        mDataBind.recycler.verticalSpacing = dp2px(20f)
        mDataBind.recycler.horizontalSpacing = dp2px(20f)

        mAdapter.setOnItemClickListener { adapter, view, position ->
            clickItem(mAdapter.data[position])
        }

        mDataBind.recycler.adapter = mAdapter

        mAdapter.setList(getListData())

        "升级新版本啊 ${eventViewModel.appOTAUpdate.value}".logE()
    }


    private fun clickItem(item: SettingInfo) {
        when (item.id) {
            0 -> {//无线网络
                goSystemWIFISetting(this)
            }

            1 -> {//有线网络
                if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.LINE_NET_NAME) {
                    val bundle = Bundle()
                    bundle.putString("wifi_type", "2")
                    toStartActivity(NetDetailsActivity::class.java, bundle)
                } else {
                    getString(R.string.sys_net_x_no).toast()
                }
            }

            2 -> {//输出分辨率
                toStartActivity(HdmiActivity::class.java)
            }

            3 -> {//画面调整
                toStartActivity(ScreenAdjustmentActivity::class.java)
            }

            4 -> {//网速检测
                if (!NetworkUtil.isAvailable(this)) {
                    getString(R.string.sys_net_no).toast()
                    return
                }
                toStartActivity(CheckNetDialogActivity::class.java)
            }

            5 -> {//蓝牙设置
                goSystemTvBoxSetting(this)
            }

            6 -> {//app升级
//                toStartActivity(OTAUpdateActivity::class.java)
                if (eventViewModel.appUpdate.value == null) {
                    getString(R.string.sys_app_version).toast()
                    return
                }
                toStartActivity(UpdateDialogActivity::class.java)
            }

            7 -> {//系统信息
                toStartActivity(SystemInfoActivity::class.java)
            }

            8 -> {//盒子遥控器
                toStartActivity(PhoneRemoteActivity::class.java)
            }

            9 -> {//日志上报
                mViewModel.getQiNiuToken()
            }
        }
    }

    private fun getListData(): ArrayList<SettingInfo> {
        val arrInfo = ArrayList<SettingInfo>()

        if (getCurrentAndroid9()) {
            arrInfo.add(
                SettingInfo(
                    0, getString(R.string.sys_net_w), getString(R.string.sys_net_w_sub), if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.WIFI_NET_NAME) R.drawable.icon_setting_wifi_success else R.drawable.icon_setting_wifi, 0,
                    if (!NetworkUtil.isAvailable(this)) getString(R.string.sys_unconnect) else if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.WIFI_NET_NAME) getString(R.string.sys_connect) else getString(R.string.sys_unconnect), ""
                )
            )//网络连接

            arrInfo.add(
                SettingInfo(
                    1, getString(R.string.sys_net_x), getString(R.string.sys_net_x_sub), if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.LINE_NET_NAME) R.drawable.icon_setting_line_success else R.drawable.icon_setting_line, 0,
                    if (!NetworkUtil.isAvailable(this)) getString(R.string.sys_unconnect) else if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.LINE_NET_NAME) getString(R.string.sys_connect) else getString(R.string.sys_unconnect), ""
                )
            )
            //有线连接

            arrInfo.add(SettingInfo(2, getString(R.string.sys_screen), getString(R.string.sys_screen_sub), R.drawable.icon_setting_hdmi_, 0, updateHdmi(), ""))//输出分辨率
            arrInfo.add(SettingInfo(3, getString(R.string.sys_hdmi), getString(R.string.sys_hdmi_sub), R.drawable.icon_setting_hdmi_zoom, 0, "", ""))//画面调整
            arrInfo.add(SettingInfo(4, getString(R.string.sys_net_check), getString(R.string.sys_net_check_sub), R.drawable.icon_setting_check_net, 0, "", ""))//网速检测
            arrInfo.add(
                SettingInfo(
                    6,
                    getString(R.string.sys_app_version_update),
                    if (eventViewModel.appUpdate.value == null) getString(R.string.sys_app_version) else getString(R.string.sys_app_new_version),
                    R.drawable.icon_setting_ota_success,
                    0,
                    if (eventViewModel.appUpdate.value == null) "V ${getAppVersion(this)}" else "V ${eventViewModel.appUpdate.value?.version}",
                    if (eventViewModel.appUpdate.value == null) "" else getString(R.string.sys_app_update)
                )
            )
            //系统升级
            arrInfo.add(SettingInfo(5, getString(R.string.sys_bluetooth), getString(R.string.sys_bluetooth_sub), R.drawable.icon_setting_blue_success, 0, "开", ""))//蓝牙设置
            arrInfo.add(SettingInfo(7, getString(R.string.sys_info), getString(R.string.sys_info_sub), R.drawable.icon_setting_info, 0, "", ""))//系统信息
        } else {

            arrInfo.add(
                SettingInfo(
                    1, getString(R.string.sys_net_x), getString(R.string.sys_net_x_sub), if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.LINE_NET_NAME) R.drawable.icon_setting_line_success else R.drawable.icon_setting_line, 0,
                    if (!NetworkUtil.isAvailable(this)) getString(R.string.sys_unconnect) else if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.LINE_NET_NAME) getString(R.string.sys_connect) else getString(R.string.sys_unconnect), ""
                )
            )

            //有线连接

            arrInfo.add(
                SettingInfo(
                    6,
                    getString(R.string.sys_app_version_update),
                    if (eventViewModel.appUpdate.value == null) getString(R.string.sys_app_version) else getString(R.string.sys_app_new_version),
                    R.drawable.icon_setting_ota_success,
                    0,
                    if (eventViewModel.appUpdate.value == null) "V ${getAppVersion(this)}" else "V ${eventViewModel.appUpdate.value?.version}",
                    if (eventViewModel.appUpdate.value == null) "" else getString(R.string.sys_app_update)
                )
            )
            //系统升级

//            arrInfo.add(SettingInfo(5, getString(R.string.sys_bluetooth), getString(R.string.sys_bluetooth_sub), R.drawable.icon_setting_blue_success, 0, "开", ""))//蓝牙设置
            arrInfo.add(SettingInfo(4, getString(R.string.sys_net_check), getString(R.string.sys_net_check_sub), R.drawable.icon_setting_check_net, 0, "", ""))//网速检测
        }

        arrInfo.add(SettingInfo(8, getString(R.string.sys_phone_remote), getString(R.string.sys_phone_remote), R.drawable.icon_setting_check_remote, 0, "", ""))//手机遥控器
        arrInfo.add(SettingInfo(9, getString(R.string.sys_bug_report), getString(R.string.sys_bug_report_sub), R.drawable.icon_setting_check_upload, 0, "", ""))//异常问题上报
        return arrInfo
    }

    private fun updateAdapter() {
        if (mAdapter != null && mAdapter.data.size > 0) {

            if (getCurrentAndroid9()) {//因为在就版本的盒子上，没有无限网络的处理
                mAdapter.data[0].statusName = if (!NetworkUtil.isAvailable(this)) getString(R.string.sys_unconnect) else if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.WIFI_NET_NAME) getString(R.string.sys_connect) else getString(R.string.sys_unconnect)
                mAdapter.data[0].logo = if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.WIFI_NET_NAME) R.drawable.icon_setting_wifi_success else R.drawable.icon_setting_wifi

                mAdapter.data[1].statusName = if (!NetworkUtil.isAvailable(this)) getString(R.string.sys_unconnect) else if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.LINE_NET_NAME) getString(R.string.sys_connect) else getString(R.string.sys_unconnect)
                mAdapter.data[1].logo = if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.LINE_NET_NAME) R.drawable.icon_setting_line_success else R.drawable.icon_setting_line
            } else {
                mAdapter.data[0].statusName = if (!NetworkUtil.isAvailable(this)) getString(R.string.sys_unconnect) else if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.LINE_NET_NAME) getString(R.string.sys_connect) else getString(R.string.sys_unconnect)
                mAdapter.data[0].logo = if (NetworkUtil.getNetworkName(NetworkUtil.getTransportType()) == NetworkUtil.LINE_NET_NAME) R.drawable.icon_setting_line_success else R.drawable.icon_setting_line
            }

            mAdapter.notifyItemChanged(0)
            mAdapter.notifyItemChanged(1)

            if (getCurrentAndroid9()) {
                mAdapter.data[2].statusName = updateHdmi()
                mAdapter.notifyItemChanged(2)
            }
        }
    }

    private fun updateHdmi(): String {
        if (getCurrentAndroid9()) {
            val hdmi = MMKVHelper.decodeString(ConstantMMVK.HDMI_SCREEN)
            if (!TextUtils.isEmpty(hdmi)) {
                if (hdmi != mOutputModeManager?.highestMatchResolution) {
                    return getString(R.string.sys_hdmi_crurren, "$hdmi")
                }
            }
            return getString(R.string.sys_hdmi_auto)
        }
        return getString(R.string.sys_hdmi_auto)
    }

    override fun onResume() {
        super.onResume()
        updateAdapter()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

}