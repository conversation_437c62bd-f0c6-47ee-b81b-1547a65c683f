package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class InteractiveListEntity(
    var total: Int? = null,
    var page: Int? = null,
    var size: Int? = null,
    var list: List<InteractiveListEntityEntity> = emptyList(),
) : Parcelable


@Parcelize
data class InteractiveListEntityEntity(
    var store_name: String? = null,
    var service_personnel_name: String? = null,
    var avatar: String? = null,
    var course_title: String? = null,
    var txt_content: String? = null,
    var video_url: String? = null,
    var video_cover_url: String? = null,
    var create_time: String? = null,
    var tv_cat_name: String? = null,
    var picture_url: List<String>? = null,
    var teacher_comment: List<InteractiveTeacherListEntity>? = null,
) : Parcelable

@Parcelize
data class InteractiveTeacherListEntity(
    var p_id: String? = null,
    var create_time: String? = null,
    var txt_content: String? = null,
) : Parcelable