package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class ExcellentRankViewModel : BaseViewModel() {

    // 排行榜
    var excellentRankEntity = MutableLiveData<ExcellentRankEntity>()


    /**
     * 获取全部的技能测评
     */
    fun geRankAll(type: String) {
        rxHttpRequest {
            onRequest = {
                excellentRankEntity.value = RxHttp.get(NetUrl.EXCELLENT_RANK)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("type", type)
                    .toResponse<ExcellentRankEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.EXCELLENT_RANK
        }
    }


}