package com.panda.course.data;

import android.app.Activity;
import android.text.TextUtils;

import com.panda.course.config.ConstantMMVK;
import com.panda.course.entity.CacheVideoDotInfo;
import com.panda.course.entity.CacheVideoDotInfoNew;
import com.panda.course.entity.CacheVideoDotList;
import com.panda.course.entity.EffectMaterial;
import com.panda.course.entity.Question;
import com.panda.course.entity.VideoDotEntity;
import com.panda.course.entity.VideoDotResource;
import com.panda.course.entity.VideoDotResourceList;
import com.panda.course.util.MMKVHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

public class PlayerDotHelper {

    /**
     * 从本地获取
     */
    public static List<VideoDotEntity> handlerDot(Activity activity, String course_code) {
        if (activity.isFinishing() || TextUtils.isEmpty(course_code)) {
            return null;
        }
        //新建一个来存储打点信息的对象
        List<VideoDotEntity> dot_list = new ArrayList<>();

        //获取存储在本地的数据，这个需要重新组装成新的
        CacheVideoDotList videoDotList = MMKVHelper.INSTANCE.decodeParcelable(ConstantMMVK.VIDEO_CACHE_DOT, CacheVideoDotList.class);
        //资源列表
        VideoDotResourceList resourceList = MMKVHelper.INSTANCE.decodeParcelable(ConstantMMVK.VIDEO_CACHE_DOT_RESOURCE, VideoDotResourceList.class);
        // 创建map存储资源，实现快速获取
        HashMap<String, VideoDotResource> resourceHashMap = new HashMap<>();
        if (resourceList != null) {
            for (VideoDotResource dotResource : resourceList.getList()) {
                resourceHashMap.put(dotResource.getId(), dotResource);
            }
        }
        // 循环拿出指定数据的内容
        if (videoDotList != null) {
            for (CacheVideoDotInfoNew infoNew : videoDotList.getList()) {
                if (TextUtils.equals(infoNew.getVideo_id(), course_code)) {
                    VideoDotResource resource = resourceHashMap.get(infoNew.getEffects_id());
                    if (resource != null) {
                        VideoDotEntity dot = new VideoDotEntity();
                        // 基础信息、秒数、触发点
                        dot.setSeconds(infoNew.getSeconds());
                        dot.setEffect_type(resource.getEffect_type());
                        dot.setEffect_time(infoNew.getCourse_id());

                        // 动态资源
                        EffectMaterial material = new EffectMaterial();
                        material.setBackground(resource.getEffect_material().getBackground());
                        material.setCountdown_color(resource.getEffect_material().getCountdown_color());
                        material.setBackground_voice(resource.getEffect_material().getBackground_voice());
                        material.setHuman(resource.getEffect_material().getHuman());
                        material.setCountdown_color(resource.getEffect_material().getCountdown_color());
                        material.setRight_act_bg_color(resource.getEffect_material().getRight_act_bg_color());
                        material.setRight_act_color(resource.getEffect_material().getRight_act_color());
                        material.setStat_count_bg_color(resource.getEffect_material().getStat_count_bg_color());
                        material.setStat_count_color(resource.getEffect_material().getStat_count_color());

                        // 答题相关的对象
                        material.setQuestion(resource.getEffect_material().getQuestion());
                        // 自助播报的
                        material.setTxt_list(resource.getEffect_material().getTxt_list());

                        dot.setEffect_material(material);
                        dot_list.add(dot);
                    }
                }
            }
        }
        return dot_list;
    }
}
