package com.panda.course.ui.adapter;

import static com.panda.course.ext.CommExtKt.getColorExt;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.leanback.widget.FocusHighlight;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.request.RequestOptions;
import com.chad.library.adapter.base.BaseSectionQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.panda.course.R;
import com.panda.course.callback.OnViewFocus;
import com.panda.course.config.ConstantMMVK;
import com.panda.course.config.base.Ktx;
import com.panda.course.entity.CourseDetail;
import com.panda.course.entity.CourseDetailInfo;
import com.panda.course.entity.DateSection;
import com.panda.course.ext.DensityExtKt;
import com.panda.course.ext.LogExtKt;
import com.panda.course.util.RoundedCornersTransform;
import com.panda.course.widget.NineOverShootInterPolator;
import com.panda.course.widget.focus.MyFocusHighlightHelper;
import com.umeng.commonsdk.debug.I;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Objects;

public class HistoryAdapter extends BaseSectionQuickAdapter<DateSection, BaseViewHolder> {

    private MyFocusHighlightHelper.BrowseItemFocusHighlight browseItemFocusHighlight;

    private OnViewFocus mOnViewFocus;

    public void setOnViewFocus(OnViewFocus mOnViewFocus) {
        this.mOnViewFocus = mOnViewFocus;
    }

    private final Animation scaleAnimation;


    public HistoryAdapter(int layoutResId, int sectionHeadResId, List<DateSection> data) {
        super(sectionHeadResId, data);
        setNormalLayout(layoutResId);
        if (browseItemFocusHighlight == null) {
            browseItemFocusHighlight = new MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL,
                    false);
        }
        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row);
        scaleAnimation.setInterpolator(new NineOverShootInterPolator());
    }

    @Override
    protected void convertHeader(@NotNull BaseViewHolder holder, @NotNull DateSection dateSection) {
        if (dateSection.getObject() instanceof String) {
            holder.setText(R.id.tv_item_history_title, (String) dateSection.getObject());
            FrameLayout qrCode = holder.getView(R.id.fl_item_history_title);
            TextView textView = holder.getView(R.id.tv_item_history_title);
            if ("暂无观看记录".equals((String) dateSection.getObject())) {
                qrCode.setVisibility(View.GONE);
                textView.setTextSize(20f);
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                layoutParams.topMargin = DensityExtKt.dp2px(20);
                layoutParams.leftMargin = DensityExtKt.dp2px(14);
                textView.setLayoutParams(layoutParams);
            } else if (ConstantMMVK.MY_VIDEO_TIPS.equals((String) dateSection.getObject())) {
                qrCode.setVisibility(View.VISIBLE);
                textView.setTextSize(20f);
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                layoutParams.topMargin = DensityExtKt.dp2px(50);
                layoutParams.leftMargin = DensityExtKt.dp2px(14);
                textView.setLayoutParams(layoutParams);
            } else if ("我的视频".equals((String) dateSection.getObject())) {
                qrCode.setVisibility(View.GONE);
                textView.setTextSize(28f);
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                layoutParams.topMargin = DensityExtKt.dp2px(20);
                layoutParams.leftMargin = DensityExtKt.dp2px(14);
                textView.setLayoutParams(layoutParams);
            } else {
                qrCode.setVisibility(View.GONE);
                textView.setTextSize(28f);
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                layoutParams.topMargin = DensityExtKt.dp2px(20);
                layoutParams.leftMargin = DensityExtKt.dp2px(14);
                textView.setLayoutParams(layoutParams);
            }
        }
    }

    @Override
    protected void convert(@NotNull BaseViewHolder holder, DateSection dateSection) {
        CourseDetailInfo info = (CourseDetailInfo) dateSection.getObject();

        ImageView imageView = holder.getView(R.id.item_iv_course_cover);

        ProgressBar progressBar = holder.getView(R.id.progressBar);
        TextView textView = holder.getView(R.id.tv_item_history_title);
        TextView sub = holder.getView(R.id.tv_item_history_sub);

        LinearLayout ll_ai_status_layout = holder.getView(R.id.ll_ai_status_layout);
        TextView aiStatus = holder.getView(R.id.tv_ai_status);
        ImageView ivAiStatus = holder.getView(R.id.iv_ai_status);

        RelativeLayout imgLayout = holder.getView(R.id.rl_item_def);//图片

        LinearLayout aiDef = holder.getView(R.id.ll_item_ai_def);//Ai课默认第一个

        ImageView ivPlay = holder.getView(R.id.iv_item_play);


        switch (info.getType()) {
            case 0:
                imgLayout.setVisibility(View.VISIBLE);
                aiDef.setVisibility(View.GONE);
                ll_ai_status_layout.setVisibility(View.GONE);
                if (!TextUtils.isEmpty(info.getVideo_time())) {
                    progressBar.setMax((int) Long.parseLong(info.getVideo_time()));
                } else {
                    progressBar.setMax(100);
                }

                sub.setVisibility(View.VISIBLE);
                textView.setVisibility(View.VISIBLE);
                progressBar.setVisibility(View.VISIBLE);

                ViewGroup.LayoutParams params = imageView.getLayoutParams();
                params.height = DensityExtKt.dp2px(120);
                imageView.setLayoutParams(params);

                if (!TextUtils.isEmpty(info.getVideo_watch_min())) {
                    textView.setMaxLines(1);
                    if ("0".equals(info.getVideo_watch_min())) {
                        sub.setText("观看小于1分钟");
                        progressBar.setProgress(5);
                    } else {
                        sub.setText("观看至第" + info.getVideo_watch_min() + "分钟");
                        progressBar.setProgress((int) Long.parseLong(info.getVideo_watch_time()));
                    }
                } else {
                    textView.setMaxLines(2);
                }

                Glide.with(getContext())
                        .asBitmap()
                        .placeholder(R.drawable.icon_placeholder)
                        .error(R.drawable.icon_error)
                        .apply(new RequestOptions().transform(new CenterCrop(),
                                new RoundedCornersTransform(getContext(), 10f, true, true, false, false)))
                        .load(info.getCourse_cover_url())
                        .into(imageView);
                break;
            case ConstantMMVK.AD_VIDEO:
                imgLayout.setVisibility(View.VISIBLE);
                progressBar.setVisibility(View.GONE);
                aiDef.setVisibility(View.GONE);
                sub.setVisibility(View.GONE);
                ll_ai_status_layout.setVisibility(View.GONE);

                ViewGroup.LayoutParams paramsAd = imageView.getLayoutParams();
                paramsAd.height = DensityExtKt.dp2px(120);
                imageView.setLayoutParams(paramsAd);

                if (!TextUtils.isEmpty(info.getCourse_cover_url())) {
                    Glide.with(getContext())
                            .asBitmap()
                            .placeholder(R.drawable.icon_placeholder)
                            .error(R.drawable.icon_error)
                            .apply(new RequestOptions().transform(new CenterCrop(),
                                    new RoundedCornersTransform(getContext(), 10f, true, true, false, false)))
                            .load(info.getCourse_cover_url())
                            .into(imageView);
                }
                break;
            case ConstantMMVK.AI_LIVE:
                imgLayout.setVisibility(View.VISIBLE);
                sub.setVisibility(View.VISIBLE);
                ll_ai_status_layout.setVisibility(View.VISIBLE);
                aiDef.setVisibility(View.GONE);
                progressBar.setVisibility(View.GONE);

                imgLayout.setBackgroundResource(R.drawable.base_app_no_focus);

                ViewGroup.LayoutParams paramsAIlIVE = imageView.getLayoutParams();
                paramsAIlIVE.height = DensityExtKt.dp2px(120);
                imageView.setLayoutParams(paramsAIlIVE);

                Glide.with(getContext())
                        .asBitmap()
                        .placeholder(R.drawable.icon_placeholder)
                        .error(R.drawable.icon_error)
                        .load(info.getCourse_cover_url())
                        .into(imageView);

                switch (Objects.requireNonNull(info.getStatus())) {
                    case "1":
                        aiStatus.setTextColor(Color.parseColor("#09BE89"));
                        ivAiStatus.setImageResource(R.drawable.icon_live_ing);
                        break;
                    case "2":
                        aiStatus.setTextColor(getColorExt(R.color.blue));
                        ivAiStatus.setImageResource(R.drawable.icon_live_no_ing);
                        break;
                    default:
                        aiStatus.setTextColor(Color.WHITE);
                        ivAiStatus.setImageResource(R.drawable.icon_live_no_live);
                        break;
                }
                aiStatus.setText(info.getVideo_watch_time());
                sub.setText(info.getTitle_sub());
                break;
            case ConstantMMVK.AI_LIVE_DEF:
                ll_ai_status_layout.setVisibility(View.GONE);
                imgLayout.setVisibility(View.GONE);
                aiDef.setVisibility(View.VISIBLE);
                break;
        }


        textView.setText(info.getTitle_main());

        ImageView aiLiveLogo = holder.getView(R.id.iv_item_ai_logo);

        LinearLayout layout = holder.getView(R.id.ll_item_layout);

        ViewGroup.LayoutParams params = layout.getLayoutParams();
        params.height = DensityExtKt.dp2px(58);
        layout.setLayoutParams(params);


        CardView cardView = holder.getView(R.id.card_item_view_history);
        holder.getView(R.id.card_item_view_history).setOnFocusChangeListener((v, hasFocus) -> {
            browseItemFocusHighlight.onItemFocused(v, hasFocus);

            if (info.getType() == ConstantMMVK.AI_LIVE) {
                imgLayout.setBackgroundResource(R.drawable.base_app_no_focus);
                ivPlay.setVisibility(View.GONE);
            } else if (info.getType() == ConstantMMVK.AI_LIVE_DEF) {
                aiDef.setBackgroundResource(hasFocus ? R.drawable.ai_live_def_bg : R.drawable.ai_live_def_no_bg);
                aiLiveLogo.setBackgroundResource(hasFocus ? R.drawable.icon_ai_select_video : R.drawable.icon_ai_video);
            } else {
                ivPlay.setVisibility(hasFocus ? View.VISIBLE : View.GONE);
                imgLayout.setBackgroundColor(Color.TRANSPARENT);
            }

            layout.setBackgroundResource(hasFocus ? R.color.white : R.color.black1);
            sub.setTextColor(hasFocus ? Color.parseColor("#25333D") : Color.parseColor("#ffffff"));
            textView.setTextColor(hasFocus ? Color.parseColor("#25333D") : Color.parseColor("#ffffff"));

            if (mOnViewFocus != null) {
                mOnViewFocus.onChangeFocus(hasFocus, getItemPosition(dateSection), v);
            }

            cardView.setCardElevation(hasFocus ? DensityExtKt.px2dp(20f) : DensityExtKt.px2dp(0f));

            cardView.setCardBackgroundColor(hasFocus ? ContextCompat.getColor(getContext(), R.color.black) : ContextCompat.getColor(getContext(), R.color.transparent));


            cardView.setRadius(hasFocus ? 16f : 0f);


            if (hasFocus) {
                cardView.clearAnimation();
                cardView.startAnimation(scaleAnimation);
            } else {
                cardView.clearAnimation();
            }
        });
    }
}
