/*
 * Copyright © 2018 <PERSON><PERSON><PERSON><PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.panda.course.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;

import com.panda.course.config.base.BaseDbActivity;
import com.panda.course.config.base.Ktx;
import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.LogExtKt;
import com.panda.course.service.CoreService;
import com.panda.course.ui.activity.CourseActivity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/6/9.
 */
public class ServerManager extends BroadcastReceiver {

    private String TAG = "ServerManager";

    private static final String ACTION = "com.panda.course.receiver.receiver";

    private static final String CMD_KEY = "CMD_KEY";
    private static final String MESSAGE_KEY = "MESSAGE_KEY";

    private static final int CMD_VALUE_START = 1;
    private static final int CMD_VALUE_ERROR = 2;
    private static final int CMD_VALUE_STOP = 4;

    /**
     * Notify serverStart.
     *
     * @param context context.
     */
    public static void onServerStart(Context context, String hostAddress) {
        sendBroadcast(context, CMD_VALUE_START, hostAddress);
    }

    /**
     * Notify serverStop.
     *
     * @param context context.
     */
    public static void onServerError(Context context, String error) {
        sendBroadcast(context, CMD_VALUE_ERROR, error);
    }

    /**
     * Notify serverStop.
     *
     * @param context context.
     */
    public static void onServerStop(Context context) {
        sendBroadcast(context, CMD_VALUE_STOP);
    }

    private static void sendBroadcast(Context context, int cmd) {
        sendBroadcast(context, cmd, null);
    }

    private static void sendBroadcast(Context context, int cmd, String message) {
        Intent broadcast = new Intent(ACTION);
        broadcast.putExtra(CMD_KEY, cmd);
        broadcast.putExtra(MESSAGE_KEY, message);
        context.sendBroadcast(broadcast);
    }

    private Intent mService;


    public ServerManager() {
        mService = new Intent(Ktx.app, CoreService.class);
    }

    /**
     * Register broadcast.
     */
    public void register() {
        IntentFilter filter = new IntentFilter(ACTION);
        Ktx.app.registerReceiver(this, filter);
    }

    /**
     * UnRegister broadcast.
     */
    public void unRegister() {
        Ktx.app.unregisterReceiver(this);
    }

    public void startServer() {
        if (mService == null) {
            mService = new Intent(Ktx.app, CoreService.class);
        }
        // 启动服务的地方
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Ktx.app.startForegroundService(mService);
        } else {
            Ktx.app.startService(mService);
        }
    }

    public void stopServer() {
        Ktx.app.stopService(mService);
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (ACTION.equals(action)) {
            int cmd = intent.getIntExtra(CMD_KEY, 0);
            switch (cmd) {
                case CMD_VALUE_START: {
                    String ip = intent.getStringExtra(MESSAGE_KEY);
//                    mActivity.onServerStart(ip);
                    LogExtKt.logE("启动服务" + ip, TAG);
                    break;
                }
                case CMD_VALUE_ERROR: {
                    String error = intent.getStringExtra(MESSAGE_KEY);
//                    mActivity.onServerError(error);
                    LogExtKt.logE("服务异常 " + error, TAG);
//                    startServer();
                    break;
                }
                case CMD_VALUE_STOP: {
                    LogExtKt.logE("服务停止", TAG);
                    startServer();
//                    mActivity.onServerStop();
                    break;
                }
            }
        }
    }
}