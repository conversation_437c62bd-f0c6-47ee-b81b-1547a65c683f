/*
 * Copyright © 2018 <PERSON><PERSON><PERSON><PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.panda.course.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.panda.course.R;
import com.panda.course.config.base.Ktx;
import com.panda.course.config.base.KtxKt;
import com.panda.course.ext.AppExtKt;
import com.panda.course.receiver.ServerManager;
import com.panda.course.server.controller.CheckConnectionController;
import com.panda.course.server.controller.PageController;
import com.panda.course.util.ToastUtil;
import com.yanzhenjie.andserver.AndServer;
import com.yanzhenjie.andserver.Server;
import com.yanzhenjie.andserver.website.AssetsWebsite;
import com.yanzhenjie.andserver.website.WebSite;

import java.net.InetAddress;
import java.util.concurrent.TimeUnit;


public class CoreService extends Service {

    private String CHANNEL_ID_STRING = "CoreService";

    private Server mServer;

    @Override
    public void onCreate() {
        try {

            //android 8后需要增加个通知栏
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
                NotificationChannel channel = null;
                channel = new NotificationChannel(CHANNEL_ID_STRING, getString(R.string.app_name), NotificationManager.IMPORTANCE_HIGH);
                notificationManager.createNotificationChannel(channel);
                Notification notification = new Notification.Builder(getApplicationContext(), CHANNEL_ID_STRING).build();
                startForeground(1, notification);
            }

            AssetManager assetManager = getAssets();
            WebSite webSite = new AssetsWebsite(assetManager, KtxKt.getAppContext().getDataDir().toString());

            mServer = AndServer.serverBuilder()
                    .port(8080)
                    .website(webSite)
                    .timeout(10, TimeUnit.SECONDS)
                    .registerHandler("/connection", new PageController())
                    .registerHandler("/checkconnection", new CheckConnectionController())
                    .listener(new Server.ServerListener() {
                        @Override
                        public void onStarted() {
//                        InetAddress address = NetUtils.getLocalIPAddress();
                            InetAddress address = AppExtKt.getLocalInetAddress();
                            if (address != null) {
                                ServerManager.onServerStart(CoreService.this, address.getHostAddress());
                            }
                        }

                        @Override
                        public void onStopped() {
                            ServerManager.onServerStop(CoreService.this);
                        }

                        @Override
                        public void onError(Exception e) {
                            e.printStackTrace();
                            ServerManager.onServerError(CoreService.this, e.getMessage());
                        }
                    })
                    .build();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        startServer();
        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        stopServer();
        super.onDestroy();
    }

    /**
     * Start server.
     */
    private void startServer() {
        mServer.startup();
    }

    /**
     * Stop server.
     */
    private void stopServer() {
        mServer.shutdown();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}