package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class NoticeListEntity(
    var total: Int? = null,
    var page: Int? = null,
    var size: Int? = null,
    var list: List<NoticeListEntityEntity> = emptyList(),
) : Parcelable


@Parcelize
data class NoticeListEntityEntity(
    var number: String? = null,
    var create_time: String? = null,
    var is_read: String? = null,
    var title: String? = null,
    var msg: String? = null,
    var course_code: String? = null,
) : Parcelable