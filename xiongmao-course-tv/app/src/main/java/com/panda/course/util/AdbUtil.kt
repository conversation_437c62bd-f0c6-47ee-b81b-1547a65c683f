package com.panda.course.util

import android.text.TextUtils
import android.util.Log
import java.io.IOException

class AdbUtil {

    val TAG = "AdbUtil"

    private fun runCmd(cmd: String): String? {
        Log.d(TAG, "runCmd: $cmd")
        if (TextUtils.isEmpty(cmd) || cmd.length < 11) {
            return ""
        }
        val cmdHead = cmd.substring(0, 9)
        return if ("adb shell" != cmdHead) {
            ""
        } else execRootCmd(cmd)
    }

    /**
     * 执行命令并且输出结果
     */
    fun execRootCmd(cmd: String): String? {
        var cmd = cmd
        var content = ""
        try {
            cmd = cmd.replace("adb shell", "")
            val process = Runtime.getRuntime().exec(cmd)
            Log.d(TAG, "process $process")
            content = process.toString()
        } catch (e: IOException) {
            Log.d(TAG, "exception " + e.toString())
            e.printStackTrace()
        }
        return content
    }

}