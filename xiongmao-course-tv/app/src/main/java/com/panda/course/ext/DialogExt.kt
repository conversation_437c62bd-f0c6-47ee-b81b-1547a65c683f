package com.panda.course.ext

import android.app.Dialog
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.*
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.afollestad.materialdialogs.MaterialDialog
import com.panda.course.R
import com.panda.course.entity.RedPaperConfigEntity
import com.panda.course.ui.activity.SettingsActivity
import com.panda.course.util.GlideUtil
import org.w3c.dom.Text


/**
 * 显示消息弹窗
 * @param message 显示对话框的内容 必填项
 * @param title 显示对话框的标题 默认 温馨提示
 * @param positiveButtonText 确定按钮文字 默认确定
 * @param positiveAction 点击确定按钮触发的方法 默认空方法
 * @param negativeButtonText 取消按钮文字 默认空 不为空时显示该按钮
 * @param negativeAction 点击取消按钮触发的方法 默认空方法
 *
 */
fun AppCompatActivity.showDialogMessage(
    message: String,
    title: String = "温馨提示",
    positiveButtonText: String = "确定",
    positiveAction: () -> Unit = {},
    negativeButtonText: String = "",
    negativeAction: () -> Unit = {}
) {
    if (!this.isFinishing) {
        MaterialDialog(this).cornerRadius(8f).cancelOnTouchOutside(false).show {
            title(text = title)
            message(text = message)
            positiveButton(text = positiveButtonText) {
                positiveAction.invoke()
            }
            if (negativeButtonText.isNotEmpty()) {
                negativeButton(text = negativeButtonText) {
                    negativeAction.invoke()
                }
            }
        }
    }
}

/**
 * @param message 显示对话框的内容 必填项
 * @param title 显示对话框的标题 默认 温馨提示
 * @param positiveButtonText 确定按钮文字 默认确定
 * @param positiveAction 点击确定按钮触发的方法 默认空方法
 * @param negativeButtonText 取消按钮文字 默认空 不为空时显示该按钮
 * @param negativeAction 点击取消按钮触发的方法 默认空方法
 */
fun Fragment.showDialogMessage(
    message: String,
    title: String = "温馨提示",
    positiveButtonText: String = "确定",
    positiveAction: () -> Unit = {},
    negativeButtonText: String = "",
    negativeAction: () -> Unit = {}
) {
    activity?.let {
        if (!it.isFinishing) {
            MaterialDialog(it).cancelOnTouchOutside(false).cornerRadius(8f).show {
                title(text = title)
                message(text = message)
                positiveButton(text = positiveButtonText) {
                    positiveAction.invoke()
                }
                if (negativeButtonText.isNotEmpty()) {
                    negativeButton(text = negativeButtonText) {
                        negativeAction.invoke()
                    }
                }
            }
        }
    }
}


/*****************************************loading框********************************************/
private var loadingDialog: Dialog? = null
private var loadingNetWorkDialog: Dialog? = null

/**
 * 打开等待框
 */
fun AppCompatActivity.showLoadingExt(message: String = getString(R.string.helper_loading_tip)) {
    loadingDialog = null
    if (!this.isFinishing) {
        if (loadingDialog == null) { //弹出loading时 把当前界面的输入法关闭
            this.hideOffKeyboard()
            loadingDialog = Dialog(this, R.style.loadingDialogTheme).apply {
                setCancelable(true)
                setCanceledOnTouchOutside(false)
                setContentView(LayoutInflater.from(this@showLoadingExt)
                    .inflate(R.layout.layout_loading_view, null)
                    .apply {
                        this.findViewById<TextView>(R.id.loading_tips).text = message
                    })
            }
            loadingDialog?.setOnDismissListener { //设置dialog关闭时 置空 不然会出现 一个隐藏bug 这里就不细说了
                dismissLoadingExt()
            }
        }
        loadingDialog?.show()
    }
}

/**
 * 打开等待框
 */
fun Fragment.showLoadingExt(message: String = getString(R.string.helper_loading_tip)) {
    loadingDialog = null
    activity?.let {
        if (!it.isFinishing) {
            if (loadingDialog == null) { //弹出loading时 把当前界面的输入法关闭
                it.hideOffKeyboard()
                loadingDialog = Dialog(requireActivity(), R.style.loadingDialogTheme).apply {
                    setCancelable(true)
                    setCanceledOnTouchOutside(false)
                    setContentView(
                        LayoutInflater.from(it).inflate(R.layout.layout_loading_view, null).apply {
                            this.findViewById<TextView>(R.id.loading_tips).text = message
                        })
                }
                loadingDialog?.setOnDismissListener { //设置dialog关闭时 置空 不然会出现 一个隐藏bug 这里就不细说了
                    dismissLoadingExt()
                }
            }
            loadingDialog?.show()
        }
    }
}


/**
 * 关闭等待框
 */
fun AppCompatActivity.dismissLoadingExt() {
    if (loadingDialog != null && loadingDialog!!.isShowing && !this.isFinishing) {
        loadingDialog?.dismiss()
        loadingDialog = null
    }
}

/**
 * 关闭等待框
 */
fun Fragment.dismissLoadingExt() {
    if (loadingDialog != null && loadingDialog!!.isShowing) {
        loadingDialog?.dismiss()
        loadingDialog = null
    }
}

/**
 * 关闭网络加载等待框
 */
fun AppCompatActivity.dismissLoadingNetWorkExt() {
    if (this == currentActivity) {
        if (loadingNetWorkDialog != null && loadingNetWorkDialog!!.isShowing && !this.isFinishing) {
            loadingNetWorkDialog?.dismiss()
            loadingNetWorkDialog = null
        }
    }
}


/**
 * 网络弹出
 */
fun AppCompatActivity.showNetDialog(listener: View.OnClickListener?) {
    loadingNetWorkDialog = null
    if (this == currentActivity) {
        if (!this.isFinishing && loadingNetWorkDialog == null) {
            "网络提示创建了".logE("NetDialog")
            val view = LayoutInflater.from(this).inflate(R.layout.layout_dialog_net, null)
            loadingNetWorkDialog = Dialog(this, R.style.dialogTransparent)
            loadingNetWorkDialog?.setContentView(view)
            val button = view.findViewById<Button>(R.id.but_dialog_connect)
            val cancel = view.findViewById<Button>(R.id.but_dialog_cancel)
            button.onFocusChangeListener = View.OnFocusChangeListener { _: View?, b: Boolean ->
                button.background = changeButtonColor(b)
            }
            cancel.onFocusChangeListener = View.OnFocusChangeListener { _: View?, b: Boolean ->
                cancel.background = changeButtonColor(b)
            }
            button.requestFocus()
            button.setOnClickListener { v: View? ->
                listener?.onClick(v)
            }
            cancel.setOnClickListener { v: View? ->
                goSystemWIFISetting(this)
            }

            cancel.setOnKeyListener { v, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_MENU) {
                    toStartActivity(SettingsActivity::class.java)
                    true
                }
                false
            }

            button.setOnKeyListener { v, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_MENU) {
                    toStartActivity(SettingsActivity::class.java)
                    true
                }
                false
            }
            if (loadingNetWorkDialog != null && !loadingNetWorkDialog!!.isShowing) {
                loadingNetWorkDialog?.show()
            }
        }
    }
}

/**
 * 显示dialog
 */
fun AppCompatActivity.showCustomMessageDialog(
    title: String,
    message: String,
    butText: String,
    listener: View.OnClickListener?
) {
    val view = LayoutInflater.from(this).inflate(R.layout.layout_dialog_custom_message, null)
    val dialog = Dialog(this, R.style.dialogTransparent)
//    dialog.setCancelable(false)
//    dialog.setCanceledOnTouchOutside(false)
    dialog.setContentView(view)
    dialog.show() //    setDialogWindowParams(dialog.window!!, 0.5f)
    dialog.findViewById<TextView>(R.id.iv_dialog_sub).text = message
    dialog.findViewById<TextView>(R.id.iv_dialog_title).text = title
    dialog.findViewById<Button>(R.id.but_dialog_cancel).text = butText
    dialog.findViewById<Button>(R.id.but_dialog_cancel).setOnClickListener {
        listener?.onClick(it)
        dialog.dismiss()
    }
    dialog.findViewById<Button>(R.id.but_dialog_cancel).setOnFocusChangeListener { v, hasFocus ->
        dialog.findViewById<Button>(R.id.but_dialog_cancel).background = changeButtonColor(hasFocus)
    }
}


/**
 * 显示红包的Dialog处理
 */
fun AppCompatActivity.showRedPaperConfig(
    data: RedPaperConfigEntity,
    listener: View.OnClickListener?,
    onDismissListener: DialogInterface.OnDismissListener?
) {
    val view = LayoutInflater.from(this).inflate(R.layout.layout_dialog_red_paper_message, null)
    val dialog = Dialog(this, R.style.dialogTransparent)
//    dialog.setCancelable(false)
//    dialog.setCanceledOnTouchOutside(false)
    dialog.setContentView(view)
    dialog.show()
    setDialogWindowParams(dialog.window!!, 0.5f)

    dialog.setOnDismissListener(onDismissListener)

    val spannableString = SpannableString(data.tips)
    for (i in data.keyword_list.indices) {
        if (!TextUtils.isEmpty(data.keyword_list[i].keyword)) {
            val key = data.keyword_list[i].keyword
            val color = data.keyword_list[i].color
            data.tips?.let {
                if (!TextUtils.isEmpty(key) && !TextUtils.isEmpty(color)) {
                    spannableString.setSpan(
                        ForegroundColorSpan(Color.parseColor(color)),
                        it.indexOf(key!!),
                        it.indexOf(key) + key.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }
        }
    }

    dialog.findViewById<TextView>(R.id.iv_dialog_red_paper_sub).text = spannableString
    dialog.findViewById<TextView>(R.id.iv_dialog_red_paper_title).text = data.title

    dialog.findViewById<Button>(R.id.but_dialog_red_paper_sure).setOnClickListener {
        listener?.onClick(it)
        dialog.dismiss()
    }
    dialog.findViewById<Button>(R.id.but_dialog_red_paper_cancel).setOnClickListener {
        dialog.dismiss()
    }

    dialog.findViewById<Button>(R.id.but_dialog_red_paper_cancel)
        .setOnFocusChangeListener { v, hasFocus ->
            dialog.findViewById<Button>(R.id.but_dialog_red_paper_cancel)
                .setTextColor(if (hasFocus) getColor(R.color.white) else getColor(R.color.black))

            dialog.findViewById<Button>(R.id.but_dialog_red_paper_cancel)
                .setBackgroundResource(if (hasFocus) R.drawable.base_border_selecter_round else R.drawable.base_border_unselecter_no_bg_stroke_round)
        }

    dialog.findViewById<Button>(R.id.but_dialog_red_paper_sure)
        .setOnFocusChangeListener { v, hasFocus ->
            dialog.findViewById<Button>(R.id.but_dialog_red_paper_sure)
                .setTextColor(if (hasFocus) getColor(R.color.white) else getColor(R.color.black))

            dialog.findViewById<Button>(R.id.but_dialog_red_paper_sure)
                .setBackgroundResource(if (hasFocus) R.drawable.base_border_selecter_round else R.drawable.base_border_unselecter_no_bg_stroke_round)


        }
}


/**
 * 显示dialog
 */
fun AppCompatActivity.showMessageDialog(message: String) {
    val view = LayoutInflater.from(this).inflate(R.layout.layout_dialog_message, null)
    val dialog = Dialog(this, R.style.dialogTransparent)
    dialog.setCancelable(false)
    dialog.setCanceledOnTouchOutside(false)
    dialog.setContentView(view)
    dialog.show() //    setDialogWindowParams(dialog.window!!, 0.5f)
    dialog.findViewById<TextView>(R.id.iv_dialog_store_name).text = message
    dialog.findViewById<Button>(R.id.but_dialog_cancel).setOnClickListener { dialog.dismiss() }
    dialog.findViewById<Button>(R.id.but_dialog_cancel).setOnFocusChangeListener { v, hasFocus ->
        dialog.findViewById<Button>(R.id.but_dialog_cancel).background = changeButtonColor(hasFocus)
    }
}


/**
 * 显示dialog 可设置返回
 */
fun AppCompatActivity.showMessageBackDialog(message: String, butTips: String) {
    val view = LayoutInflater.from(this).inflate(R.layout.layout_dialog_message, null)
    val dialog = Dialog(this, R.style.dialogTransparent)
    dialog.setCancelable(true)
    dialog.setCanceledOnTouchOutside(false)
    dialog.setContentView(view)
    dialog.show() //    setDialogWindowParams(dialog.window!!, 0.5f)
    dialog.findViewById<TextView>(R.id.iv_dialog_store_name).text = message
    dialog.findViewById<Button>(R.id.but_dialog_cancel).text = butTips
    dialog.findViewById<Button>(R.id.but_dialog_cancel).setOnFocusChangeListener { v, hasFocus ->
        dialog.findViewById<Button>(R.id.but_dialog_cancel).background = changeButtonColor(hasFocus)
    }
}


/**
 * 消息通知的接口
 */
fun AppCompatActivity.showNoticeDialog(imageUrl: String, listener: View.OnClickListener?): Dialog {
    val view = LayoutInflater.from(this).inflate(R.layout.activity_surprise, null)
    val loadingNetWorkDialog = Dialog(this, R.style.dialogTransparent)
    loadingNetWorkDialog.setContentView(view)

    val imageView = view.findViewById<ImageView>(R.id.iv_surprise)
    GlideUtil.loadPic(this, imageUrl, imageView)
    view.findViewById<Button>(R.id.tv_surprise_ok).setOnClickListener { v: View? ->
        listener?.onClick(v)
    }

    return loadingNetWorkDialog
}


/**
 * app升级的接口
 */
fun AppCompatActivity.showAppUpdateDialog(
    imageUrl: String,
    listener: View.OnClickListener?
): Dialog {
    val view = LayoutInflater.from(this).inflate(R.layout.activity_update_dialog, null)
    val dialog = Dialog(this, R.style.dialogTransparent)
    dialog.setContentView(view)



    return dialog
}


/**
 * 设置dialog的
 *
 * @param window
 * @param width
 * @return
 */
fun setDialogWindowParams(window: Window, width: Float) {
    window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
    val params = window.attributes //设置Dialog的Width
    params.width = (getScreenWidth() * width).toInt() //设置Dialog的Height
    params.height = WindowManager.LayoutParams.WRAP_CONTENT
    window.attributes = params
}


/**
 * 设置dialog的
 *
 * @param window
 * @param width
 * @return
 */
fun setDialogWindowParams(window: Window, width: Float, height: Float) {
    window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
    val params = window.attributes //设置Dialog的Width
    params.width = (getScreenWidth() * width).toInt() //设置Dialog的Height
    params.height = (getScreenHeight() * height).toInt()
    window.attributes = params
}