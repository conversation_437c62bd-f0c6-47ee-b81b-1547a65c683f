package com.panda.course.ui.activity.wifi

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import com.panda.course.R
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.databinding.ActivityWiFiConnectBinding
import com.panda.course.ext.eventViewModel
import com.panda.course.ext.toast

class WiFiConnectActivity : BaseDbActivity<BaseViewModel, ActivityWiFiConnectBinding>() {


    override fun initView(savedInstanceState: Bundle?) {
        intent?.extras?.apply {
            mDataBind.tvWifiCurrent.text = "请输入密码连接${getString("wifi_name")}"
        }

    }


    override fun onBindViewClick() {
        super.onBindViewClick()
        mDataBind.butWifiConnect.setOnClickListener {
            if (mDataBind.tvWifiPwd.text.isEmpty()) {
                "请输入WIFI密码".toast()
                return@setOnClickListener
            }
            if (mDataBind.tvWifiPwd.text.length < 8) {
                "请输入8位WIFI密码".toast()
                return@setOnClickListener
            }
            mDataBind.butWifiConnect.text = "正在连接..."
            eventViewModel.wifiConnectState.value = mDataBind.tvWifiPwd.text.toString()
        }
        mDataBind.butWifiConnect.setOnFocusChangeListener { _, hasFocus ->
            mDataBind.butWifiConnect.setBackgroundResource(if (hasFocus) R.color.green else R.color.black1)
        }
    }


}