package com.panda.course.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent

/**
 * 代码可以直接用，这是UDP的方式 与RemoteService配套
 */
class RemoteReceiver : BroadcastReceiver() {

    companion object {
        const val ACTION_DESTROY = "com.panda.course.receiver.destroy"
    }

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED || intent.action == ACTION_DESTROY) {
            val startIntent = Intent("com.panda.course.receiver")
            context.startService(startIntent)
        }
    }

}