package com.panda.course.ui.viewmodel

import android.text.style.UpdateAppearance
import android.view.View
import androidx.lifecycle.MutableLiveData
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetHttpClient
import com.panda.course.network.NetUrl
import com.panda.course.util.MMKVHelper
import rxhttp.toStr
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse


class CourseViewModel : BaseViewModel() {

    // 登录二维码
    var qrCodeEntity = MutableLiveData<QRCodeEntity>()

    // 用户信息
    var userEntity = MutableLiveData<UserEntity>()
    var mCourseList = MutableLiveData<CourseList>()
    var mVideoHistory = MutableLiveData<IndexEntity>()
    var mRowList = MutableLiveData<RowEntity>()
    var mCourseDetail = MutableLiveData<CourseDetail>()
    var mHourEntity = MutableLiveData<ClassHourListEntity>()
    var mUpdateInfo = MutableLiveData<UpdateInfo>()

    // 退出登录
    var logOut = MutableLiveData<Boolean>()

    // 上传视频
    var seeProgress = MutableLiveData<Boolean>()
    var progress = MutableLiveData<Boolean>()
    var checkDevice = MutableLiveData<Boolean>()
    var checkTipsDevice = MutableLiveData<String>()

    // 检查设备是否过期
    var checkCodeDevice = MutableLiveData<Int>()

    // 视频打点信息
    var mVideoDotEntity = MutableLiveData<VideoDotList>()

    // 签到二维码
    var qrSignCode = MutableLiveData<QRCodeEntity>()

    // 作业二维码
    var exerciseCode = MutableLiveData<QRCodeEntity>()

    // 作业列表
    var exerciseList = MutableLiveData<VideoSignOrDynamicList>()

    // 毕业表彰列表
    var mGraduationList = MutableLiveData<GraduateListEntity>()
    var mGraduationList2 = MutableLiveData<GraduateListEntity>()

    // 设备到期
    var deviceExpireDate = MutableLiveData<DeviceExpireDate>()

    // IM信息
    var mIMInfoEntity = MutableLiveData<IMInfoEntity>()

    // 分享课程二维码
    var shareQRCodeEntity = MutableLiveData<QRCodeEntity>()

    //cache的内容
    var cacheVideoList = MutableLiveData<CacheVideoList>()

    //所有的打点
    var cacheVideoDotList = MutableLiveData<CacheVideoDotList>()

    //打点的资源
    var cacheVideoDotResource = MutableLiveData<VideoDotResourceList>()

    //惊喜活动
    var surpriseEntity = MutableLiveData<SurpriseEntity>()
    var surpriseListEntity = MutableLiveData<SurpriseListEntity>()
    var surpriseContentEntity = MutableLiveData<SurpriseContentEntity>()

    //看客任务状态奖励
    var todayFinishEntity = MutableLiveData<TodayFinishEntity>()

    //ota的升级信息
    var mOTAUpdateInfo = MutableLiveData<UpdateInfo>()

    //课时知识点
    var mKnowledgePointsEntity = MutableLiveData<KnowledgePointsEntity>()

    //消息通知
    var noticeEntity = MutableLiveData<NoticeEntity>()

    //查询消息是否接受
    var checkAcceptNotice = MutableLiveData<CheckAcceptNotice>()

    //离线上报
    var uploadOffline = MutableLiveData<Any>()


    // 获取红包动效
    var redPaperEntity = MutableLiveData<RedPaperEntity>()

    //红包config
    var redPaperConfigEntity = MutableLiveData<RedPaperConfigEntity>()

    //用户创建红包
    var redCreate = MutableLiveData<Any>()

    //雪碧图
    var spritesEntity = MutableLiveData<ImageSpritesEntity>()

    //课件列表
    var classPdfRows = MutableLiveData<ClassPdfListEntity>()


    private var pageIndex = 1

    /**
     * 获取登录二维码数据
     */
    fun getQrCode(device_code: String, ip_address: String?) {
        rxHttpRequest {
            onRequest = {
                qrCodeEntity.value =
                    RxHttp.get(NetUrl.QR_CODE).add("ip_address", "$ip_address")
                        .add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<QRCodeEntity>()
                        .await()
            }
            loadingType = LoadingType.LOADING_XML
            requestUrl = NetUrl.QR_CODE
        }
    }

    /**
     * 刷新获取登录的状态
     */
    fun getRefreshLoginStatus(qrCode: String) {
        rxHttpRequest {
            onRequest = {
                userEntity.value =
                    RxHttp.get(NetUrl.LOGIN_INFO).add("code", "$qrCode")
                        .add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<UserEntity>()
                        .await()
            }
            requestUrl = NetUrl.LOGIN_INFO
        }
    }

    /**
     * 获取用户数据
     */
    fun getUserInfo(token: String) {
        rxHttpRequest {
            onRequest = {
                userEntity.value = RxHttp.get(NetUrl.GET_USER_INFO).add("is_im_info", "1")
                    .add("is_device_info", "1")
                    .add("is_store_website_info", "1")
                    .add("device_id", getAndroidId())
                    .add("device_mac", getWireMac())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<UserEntity>().await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_USER_INFO
        }
    }


    /**
     * 获取课程的分类列表
     */
    fun getRowList() {
        rxHttpRequest {
            onRequest = {
                mRowList.value =
                    RxHttp.get(NetUrl.ROW_LIST).add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<RowEntity>()
                        .await()
            }
            requestUrl = NetUrl.ROW_LIST
        }
    }

    /**
     * 获取观看记录、宣传片
     */
    fun getVideoHistoryList() {
        rxHttpRequest {
            onRequest = {
                mVideoHistory.value =
                    RxHttp.get(NetUrl.RECOMMEND).add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<IndexEntity>()
                        .await()
            }
            requestUrl = NetUrl.RECOMMEND
        }
    }

    /**
     * 获取课程的列表
     */
    fun getCourseList(type_id: String, hideDialog: Boolean = false) {
        rxHttpRequest {
            onRequest = {
                mCourseList.value =
                    RxHttp.get(NetUrl.COURSE_LIST).add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac())
                        .add("device_id", getAndroidId()).add("page", "1")
                        .add("size", "" + ConstantMMVK.PAGE_SIZE)
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .add("type_id", "$type_id")
                        .toResponse<CourseList>().await()
            }

            //            loadingType = if (hideDialog) LoadingType.LOADING_NULL else LoadingType.LOADING_CUSTOM
            requestUrl = NetUrl.COURSE_LIST
        }
    }

    /**
     * 获取某个课程的详情
     */
    fun getCourseDetail(
        isRefresh: Boolean, code: String?, uuid: String? = null, hideDialog: Boolean = false
    ) {
        if (isRefresh) {
            pageIndex = 1
        }
        rxHttpRequest {
            onRequest = {
                mCourseDetail.value =
                    RxHttp.get(NetUrl.GET_COURSE_DETAIL).add("code", "$code")
                        .add("page", "$pageIndex")
                        .add("size", "" + ConstantMMVK.PAGE_SIZE)
                        .add("device_code", getSerialNumber())
                        .add("uuid", "$uuid").add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<CourseDetail>()
                        .await() // 请求成功，页码+1
                pageIndex++ // 状态给他
                mCourseDetail.value?.isRefresh = isRefresh
            }
            loadingType = if (hideDialog) LoadingType.LOADING_NULL else LoadingType.LOADING_CUSTOM
            isRefreshRequest = isRefresh
            requestUrl = NetUrl.GET_COURSE_DETAIL
        }
    }

    //获取课室的分页
    fun getCourseHourDetails(isRefresh: Boolean, code: String) {
        if (isRefresh) {
            pageIndex = 1
        }
        rxHttpRequest {
            onRequest = {
                mHourEntity.value =
                    RxHttp.get(NetUrl.HOUR_DETAILS).add("code", "$code")
                        .add("page", "$pageIndex")
                        .add("size", "" + ConstantMMVK.PAGE_SIZE)
                        .add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<ClassHourListEntity>()
                        .await() // 请求成功，页码+1
                pageIndex++ // 状态给他
            }
            loadingType = LoadingType.LOADING_CUSTOM
            requestUrl = NetUrl.HOUR_DETAILS
        }
    }


    /**
     * 退出登录
     */
    fun logout() {
        rxHttpRequest {
            onRequest = {
                logOut.value =
                    RxHttp.get(NetUrl.LOG_OUT).add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<Boolean>()
                        .await()
            }
            requestUrl = NetUrl.LOG_OUT
        }

    }

    /**
     * 更新App
     */
    fun updateAppVersion() {
        rxHttpRequest {
            onRequest = {
                mUpdateInfo.value =
                    RxHttp.get(NetUrl.GET_VERSION).add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("type", if (getCurrentAndroid9()) "2" else "1")
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<UpdateInfo>()
                        .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_VERSION
        }

    }

    /**
     * 上传视频的进度
     */
    fun uploadVideoProgress(code: String? = null, watch_time: Long) {
        rxHttpRequest {
            onRequest = {
                seeProgress.value =
                    RxHttp.get(NetUrl.UPLOAD_PROGRESS).add("code", "$code")
                        .add("watch_time", "$watch_time")
                        .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<Boolean>()
                        .await()
            }
            requestUrl = NetUrl.UPLOAD_PROGRESS
        }
    }

    /**
     * 统计视频的时长
     */
    fun uploadVideoStatistics(code: String? = null) {
        rxHttpRequest {
            onRequest = {
                progress.value = RxHttp.get(NetUrl.COURSE_STAT).add("code", "$code")
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<Boolean>().await()
            }
            requestUrl = NetUrl.COURSE_STAT
        }
    }


    /**
     * 统计客户视频的时长
     */
    fun uploadStoreVideoStatistics(id: String? = null) {
        rxHttpRequest {
            onRequest = {
                RxHttp.get(NetUrl.STORE_VIDEO).add("id", "$id").add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<Any>().await()
            }
            requestUrl = NetUrl.QR_CODE
        }
    }


    /**
     * 统计客户视频的时长
     */
    fun uploadPropagandaStoreVideoStatistics(id: String? = null) {
        rxHttpRequest {
            onRequest = {
                RxHttp.get(NetUrl.STORE_WATCH_TIME).add("id", "$id").add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<Any>().await()
            }
            requestUrl = NetUrl.STORE_WATCH_TIME
        }
    }


    /**
     * 上报当前设备的版本号
     */
    fun uploadDeviceVersion() {
        rxHttpRequest {
            onRequest = {
                RxHttp.get(NetUrl.UPDATE_DEVICE_VERSION)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<Any>().await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.UPDATE_DEVICE_VERSION
        }
    }

    /**
     * 检测设备是否有效
     */
    fun checkDevice(showDialog: Boolean = true) {
        rxHttpRequest {
            onRequest = {
                checkDevice.value =
                    RxHttp.get(NetUrl.CHECK_DEVICE).add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<Boolean>().await()
            }
            onError = {
                "${it.message}".logE("检测设备")
                "${it.code}".logE("检测设备")
                "${it.msg}".logE("检测设备")
                checkTipsDevice.value = it.msg
                checkCodeDevice.value = it.code
            }
            loadingType = if (showDialog) LoadingType.LOADING_CUSTOM else LoadingType.LOADING_NULL

            requestUrl = NetUrl.CHECK_DEVICE
        }
    }


    /**
     * 1.课程结业考试二维码
     * 2.获取师生互动二维码
     * 3.课程练习生成二维码
     */
    fun getClassQrCode(code: String, type: Int) {
        rxHttpRequest {
            onRequest = {
                var url: String? = null
                when (type) {
                    ConstantMMVK.POPUP_EXAMINATION -> url = NetUrl.EXAM_QR_CODE
                    ConstantMMVK.POPUP_TEACHER -> url = NetUrl.TEACHER_QR_CODE
                }
                qrCodeEntity.value =
                    RxHttp.get(url).add("code", code).add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<QRCodeEntity>()
                        .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.EXAM_QR_CODE
        }
    }


    /**
     * 获取打点信息
     */
    fun getVideoDotList(code: String? = null) {
        rxHttpRequest {
            onRequest = {
                mVideoDotEntity.value =
                    RxHttp.get(NetUrl.FIND_VIDEO_DOT).add("code", code)
                        .add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<VideoDotList>()
                        .await()
            }
            loadingType = LoadingType.LOADING_NULL
            onError = {
                val stringHashMap = java.util.HashMap<String, Any>()
                stringHashMap["device"] = "${getWireMac()}"
                eventViewModel.appUserInfo.value?.let { info ->
                    stringHashMap["store"] = "${info.store_name}"
                }
                stringHashMap["dot_error"] = "${it.message}"
                appUMEventObject(UMConstant.KEY_IMAGE_LOAD_ERROR, stringHashMap)
                getVideoDotList(code)
            }
            requestUrl = NetUrl.FIND_VIDEO_DOT
        }
    }


    /**
     * 获取签到二维码
     */
    fun getSignQrCode(code: String? = null) {
        rxHttpRequest {
            onRequest = {
                qrSignCode.value = RxHttp.get(NetUrl.SIGN_QR_CODE).add("code", code)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<QRCodeEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL

            requestUrl = NetUrl.SIGN_QR_CODE
        }

    }

    /**
     * 获取作业二维码
     */
    fun getExerciseQrCode(code: String? = null, dot_id: String) {
        rxHttpRequest {
            onRequest = {
                exerciseCode.value =
                    RxHttp.get(NetUrl.EXERCISE_QR_CODE).add("code", code).add("dot_id", dot_id)
                        .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<QRCodeEntity>()
                        .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.EXERCISE_QR_CODE
        }
    }


    /**
     * 获取作业列表
     */
    fun getExerciseListCode(code: String? = null, dot_id: String, isSign: Boolean) {
        rxHttpRequest {
            onRequest = {
                exerciseList.value =
                    RxHttp.get(if (isSign) NetUrl.FIND_SIGN_LIST else NetUrl.DOT_EXERCISE_LIST)
                        .add("code", code)
                        .add("dot_id", dot_id).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<VideoSignOrDynamicList>().await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = if (isSign) NetUrl.FIND_SIGN_LIST else NetUrl.DOT_EXERCISE_LIST
        }
    }

    /**
     * 获取毕业表彰
     */
    fun getGraduationList(type: String, course_code: String, isShowDialog: Boolean? = false) {
        rxHttpRequest {
            onRequest = {
                mGraduationList.value =
                    RxHttp.get(NetUrl.GRADUATION_LIST)
                        .add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("page", "1").add("size", "" + ConstantMMVK.PAGE_SIZE)
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .add("type", type)
                        .add("course_code", course_code)
                        .toResponse<GraduateListEntity>().await()
            }
            loadingType = if (isShowDialog == true) LoadingType.LOADING_CUSTOM else LoadingType.LOADING_NULL
            requestUrl = NetUrl.GRADUATION_LIST
        }
    }


    /**
     * 获取毕业表彰
     */
    fun getGraduationList2(type: String, course_code: String, isShowDialog: Boolean? = false) {
        rxHttpRequest {
            onRequest = {
                mGraduationList2.value =
                    RxHttp.get(NetUrl.GRADUATION_LIST)
                        .add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("page", "1").add("size", "" + ConstantMMVK.PAGE_SIZE)
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .add("type", type)
                        .add("course_code", course_code)
                        .toResponse<GraduateListEntity>().await()
            }
            loadingType = if (isShowDialog == true) LoadingType.LOADING_CUSTOM else LoadingType.LOADING_NULL
            requestUrl = NetUrl.GRADUATION_LIST
        }
    }


    /**
     * 获取设备服务日期
     */
    fun getDeviceExpireDate() {
        rxHttpRequest {
            onRequest = {
                deviceExpireDate.value =
                    RxHttp.get(NetUrl.DEVICE_EXPIREDATE).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<DeviceExpireDate>()
                        .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.DEVICE_EXPIREDATE
        }
    }


    /**
     * 获取IM信息
     */
    fun getIMInfo() {
        rxHttpRequest {
            onRequest = {
                mIMInfoEntity.value =
                    RxHttp.get(NetUrl.GET_IM_INFO).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<IMInfoEntity>()
                        .await()
            }
            requestUrl = NetUrl.GET_IM_INFO
        }
    }

    /**
     * 获取课程分享二维码
     */
    fun getShareCourseQRCode(code: String) {
        rxHttpRequest {
            onRequest = {
                shareQRCodeEntity.value =
                    RxHttp.get(NetUrl.SHARE_COURSE_QR_CODE).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .add("code", code)
                        .toResponse<QRCodeEntity>().await()
            }
            requestUrl = NetUrl.SHARE_COURSE_QR_CODE
        }
    }


    /**
     * 获取cache的视频
     */
    fun getCacheVideoList() {
        rxHttpRequest {
            onRequest = {
                cacheVideoList.value =
                    RxHttp.get(NetUrl.CACHE_DOWNLOADER_LIST).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<CacheVideoList>()
                        .await()
            }
            requestUrl = NetUrl.CACHE_DOWNLOADER_LIST
        }
    }

    /**
     * 预置的打点信息
     */
    fun getVideoAllDot() {
        rxHttpRequest {
            onRequest = {
                cacheVideoDotList.value =
                    RxHttp.get(NetUrl.CACHE_ALL_DOT).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<CacheVideoDotList>().await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.CACHE_ALL_DOT
        }
    }

    /**
     * 预置的打点资源
     */
    fun getVideoDotResource() {
        rxHttpRequest {
            onRequest = {
                cacheVideoDotResource.value =
                    RxHttp.get(NetUrl.CACHE_ALL_DOT_RESOURCE).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<VideoDotResourceList>().await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.CACHE_ALL_DOT_RESOURCE
        }
    }


    /**
     * 上报 本地视频统计的情况
     */
    fun uploadVideoDownLoadState(schedule: MutableList<VideoDownloadInfo>) {
        rxHttpRequest {
            onRequest = {
                RxHttp.get(NetUrl.UPLOAD_VIDEO_STATE).add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("schedule", "" + JSON.toJSONString(schedule)).toResponse<Any>().await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.UPLOAD_VIDEO_STATE
        }
    }


    /**
     * 获取 惊喜 活动
     */
    fun getSurpriseActivityAd() {
        rxHttpRequest {
            onRequest = {
                surpriseEntity.value =
                    RxHttp.get(NetUrl.SURPRISE_ACTIVITY_AD).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<SurpriseEntity>()
                        .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.SURPRISE_ACTIVITY_AD
        }
    }

    /**
     * 获取 惊喜 活动
     */
    fun getSurpriseActivityAdList() {
        rxHttpRequest {
            onRequest = {
                surpriseListEntity.value =
                    RxHttp.get(NetUrl.SURPRISE_ACTIVITY_LIST).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<SurpriseListEntity>().await()
            }
            requestUrl = NetUrl.SURPRISE_ACTIVITY_LIST
        }
    }

    /**
     * 获取 惊喜 活动 界面内容
     */
    fun getSurpriseActivityContentAll(activity_id: String) {
        rxHttpRequest {
            onRequest = {
                surpriseContentEntity.value =
                    RxHttp.get(NetUrl.SURPRISE_ACTIVITY_ALL).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .add("activity_id", activity_id)
                        .add("is_finished_total", "1")
                        .add("store_uuid", eventViewModel.appUserInfo.value?.uuid)
                        .toResponse<SurpriseContentEntity>().await()
            }
            requestUrl = NetUrl.SURPRISE_ACTIVITY_ALL
            loadingType = LoadingType.LOADING_DIALOG
        }
    }

    /**
     * 获取 惊喜 活动 界面内容
     */
    fun getUserQueryCourseReward() {
        rxHttpRequest {
            onRequest = {
                todayFinishEntity.value =
                    RxHttp.get(NetUrl.TODAY_IS_FINISHED).add("device_mac", getWireMac())
                        .add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<TodayFinishEntity>()
                        .await()
            }
            requestUrl = NetUrl.TODAY_IS_FINISHED
            loadingType = LoadingType.LOADING_NULL
        }
    }


    /**
     * 升级OTA
     */
    fun getOTAInfo() {
        rxHttpRequest {
            onRequest = {
                mOTAUpdateInfo.value =
                    RxHttp.get(NetUrl.OTA_UPDATE).add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                        .add("firmware", getDeviceRadioFirmware())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .toResponse<UpdateInfo>()
                        .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.NET_WORK_SPEED
        }
    }

    /**
     * 获取课时的知识点
     */
    fun getKnowledgePoints(video_code: String) {
        rxHttpRequest {
            onRequest = {
                mKnowledgePointsEntity.value =
                    RxHttp.get(NetUrl.KNOWLEDGE_POINTS).add("device_code", getSerialNumber())
                        .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                        .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                        .add("video_code", video_code)
                        .toResponse<KnowledgePointsEntity>()
                        .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.KNOWLEDGE_POINTS
        }
    }


    /**
     * 获取消息通知
     * is_notice_content 是否需要消息内容 0否 1是，默认0, 值为1时才会有notice对象
     */
    fun getNoticeTotal(
        is_notice_content: String? = null,
        number: String? = null,
        notice_id: String? = null
    ) {
        rxHttpRequest {
            onRequest = {
                noticeEntity.value = RxHttp.get(NetUrl.UN_READ_NOTICE)
                    .add("device_code", getSerialNumber())
                    .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("is_notice_content", is_notice_content)
                    .add("number", number)
                    .add("notice_id", notice_id)
                    .toResponse<NoticeEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.UN_READ_NOTICE
        }
    }

    /**
     * 获取消息通知
     */
    fun checkAcceptNotice(notice_id: String) {
        rxHttpRequest {
            onRequest = {
                checkAcceptNotice.value = RxHttp.get(NetUrl.CHECK_ACCEPT_NOTICE)
                    .add("device_code", getSerialNumber())
                    .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("notice_id", notice_id)
                    .toResponse<CheckAcceptNotice>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.CHECK_ACCEPT_NOTICE
        }
    }


    /**
     * 上传离线数据
     */
    fun uploadOfflineCourse(video_data: String) {
        rxHttpRequest {
            onRequest = {
                uploadOffline.value = RxHttp.get(NetUrl.OFFLINE_COURSE_WATCH)
                    .add("video_data", video_data)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<Any>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.OFFLINE_COURSE_WATCH
        }
    }


    /**
     * 获取全部的红包特效
     */
    fun getRedPaper(number: String, tv_device_id: String) {
        rxHttpRequest {
            onRequest = {
                redPaperEntity.value = RxHttp.get(NetUrl.GET_RED_PAPER)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("number", "" + number)
                    .add("tv_device_id", "" + tv_device_id)
                    .toResponse<RedPaperEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_RED_PAPER
        }
    }


    /**
     * 获取红包配置
     */
    fun getRedPaperConfig() {
        rxHttpRequest {
            onRequest = {
                redPaperConfigEntity.value = RxHttp.get(NetUrl.GET_RED_PAPER_SETTING)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<RedPaperConfigEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_RED_PAPER_SETTING
        }
    }


    /**
     * 用户确定红包
     */
    fun createRedPaper(tv_device_id: String) {
        rxHttpRequest {
            onRequest = {
                redCreate.value = RxHttp.get(NetUrl.RED_PAPER_CREATE)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("tv_device_id", "" + tv_device_id)
                    .toResponse<Any>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_RED_PAPER
        }
    }


    /**
     * 遥控器答题提交
     */
    fun submitExercise(tv_device_id: String, dot_id: String?, answer: String?) {
        rxHttpRequest {
            onRequest = {
                redCreate.value = RxHttp.get(NetUrl.SUBMIT_EXERCISE)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("tv_device_id", "" + tv_device_id)
                    .add("dot_id", "" + dot_id)
                    .add("answer", "" + answer)
                    .toResponse<Any>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_RED_PAPER
        }
    }


    /**
     * 获取雪碧图
     */
    fun getImageSprites(tencent_video_id: String) {
        rxHttpRequest {
            onRequest = {
                spritesEntity.value = RxHttp.get(NetUrl.GET_IMAGE_SPRITE)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("tencent_video_id", "" + tencent_video_id)
                    .toResponse<ImageSpritesEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_IMAGE_SPRITE
        }
    }

    /**
     * 获取课件的详情
     */
    fun getClassPdfRows(code: String? = "") {
        rxHttpRequest {
            onRequest = {
                classPdfRows.value = RxHttp.get(NetUrl.CLASS_VIDEO_LIST)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("code", "" + code)
                    .toResponse<ClassPdfListEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.CLASS_VIDEO_LIST
        }
    }

}