package com.panda.course.ui.adapter

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.AuntSkillListEntity
import com.panda.course.entity.AuntSkillRecordListEntity
import com.panda.course.entity.RowStringEntity
import com.panda.course.ext.dp2px
import com.panda.course.ext.px2dp
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper

class SkillRecordAdapter(var isRow: Boolean) : BaseQuickAdapter<AuntSkillRecordListEntity, BaseViewHolder>(R.layout.item_skill_record) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var onItemFocus: OnViewFocus? = null


    fun setOnViewFocus(onItemFocus: OnViewFocus?) {
        this.onItemFocus = onItemFocus
    }

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(MyFocusHighlightHelper.ZOOM_FACTOR_NO, false)
        }
    }

    override fun convert(holder: BaseViewHolder, item: AuntSkillRecordListEntity) {

        holder.setText(R.id.tv_item_skill_record_name, if (!TextUtils.isEmpty(item.name)) item.name else item.store_name + "(${item.service_personnel_name})")
            .setText(R.id.tv_item_skill_record_score_course_name, item.skill_post_name)
            .setText(R.id.tv_item_skill_record_score_course_date, item.update_time)
            .setText(R.id.tv_item_skill_record_score, item.score?.let { resultSalary(it) } + "   |    " + item.label)


        val recordName = holder.getView<TextView>(R.id.tv_item_skill_record_name)
        recordName.maxEms = (if (isRow) 10 else 14)

        val imageView = holder.getView<ImageView>(R.id.iv_item_skill_record_pic)

        Glide.with(context).asBitmap().apply(RequestOptions.bitmapTransform(CircleCrop()))
            .placeholder(R.drawable.def_pic).error(R.drawable.def_pic).load(item.service_personnel_avatar)
            .into(imageView)

        val cardView = holder.getView<CardView>(R.id.row_card_view)

        // 隐藏的代码是为了需求变更使用、之前是view放大，现在是View 选中状态
        cardView.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->

            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)

            onItemFocus?.onChangeFocus(hasFocus, getItemPosition(item), v)

            // 设置阴影
            cardView.cardElevation = if (hasFocus) px2dp(20f).toFloat() else px2dp(0f).toFloat()

            cardView.setCardBackgroundColor(
                if (hasFocus) ContextCompat.getColor(
                    context, R.color.black
                ) else ContextCompat.getColor(context, R.color.transparent)
            )

            cardView.radius = if (hasFocus) 16f else 0f
        }
    }

    private fun resultSalary(salary: String): String {
        return if (salary.contains(".")) salary.substring(0, salary.indexOf(".")) else salary
    }

}