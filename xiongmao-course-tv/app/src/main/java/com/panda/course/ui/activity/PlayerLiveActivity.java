package com.panda.course.ui.activity;

import static com.panda.course.ext.AppExtKt.appUMEvent;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.Toast;

import com.baijiayun.livebase.context.LPConstants;
import com.baijiayun.livebase.context.LPError;
import com.baijiayun.livebase.models.LPJoinCodeEnterRoomModel;
import com.baijiayun.livebase.models.imodels.IUserModel;
import com.baijiayun.livecore.LiveSDK;
import com.baijiayun.livecore.context.LiveRoom;
import com.baijiayun.livecore.listener.LPRoomStatusListener;
import com.baijiayun.livecore.models.imodels.IMediaModel;
import com.baijiayun.livecore.models.imodels.IMessageModel;
import com.baijiayun.livecore.viewmodels.ChatVM;
import com.baijiayun.livecore.wrapper.LPPlayer;
import com.baijiayun.livecore.wrapper.impl.LPVideoView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.orient.tea.barragephoto.adapter.BarrageAdapter;
import com.orient.tea.barragephoto.ui.BarrageView;
import com.panda.course.R;
import com.panda.course.callback.OnViewFocus;
import com.panda.course.config.App;
import com.panda.course.config.UMConstant;
import com.panda.course.config.base.BaseDbActivity;
import com.panda.course.data.CommonDataUtil;
import com.panda.course.data.CommonViewUtil;
import com.panda.course.databinding.ActivityLivePlayerBinding;
import com.panda.course.entity.BarrageData;
import com.panda.course.entity.ControllerViewState;
import com.panda.course.entity.LiveBarrageEntity;
import com.panda.course.entity.QRCodeEntity;
import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.LogExtKt;
import com.panda.course.ui.adapter.CommonSpecialFunctionsAdapter;
import com.panda.course.ui.adapter.LiveBarrageViewHolder;
import com.panda.course.ui.viewmodel.LiveModel;
import com.panda.course.util.GlideImageLoader;
import com.panda.course.util.QRCodeUtil;
import com.panda.course.util.ToastUtil;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.net.URLDecoder;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public class PlayerLiveActivity extends BaseDbActivity<LiveModel, ActivityLivePlayerBinding> {

    //房间信息
    private LiveRoom liveRoom;
    //任务调度监听
    private CompositeDisposable compositeDisposable = new CompositeDisposable();

    private String titleName;

    private LPPlayer lpPlayer;
    protected ViewGroup.LayoutParams layoutParams;
    LinearLayout remoteVideoContainerLl;

    private BarrageAdapter<BarrageData> mBarrAgeAdapter = null;

    private List<String> customStr;

    //控制弹幕
    private boolean isShowBarrage = true;


    private CommonSpecialFunctionsAdapter commonSubTitleAdapter = new CommonSpecialFunctionsAdapter();
    private CommonSpecialFunctionsAdapter commonQrAdapter = new CommonSpecialFunctionsAdapter();


    //弹幕的定时 处理
    private static Timer timer;
    private static TimerTask timerTask;
    private static int currentIndex = 0;//记录当前索引到哪儿

    private String course_code;

    /**
     * 默认分类
     */
    private int focus_danku = 1;

    /**
     * 分类课程
     */
    private int focus_qrcode = 2;


    private int currentFocusType = focus_danku;

    private int dankuPostion = 0;
    private int qrCodePosition = 0;

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        remoteVideoContainerLl = mDataBind.mainMenuPlayerRemoteVideo;
        initRecyclerView();
        danConfig();


//        titleName = getIntent().getExtras().getString("title_name");
        String code = getIntent().getExtras().getString("code", "");
        course_code = getIntent().getExtras().getString("course_code", "");
        LogExtKt.logE("请求的参数 code " + code, "请求的参数");
        LogExtKt.logE("请求的参数 course_code " + course_code, "请求的参数");
        mViewModel.getBarrage();
        mViewModel.getLiveQrCode(code, course_code);
        mViewModel.getLiveInfo(course_code);


//        mDataBind.tvInitTitle.setText(titleName);
//        mDataBind.tvLiveCourseName.setText(titleName);
        beforePlayer();
    }

    private void initRecyclerView() {
        commonSubTitleAdapter.setList(new CommonDataUtil().getVideoSubTitle());
        commonQrAdapter.setList(new CommonDataUtil().getVideoSubTitle());

        LinearLayoutManager manager = new LinearLayoutManager(this);
        manager.setOrientation(LinearLayoutManager.HORIZONTAL);
        mDataBind.gridSubTitle.setLayoutManager(manager);
        mDataBind.gridSubTitle.setAdapter(commonSubTitleAdapter);
        //qr
        LinearLayoutManager managerQr = new LinearLayoutManager(this);
        managerQr.setOrientation(LinearLayoutManager.HORIZONTAL);
        mDataBind.gridSubQr.setLayoutManager(managerQr);
        mDataBind.gridSubQr.setAdapter(commonQrAdapter);
    }

    private void danConfig() {
        BarrageView.Options options = new BarrageView.Options()
                .setGravity(BarrageView.GRAVITY_FULL) // 设置弹幕的位置
                .setInterval(250) // 设置弹幕的发送间隔
                .setSpeed(300, 0) // 设置速度和波动值
                .setModel(BarrageView.MODEL_COLLISION_DETECTION) // 设置弹幕生成模式
                .setRepeat(1) // 循环播放 默认为1次 -1 为无限循环
                .setClick(false); // 设置弹幕是否可以点击
        mDataBind.danmukuView.setOptions(options);
        mBarrAgeAdapter = new BarrageAdapter<BarrageData>(null, this) {
            @Override
            protected BarrageViewHolder<BarrageData> onCreateViewHolder(View root, int type) {
                return new LiveBarrageViewHolder(root);
            }

            @Override
            public int getItemLayout(BarrageData barrageData) {
                return R.layout.barrage_item_normal;
            }
        };
        mDataBind.danmukuView.setAdapter(mBarrAgeAdapter);
    }

    //播放之前需要显示的
    private void beforePlayer() {
        mDataBind.llLiveTop.setVisibility(View.GONE);
        mDataBind.llInitNet.setVisibility(View.VISIBLE);
        mDataBind.progressInit.startRun();
    }

    //播放后需要显示de
    private void afterPlayer() {
        mDataBind.llLiveTop.setVisibility(View.VISIBLE);
        mDataBind.llInitNet.setVisibility(View.GONE);
        mDataBind.progressInit.stopRun();
        //延迟5秒后
        mDataBind.llLiveButtom.postDelayed(new Runnable() {
            @Override
            public void run() {
                mDataBind.llLiveTop.setVisibility(View.GONE);
                mDataBind.llLiveButtom.setVisibility(View.GONE);
            }
        }, 5000);
        //每10分钟重新拉一下二维码
        compositeDisposable.add(Observable.interval(10, TimeUnit.MINUTES)
                .subscribe(this::timerAction));
    }

    private void timerAction(Long aLong) {
        mViewModel.getLiveInfo(course_code);
    }


    @Override
    public void onRequestSuccess() {
        super.onRequestSuccess();
        //教师参加码
        mViewModel.getJoinEntity().observe(this, new Observer<QRCodeEntity>() {
            @Override
            public void onChanged(QRCodeEntity qrCodeEntity) {
                //yra4jy
                enterRoom(qrCodeEntity.getJoin_code(), AppExtKt.getEventViewModel().getAppUserInfo().getValue().getStore_name() + "@AI@" + AppExtKt.getEventViewModel().getAppUserInfo().getValue().getStore_id());
            }
        });
        // 独立显示的二维码
        mViewModel.getLiveQrEntity().observe(this, new Observer<QRCodeEntity>() {
            @Override
            public void onChanged(QRCodeEntity qrCodeEntity) {
                if (!TextUtils.isEmpty(qrCodeEntity.getQr_code_content())) {
                    mDataBind.llLiveQrCode.setVisibility(View.VISIBLE);
                    mDataBind.ivLiveQrCode.setImageBitmap(QRCodeUtil.getInstance().createQRCode(qrCodeEntity.getQr_code_content()));
//                    GlideImageLoader.INSTANCE.loadImageWithRetry(PlayerLiveActivity.this, qrCodeEntity.getQr_code_url(), mDataBind.ivLiveQrCode, 10, 3000);
                }
            }
        });
        //弹幕规则
        mViewModel.getLiveBarrageEntity().observe(this, new Observer<LiveBarrageEntity>() {
            @Override
            public void onChanged(LiveBarrageEntity liveBarrageEntity) {
                //执行规则  1、每10秒执行一次操作，周而复始  2、每次只取10个值 不够10个，按一组来  3、假设有真实内容进来 则重新计数
                customStr = mViewModel.getLiveBarrageEntity().getValue().getList();
                timer = null;
                timerTask = null;
                startBarrageTimer();
            }
        });
    }

    /**
     * 执行弹幕定时器
     */
    private void startBarrageTimer() {
        if (timer == null) {
            timer = new Timer();
        }
        if (timerTask == null) {
            timerTask = new TimerTask() {
                @Override
                public void run() {
                    if (customStr != null && customStr.size() > 0) {
                        // 记录当前的起始索引
                        int startIndex = currentIndex;
                        // 取出10个值进行处理
                        for (int i = startIndex; i < startIndex + 2 && i < customStr.size(); i++) {
                            //弹幕 customStr.get(i)
                            if (!TextUtils.isEmpty(customStr.get(i)) && isShowBarrage) {
                                mBarrAgeAdapter.add(new BarrageData(customStr.get(i), null, 0, 1));
                            }
                        }
                        // 更新当前索引
                        currentIndex = startIndex + 2;
                        // 检查是否还有剩余数据
                        if (currentIndex >= customStr.size()) {
                            // 定时器任务完成，可以选择停止定时器
                            currentIndex = 0;
                        }
                    }
                }
            };
        }
        //每10秒执行一次
        timer.scheduleAtFixedRate(timerTask, 30 * 1000, 30 * 1000);
    }

    /**
     * 停止定时器
     */
    private void stopTimer(boolean isReStart) {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        if (timerTask != null) {
            timerTask.cancel();
            timerTask = null;
        }
        if (isReStart && liveRoom.isClassStarted()) {
            //停止后，重新开启
            reStartTimer();
        }
    }

    private void reStartTimer() {
        startBarrageTimer();
    }


    @Override
    public void initObserver() {
        super.initObserver();
        mDataBind.butRetry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //如果点击了，那么重新进入房间
                mDataBind.llError.setVisibility(View.GONE);
                mDataBind.llLiveFinishLayout.setVisibility(View.GONE);
                beforePlayer();
                reconnect();
            }
        });

        commonSubTitleAdapter.setOnViewFocus(new OnViewFocus() {
            @Override
            public void onChangeFocus(boolean hasFocus, int position, View view) {
                if (hasFocus) {
                    dankuPostion = position;
                }
                currentFocusType = focus_danku;
            }
        });

        //点击关闭或者开启弹幕
        commonSubTitleAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull @NotNull BaseQuickAdapter<?, ?> adapter, @NonNull @NotNull View view, int position) {
                mDataBind.flController.setVisibility(View.GONE);
                if (0 == position) {
                    isShowBarrage = true;
                    ToastUtil.show("您已开启弹幕");
                    stopTimer(true);
                } else {
                    isShowBarrage = false;
                    ToastUtil.show("您已关闭弹幕");
                    stopTimer(false);
                }
                appUMEvent(UMConstant.AI_PLAY_BARRAGE);
                new CommonViewUtil().changeAdapterSelected(
                        commonSubTitleAdapter,
                        mDataBind.flController,
                        commonSubTitleAdapter,
                        ControllerViewState.VIEW_CONTROLLER_SUBTITLE,
                        position
                );
                notifyBarrage();
            }
        });


        commonQrAdapter.setOnViewFocus(new OnViewFocus() {
            @Override
            public void onChangeFocus(boolean hasFocus, int position, View view) {
                if (hasFocus) {
                    qrCodePosition = position;
                }
                currentFocusType = focus_qrcode;
            }
        });

        commonQrAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull @NotNull BaseQuickAdapter<?, ?> adapter, @NonNull @NotNull View view, int position) {
                mDataBind.flController.setVisibility(View.GONE);
                if (0 == position) {
                    mDataBind.llLiveQrCode.setVisibility(View.VISIBLE);
                } else {
                    mDataBind.llLiveQrCode.setVisibility(View.GONE);
                }
                new CommonViewUtil().changeAdapterSelected(
                        commonQrAdapter,
                        mDataBind.flController,
                        commonQrAdapter,
                        ControllerViewState.VIEW_CONTROLLER_SUBTITLE,
                        position
                );
                appUMEvent(UMConstant.AI_QR_CODE_PLAY);
            }
        });
    }

    /**
     * 控制弹幕的View
     */
    private void notifyBarrage() {
        mDataBind.danmukuView.setVisibility(isShowBarrage ? View.VISIBLE : View.GONE);
    }

    /**
     * 对视频重新进行链接
     */
    private void reconnect() {
        liveRoom.reconnect(new LPRoomStatusListener() {
            @Override
            public void onLaunchSteps(int i, int i1) {
                LogExtKt.logE("重连 - onLaunchSteps --> ", "百家云直播");
            }

            @Override
            public void onLaunchError(LPError lpError) {
                LogExtKt.logE("重连 - onLaunchError --> " + lpError.getCode() + " ; " + lpError.getMessage(), "百家云直播");
                if (lpError.getCode() == LPError.CODE_ERROR_LOGIN_KICK_OUT) {
                    userKickRoom();
                    return;
                }
                mDataBind.llError.setVisibility(View.VISIBLE);
                mDataBind.butRetry.requestFocus(1000);
            }

            @Override
            public void onLaunchSuccess(LiveRoom liveRoom) {
                mDataBind.llInitNet.setVisibility(View.GONE);
                remoteVideoContainerLl.removeAllViews();
                afterPlayer();
                LogExtKt.logE("重连 - onLaunchSuccess --> 进入房间成功 , 开始监听房间状态", "百家云直播");
                mDataBind.llInitNet.setVisibility(View.GONE);
                mDataBind.llError.setVisibility(View.GONE);
                subscribe();
            }

            @Override
            public void onLivingError(LPError lpError) {
                LogExtKt.logE("重连 - onLivingError --> " + lpError.getCode() + " ; " + lpError.getMessage(), "百家云直播");
                mDataBind.llError.setVisibility(View.VISIBLE);
                mDataBind.butRetry.requestFocus(1000);
            }

            @Override
            public void onQuitRoom() {
                LogExtKt.logE("重连 - onQuitRoom --> 退出room", "百家云直播");
            }
        });
    }


    /**
     * 进入房间
     *
     * @param code
     * @param userName
     */
    private void enterRoom(String code, String userName) {
        LogExtKt.logE("code  --> " + code, "百家云直播");

        LiveSDK.customEnvironmentPrefix = "e91898305";//设置专属域名
        LPJoinCodeEnterRoomModel lpJoinCodeEnterRoomModel = new LPJoinCodeEnterRoomModel(code, userName, null, LPConstants.LPUserType.Student);
        liveRoom = LiveSDK.enterRoom(this, lpJoinCodeEnterRoomModel, new LPRoomStatusListener() {
            @Override
            public void onLaunchSteps(int i, int i1) {
                LogExtKt.logE("onLaunchSteps --> ", "百家云直播");
            }

            @Override
            public void onLaunchError(LPError lpError) {
                LogExtKt.logE("onLaunchError --> " + lpError.getCode() + " ; " + lpError.getMessage(), "百家云直播");
                if (lpError.getCode() == LPError.CODE_ERROR_LOGIN_KICK_OUT) {
                    userKickRoom();
                    return;
                }
                mDataBind.llError.setVisibility(View.VISIBLE);
                mDataBind.butRetry.requestFocus(1000);
            }

            @Override
            public void onLaunchSuccess(LiveRoom liveRoom) {
                mDataBind.llError.setVisibility(View.GONE);
                LogExtKt.logE("onLaunchSuccess --> 进入房间成功 , 开始监听房间状态", "百家云直播");
                subscribe();
            }

            @Override
            public void onLivingError(LPError lpError) {
                //                Toast.makeText(PlayerLiveActivity.this, "onLivingError --> " + lpError.getCode() + " ; " + lpError.getMessage(), Toast.LENGTH_SHORT).show();
                LogExtKt.logE("onLivingError --> " + lpError.getCode() + " ; " + lpError.getMessage(), "百家云直播");
                mDataBind.llError.setVisibility(View.VISIBLE);
                mDataBind.butRetry.requestFocus(1000);
            }

            @Override
            public void onQuitRoom() {
                LogExtKt.logE("onQuitRoom --> 退出room", "百家云直播");
            }
        });


    }

    /**
     * 踢出房间
     */
    private void userKickRoom() {
        mDataBind.ivLogo.setImageResource(R.drawable.icon_live_kict);
        mDataBind.tvLiveFinishCourseName.setText("您已被请出教室，请联系老师～");
        mDataBind.llLiveFinishLayout.setVisibility(View.VISIBLE);
    }

    /**
     * 监听房间状态
     */
    private void subscribe() {
        LogExtKt.logE("subscribe -- 当前课堂状态= " + liveRoom.isClassStarted(), "百家云直播");
        lpPlayer = liveRoom.getPlayer();

        //假如当前课堂的状态是未上课状态，增加个显示
        if (!liveRoom.isClassStarted()) {
            mDataBind.llLiveFinishLayout.setVisibility(View.VISIBLE);
            mDataBind.tvLiveFinishCourseName.setText("老师正在赶来，请您耐心等待~");
        } else {
            mDataBind.llLiveFinishLayout.setVisibility(View.GONE);
        }

        //监听老师开麦
        compositeDisposable.add(liveRoom.getSpeakQueueVM()
                .getObservableOfMediaPublish()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<IMediaModel>() {
                    @Override
                    public void accept(IMediaModel iMediaModel) throws Exception {
                        LogExtKt.logE("拉流画面变动 getObservableOfMediaPublish " + iMediaModel, "百家云");
                        handleMediaPublish(iMediaModel);
                    }
                })
        );
        //监听踢人 房间坚挺
        compositeDisposable.add(liveRoom.getOnlineUserVM()
                .getObservableOfKickOut()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<LPError>() {
                    @Override
                    public void accept(LPError lpError) throws Exception {
                        userKickRoom();
                    }
                }));

        //监听课堂状态
        compositeDisposable.add(liveRoom.getSpeakQueueVM()
                .getObservableOfActiveUsers()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(iMediaModels -> {
                    LogExtKt.logE("拉流画面变动 getObservableOfActiveUsers ", "百家云");
                    mDataBind.llInitNet.setVisibility(View.GONE);
                    remoteVideoContainerLl.removeAllViews();
                    afterPlayer();
                    for (IMediaModel iMediaModel : iMediaModels) {
                        handleMediaPublish(iMediaModel);
                    }
                }));
        // 上课监听
        compositeDisposable.add(liveRoom.getObservableOfClassStart()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(a -> {
                    LogExtKt.logE("开始上课了", "百家云");
                    ToastUtil.show("开始上课啦");
                    mDataBind.tvLiveFinishCourseName.setText("老师正在赶来，请您耐心等待~");
                }));

        // 下课监听
        compositeDisposable.add(liveRoom.getObservableOfClassEnd()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(a -> {
                    LogExtKt.logE("下课了", "百家云");
                    ToastUtil.show("下课啦～等待老师上课");
                    mDataBind.tvLiveFinishCourseName.setText("下课啦～等待老师上课");
                    mDataBind.llLiveFinishLayout.setVisibility(View.VISIBLE);
                }));

        //用户进入房间监听
        compositeDisposable.add(liveRoom.getOnlineUserVM().getObservableOfUserIn()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(a -> {
                    if (isShowBarrage) {
                        mBarrAgeAdapter.add(new BarrageData(URLDecoder.decode(a.getUser().getName(), "UTF-8") + "进入房间", null, 0, 1));
                        stopTimer(true);
                    }
                }));

        //监听发言信息
        compositeDisposable.add(liveRoom.getChatVM().getObservableOfReceiveMessage()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(this::onUpdateChat));

        //监听当前直播间总人数
        compositeDisposable.add(liveRoom.getOnlineUserVM().getObservableOfOnLineUserCount()
                .observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<Integer>() {
                    @Override
                    public void accept(Integer integer) throws Exception {
                        mDataBind.tvLiveUserCount.setText("" + integer);
                    }
                }));
    }

    private void onUpdateChat(IMessageModel iMessageModel) {
        if (isShowBarrage) {
            mBarrAgeAdapter.add(new BarrageData(iMessageModel.getContent(), null, 0, 1));
            stopTimer(true);
        }
    }

    /**
     * 处理拉流信息
     *
     * @param mediaModel
     */
    private void handleMediaPublish(IMediaModel mediaModel) {
        if (mediaModel != null) {
            LogExtKt.logE("拉流了" + mediaModel.getVideoDefinitions().toString(), "百家云");
            LogExtKt.logE("是否有音频" + mediaModel.isAudioOn(), "百家云");
            LogExtKt.logE("是否有视频" + mediaModel.isVideoOn(), "百家云");

            if (!mediaModel.isVideoOn() && mediaModel.isAudioOn() && mediaModel.getUser().getType() == LPConstants.LPUserType.Teacher) {//说明关闭了摄影头
                mDataBind.tvLiveFinishCourseName.setText("语音直播中");
                mDataBind.ivLogo.setImageResource(R.drawable.icon_live_audio);
                mDataBind.llLiveFinishLayout.setVisibility(View.VISIBLE);
                return;
            } else if (!mediaModel.isVideoOn() && !mediaModel.isAudioOn() && mediaModel.getUser().getType() == LPConstants.LPUserType.Teacher) {
                mDataBind.tvLiveFinishCourseName.setText("老师正在赶来，请稍后~");
                mDataBind.ivLogo.setImageResource(R.drawable.icon_live_finish);
                mDataBind.llLiveFinishLayout.setVisibility(View.VISIBLE);
                return;
            }

            //设置分辨率的模式
            lpPlayer.setCDNResolution(LPConstants.LPCDNResolution.ORIGINAL);
            LPVideoView remoteVideoView = getRemoteVideoView(mediaModel.getMediaId());
            if (remoteVideoView != null) {
                lpPlayer.playAVClose(mediaModel.getMediaId());
                if (mediaModel.isVideoOn()) {
                    lpPlayer.playVideo(mediaModel.getMediaId(), remoteVideoView);
                } else if (mediaModel.isAudioOn()) {
                    lpPlayer.playAudio(mediaModel.getMediaId());
                } else {
                    lpPlayer.playAVClose(mediaModel.getMediaId());
                    View toRemoveVideoView = getRemoteVideoView(mediaModel.getMediaId());
                    if (toRemoveVideoView != null) {
                        remoteVideoContainerLl.removeView(toRemoveVideoView);
                    }
                }
            } else {
                if (mediaModel.isVideoOn() || mediaModel.isAudioOn()) {
                    remoteVideoView = createRemoteVideoView();
                    remoteVideoView.setTag(mediaModel.getMediaId());
                    remoteVideoView.setOnClickListener(view -> {
                    });
                    remoteVideoContainerLl.addView(remoteVideoView, layoutParams);
                    if (mediaModel.isVideoOn()) {
                        lpPlayer.playVideo(mediaModel.getMediaId(), remoteVideoView);
                    } else {
                        lpPlayer.playAudio(mediaModel.getMediaId());
                    }
                }
            }
            if (liveRoom.isClassStarted()) {
                mDataBind.llLiveFinishLayout.setVisibility(View.GONE);
            }
        }

    }


    private LPVideoView createRemoteVideoView() {
        LPVideoView lpVideoView;
        lpVideoView = new LPVideoView(this);
        lpVideoView.setViewType(LPConstants.LPVideoViewType.SURFACE_VIEW);
        lpVideoView.setAspectRatio(LPConstants.LPAspectRatio.Fill);
        return lpVideoView;
    }


    private LPVideoView getRemoteVideoView(String mediaId) {
        for (int i = 0; i < remoteVideoContainerLl.getChildCount(); i++) {
            View childView = remoteVideoContainerLl.getChildAt(i);
            if ((childView instanceof LPVideoView) && childView.getTag().equals(mediaId)) {
                return (LPVideoView) childView;
            }
        }
        return null;
    }

    @Override
    protected void onDestroy() {
        if (lpPlayer != null) {
            lpPlayer.release();
        }
        if (compositeDisposable != null) {
            compositeDisposable.clear();
        }
        if (liveRoom != null) {
            liveRoom.quitRoom();
        }
        if (timer != null) {
            timer.cancel();
        }
        if (timerTask != null) {
            timerTask.cancel();
        }
        super.onDestroy();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (onMyKeyDown(event.getKeyCode(), event)) { // 加一层判断，实现android 9 以及其他的情况
            return true;
        }
        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_UP:
                if (mDataBind.flController.getVisibility() == View.VISIBLE && liveRoom.isClassStarted() && currentFocusType == focus_danku) {
                    return true;
                }
                break;
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                if (mDataBind.flController.getVisibility() == View.VISIBLE && liveRoom.isClassStarted() && currentFocusType == focus_danku && dankuPostion == 1) {
                    return true;
                }
                if (mDataBind.flController.getVisibility() == View.VISIBLE && liveRoom.isClassStarted() && currentFocusType == focus_qrcode && qrCodePosition == 1) {
                    return true;
                }
                break;
            case KeyEvent.KEYCODE_DPAD_LEFT:
                if (mDataBind.flController.getVisibility() == View.VISIBLE && liveRoom.isClassStarted() && currentFocusType == focus_danku && dankuPostion == 0) {
                    return true;
                }
                if (mDataBind.flController.getVisibility() == View.VISIBLE && liveRoom.isClassStarted() && currentFocusType == focus_qrcode && qrCodePosition == 0) {
                    return true;
                }
                break;
            case KeyEvent.KEYCODE_DPAD_DOWN:
                if (mDataBind.flController.getVisibility() == View.GONE && liveRoom.isClassStarted()) {
//                if (mDataBind.flController.getVisibility() == View.GONE) {
                    mDataBind.flController.setVisibility(View.VISIBLE);
                    mDataBind.flController.post(new Runnable() {
                        @Override
                        public void run() {
                            new CommonViewUtil().controllerViewAction(true, mDataBind.flController, commonSubTitleAdapter.getViewByPosition(0, R.id.rl_common_item_view), ControllerViewState.VIEW_CONTROLLER_COMMON);
                        }
                    });
                }
                break;
            case KeyEvent.KEYCODE_BACK:
                if (mDataBind.flController.getVisibility() == View.VISIBLE) {
                    mDataBind.flController.setVisibility(View.GONE);
                    return true;
                }
                break;
        }
        return super.onKeyDown(keyCode, event);
    }
}