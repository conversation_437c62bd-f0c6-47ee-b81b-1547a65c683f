package com.panda.course.config.base

import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.view.KeyEvent
import android.view.LayoutInflater
import androidx.databinding.ViewDataBinding
import com.mallotec.reb.localeplugin.utils.LocaleHelper
import com.noober.background.BackgroundLibrary
import com.panda.course.ext.*
import com.panda.course.receiver.ServerManager
import com.panda.course.ui.activity.*
import com.panda.course.ui.activity.dialog.CourseImgDialogActivity
import com.panda.course.ui.activity.dialog.SkillRecordActivity
import com.panda.course.util.DeviceSearchResponser
import com.panda.course.util.RxTimerUtil
import com.panda.course.util.TrafficBean
import io.reactivex.rxjava3.disposables.Disposable
import java.lang.ref.WeakReference
import java.lang.reflect.ParameterizedType


/**
 * 在Base处理一下 的生命周期
 */
abstract class BaseDbActivity<VM : BaseViewModel, DB : ViewDataBinding>() : BaseVmActivity<VM>(), BaseIView {

    //使用了DataBinding 就不需要 layoutId了，因为 会从DB泛型 找到相关的view
    override val layoutId: Int = 0

    lateinit var mDataBind: DB

    protected var mRestReminder = 0//提醒用户该休息了 tips:6个小时提醒 （今天已经看了6小时的课程了，休息一下吧）暂停视频播放 60*60*6=21600秒

    // 记录
    var disposable: Disposable? = null
    var disposableDownload: Disposable? = null

    // 是否有宣传片,默认false
    var isPropagandaVideo = false

    private var mStateFlag = false

    private var trafficBean: TrafficBean? = null

    var mTime: Long = 0 // 后台设置的时间

    var mInitServer = false //记录是否初始化过

    private lateinit var mHandler: MyHandler
    private var eatKeyEvent = false
    private val EATKEYEVENT = 10010
    private val keyEventTime = 800
    private val maxClickDuration = 80

    private class MyHandler(activity: BaseDbActivity<*, *>) : Handler() {
        private val mActivity: WeakReference<BaseDbActivity<*, *>> = WeakReference(activity)

        override fun handleMessage(msg: Message) {
            val activity = mActivity.get()
            activity?.handleMessage(msg)
        }
    }


    private fun handleMessage(msg: Message) {
        when (msg.what) {
            EATKEYEVENT -> eatKeyEvent = false
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        initDataBind()
        super.onCreate(savedInstanceState)
        if (!mInitServer) {
            startHttpServer()
        }
        mHandler = MyHandler(this)
    }


    /**
     * 创建DataBinding
     */
    private fun initDataBind() { //利用反射 根据泛型得到 ViewDataBinding
        val superClass = javaClass.genericSuperclass
        val aClass = (superClass as ParameterizedType).actualTypeArguments[1] as Class<*>
        BackgroundLibrary.inject(this)
        val method = aClass.getDeclaredMethod("inflate", LayoutInflater::class.java)
        mDataBind = method.invoke(null, layoutInflater) as DB
        dataBindView = mDataBind.root
        mDataBind.lifecycleOwner = this
    }

    /**
     * 启动服务
     */
     fun startHttpServer() {
        val serverManager = ServerManager()
        serverManager.register()
        serverManager.startServer()
        //启动响应局域网检索
        DeviceSearchResponser.open()
        mInitServer = true
    }

    fun goPropagandaVideo() {
        if (isPropagandaVideo) {
            pausePropaganda()
            if (mTime <= 0 || mStateFlag) {
                return
            }
            disposable = RxTimerUtil.interval(((1000 * 60) * mTime)).subscribe {
                mStateFlag = false
                if (currentActivity is PlayerActivity || currentActivity is MainActivity || currentActivity is PropagandaActivity || currentActivity is OtherPlayActivity || currentActivity is PlayerLiveActivity) {
                    pausePropaganda()
                } else {
                    toStartActivity(PropagandaActivity::class.java)
                }
            } as Disposable
            mStateFlag = true //已经创建任务了
        }
        startDownloading()
    }

    /**
     * 空闲时执行下载任务 5分钟后空闲执行此任务
     */
    fun startDownloading() {
        pauseDownLoading()
        disposableDownload = RxTimerUtil.interval((1000 * 60 * 2)).subscribe { // 默认2分钟执行一次
            if (currentActivity is MainActivity || currentActivity is PlayerActivity || currentActivity is PropagandaActivity || currentActivity is OtherPlayActivity || currentActivity is PlayerLiveActivity) {
                pauseDownLoading()
            } else {
                startDownloadVideo()
            }
        } as Disposable
    }

    /**
     * 多语言设置
     */
    override fun applyOverrideConfiguration(overrideConfiguration: Configuration?) {
        overrideConfiguration?.setLocale(LocaleHelper.getInstance().getSetLocale())
        super.applyOverrideConfiguration(overrideConfiguration)
    }

    open fun startDownloadVideo() {}


    private fun pausePropaganda() {
        disposable?.dispose()
        disposable = null
    }

    private fun pauseDownLoading() {
        eventViewModel.appUserFreeTime.value = false
        disposableDownload?.dispose()
        disposableDownload = null
    }


    fun hookWebView() {
        val sdkInt = Build.VERSION.SDK_INT
        try {
            val factoryClass = Class.forName("android.webkit.WebViewFactory")
            val field = factoryClass.getDeclaredField("sProviderInstance")
            field.isAccessible = true
            var sProviderInstance = field.get(null)
            if (sProviderInstance != null) {
                ("sProviderInstance isn't null").logE()
                return
            }
            val getProviderClassMethod = if (sdkInt > 22) {
                factoryClass.getDeclaredMethod("getProviderClass")
            } else if (sdkInt == 22) {
                factoryClass.getDeclaredMethod("getFactoryClass")
            } else {
                ("Don't need to Hook WebView").logE()
                return
            }
            getProviderClassMethod.isAccessible = true
            val factoryProviderClass =
                getProviderClassMethod.invoke(factoryClass) as Class<*>
            val delegateClass =
                Class.forName("android.webkit.WebViewDelegate")
            val delegateConstructor = delegateClass.getDeclaredConstructor()
            delegateConstructor.isAccessible = true
            if (sdkInt < 26) { //低于Android O版本
                val providerConstructor =
                    factoryProviderClass.getConstructor(delegateClass)
                providerConstructor.isAccessible = true
                sProviderInstance =
                    providerConstructor.newInstance(delegateConstructor.newInstance())
            } else {
                val chromiumMethodName =
                    factoryClass.getDeclaredField("CHROMIUM_WEBVIEW_FACTORY_METHOD")
                chromiumMethodName.isAccessible = true
                val chromiumMethodNameStr =
                    chromiumMethodName.get(null) as String
                val staticFactory =
                    factoryProviderClass.getMethod(chromiumMethodNameStr, delegateClass)
                if (staticFactory != null) {
                    sProviderInstance =
                        staticFactory.invoke(null, delegateConstructor.newInstance())
                }
            }
            field.set("sProviderInstance", sProviderInstance)
            ("Hook success!").logE()
        } catch (e: Throwable) {
            ("catch $e").logE()
        }
    }


    open fun onMyKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        when (keyCode) {

            KeyEvent.KEYCODE_HOME -> {
                if (currentActivity is MainActivity) {
                    finishOtherMainActivity()
                } else {
                    finishOtherActivity()
                }
                return true
            }
            KeyEvent.KEYCODE_MENU -> {

                if (currentActivity is SettingsActivity || currentActivity is BleActivity || currentActivity is RemoteControlActivity
                    || currentActivity is PlayerActivity || currentActivity is OtherPlayActivity || currentActivity is PropagandaActivity
                ) {
                    return true
                }

                toStartActivity(SettingsActivity::class.java)
                return true
            }
            KeyEvent.KEYCODE_F5,
            KeyEvent.KEYCODE_F7,
            KeyEvent.KEYCODE_TV_INPUT_VGA_1 -> {//适配不同的遥控器
                onLoadRetry()
                return true
            }
        }
        return false
    }


    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (currentActivity is PlayerActivity || currentActivity is SkillRecordActivity || currentActivity is ExamRankActivity ||
            currentActivity is CourseImgDialogActivity
        ) {
            return super.dispatchKeyEvent(event)
        }
        if (eatKeyEvent) {
            return true
        } else {
            if (event.repeatCount >= 1) {
                eatKeyEvent = true
                mHandler.removeMessages(EATKEYEVENT)
                val msg = mHandler.obtainMessage(EATKEYEVENT)
                mHandler.sendMessageDelayed(msg, keyEventTime.toLong())
            } else {
//                    val currentTime = System.currentTimeMillis()
//                    if (currentTime - lastClickTime < maxClickDuration) {
//                        // Single click duration exceeds the limit
//                        return true
//                    }
//                    lastClickTime = currentTime
                return super.dispatchKeyEvent(event)
            }
        }
        return super.dispatchKeyEvent(event)
    }


    override fun onDestroy() {
        super.onDestroy()
        mHandler.removeCallbacksAndMessages(null)
    }
}