package com.panda.course.ui.activity.dialog

import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.animation.Animation
import com.panda.course.R
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityGraduateBinding
import com.panda.course.entity.GraduateListEntity
import com.panda.course.entity.GraduateListImgEntity
import com.panda.course.entity.GraduateStudentListImgEntity
import com.panda.course.entity.PictureGroup
import com.panda.course.ext.*
import com.panda.course.ui.adapter.GraduateBigAdapter
import com.panda.course.ui.adapter.MultipleTypesAdapter
import com.panda.course.ui.adapter.ViewPager2Adapter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.util.AnimationUtils
import com.panda.course.util.NetworkUtil
import com.tencent.liteav.demo.superplayer.SuperPlayerDef
import com.youth.banner.config.IndicatorConfig
import com.youth.banner.indicator.CircleIndicator
import java.text.SimpleDateFormat

/**
 * 毕业表勋
 */
class GraduateDialogActivity : BaseDbActivity<CourseViewModel, ActivityGraduateBinding>() {


    private var mAdapter: GraduateBigAdapter? = null

    private var mCurrentIndex = 1 //从1 开始

    private var mCourseName: String? = null

    private var jump_type = ""


    override fun initView(savedInstanceState: Bundle?) {
        intent.extras?.apply {
            mCourseName = getString("mCourseName")
            if ("interactive" == getString("jump_type")) {//是从师生互动过来的，从intent获取数据模型
                jump_type = getString("jump_type").toString()
                mCurrentIndex = getInt("position")
                val urls = getStringArrayList("data_urls")
                if (!urls.isNullOrEmpty()) {
                    initInteractiveBanner(urls)
                }
            } else if ("map" == getString("jump_type")) {//从毕业表彰过来的
                mCurrentIndex = getInt("position")
                val entity = getParcelable<GraduateListEntity>("data")
                initGraduate(entity, getInt("focusType"))
            } else {
                mViewModel.getGraduationList("1", "")
            }
        }

        if (!NetworkUtil.isAvailable(this)) {
            "当前无网络，请检查当前网络情况是否正常".toast()
            finish()
        }
        mDataBind.banner.addBannerLifecycleObserver(this)
            .setUserInputEnabled(false)
            .isAutoLoop(false)
            .indicator = CircleIndicator(this)
        taskHideIndicator()
        mDataBind.llProgress.postDelayed({
            mDataBind.llProgress.gone()
            taskButton()
        }, 2000)
    }

    /**
     * 从毕业表彰 做个处理，重新
     */
    private fun initGraduate(entity: GraduateListEntity?, focusType: Int) {
        if (entity != null) {
            initBanner(entity.list, entity.student_list, false, focusType)
            mDataBind.banner.currentItem = mCurrentIndex
        }
    }

    /**
     * 从师生互动过来的
     */
    private fun initInteractiveBanner(urls: ArrayList<String>) {
        val mList = ArrayList<GraduateListImgEntity>()
        for (i in urls.indices) {
            mList.add(GraduateListImgEntity(urls[i]))
        }
        initBanner(mList, null, true, 0)
    }

    /**
     * 执行runnable
     */
    private var mHideButtonRunnable = Runnable {
        mDataBind.butBack.clearAnimation()
        mDataBind.butBack.startAnimation(
            AnimationUtils.getHiddenAlphaAnimation(1000, object : Animation.AnimationListener {

                override fun onAnimationStart(animation: Animation?) {}

                override fun onAnimationEnd(animation: Animation?) {
                    mDataBind.butBack.gone()
                }

                override fun onAnimationRepeat(animation: Animation?) {}
            })
        )
    }

    /**
     * 3秒后隐藏这个控制器
     */
    private fun taskButton() {
        mDataBind.butBack.visible()
        mDataBind.butBack.removeCallbacks(mHideButtonRunnable)
        mDataBind.butBack.postDelayed(mHideButtonRunnable, 2000)
    }

    private fun initBanner(mList: List<GraduateListImgEntity>, names: List<GraduateStudentListImgEntity>? = null, isHidePictures: Boolean, focusType: Int) {
        "总数是 = ${mList.size}".logE()
        mAdapter = GraduateBigAdapter(this, mCourseName, mList, names, isHidePictures, jump_type, focusType)
        mDataBind.banner.setAdapter(mAdapter)
    }

    override fun onRequestSuccess() {
        super.onRequestSuccess()
        mViewModel.mGraduationList.observe(this, androidx.lifecycle.Observer {
            initBanner(it.list, null, false, 0)
        })
    }

    /**
     * 执行runnable
     */
    private var mHideBannerIndicatorRunnable = Runnable {
        mDataBind.banner.removeIndicator()
    }


    /**
     * 3秒后隐藏这个控制器
     */
    private fun taskHideIndicator() {
        mDataBind.banner.indicator = CircleIndicator(this)
        mDataBind.banner.removeCallbacks(mHideBannerIndicatorRunnable)
        mDataBind.banner.postDelayed(mHideBannerIndicatorRunnable, 4000)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_DPAD_LEFT -> {
                appUMEvent(UMConstant.GRADUATE_COMMEND_CHANGE)
                if (mDataBind.banner.realCount > 1) {
                    if (mCurrentIndex > 1) {
                        mCurrentIndex -= 1
                        mDataBind.banner.currentItem = mCurrentIndex
                    }
                    "_LEFT = $mCurrentIndex".logE()
                    appUMEvent(UMConstant.GRADUATION_COMMENTD_NEXT)
                }
                taskHideIndicator()
                return true
            }
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                appUMEvent(UMConstant.GRADUATE_COMMEND_CHANGE)
                if (mDataBind.banner.realCount > 1) {
                    if (mCurrentIndex == mDataBind.banner.realCount) {
                        "_RIGHT = $mCurrentIndex".logE() //                        mCurrentIndex = 1
                        //                        mDataBind.banner.currentItem = mCurrentIndex
                    } else {
                        mCurrentIndex += 1
                        "_RIGHT = $mCurrentIndex , = ${mDataBind.banner.realCount}".logE()
                        mDataBind.banner.currentItem = mCurrentIndex
                    }
                    appUMEvent(UMConstant.GRADUATION_COMMENTD_NEXT)
                }
                taskHideIndicator()
                return true
            }
            KeyEvent.KEYCODE_MENU -> {
                goSystemTvBoxSetting(this)
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onDestroy() {
        super.onDestroy()
        mDataBind.banner.stop()
    }

}