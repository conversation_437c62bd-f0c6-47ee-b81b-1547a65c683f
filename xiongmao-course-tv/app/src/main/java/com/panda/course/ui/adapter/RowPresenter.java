package com.panda.course.ui.adapter;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.leanback.widget.Presenter;

import com.bumptech.glide.Glide;
import com.panda.course.R;
import com.panda.course.entity.RowStringEntity;
import com.panda.course.widget.focus.MyFocusHighlightHelper;

import static androidx.leanback.widget.FocusHighlight.ZOOM_FACTOR_SMALL;

public class RowPresenter extends Presenter {

    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent) {
        View inflate = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_row, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(ZOOM_FACTOR_SMALL, false);
        }
        return new Holder(inflate);
    }

    @Override
    public void onBindViewHolder(ViewHolder viewHolder, Object item) {
        Holder holder = (Holder) viewHolder;
        if (item instanceof RowStringEntity) {
            Log.i("->>", "onBindViewHolder: 加载数据");
            RowStringEntity data = (RowStringEntity) item;
            Glide.with(((Holder) viewHolder).mImageView.getContext())
                    .load(data.getCover_url())
                    .into(holder.mImageView);
            holder.mCardView.setOnFocusChangeListener((v, hasFocus) -> {

                if (mBrowseItemFocusHighlight != null) {
                    mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
                }

                holder.mImageView.setBackground(hasFocus ? ContextCompat.getDrawable(holder.mCardView.getContext(), R.drawable.base_border_selecter) :
                        ContextCompat.getDrawable(holder.mCardView.getContext(), R.drawable.base_border));
            });
        } else {
            Log.i("->>", "onBindViewHolder: 数据不对");
        }
    }

    @Override
    public void onUnbindViewHolder(ViewHolder viewHolder) {

    }

    public static class Holder extends ViewHolder {
        private ImageView mImageView;
        private CardView mCardView;

        public Holder(View view) {
            super(view);
            mImageView = view.findViewById(R.id.iv_item_row);
            mCardView = view.findViewById(R.id.row_card_view);
        }
    }
}
