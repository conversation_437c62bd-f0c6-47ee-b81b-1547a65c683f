package com.panda.course.util;

import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.panda.course.R;
import com.panda.course.config.ConstantMMVK;
import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.LogExtKt;
import com.tencent.qcloud.tuicore.util.MD5Utils;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;

import static com.panda.course.ext.LocalUtilExtKt.findLocalMp3;

/**
 * 播放mp3
 * 先判断本地有没有这个url的mp3
 * 如果有就播放本地的模式
 * 如果没有就播放网络的
 */
public class MediaHelper {
    private static MediaPlayer mPlayer;
    private static boolean isPause = false;

    public static void playSound(Context context, String mp3, MediaPlayer.OnCompletionListener listener) {
        if (mPlayer == null) {
            mPlayer = new MediaPlayer();
        } else {
            mPlayer.reset();
        }
        mPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
        mPlayer.setOnCompletionListener(listener);
        mPlayer.setOnErrorListener((mp, what, extra) -> {
            mPlayer.reset();
            return false;
        });
        try {
            if (!TextUtils.isEmpty(findLocalMp3(mp3))) {
                mPlayer.setDataSource(context, Uri.parse(findLocalMp3(mp3)));//播放本地mp3
                LogExtKt.logE("播放本地的", "mp3");
            } else {
                mPlayer.setDataSource(mp3);
                LogExtKt.logE("播放在线的", "mp3");
            }
            mPlayer.setLooping(true);
            mPlayer.prepare();
            mPlayer.start();

        } catch (Exception e) {
            e.printStackTrace();
            LogExtKt.logE("" + e.getMessage(), "播放Mp3");
        }
    }


    public static void start() {
        if (mPlayer != null) {
            mPlayer.start();
            isPause = false;
        }
    }

    public static void pause() {
        if (mPlayer != null && mPlayer.isPlaying()) {
            mPlayer.pause();
            isPause = true;
        }
    }

    public static void stop() {
        if (mPlayer != null && mPlayer.isPlaying()) {
            mPlayer.stop();
            isPause = true;
        }
    }

    // 继续
    public static void resume() {
        if (mPlayer != null && isPause) {
            mPlayer.start();
            isPause = false;
        }
    }

    public static void release() {
        if (mPlayer != null) {
            mPlayer.release();
            mPlayer = null;
        }
    }
}
