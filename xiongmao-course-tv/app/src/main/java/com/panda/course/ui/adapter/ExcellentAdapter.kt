package com.panda.course.ui.adapter

import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.leanback.widget.FocusHighlight.ZOOM_FACTOR_MEDIUM
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.config.base.appContext
import com.panda.course.entity.ExcellentRankListEntity
import com.panda.course.ext.dp2px
import com.panda.course.ext.gone
import com.panda.course.ext.visible
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper

class ExcellentAdapter : BaseQuickAdapter<ExcellentRankListEntity, BaseViewHolder>(R.layout.item_row_excellent) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var onItemFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null


    fun setOnViewFocus(onItemFocus: OnViewFocus?) {
        this.onItemFocus = onItemFocus
    }

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(ZOOM_FACTOR_MEDIUM, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()
    }

    override fun convert(holder: BaseViewHolder, item: ExcellentRankListEntity) {

        val frameLayout = holder.getView<FrameLayout>(R.id.fl_item_layout)
        val imageView = holder.getView<ImageView>(R.id.iv_item_pic)

        if (!item.seize_seat) {
            holder.getView<TextView>(R.id.tv_item_index_tag).visible()
            imageView.visible()
            frameLayout.alpha = 1.0f

            Glide.with(appContext).asBitmap().load(item.avatar)
                .apply(RequestOptions.bitmapTransform(CircleCrop()))
                .placeholder(R.drawable.def_pic).error(R.drawable.def_pic)
                .into(imageView)

            holder.setText(R.id.iv_item_name, "" + item.service_personnel_name)
                .setText(R.id.iv_item_content, "答题平均分：${item.avg_score}\n答题正确率：${item.right_percent}")
                .setText(R.id.tv_item_index_tag, "${getItemPosition(item) + 4}")
        } else {//这里是占位图，需要透明度设置0.5f
            holder.setText(R.id.iv_item_content, "")
                .setText(R.id.iv_item_name, "")

            holder.getView<TextView>(R.id.tv_item_index_tag).gone()
            frameLayout.alpha = 0.1f
            imageView.gone()
        }

        frameLayout.setOnFocusChangeListener { v, hasFocus ->
            mBrowseItemFocusHighlight?.onItemFocused(frameLayout, hasFocus)

            if (onItemFocus != null) {
                onItemFocus!!.onChangeFocus(hasFocus, getItemPosition(item), v)
            }

            if (hasFocus) {
                if (recyclerView != null) {
                    val amount = getScrollAmount(recyclerView, v) //计算需要滑动的距离
                    //滑动到指定距离
                    scrollToAmount(recyclerView, amount!![0], amount[1])
                }
            }
//            val layoutParams = FrameLayout.LayoutParams(dp2px(173f), dp2px(150f))
//            if (getItemPosition(item) == data.size - 1) {
//                layoutParams.setMargins(dp2px(16f), dp2px(16f), dp2px(16f), dp2px(16f))
//            } else {
//                layoutParams.setMargins(dp2px(16f), dp2px(16f), dp2px(0f), dp2px(16f))
//            }
//            frameLayout.layoutParams = layoutParams
        }
    }


    fun setRecycler(recyclerView: RecyclerView?) {
        this.recyclerView = recyclerView!!
    }

    //根据坐标滑动到指定距离
    private fun scrollToAmount(recyclerView: RecyclerView, dx: Int, dy: Int) {
        //如果没有滑动速度等需求，可以直接调用这个方法，使用默认的速度
        recyclerView.smoothScrollBy(dx, dy)
    }

    /**
     * 计算需要滑动的距离,使焦点在滑动中始终居中
     *
     * @param recyclerView
     * @param view
     */
    private fun getScrollAmount(recyclerView: RecyclerView, view: View): IntArray? {
        val out = IntArray(2)
        val parentLeft = recyclerView.paddingLeft
        val parentTop = recyclerView.paddingTop
        val parentRight = recyclerView.width - recyclerView.paddingRight
        val childLeft = view.left + 0 - view.scrollX
        val childTop = view.top + 0 - view.scrollY
        val dx = childLeft - parentLeft - (parentRight - view.width) / 2 //item左边距减去Recyclerview不在屏幕内的部分，加当前Recyclerview一半的宽度就是居中
        val dy = childTop - parentTop - (parentTop - view.height) / 2 //同上
        out[0] = dx
        out[1] = dy
        return out
    }

}