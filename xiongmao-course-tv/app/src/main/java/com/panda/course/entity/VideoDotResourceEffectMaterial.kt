package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class VideoDotResourceEffectMaterial(
    var background: String? = null,
    var countdown_color: String? = null,
    var stat_count_color: String? = null,
    var stat_count_bg_color: String? = null,
    var right_act_color: String? = null,
    var right_act_bg_color: String? = null,
    var background_voice: String? = null,
    var human: String? = null,
    var countdown_time: String? = null,
    var question: Question? = null,
    var txt_list: List<TextList> = emptyList(),
) : Parcelable



