package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class LiveCourseEntity(
    var code: String? = null,
    var title_main: String? = null,
    var course_introduction: String? = null,
    var course_cover_image: String? = null,
    var course_status: String? = null,
    var course_status_name: String? = null,
    var examination_start_time: String? = null,
    var examination_end_time: String? = null,
    var course_video_status: String? = null,
    var course_video_status_name: String? = null,
    var video_introduction: String? = null,
    var show_status_name: String? = null,
) : Parcelable
