package com.panda.course.ui.activity.dialog

import android.graphics.Color
import android.net.http.HttpResponseCache.install
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import com.king.app.updater.AppUpdater
import com.king.app.updater.UpdateConfig
import com.king.app.updater.callback.UpdateCallback
import com.king.app.updater.http.OkHttpManager
import com.king.app.updater.util.AppUtils
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityUpdateDialogBinding
import com.panda.course.entity.UpdateInfo
import com.panda.course.ext.*
import com.panda.course.receiver.InstallResultReceiver
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.util.ApkController.install
import com.panda.course.util.InstallUtils
import com.panda.course.util.PackageManagerCompatP
import java.io.File

/**
 *  /storage/emulated/0/Android/data/com.panda.course/files/apk/
 */
class UpdateDialogActivity : BaseDbActivity<CourseViewModel, ActivityUpdateDialogBinding>() {

    private val TAG = "UpdateDialogActivity"

    private var updateInfo: UpdateInfo? = null
    private var mAppUpdater: AppUpdater? = null
    private var commitCount: Int = 0
    private var cancelCount: Int = 0
    private var mDownloadState = false
    private var mDownloadFile: File? = null

    override fun initView(savedInstanceState: Bundle?) {
        mViewModel.updateAppVersion()
    }

    override fun onResume() {
        super.onResume()
        if (mDownloadState && mDownloadFile != null) {//说明用户主动返回了，没去安装
            mDataBind.llSelectLayout.visible()
            mDataBind.rlUpdateProgress.gone()
        }
    }

    override fun onRequestSuccess() {
        super.onRequestSuccess()
        mViewModel.mUpdateInfo.observe(this, androidx.lifecycle.Observer { updateInfo: UpdateInfo? ->
            if (updateInfo == null) {
                eventViewModel.appUpdate.value = null
                finish()
                return@Observer
            }

            this.updateInfo = updateInfo

            mDataBind.butDialogTitle.text = "发现新版本\n" + updateInfo.version
            mDataBind.butDialogDesc.text = updateInfo.desc


            val sdcardDir: File? = this.getExternalFilesDir(null)
            val mApkFile = File(sdcardDir?.path + "/apk/")
            getAllDataFileName(mApkFile)


            if ("1" == updateInfo.upgrade_demand) {
                mDataBind.butDialogCancel.visibility = View.GONE
            }
        })
    }

    /**
     * 删除本地之前下载好的Apk
     */
    fun getAllDataFileName(makeDataFile: File?) {
        val collectionPath: String = makeDataFile?.path ?: ""
        if (!TextUtils.isEmpty(collectionPath)) {
            val file = File(collectionPath)
            val tempList = file.listFiles()
            if (tempList != null && tempList.size > 1) {
                for (i in tempList.indices) {
                    if (tempList[i].isFile) {
                        tempList[i].delete()
                    }
                }
            }
        }
    }

    override fun initObserver() {
        mDataBind.butDialogCommit.requestFocus()
        mDataBind.butDialogCancel.onFocusChangeListener = View.OnFocusChangeListener { _: View, b: Boolean ->
            mDataBind.butDialogCancel.setTextColor(if (b) Color.parseColor("#FFFFFF") else Color.parseColor("#06B07F"))
        }
        mDataBind.butDialogCommit.onFocusChangeListener = View.OnFocusChangeListener { _: View, b: Boolean ->
            mDataBind.butDialogCommit.setTextColor(if (b) Color.parseColor("#FFFFFF") else Color.parseColor("#06B07F"))
        }

        mDataBind.butDialogCancel.setOnClickListener { finish() }
        mDataBind.butDialogCommit.setOnClickListener {
            if (mDownloadState && mDownloadFile != null) {//说明用户主动返回了，没去安装
                if (getCurrentAndroid9()) {
                    InstallUtils.installApk(this@UpdateDialogActivity, mDownloadFile?.absolutePath)
                } else {
                    InstallUtils.installApk(this, mDownloadFile?.absolutePath)
                }
            } else {
                if (!TextUtils.isEmpty(updateInfo?.url)) {
                    startDownloadApk()
                }
            }
        }
    }

    private fun startDownloadApk() {
        mDataBind.rlUpdateProgress.visibility = View.VISIBLE
        mDataBind.llSelectLayout.visibility = View.GONE
        val config = UpdateConfig()
        config.url = updateInfo?.url
        config.authority = "${getPackageNameName(this)}.fileprovider"
        config.isInstallApk = !getCurrentAndroid9() //判断是不是android9的设备
        mAppUpdater = AppUpdater(this, config).setHttpManager(OkHttpManager.getInstance())
            .setUpdateCallback(object : UpdateCallback {
                override fun onDownloading(isDownloading: Boolean) {}
                override fun onStart(url: String) {
                    mDataBind.tvProgress.text = "正在获取下载数据中"
                }

                override fun onProgress(progress: Long, total: Long, isChange: Boolean) {
                    if (isChange) {
                        val mCurrProgress = (progress * 1.0f / total * 100.0f).toInt()
                        mDataBind.tvProgress.text = "正在下载...${mCurrProgress}%"
                        mDataBind.progressBar.progress = mCurrProgress
                    }
                }

                override fun onFinish(file: File) {
                    "${file.absolutePath}".logD(TAG)
                    mDataBind.tvProgress.text = "下载完成...100%"
                    mDownloadFile = file
                    mDownloadState = true
                    if (getCurrentAndroid9()) {
                        InstallUtils.installApk(this@UpdateDialogActivity, mDownloadFile?.absolutePath)
//                        PackageManagerCompatP.install(this@UpdateDialogActivity, mDownloadFile?.absolutePath, packageManager)
//                        InstallUtils.installs(file.absolutePath)
                    }
                }

                override fun onError(e: Exception) {
                    mAppUpdater?.start()
                }

                override fun onCancel() {
                }
            })
        mAppUpdater?.start()
    }

    private fun downUpAction(): Boolean {
        if (mDataBind.butDialogCancel.isFocused) {
            mDataBind.butDialogCancel.clearAnimation()
            mDataBind.butDialogCancel.startAnimation(mShakeYAnimation)
            return true
        }
        if (mDataBind.butDialogCommit.isFocused) {
            mDataBind.butDialogCommit.clearAnimation()
            mDataBind.butDialogCommit.startAnimation(mShakeYAnimation)
            return true
        }
        return false
    }


    override fun onDestroy() {
        super.onDestroy()
        mAppUpdater?.stop()
    }

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        goPropagandaVideo()

        if (onMyKeyDown(event.keyCode, event)) { // 加一层判断，实现android 9 以及其他的情况
            return true
        }


        when (event.keyCode) {
            KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                return true
            }

            KeyEvent.KEYCODE_DPAD_DOWN, KeyEvent.KEYCODE_DPAD_UP -> {
                return downUpAction()
            }
            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (mDataBind.butDialogCommit.isFocused) {
                    commitCount += 1
                    if (commitCount >= 2) {
                        mDataBind.butDialogCommit.clearAnimation()
                        mDataBind.butDialogCommit.startAnimation(mShakeAnimation)
                    }
                }
                cancelCount = 0
            }
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (mDataBind.butDialogCancel.isFocused) {
                    cancelCount += 1
                    if (cancelCount >= 2) {
                        mDataBind.butDialogCancel.clearAnimation()
                        mDataBind.butDialogCancel.startAnimation(mShakeAnimation)
                    }
                }
                commitCount = 0
            }
            KeyEvent.KEYCODE_BACK -> {
                if ("1" == updateInfo?.upgrade_demand) {
                    return true
                }
                mAppUpdater?.stop()
            }
            KeyEvent.KEYCODE_MENU -> {
                goSystemTvBoxSetting(this)
                return true
            }
        }
        return super.dispatchKeyEvent(event)
    }


}