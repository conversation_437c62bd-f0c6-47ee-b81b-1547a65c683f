package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class VideoDotEntity(
    var id: String? = null,
    var seconds: String? = null,
    var effect_type: String? = null,
    var effect_time: String? = null,
    var effect_material: EffectMaterial? = null,

    ) : Parcelable {
    override fun toString(): String {
        return "VideoDotEntity(id=$id, seconds=$seconds, effect_type=$effect_type, effect_time=$effect_time, effect_material=$effect_material)"
    }
}

@Parcelize
data class EffectMaterial(
    var background: String? = null,
    var countdown_color: String? = null,
    var background_voice: String? = null,
    var human: String? = null,
    var countdown_time: String? = null,
    var right_act_bg_color: String? = null,
    var right_act_color: String? = null,
    var stat_count_color: String? = null,
    var stat_count_bg_color: String? = null,
    var question: Question? = null,
    var txt_list: List<TextList> = emptyList()
) : Parcelable

@Parcelize
data class TextList(
    var content: String? = null,
    var is_voice: String? = null,
    var is_txt: String? = null,
    var act_time: String? = null,
) : Parcelable {


    override fun toString(): String {
        return "TextList{" + "content='" + content + '\'' + ", is_voice='" + is_voice + '\'' + ", is_txt='" + is_txt + '\'' + ", act_time='" + act_time + '\'' + '}'
    }
}


@Parcelize
data class Question(
    var title: String? = null,
    var options: List<String> = emptyList(),
    var answer: List<String> = emptyList(),
) : Parcelable