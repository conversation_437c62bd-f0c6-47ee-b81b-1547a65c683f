package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FeedBackConfigEntity(
    var user: CustomUserEntity,
    var help_tag_list: List<HelpTagEntity> = emptyList(),
    var feedback_tag_list: List<FeedBackTagEntity> = emptyList(),
) : Parcelable


@Parcelize
data class CustomUserEntity(
    var name: String? = null,
    var mobile: String? = null,
    var avatar: String? = null,
    var desc: String? = null,
) : Parcelable


@Parcelize
data class HelpTagEntity(
    var id: String? = null,
    var name: String? = null,
) : Parcelable

@Parcelize
data class FeedBackTagEntity(
    var name: String? = null,
) : Parcelable