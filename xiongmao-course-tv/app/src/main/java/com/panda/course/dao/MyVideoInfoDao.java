package com.panda.course.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MY_VIDEO_INFO".
*/
public class MyVideoInfoDao extends AbstractDao<MyVideoInfo, Long> {

    public static final String TABLENAME = "MY_VIDEO_INFO";

    /**
     * Properties of entity MyVideoInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property Code = new Property(1, String.class, "code", false, "CODE");
        public final static Property Watch_time_long = new Property(2, long.class, "watch_time_long", false, "WATCH_TIME_LONG");
        public final static Property Watch_time = new Property(3, long.class, "watch_time", false, "WATCH_TIME");
        public final static Property Ts = new Property(4, String.class, "ts", false, "TS");
        public final static Property Watch_date = new Property(5, String.class, "watch_date", false, "WATCH_DATE");
        public final static Property Version = new Property(6, String.class, "version", false, "VERSION");
    }


    public MyVideoInfoDao(DaoConfig config) {
        super(config);
    }
    
    public MyVideoInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MY_VIDEO_INFO\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"CODE\" TEXT," + // 1: code
                "\"WATCH_TIME_LONG\" INTEGER NOT NULL ," + // 2: watch_time_long
                "\"WATCH_TIME\" INTEGER NOT NULL ," + // 3: watch_time
                "\"TS\" TEXT," + // 4: ts
                "\"WATCH_DATE\" TEXT," + // 5: watch_date
                "\"VERSION\" TEXT);"); // 6: version
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MY_VIDEO_INFO\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, MyVideoInfo entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String code = entity.getCode();
        if (code != null) {
            stmt.bindString(2, code);
        }
        stmt.bindLong(3, entity.getWatch_time_long());
        stmt.bindLong(4, entity.getWatch_time());
 
        String ts = entity.getTs();
        if (ts != null) {
            stmt.bindString(5, ts);
        }
 
        String watch_date = entity.getWatch_date();
        if (watch_date != null) {
            stmt.bindString(6, watch_date);
        }
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(7, version);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, MyVideoInfo entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String code = entity.getCode();
        if (code != null) {
            stmt.bindString(2, code);
        }
        stmt.bindLong(3, entity.getWatch_time_long());
        stmt.bindLong(4, entity.getWatch_time());
 
        String ts = entity.getTs();
        if (ts != null) {
            stmt.bindString(5, ts);
        }
 
        String watch_date = entity.getWatch_date();
        if (watch_date != null) {
            stmt.bindString(6, watch_date);
        }
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(7, version);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public MyVideoInfo readEntity(Cursor cursor, int offset) {
        MyVideoInfo entity = new MyVideoInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // code
            cursor.getLong(offset + 2), // watch_time_long
            cursor.getLong(offset + 3), // watch_time
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // ts
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // watch_date
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6) // version
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, MyVideoInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setCode(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setWatch_time_long(cursor.getLong(offset + 2));
        entity.setWatch_time(cursor.getLong(offset + 3));
        entity.setTs(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setWatch_date(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setVersion(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(MyVideoInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(MyVideoInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(MyVideoInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
