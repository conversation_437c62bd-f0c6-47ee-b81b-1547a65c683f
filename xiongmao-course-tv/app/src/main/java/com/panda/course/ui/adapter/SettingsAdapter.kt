package com.panda.course.ui.adapter

import android.text.TextUtils
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.leanback.widget.FocusHighlight.ZOOM_FACTOR_SMALL
import androidx.leanback.widget.FocusHighlight.ZOOM_FACTOR_XSMALL
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.config.base.Ktx
import com.panda.course.entity.SettingInfo
import com.panda.course.ext.gone
import com.panda.course.ext.visible
import com.panda.course.util.GlideUtil
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper

/**
 * 设置界面
 */
class SettingsAdapter : BaseQuickAdapter<SettingInfo, BaseViewHolder>(R.layout.item_app_settings) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var scaleAnimation: ScaleAnimation? = null

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(ZOOM_FACTOR_SMALL, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()
    }

    override fun convert(holder: BaseViewHolder, item: SettingInfo) {
        val imageView = holder.getView<ImageView>(R.id.iv_item_setting_logo)
        GlideUtil.loadPic(context, item.logo, imageView)

        holder.setText(R.id.tv_item_setting_name, item.name)
            .setText(R.id.tv_item_setting_sub_name, item.subContent)
            .setText(R.id.tv_item_setting_status_name, item.statusName)


        if (!TextUtils.isEmpty(item.rightSubContent)) {
            holder.getView<TextView>(R.id.tv_item_setting_right_sub).visible()
        } else {
            holder.getView<TextView>(R.id.tv_item_setting_right_sub).gone()
        }

        holder.getView<RelativeLayout>(R.id.rl_layout).setOnFocusChangeListener { v, hasFocus ->
            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
        }
    }


}