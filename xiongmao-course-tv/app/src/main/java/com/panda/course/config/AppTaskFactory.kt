package com.panda.course.config

//import com.yanzhenjie.andserver.util.IOUtils
import com.baijiayun.bjyutils.log.LPLogger
import com.baijiayun.livecore.LiveSDK
import com.effective.android.anchors.task.Task
import com.effective.android.anchors.task.project.Project
import com.kingja.loadsir.callback.SuccessCallback
import com.kingja.loadsir.core.LoadSir
import com.panda.course.config.base.appContext
import com.panda.course.ext.getWireMac
import com.panda.course.network.MyHeadInterceptor
import com.panda.course.network.NetHttpClient
import com.panda.course.network.NetUrl
import com.panda.course.network.interception.LogInterceptor
import com.panda.course.state.BaseEmptyCallback
import com.panda.course.state.BaseErrorCallback
import com.panda.course.state.BaseLoadingCallback
import com.panda.course.util.CrashHandler
import com.panda.course.util.im.TUIUtils
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.rtmp.TXLiveBase
import com.tencent.rtmp.TXLiveConstants
import com.umeng.commonsdk.UMConfigure
import rxhttp.wrapper.param.RxHttp
import java.util.*


object TaskCreator : com.effective.android.anchors.task.TaskCreator {
    override fun createTask(taskName: String): Task {
        return when (taskName) {
            InitDefault.TASK_ID -> InitDefault()
            InitNetWork.TASK_ID -> InitNetWork()
            InitComm.TASK_ID -> InitComm()
            InitUtils.TASK_ID -> InitUtils()
            InitIM.TASK_ID -> InitIM()
            InitBugly.TASK_ID -> InitBugly()
            InitUM.TASK_ID -> InitUM()
            InitServer.TASK_ID -> InitServer()
            else -> InitDefault()
        }
    }
}

class InitDefault : Task(TASK_ID, false) {
    companion object {
        const val TASK_ID = "0"
    }

    override fun run(name: String) { // 适配

        // 配置默认的appid
        TXLiveBase.setAppID(ConstantMMVK.DEFAULT_APPID.toString())
        TXLiveBase.setLogLevel(TXLiveConstants.LOG_LEVEL_DEBUG)
    }
}


/**
 * 初始化网络
 */
class InitNetWork : Task(TASK_ID, true) {
    companion object {
        const val TASK_ID = "1"
    }

    override fun run(name: String) {

        //传入自己的OKHttpClient 并添加了自己的拦截器
        RxHttp.init(NetHttpClient.getDefaultOkHttpClient().run {
//            addInterceptor(LogInterceptor()) //添加Log拦截器
            addInterceptor(MyHeadInterceptor()) //添加公共heads 注意要设置在日志拦截器之前，不然Log中会不显示head信息
        }.build())

    }
}


//初始化常用控件类
class InitComm : Task(TASK_ID, true) {
    companion object {
        const val TASK_ID = "2"
    }

    override fun run(name: String) { //注册界面状态管理
        LoadSir.beginBuilder().addCallback(BaseErrorCallback()).addCallback(BaseEmptyCallback())
            .addCallback(BaseLoadingCallback()).setDefaultCallback(SuccessCallback::class.java)
            .commit()
    }
}

//初始化Utils
class InitUtils : Task(TASK_ID, false) {
    companion object {
        const val TASK_ID = "3"
    }

    override fun run(name: String) { //初始化Log打印
    }
}

//初始化IM
class InitIM : Task(TASK_ID, false) {
    companion object {
        const val TASK_ID = "4"
    }

    override fun run(name: String) { //初始化吐司 这个吐司必须要主线程中初始化
        TUIUtils.init(
            appContext,
            if (NetUrl.DEF_URL.contains("test") || NetUrl.DEF_URL.contains("dev")) ConstantMMVK.IM_TEST_KEY else ConstantMMVK.IM_KEY,
            null,
            null
        )
    }
}

//初始化bugly
class InitBugly : Task(TASK_ID, true) {
    companion object {
        const val TASK_ID = "5"
    }

    override fun run(name: String) { //初始化吐司 这个吐司必须要主线程中初始化
        val strategy = CrashReport.UserStrategy(appContext)
        strategy.deviceID = getWireMac()
        CrashReport.setUserId(appContext, getWireMac())
        CrashReport.initCrashReport(appContext, "013b66ff72", true, strategy)

        //自定义的异常捕获器
//        CrashHandler.getInstance().init(appContext)
    }
}

//初始化友盟
class InitUM : Task(TASK_ID, true) {
    companion object {
        const val TASK_ID = "6"
    }

    override fun run(name: String) { //初始化吐司 这个吐司必须要主线程中初始化
        UMConfigure.setLogEnabled(true)
        UMConfigure.init(
            appContext,
            "61bc8dfae014255fcbbc828e",
            "T95Plus",
            UMConfigure.DEVICE_TYPE_BOX,
            ""
        )
        LPLogger.enable = true
        LiveSDK.init(appContext)
        // 这里是竖屏 demo，设置为竖屏采集
        LiveSDK.IS_TRTC_VIDEO_RESOLUTION_MODE_LANDSCAPE = false
    }
}


//初始化
class InitServer : Task(TASK_ID, false) {
    companion object {
        const val TASK_ID = "7"
    }

    override fun run(name: String) {
//        val mRootDir: File = File(appContext.filesDir, "AndServer")
//        IOUtils.createFolder(mRootDir)
    }
}

class AppTaskFactory : Project.TaskFactory(TaskCreator)

/**
 * 模拟初始化SDK
 * @param millis Long
 */
fun doJob(millis: Long) {
    val nowTime = System.currentTimeMillis()
    while (System.currentTimeMillis() < nowTime + millis) { //程序阻塞指定时间
        val min = 10
        val max = 99
        val random = Random()
        val num = random.nextInt(max) % (max - min + 1) + min
    }
}