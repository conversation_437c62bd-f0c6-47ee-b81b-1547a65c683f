package com.panda.course.entity;

public class VideoDynamicEffect {
    private String index;
    private String type;
    private String service_personnel_name;
    private String avatar;
    private String score;
    private String data_id;//数据id
    private String status; //状态 1正常 2撤回
    private String accept_range;
    private String service_personnel_phone;
    private String number;//红包特效用到
    private String service_personnel_avatar;
    private String money;
    private String remain_envelope_num;
    private String envelope_num;
    private String envelope_title;


    public VideoDynamicEffect(String index, String type, String service_personnel_name, String avatar, String score) {
        this.index = index;
        this.type = type;
        this.service_personnel_name = service_personnel_name;
        this.avatar = avatar;
        this.score = score;
    }

    public void setEnvelope_title(String envelope_title) {
        this.envelope_title = envelope_title;
    }

    public String getEnvelope_title() {
        return envelope_title;
    }

    public String getService_personnel_avatar() {
        return service_personnel_avatar;
    }

    public void setService_personnel_avatar(String service_personnel_avatar) {
        this.service_personnel_avatar = service_personnel_avatar;
    }

    public String getMoney() {
        return money;
    }

    public void setMoney(String money) {
        this.money = money;
    }

    public String getRemain_envelope_num() {
        return remain_envelope_num;
    }

    public void setRemain_envelope_num(String remain_envelope_num) {
        this.remain_envelope_num = remain_envelope_num;
    }

    public String getEnvelope_num() {
        return envelope_num;
    }

    public void setEnvelope_num(String envelope_num) {
        this.envelope_num = envelope_num;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getNumber() {
        return number;
    }

    public String getData_id() {
        return data_id;
    }

    public void setData_id(String data_id) {
        this.data_id = data_id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAccept_range() {
        return accept_range;
    }

    public void setAccept_range(String accept_range) {
        this.accept_range = accept_range;
    }

    public String getService_personnel_phone() {
        return service_personnel_phone;
    }

    public void setService_personnel_phone(String service_personnel_phone) {
        this.service_personnel_phone = service_personnel_phone;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getIndex() {
        return index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getService_personnel_name() {
        return service_personnel_name;
    }

    public void setService_personnel_name(String service_personnel_name) {
        this.service_personnel_name = service_personnel_name;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }
}
