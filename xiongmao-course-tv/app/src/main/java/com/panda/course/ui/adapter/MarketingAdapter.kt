package com.panda.course.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.config.base.Ktx
import com.panda.course.entity.SurpriseListContentEntity
import com.panda.course.entity.SurpriseListEntity
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper
import com.youth.banner.adapter.BannerAdapter

class MarketingAdapter : BaseQuickAdapter<SurpriseListContentEntity, BaseViewHolder>(R.layout.item_marketing) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var scaleAnimation: ScaleAnimation? = null

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()
    }

    override fun convert(holder: BaseViewHolder, item: SurpriseListContentEntity) {
        holder.setText(R.id.tv_item_title, item.receive_reward_msg).setText(R.id.tv_item_time, item.reward_time)

        holder.getView<LinearLayout>(R.id.rl_common_item_view).setOnFocusChangeListener { v, hasFocus ->

            mBrowseItemFocusHighlight?.onItemFocused(holder.getView<LinearLayout>(R.id.rl_common_item_view), hasFocus)

            if (hasFocus) {
                holder.getView<LinearLayout>(R.id.rl_common_item_view).clearAnimation()
                holder.getView<LinearLayout>(R.id.rl_common_item_view).startAnimation(scaleAnimation)
            } else {
                holder.getView<LinearLayout>(R.id.rl_common_item_view).clearAnimation()
            }
        }
    }


}