package com.panda.course.ui.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.orient.tea.barragephoto.adapter.BarrageAdapter;
import com.panda.course.R;
import com.panda.course.entity.BarrageData;
import com.panda.course.util.GlideUtil;

import java.util.Random;

public class LiveBarrageViewHolder extends BarrageAdapter.BarrageViewHolder<BarrageData> {

    private ImageView mHeadView;
    private TextView mContent;


    public LiveBarrageViewHolder(View itemView) {
        super(itemView);
        mHeadView = itemView.findViewById(R.id.iv_barrage_pic);
        mContent = itemView.findViewById(R.id.tv_barrage_name);
    }

    @Override
    protected void onBind(BarrageData data) {
        mHeadView.setVisibility(View.GONE);
        mContent.setText(data.getContent());
    }
}