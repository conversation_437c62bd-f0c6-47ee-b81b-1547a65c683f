package com.panda.course.ui.activity.dialog

import android.graphics.Color
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.view.KeyEvent
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.data.CommonAnimationUtil
import com.panda.course.databinding.ActivityCheckNetWorkBinding
import com.panda.course.databinding.ActivitySurpriseContentBinding
import com.panda.course.ext.*
import com.panda.course.ui.adapter.SurpriseContentAdapter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.util.GlideUtil
import com.panda.course.util.MMKVHelper

/**
 * 活动详情界面
 */
class SurpriseContentActivity : BaseDbActivity<CourseViewModel, ActivitySurpriseContentBinding>() {

    private val mAdapter = SurpriseContentAdapter()

    /**
     * 初始化view
     */
    override fun initView(savedInstanceState: Bundle?) {

        intent?.extras?.let {
            it.getString("activity_id")?.let { id ->
                mViewModel.getSurpriseActivityContentAll(id)
            }
        }

        mDataBind.tvSurpriseClose.requestFocus()
    }

    override fun initObserver() {
        mDataBind.recyclerView.adapter = mAdapter

        mDataBind.tvSurpriseRecord.setOnFocusChangeListener { v, hasFocus ->
            mDataBind.tvSurpriseRecord.setTextColor(
                if (hasFocus) ContextCompat.getColor(
                    this, R.color.white
                ) else Color.parseColor("#DB5C63")
            )
        }
    }

    override fun onBindViewClick() {
        super.onBindViewClick()
        mDataBind.tvSurpriseRecord.setOnClickListener {
            appUMEvent(UMConstant.HOME_AD_DETAILS_GRANT)
            toStartActivity(MarketingDialogActivity::class.java)
        }

        mDataBind.tvSurpriseClose.setOnClickListener {
            finish()
        }
    }

    override fun onRequestSuccess() {
        mViewModel.surpriseContentEntity.observe(this, androidx.lifecycle.Observer{
            if (it.list.isNullOrEmpty()) {
                "活动已关闭".toast()
                finish()
                return@Observer
            }

            GlideUtil.loadPic(this@SurpriseContentActivity, it.activity?.img_url, mDataBind.ivRoot)
            it.activity?.task_finished_bg?.let { it1 -> mAdapter.setLockBg(it1) }
            it.activity?.task_un_finished_bg?.let { it1 -> mAdapter.setUnLockBg(it1) }

            val layoutManager = GridLayoutManager(this, it.list.size)
            mDataBind.recyclerView.layoutManager = layoutManager
            mAdapter.setList(it.list)

            mDataBind.tvSurpriseRecord.text = "发放记录(${it.finished_total})"
            mDataBind.tvSurpriseTips.text = "奖励发放规则:\n${it.tips}"
        })
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_DPAD_RIGHT, KeyEvent.KEYCODE_DPAD_LEFT -> {
                CommonAnimationUtil().startShakeAnimation(
                    false,
                    if (mDataBind.tvSurpriseClose.isFocused) mDataBind.tvSurpriseClose else mDataBind.tvSurpriseRecord
                )
            }
            KeyEvent.KEYCODE_DPAD_UP -> {
                if (mDataBind.tvSurpriseRecord.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.tvSurpriseRecord)
                }
            }
            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (mDataBind.tvSurpriseClose.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.tvSurpriseClose)
                }
            }
        }
        return super.onKeyDown(keyCode, event)
    }
}