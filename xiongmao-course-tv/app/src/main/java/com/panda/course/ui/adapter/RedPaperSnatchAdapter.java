package com.panda.course.ui.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.panda.course.R;
import com.panda.course.entity.RedPaperPeopleEntity;
import com.panda.course.util.GlideUtil;

import org.jetbrains.annotations.NotNull;

public class RedPaperSnatchAdapter extends BaseQuickAdapter<RedPaperPeopleEntity, BaseViewHolder> {

    public RedPaperSnatchAdapter() {
        super(R.layout.item_red_paper_snatch);
    }

    @Override
    protected void convert(@NotNull BaseViewHolder baseViewHolder, RedPaperPeopleEntity item) {

        ImageView imageView = baseViewHolder.getView(R.id.iv_red_paper);

        Glide.with(getContext()).asBitmap().apply(RequestOptions.bitmapTransform(new CircleCrop()))
                .placeholder(R.drawable.def_pic).error(R.drawable.def_pic).load(item.getService_personnel_avatar())
                .into(imageView);

        TextView status = baseViewHolder.getView(R.id.tv_red_paper_money);
        status.setText(item.getEnvelope_title());
        baseViewHolder.setText(R.id.tv_red_paper_name, item.getService_personnel_name());
    }
}