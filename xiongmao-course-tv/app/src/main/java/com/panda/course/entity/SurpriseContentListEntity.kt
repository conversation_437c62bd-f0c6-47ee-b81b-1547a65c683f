package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
data class SurpriseContentListEntity(
    var id: String? = null, //任务id
    var activity_name: String? = null, //任务名称
    var worth: String? = null, //价值
    var activity_type: String? = null, //任务类型 0未知 1-每日任务 2-连续任务
    var is_finished: String? = null, //是否已完成任务 1是 2否
    var rule_msg: String? = null, //规则
) : Parcelable

