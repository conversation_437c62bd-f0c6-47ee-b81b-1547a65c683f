package com.panda.course.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;

import com.panda.course.R;
import com.panda.course.config.ConstantMMVK;
import com.panda.course.entity.ApiResponse;
import com.panda.course.entity.CacheVideoInfo;
import com.panda.course.entity.CacheVideoList;
import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.LogExtKt;
import com.panda.course.network.NetUrl;
import com.panda.course.network.ResponseParser;
import com.panda.course.util.MMKVHelper;
import com.panda.course.util.TXVodUtil;
import com.tencent.rtmp.downloader.ITXVodDownloadListener;
import com.tencent.rtmp.downloader.TXVodDownloadManager;
import com.tencent.rtmp.downloader.TXVodDownloadMediaInfo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.wrapper.param.RxHttp;

/**
 * 下载需要的视频到本地
 */
public class DownloadVideoService extends Service {

    private final String TAG = "DownloadVideoService";

    private List<TXVodDownloadMediaInfo> taskItems;

    private boolean freeTime = false;


    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        LogExtKt.logE("onBind()", TAG);
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        LogExtKt.logE("onCreate()", TAG);
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
                NotificationChannel channel = null;
                channel = new NotificationChannel("10001", getString(R.string.app_name), NotificationManager.IMPORTANCE_HIGH);
                notificationManager.createNotificationChannel(channel);
                Notification notification = new Notification.Builder(getApplicationContext(), "10001").build();
                startForeground(1, notification);
            }

        } catch (Exception e) {
            e.printStackTrace();
            LogExtKt.logE("" + e.getMessage(), TAG);
        }


        taskItems = TXVodDownloadManager.getInstance().getDownloadMediaInfoList();
        TXVodDownloadManager.getInstance().setListener(downloadListener);


        // 监听用户空闲时刻要干的事情
        AppExtKt.getEventViewModel().getAppUserFreeTime().observeForever(new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean isFree) {
                freeTime = isFree;
                taskItems = TXVodDownloadManager.getInstance().getDownloadMediaInfoList();
//                LogExtKt.logE("FreeTime 收到消息 " + freeTime, TAG);
                if (isFree) { // 如果是true 那么就重新执行下载任务
//                resumeVodDownload();
                } else {
                    TXVodUtil.getInstance().stopAllVideoDownload();
                }
            }
        });

        AppExtKt.getEventViewModel().getAppDownloadVideoState().observeForever(new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (!aBoolean) {
                    onDestroy();
                }
            }
        });
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        LogExtKt.logE("onStartCommand()", TAG);
        startHttpCache();
        return super.onStartCommand(intent, flags, startId);
    }

    /**
     * 启动本地数据库已经有的数据
     */
    private void resumeVodDownload() {
        for (TXVodDownloadMediaInfo txVods : TXVodDownloadManager.getInstance().getDownloadMediaInfoList()) {
            if (!txVods.isDownloadFinished()) {
                TXVodDownloadManager.getInstance().startDownloadUrl(txVods.getUrl(), txVods.getUserName());
            }
        }
    }

    /**
     * 请求缓存的视频
     */
    private void startHttpCache() {
        AppExtKt.getEventViewModel().getAppCacheVideo().observeForever(new Observer<CacheVideoList>() {
            @Override
            public void onChanged(CacheVideoList cacheVideoList) {
                if (cacheVideoList != null && cacheVideoList.getList() != null && cacheVideoList.getList().size() > 0) {
                    configVideoInfo(cacheVideoList.getList());
                }
            }
        });
    }

    /**
     * 添加任务后，开启所有的下载任务
     * 1.根据code匹配本地是否已有下载的
     * 2.不在最新列表里面的，删除掉
     * 3.本地数据库没有的才去下载
     *
     * @param list
     */
    private void configVideoInfo(List<CacheVideoInfo> list) {
        if (list == null || list.size() <= 0) {
            return;
        }

        freeTime = true;

        // 先删除本地不相同的
        for (CacheVideoInfo videoInfo : list) { //1234
            if (taskItems != null && taskItems.size() > 0) {
                for (TXVodDownloadMediaInfo local : taskItems) {//23
                    if (!local.getUserName().equals(videoInfo.getCode())) { //不相同，说明最新里面已经没了本地的这个了，直接删本地
                        TXVodDownloadManager.getInstance().stopDownload(local);
                        TXVodDownloadManager.getInstance().deleteDownloadMediaInfo(local);
                        break;
                    }
                }
            }
        }


        //后下载
        for (CacheVideoInfo videoInfo : list) { //1234
            if (taskItems != null && taskItems.size() > 0) {
                for (TXVodDownloadMediaInfo local : taskItems) {//23
                    if (!local.getUserName().equals(videoInfo.getCode())) { //不相同，新的，所以要重新下载
                        TXVodDownloadManager.getInstance().startDownloadUrl(videoInfo.getVideo_url(), videoInfo.getCode());
                        break;
                    }
                }
            } else {
                TXVodDownloadManager.getInstance().startDownloadUrl(videoInfo.getVideo_url(), videoInfo.getCode());
            }
        }


    }


    private ITXVodDownloadListener downloadListener = new ITXVodDownloadListener() {
        @Override
        public void onDownloadStart(TXVodDownloadMediaInfo downloadMediaInfo) {
            LogExtKt.logE("开始下载 ", TAG);
        }

        @Override
        public void onDownloadProgress(TXVodDownloadMediaInfo downloadMediaInfo) {
            if (!freeTime) { // 如果不是空闲时间，就停止执行中的任务
                TXVodDownloadManager.getInstance().stopDownload(downloadMediaInfo);
                return;
            }
//            float progress = downloadMediaInfo.getProgress() * 1.0f * 100;
//            LogExtKt.logE("刷新进度 " + (int) progress, TAG);
        }

        @Override
        public void onDownloadStop(TXVodDownloadMediaInfo downloadMediaInfo) {
            LogExtKt.logE("暂停下载 ", TAG);
        }

        @Override
        public void onDownloadFinish(TXVodDownloadMediaInfo downloadMediaInfo) {
            LogExtKt.logE("下载完成 " + downloadMediaInfo.getPlayPath(), TAG);
        }

        @Override
        public void onDownloadError(TXVodDownloadMediaInfo downloadMediaInfo, int i, String s) {
            LogExtKt.logE("下载失败 code = " + i + " ; 错误信息 = " + s, TAG);
        }

        @Override
        public int hlsKeyVerify(TXVodDownloadMediaInfo txVodDownloadMediaInfo, String s, byte[] bytes) {
            return 0;
        }
    };


    @Override
    public void onDestroy() {
        super.onDestroy();
        if (taskItems != null) {
            taskItems.clear();
            taskItems = null;
        }
        stopSelf();
    }
}
