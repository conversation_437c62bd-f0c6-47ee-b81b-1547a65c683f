package com.panda.course.entity;

public class RedPaperPeopleEntity {

    private String service_personnel_name;
    private String service_personnel_avatar;
    private String money;
    private String remain_envelope_num;
    private String envelope_num;
    private String envelope_title;
    private String envelope_type;

    public RedPaperPeopleEntity(String service_personnel_name, String service_personnel_avatar, String money, String remain_envelope_num, String envelope_num, String envelope_title) {
        this.service_personnel_name = service_personnel_name;
        this.service_personnel_avatar = service_personnel_avatar;
        this.money = money;
        this.remain_envelope_num = remain_envelope_num;
        this.envelope_num = envelope_num;
        this.envelope_title = envelope_title;
    }


    public void setEnvelope_type(String envelope_type) {
        this.envelope_type = envelope_type;
    }

    public String getEnvelope_type() {
        return envelope_type;
    }

    public void setEnvelope_title(String envelope_title) {
        this.envelope_title = envelope_title;
    }

    public String getEnvelope_title() {
        return envelope_title;
    }

    public String getMoney() {
        return money;
    }

    public void setMoney(String money) {
        this.money = money;
    }

    public String getRemain_envelope_num() {
        return remain_envelope_num;
    }

    public void setRemain_envelope_num(String remain_envelope_num) {
        this.remain_envelope_num = remain_envelope_num;
    }

    public String getEnvelope_num() {
        return envelope_num;
    }

    public void setEnvelope_num(String envelope_num) {
        this.envelope_num = envelope_num;
    }

    public String getService_personnel_name() {
        return service_personnel_name;
    }

    public void setService_personnel_name(String service_personnel_name) {
        this.service_personnel_name = service_personnel_name;
    }

    public String getService_personnel_avatar() {
        return service_personnel_avatar;
    }

    public void setService_personnel_avatar(String service_personnel_avatar) {
        this.service_personnel_avatar = service_personnel_avatar;
    }
}
