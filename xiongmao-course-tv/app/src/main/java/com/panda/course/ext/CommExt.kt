package com.panda.course.ext

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.WindowManager
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.Toast
import androidx.annotation.NonNull
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.google.gson.Gson
import com.panda.course.R
import com.panda.course.callback.EventViewModel
import com.panda.course.config.App
import com.panda.course.config.base.Ktx
import com.panda.course.config.base.appContext
import com.panda.course.util.MMKVHelper
import com.panda.course.util.ToastUtil
import java.io.BufferedReader
import java.io.EOFException
import java.io.InputStreamReader
import java.security.Permission


val gson: Gson by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) { Gson() }

fun Any?.toJsonStr(): String {
    return gson.toJson(this)
}

fun Any?.toast() {
    Toast.makeText(Ktx.app, "$this", Toast.LENGTH_SHORT).show()
}

/**
 * 关闭键盘
 */
fun EditText.hideKeyboard() {
    val imm = appContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.hideSoftInputFromWindow(this.windowToken, InputMethodManager.HIDE_IMPLICIT_ONLY)
}

/**
 * 打开键盘
 */
fun EditText.openKeyboard() {
    this.apply {
        isFocusable = true
        isFocusableInTouchMode = true
        requestFocus()
    }
    val inputManager = appContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    inputManager.showSoftInput(this, 0)
}

/**
 * 关闭键盘焦点
 */
fun Activity.hideOffKeyboard() {
    val imm = this.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    if (imm.isActive && this.currentFocus != null) {
        if (this.currentFocus?.windowToken != null) {
            imm.hideSoftInputFromWindow(
                this.currentFocus?.windowToken, InputMethodManager.HIDE_NOT_ALWAYS
            )
        }
    }
}

fun toAppContextStartActivity(@NonNull clz: Class<*>) {
    val intent = Intent(appContext, clz)
    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
    appContext.startActivity(intent)
}


fun Activity.toStartActivity(@NonNull clz: Class<*>, bundle: Bundle? = null) {
    val intent = Intent(this, clz)
    intent.apply {
        bundle?.let { putExtras(it) }
    }
    this.startActivity(intent)
}

fun toStartActivity(@NonNull clz: Class<*>, @NonNull bundle: Bundle) {
    val intent = Intent(appContext, clz)
    intent.apply {
        putExtras(bundle)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    appContext.startActivity(intent)
}

fun toStartActivity(
    activity: Activity, @NonNull clz: Class<*>, code: Int, @NonNull bundle: Bundle
) {
    activity.startActivityForResult(Intent(appContext, clz).putExtras(bundle), code)
}

fun toStartActivity(
    fragment: Fragment, @NonNull clz: Class<*>, code: Int, @NonNull bundle: Bundle
) {
    fragment.startActivityForResult(Intent(appContext, clz).putExtras(bundle), code)
}

fun toStartActivity(activity: Activity, @NonNull intent: Intent, code: Int) {
    activity.startActivityForResult(intent, code)
}

fun toStartActivity(
    @NonNull type: Any, @NonNull clz: Class<*>, code: Int, @NonNull bundle: Bundle
) {
    if (type is Activity) {
        toStartActivity(type, clz, code, bundle)
    } else if (type is Fragment) {
        toStartActivity(type, clz, code, bundle)
    }
}

fun toStartService(@NonNull clz: Class<*>, @NonNull bundle: Bundle? = null) {
    try {
        val intent = Intent(appContext, clz)
        bundle?.let { intent.putExtras(it) }
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            appContext.startForegroundService(intent)
        } else {
            appContext.startService(intent)
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

fun checkLocationDataNULL(keys: String): Boolean {
    val jsonData = MMKVHelper.decodeString(keys)
    return jsonData != null
}

/**
 * 隐藏状态栏
 */
fun hideStatusBar(activity: Activity) {
    val attrs = activity.window.attributes
    attrs.flags = attrs.flags or WindowManager.LayoutParams.FLAG_FULLSCREEN
    activity.window.attributes = attrs
}

/**
 * 显示状态栏
 */
fun showStatusBar(activity: Activity) {
    val attrs = activity.window.attributes
    attrs.flags = attrs.flags and WindowManager.LayoutParams.FLAG_FULLSCREEN.inv()
    activity.window.attributes = attrs
}

/**
 * 横竖屏
 */
fun isLandscape(context: Context) =
    context.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE

/**
 * 应用商店
 */
fun gotoStore() {
    val uri = Uri.parse("market://details?id=" + appContext.packageName)
    val goToMarket = Intent(Intent.ACTION_VIEW, uri)
    try {
        goToMarket.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        appContext.startActivity(goToMarket)
    } catch (ignored: ActivityNotFoundException) {
    }
}

/**
 * 字符串相等
 */
fun isEqualStr(value: String?, defaultValue: String?) =
    if (value.isNullOrEmpty() || defaultValue.isNullOrEmpty()) false else TextUtils.equals(
        value, defaultValue
    )

/**
 * Int类型相等
 *
 */
fun isEqualIntExt(value: Int, defaultValue: Int) = value == defaultValue

fun getDrawableExt(id: Int): Drawable? = ContextCompat.getDrawable(appContext, id)

fun getColorExt(id: Int): Int = ContextCompat.getColor(appContext, id)

fun getStringExt(id: Int) = appContext.resources.getString(id)

fun getStringArrayExt(id: Int): Array<String> = appContext.resources.getStringArray(id)

fun getIntArrayExt(id: Int) = appContext.resources.getIntArray(id)

fun getDimensionExt(id: Int) = appContext.resources.getDimension(id)

/**
 * 通用动画
 */
val mShakeAnimation: Animation by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    AnimationUtils.loadAnimation(
        appContext, R.anim.host_shake
    )
}

/**
 * 通用动画
 */
val mShakeYAnimation: Animation by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    AnimationUtils.loadAnimation(
        appContext, R.anim.host_shake_y
    )
}

/**
 * 获取目录下的文件
 */
fun readFileFromAssets(name: String): String {
    val b = BufferedReader(InputStreamReader(appContext.assets.open(name)))
    var ret = ""
    try {
        while (true) {
            val s = b.readLine() ?: break
            ret += s
        }
    } catch (e: EOFException) {
    }
    return ret
}


/**
 * 检查权限
 */
fun Activity.checkPermission(array: Array<String>): Boolean {
    for (permission in array) {
        if (ActivityCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
            return false
        }
    }
    return true
}
