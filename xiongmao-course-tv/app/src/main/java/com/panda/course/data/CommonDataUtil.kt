package com.panda.course.data

import com.panda.course.entity.*
import java.util.*
import kotlin.collections.ArrayList

class CommonDataUtil {
    /**
     * 常用功能的数据
     *
     * @return
     */
    val commonFunctions: ArrayList<CommonFunctions>
        get() {
            val commonFunctions = ArrayList<CommonFunctions>()
            commonFunctions.add(CommonFunctions("下一个", "", CommonFunctions.VIEW_IMG))
            commonFunctions.add(CommonFunctions("清晰度", "超清", CommonFunctions.VIEW_BIG_DEF))
            commonFunctions.add(CommonFunctions("倍速", "正常", CommonFunctions.VIEW_BIG_DEF))
            commonFunctions.add(CommonFunctions("字幕", "开", CommonFunctions.VIEW_BIG_DEF))
            return commonFunctions
        }

    /**
     * 常用功能的数据
     *
     * @return
     */
    val propagandaCommonFunctions: ArrayList<CommonFunctions>
        get() {
            val commonFunctions = ArrayList<CommonFunctions>()
            commonFunctions.add(CommonFunctions("清晰度", "超清", CommonFunctions.VIEW_BIG_DEF))
            commonFunctions.add(CommonFunctions("倍速", "正常", CommonFunctions.VIEW_BIG_DEF))
            return commonFunctions
        }

    /**
     * 视频清晰度的数据
     *
     * @return
     */
    val videoDefinitions: ArrayList<CommonFunctions>
        get() {
            val commonFunctions = ArrayList<CommonFunctions>()
            commonFunctions.add(CommonFunctions("超清", "超清", CommonFunctions.VIEW_TEXT, true))
            commonFunctions.add(CommonFunctions("高清", "高清", CommonFunctions.VIEW_TEXT))
            return commonFunctions
        }

    /**
     * 视频倍速的数据
     *
     * @return
     */
    val videoSpeed: ArrayList<CommonFunctions>
        get() {
            val commonFunctions = ArrayList<CommonFunctions>()
            commonFunctions.add(CommonFunctions("正常", "正常", CommonFunctions.VIEW_TEXT, true))
            commonFunctions.add(CommonFunctions("0.75倍", "0.75倍", CommonFunctions.VIEW_TEXT))
            commonFunctions.add(CommonFunctions("1.25倍", "1.25倍", CommonFunctions.VIEW_TEXT))
            commonFunctions.add(CommonFunctions("1.5倍", "1.5倍", CommonFunctions.VIEW_TEXT))
            commonFunctions.add(CommonFunctions("2.0倍", "2.0倍", CommonFunctions.VIEW_TEXT))
            return commonFunctions
        }

    /**
     * 字幕的数据
     *
     * @return
     */
    val videoSubTitle: ArrayList<CommonFunctions>
        get() {
            val commonFunctions = ArrayList<CommonFunctions>()
            commonFunctions.add(CommonFunctions("开", "开", CommonFunctions.VIEW_TEXT, true))
            commonFunctions.add(CommonFunctions("关", "关", CommonFunctions.VIEW_TEXT))
            return commonFunctions
        }


    /**
     * 营销活动测试的数据
     *
     * @return
     */
    val marketingActivity: ArrayList<String>
        get() {
            val strings = ArrayList<String>()
            for (i in 0..49) {
                strings.add("完成【奖励一】收到" + i + "张【20元优惠券】")
            }
            return strings
        }

    /**
     * 课时列表使用创建新的item
     */
    fun createCourseDetailInfo(cover: String, type: Int): CourseDetailInfo {
        return CourseDetailInfo(
            code = null,
            chapter_code = null,
            course_code = null,
            title_main = null,
            title_sub = null,
            course_cover_url = null,
            video_cover_image = cover,
            video_introduction = null,
            video_url = null,
            presetTsSize = null,
            totalTsSize = null,
            hd_video_url_list = null,
            video_watch_time = null,
            video_watch_min = null,
            video_time = null,
            type = type,
            tencent_video_id = null,
            training_aid_introduction = null,
            hd_video_subtitle_url = null,
            status = null,
        )
    }


    /**
     * 创建测试的消息通知列表
     */
    fun createNoticeList(isAdd: Boolean = false): ArrayList<NoticeListEntityEntity> {
        val mList = ArrayList<NoticeListEntityEntity>()
        for (i in if (!isAdd) 0..10 else 10..20) {
            mList.add(
                NoticeListEntityEntity(
                    title = "课程列表$i", create_time = "这里时间", is_read = "0", number = "1004",
                    msg = "消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i ,消息通知$i "
                )
            )
        }
        return mList
    }

    /**
     * 创建测试的师生互动列表
     */
    fun createInteractiveList(isAdd: Boolean = false): ArrayList<InteractiveListEntityEntity> {
        val mList = ArrayList<InteractiveListEntityEntity>()
        for (i in if (!isAdd) 0..10 else 10..20) {
            mList.add(
                InteractiveListEntityEntity(
                    store_name = "演示门店$i", service_personnel_name = "服务人员名称$i", course_title = "高级育婴师$i",
                    txt_content = "客户价值第一$i", create_time = "今天"
                )
            )
        }
        return mList
    }

    /**
     * 创建消息通知的title
     */
    fun createNoticeRowList(): ArrayList<NoticeRowEntity> {
        val mList = ArrayList<NoticeRowEntity>()
        mList.add(NoticeRowEntity("消息通知", 1))
        return mList
    }
}