package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import java.util.*
import kotlin.collections.ArrayList

@Parcelize
data class CourseDetail(
    var code: String?,
    var title_main: String?,
    var title_sub: String?,
    var course_introduction: String?,
    var teacher_name: String?,
    var isRefresh: Boolean,
    var store_log: String,
    var final_exam_pic: String,
    var schedule_pic: String,
    var teacher_interactive_pic: String,
    var course_cover_image: String,
    var course_status: String,
    var graduation_photo_pic: String,
    var course_ware_url: String,
    var has_course_ware: String,
    var video_list: ArrayList<CourseDetailInfo>?,
) : Parcelable


@Parcelize
data class CourseDetailInfo(
    var code: String?,
    var chapter_code: String?,
    var course_code: String?,
    var title_main: String?,
    var title_sub: String?,
    var course_cover_url: String?,
    var video_cover_image: String?,
    var video_introduction: String?,
    var training_aid_introduction: String?,
    var video_url: String?,
    var presetTsSize: Int?,
    var totalTsSize: Int?,
    var video_watch_time: String?,
    var hd_video_url_list: CourseHDUrlListInfo?,
    var video_watch_min: String?,
    var video_time: String?,
    var type: Int = 0,
    var tencent_video_id: String?,
    var status: String?,
    var ad_Info: Ad? = null,
    var isLeft: Boolean = false,
    var hd_video_subtitle_url: String?,
    var final_exam_pic: String? = "",
    var videos_count: String? = "",
    var is_new: String? = "",
    var valid_date: String? = "",
    var update_time: String? = "",
) : Parcelable


@Parcelize
data class CourseHDUrlListInfo(
    var hd: String?,
    var standard: String?,
) : Parcelable