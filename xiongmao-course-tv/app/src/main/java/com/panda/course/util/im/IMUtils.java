package com.panda.course.util.im;

import com.panda.course.ext.LogExtKt;
import com.tencent.imsdk.group.GroupInfo;
import com.tencent.imsdk.v2.V2TIMCallback;
import com.tencent.imsdk.v2.V2TIMManager;
import com.tencent.qcloud.tuicore.TUILogin;
import com.tencent.qcloud.tuicore.component.interfaces.IUIKitCallback;

/**
 * IM
 */
public class IMUtils {

    private String TAG = "IMUtils";

    public void joinGroup(String groupId, String addWording, IUIKitCallback<Void> callback) {
        V2TIMManager.getInstance().joinGroup(groupId, addWording, new V2TIMCallback() {
            @Override
            public void onError(int code, String desc) {
                LogExtKt.logE("addGroup err code = " + code + ", desc = " + ErrorMessageConverter.convertIMError(code, desc), TAG);
                if (callback != null) {
                    callback.onError("", code, desc);
                }
            }

            @Override
            public void onSuccess() {
                LogExtKt.logE("加群成功", TAG);
                if (callback != null) {
                    callback.onSuccess(null);
                }
                TUILogin.addGroup = true;
            }
        });
    }




}
