package com.panda.course.config

object UMConstant {

    //分类 获取焦点就上报
    const val CLASS_EVENT = "Lef_sort"

    //观影记录
    const val HISTORY_EVENT = "Viewing_records"

    //宣传
    const val PRO_VIDEO_EVENT = "Home_my"

    //分类视频
    const val COURSE_VIDEO = "Cours_list"

    //课时列表
    const val COURSE_DETAILS_VIDEO = "Class_hour_list"

    //结业考试
    const val GRADUATE = "Final_test"

    //师生互动
    const val TEACHER_INTERACTION = "Teacher_interaction"

    //毕业表彰
    const val GRADUATE_COMMEND = "Graduation_Commend"

    //毕业表彰切换
    const val GRADUATE_COMMEND_CHANGE = "Graduation_Commend_change"

    //消息通知
    const val HOME_NOTICE_MESSAGE = "Home_message"

    //技能测评
    const val HOME_SKILL_EVALUATION = "Home_skill_Evaluation"

    //订单大厅
    const val HOME_ORDER_HALL = "Home_orderHall"

    //优秀学员
    const val HOME_EXCELLENT_STUDENT = "Home_excellent_Student"

    //卡片点击
    const val COURSE_CARD_LIST = "Cours_card_list"


    //门店排行包 获取焦点就上报
    const val TOP_DAY = "Ranking_list_Day"
    const val TOP_WEEK = "Ranking_week"
    const val TOP_ALL = "Ranking_whole"
    const val STORE = "Store_ranking"

    // 全国排行榜 获取焦点就上报s
    const val National = "National_ranking"
    const val National_DAY = "National_ranking_Day"
    const val National_WEEK = "National_ranking_Week"
    const val National_ALL = "National_ranking_Whole"

    //按键上报
    const val KEY_SIGN = "Check_in_Skip"
    const val KEY_ANSWER = "Answer_skip"
    const val KEY_DURING = "Skip_during_Recess"
    const val KEY_QR_CODE = "Pause_QR_code"

    //统计图片失败的情况
    const val KEY_IMAGE_LOAD_ERROR = "Image_loading_Erro_message"


    //统计当前门店的网速
    const val STORE_NET_SPEED = "network_Speed"

    //首页广告固定入口
    const val HOME_AD_FIXED = "Home_ad_Fixed_entrance"

    //活动详情页发放记录
    const val HOME_AD_DETAILS_GRANT = "Home_ad_Details_grant"

    //首页按钮点击进入
    const val HOME_AD_DETAILS = "Home_ad_Details"

    //首页网速检测
    const val HOME_NET_SPEED = "Home_netPerSec"

    //播放页常用功能点击下一个
    const val PLAY_CLICK_NEXT = "Play_commoFunctions_next"

    //播放页常用功能点击清晰度
    const val PLAY_CLICK_DEFINITION = "Play_commoFunctions_definition"

    //播放页常用功能点击倍速
    const val PLAY_CLICK_SPEED = "Play_commoFunctions_speed"

    //播放页点击清晰度切换
    const val PLAY_CLICK_SWITCH_DEFINITION = "Play_switch_Definition"

    //播放页点击倍速切换
    const val PLAY_CLICK_SWITCH_SPEED = "Play_switch_Speed"

    //播放页点击课程切换
    const val PLAY_CLICK_SWITCH_CURRICULUM = "Play_switch_Curriculum"

    //毕业表彰下一张
    const val GRADUATION_COMMENTD_NEXT = "Graduation_commend_Next"

    //课程表
    const val DETAILS_CLASS_SCHEDULE_CARD = "Details_class_Schedule_card"

    //播放页点击知识点
    const val PLAY_CLICK_SWITCH_KNOWLEDGE = "Play_switch_knowledge"

    //播放页常用功能点击知识点
    const val PLAY_CLICK_KNOWLEDGE = "Play_commoFunctions_knowledge"

    //播放页点击字幕
    const val PLAY_CLICK_SWITCH_SUBTITLE = "Play_switch_subtitle"

    //播放页常用功能点击字幕
    const val PLAY_CLICK_SUBTITLE = "Play_commoFunctions_subtitle"

    //首页帮助于反馈
    const val HOME_HELP_FEEDBACK = "Home_help_feedback"

    //首页设置
    const val HOME_SETTING = "Home_setup"

    //错误情况的收集
    const val KEY_IM_ERROR = "IM_Info_Ai"

    //毕业表彰收集 门店点击
    const val GRADUATION_STORE = "Graduation_store"

    //毕业表彰收集 学生点击
    const val GRADUATION_STUDENT = "Graduation_student"

    //直播的全部入口
    const val AI_ALL_CLICK = "Home_All_live_broadcasts"

    //AI 开课中
    const val AI_LEAN_LIVE = "Live_streaming"

    //AI 待开课
    const val AI_LEAN_NOT_LIVE = "Not_live_streamed"

    //播放页-关闭弹幕
    const val AI_PLAY_BARRAGE = "Play_barrage"

    //播放页-关闭互动二维码
    const val AI_QR_CODE_PLAY = "Play_interaction"
    
    //课件买
    const val COURSE_COURSEWARE = "Course_courseware"

}