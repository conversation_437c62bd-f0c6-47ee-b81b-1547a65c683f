package com.panda.course.ui.activity

import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityFeedbackBinding
import com.panda.course.ext.*
import com.panda.course.ui.adapter.FeedBackQuestionAdapter
import com.panda.course.ui.adapter.RowSkillAdapter
import com.panda.course.ui.viewmodel.FeedBackViewModel

class FeedbackActivity : BaseDbActivity<FeedBackViewModel, ActivityFeedbackBinding>() {

    private var mRowAdapter = RowSkillAdapter()

    private var mTagAdapter = RowSkillAdapter()

    private var mFeedBackAdapter = FeedBackQuestionAdapter()


    private var mRowView: View? = null

    /**
     * 默认分类
     */
    private val focus_row = 1

    /**
     * 在线反馈
     */
    private val focus_feedback = 2

    /**
     * 列表
     */
    private val focus_feedback_question = 3

    /**
     * 记录当前的焦点在哪里
     */
    private var currentFocusType: Int = focus_row

    private var mRowPosition = 0


    override fun initView(savedInstanceState: Bundle?) {

        mViewModel.getFeedBackConfig()

        mDataBind.recyclerRow.adapter = mRowAdapter
        mDataBind.recycler.adapter = mFeedBackAdapter
        mFeedBackAdapter.setEmptyView(getEmptyView(tips = "暂无此类问题答复"))


        mDataBind.recyclerTag.adapter = mTagAdapter
        mDataBind.recyclerTag.layoutManager = GridLayoutManager(this, 4)


        mDataBind.tvFeedbackTips.text = changeFontColor(content = mDataBind.tvFeedbackTips.text.toString(), startIndex = 9,
                endIndex = 14, color = ContextCompat.getColor(this, R.color.bright_green))
    }

    override fun initObserver() {
        mRowAdapter.setOnViewFocus(OnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mRowView = view
                mRowPosition = position
                when (position) {
                    0 -> {
                        mDataBind.recycler.gone()
                        mDataBind.rlCustomLayou.visible()
                    }
                    else -> {
                        mDataBind.rlCustomLayou.gone()
                        mDataBind.recycler.visible()
                        mViewModel.getHelpList(mRowAdapter.data[position])
                    }
                }
            } else {
                mRowView?.background = ContextCompat.getDrawable(this, R.drawable.base_border_unselecter)
            }

            currentFocusType = focus_row
        })

        mFeedBackAdapter.setOnItemFocus(OnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mRowView?.background = ContextCompat.getDrawable(this, R.drawable.base_border_selecter)
                currentFocusType = focus_feedback_question
            }
        })

        mTagAdapter.setOnViewFocus(OnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mRowView?.background = ContextCompat.getDrawable(this, R.drawable.base_border_selecter)
                currentFocusType = focus_feedback
            }
        })

        mTagAdapter.setOnItemClickListener { adapter, view, position ->
            mViewModel.sendFeedback(mTagAdapter.data[position])
        }
    }

    override fun onRequestSuccess() {
        mViewModel.feedBackConfigEntity.observe(this, Observer {
            if (it == null) {
                return@Observer
            }
            val str = ArrayList<String>()
            str.add("在线反馈")
            for (i in it.help_tag_list.indices) {
                str.add("" + it.help_tag_list[i].name)
            }
            mRowAdapter.setList(str)


            val strTag = ArrayList<String>()
            for (i in it.feedback_tag_list.indices) {
                strTag.add("" + it.feedback_tag_list[i].name)
            }
            mTagAdapter.setList(strTag)

            //填充个人信息
            Glide.with(this).asBitmap().load(it.user.avatar).apply(RequestOptions.bitmapTransform(CircleCrop()))
                    .placeholder(R.drawable.def_pic).error(R.drawable.def_pic).into(mDataBind.ivFeedbackPic)

            mDataBind.tvFeedbackName.text = it.user.name
            mDataBind.tvFeedbackPhone.text = "电话：${it.user.mobile}"
            mDataBind.tvFeedbackContent.text = it.user.desc
        })

        mViewModel.questionEntity.observe(this, Observer {
            if (it == null) {
                return@Observer
            }
            mFeedBackAdapter.setList(it.list)
        })

        mViewModel.feedback.observe(this, Observer {
            showMessageBackDialog("提交成功，稍后您的专属客服会立即联系您～", "按返回键关闭此提示")
        })
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {

        val focusView = window.decorView.findFocus()
        Log.e("获取界面焦点", "===当前获取焦点的View===$focusView")

        goPropagandaVideo() // 宣传片

        if (onMyKeyDown(keyCode, event)) { // 加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_LEFT -> {
//                if (currentFocusType == focus_row) {
//                    mRowView?.background = ContextCompat.getDrawable(this, R.drawable.base_border)
//                }
            }
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (mRowPosition != 0 && mFeedBackAdapter.data.size <= 0) {
                    return true
                }
            }
        }

        return super.onKeyDown(keyCode, event)
    }
}