package com.panda.course.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.annotation.LayoutRes
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.panda.course.R
import com.youth.banner.adapter.BannerAdapter

class ImageAdapter(datas: List<String>) : BannerAdapter<String, ImageHolder>(datas) {

    override fun onCreateHolder(parent: ViewGroup, viewType: Int): ImageHolder {
        val imageView = getView(parent, R.layout.banner_image) as ImageView
        return ImageHolder(imageView)
    }

    override fun onBindView(holder: ImageHolder, data: String, position: Int, size: Int) {
        Glide.with(holder.itemView).load(data).thumbnail(Glide.with(holder.itemView).load(R.drawable.icon_placeholder))
            .skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE).into(holder.imageView)
    }

    /**
     * 将布局文件转成view，这里为了适配viewpager2中高宽必须为match_parent
     *
     * @param parent
     * @param layoutId
     * @return
     */
    fun getView(parent: ViewGroup, @LayoutRes layoutId: Int): View {
        val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
        val params = view.layoutParams //这里判断高度和宽带是否都是match_parent
        if (params.height != -1 || params.width != -1) {
            params.height = -1
            params.width = -1
            view.layoutParams = params
        }
        return view
    }


}