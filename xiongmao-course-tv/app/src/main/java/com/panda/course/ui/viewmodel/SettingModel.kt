package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class SettingModel : BaseViewModel() {

    //ota的升级信息
    var updateInfo = MutableLiveData<UpdateInfo>()

    // 设备到期
    var deviceExpireDate = MutableLiveData<DeviceExpireDate>()

    //获取gettoken
    var qiniuInfo = MutableLiveData<QiniuInfo>()

    fun getOTAInfo() {
        rxHttpRequest {
            onRequest = {
                updateInfo.value = RxHttp.get(NetUrl.OTA_UPDATE).add("device_code", getSerialNumber())
                    .add("device_mac", getWireMac()).add("device_id", getAndroidId())
                    .add("firmware", getDeviceRadioFirmware())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).toResponse<UpdateInfo>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.OTA_UPDATE
        }
    }


    /**
     * 获取设备服务日期
     */
    fun getDeviceExpireDate() {
        rxHttpRequest {
            onRequest = {
                deviceExpireDate.value = RxHttp.get(NetUrl.DEVICE_EXPIREDATE).add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).toResponse<DeviceExpireDate>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.DEVICE_EXPIREDATE
        }
    }


    /**
     * 获取七牛的token
     */
    fun getQiNiuToken() {
        rxHttpRequest {
            onRequest = {
                qiniuInfo.value = RxHttp.get(NetUrl.GET_QINNIU_TOKEN).toResponse<QiniuInfo>().await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_QINNIU_TOKEN
        }
    }


}