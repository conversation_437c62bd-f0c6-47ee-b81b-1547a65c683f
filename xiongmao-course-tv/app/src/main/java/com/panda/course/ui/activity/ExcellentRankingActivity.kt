package com.panda.course.ui.activity

import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.leanback.widget.FocusHighlight
import androidx.leanback.widget.FocusHighlight.ZOOM_FACTOR_LARGE
import androidx.leanback.widget.FocusHighlight.ZOOM_FACTOR_XSMALL
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.panda.course.R
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.appContext
import com.panda.course.data.CommonAnimationUtil
import com.panda.course.databinding.ActivityExcellentRankingBinding
import com.panda.course.databinding.ActivityOrderHallBinding
import com.panda.course.entity.ExcellentRankEntity
import com.panda.course.entity.ExcellentRankListEntity
import com.panda.course.ext.*
import com.panda.course.ui.adapter.ExcellentAdapter
import com.panda.course.ui.viewmodel.ExcellentRankViewModel
import com.panda.course.ui.viewmodel.OrderHallViewModel
import com.panda.course.util.SpaceItemDecoration
import com.panda.course.widget.focus.MyFocusHighlightHelper

class ExcellentRankingActivity : BaseDbActivity<ExcellentRankViewModel, ActivityExcellentRankingBinding>() {

    private val excellentAdapter = ExcellentAdapter()

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var mSelectedType = true //true代表本月 false代表上月

    private var monthRankEntity: ExcellentRankEntity? = null
    private var lastMonthRankEntity: ExcellentRankEntity? = null


    override fun initView(savedInstanceState: Bundle?) {

        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(ZOOM_FACTOR_LARGE, false)
        }

        val manger = LinearLayoutManager(this)
        manger.orientation = LinearLayoutManager.HORIZONTAL
        mDataBind.recycler.layoutManager = manger
        mDataBind.recycler.adapter = excellentAdapter
        excellentAdapter.setRecycler(mDataBind.recycler)
    }


    override fun onRequestSuccess() {
        mViewModel.excellentRankEntity.observe(this, Observer {
            if (it != null) {
                if (mDataBind.butMonth.isFocused) {
                    monthRankEntity = it
                    fillData(monthRankEntity)
                }
                if (mDataBind.butLastMonth.isFocused) {
                    lastMonthRankEntity = it
                    fillData(lastMonthRankEntity)
                }
            }
        })
    }

    private fun fillData(it: ExcellentRankEntity?) {
        if (it == null) {
            return
        }
        mDataBind.butMonth.text = "本月排行榜(${it.current_month_total})"
        mDataBind.butLastMonth.text = "上月排行榜(${it.last_month_total})"
        if (it.list.size > 0) {
            visibleViews(mDataBind.recycler, mDataBind.llNo1, mDataBind.llNo2, mDataBind.llNo3)
            goneViews(mDataBind.llEmpty)
            excellentAdapter.setList(null)
            if (it.list.size > 3) {
                if (it.list[0] != null) {
                    it.list[0].avatar?.let { it1 -> loadAvatar(mDataBind.ivNo1, it1) }
                    mDataBind.tvNo1Name.text = "${it.list[0].service_personnel_name}"
                    mDataBind.tvNo1Content.text = "答题次数：${it.list[0].exercise_times}\n答题平均分：${it.list[0].avg_score}\n答题正确率：${it.list[0].right_percent}"
                }
                if (it.list[1] != null) {
                    it.list[1].avatar?.let { it1 -> loadAvatar(mDataBind.ivNo2, it1) }
                    mDataBind.tvNo2Name.text = "${it.list[1].service_personnel_name}"
                    mDataBind.tvNo2Content.text = "答题次数：${it.list[1].exercise_times}\n答题平均分：${it.list[1].avg_score}\n答题正确率：${it.list[1].right_percent}"
                }
                if (it.list[2] != null) {
                    it.list[2].avatar?.let { it1 -> loadAvatar(mDataBind.ivNo3, it1) }
                    mDataBind.tvNo3Name.text = "${it.list[2].service_personnel_name}"
                    mDataBind.tvNo3Content.text = "答题次数：${it.list[2].exercise_times}\n答题平均分：${it.list[2].avg_score}\n答题正确率：${it.list[2].right_percent}"
                }

                val resultList = it.list.subList(3, it.list.size).toMutableList()
                if (resultList.size < 7) {
                    val repetitions = 7 - resultList.size
                    for (i in 0 until repetitions) {
                        resultList.add(ExcellentRankListEntity(seize_seat = true))
                    }
                }
                excellentAdapter.setList(resultList)
            } else {
                if (it.list.size >= 1) {
                    if (it.list[0] != null) {
                        it.list[0].avatar?.let { it1 -> loadAvatar(mDataBind.ivNo1, it1) }
                        mDataBind.tvNo1Name.text = "${it.list[0].service_personnel_name}"
                        mDataBind.tvNo1Content.text = "答题次数：${it.list[0].exercise_times}\n答题平均分：${it.list[0].avg_score}\n答题正确率：${it.list[0].right_percent}"
                    }
                }
                if (it.list.size >= 2) {
                    if (it.list[1] != null) {
                        it.list[1].avatar?.let { it1 -> loadAvatar(mDataBind.ivNo2, it1) }
                        mDataBind.tvNo2Name.text = "${it.list[1].service_personnel_name}"
                        mDataBind.tvNo2Content.text = "答题次数：${it.list[1].exercise_times}\n答题平均分：${it.list[1].avg_score}\n答题正确率：${it.list[1].right_percent}"
                    }
                }
                if (it.list.size >= 3) {
                    if (it.list[2] != null) {
                        it.list[2].avatar?.let { it1 -> loadAvatar(mDataBind.ivNo3, it1) }
                        mDataBind.tvNo3Name.text = "${it.list[2].service_personnel_name}"
                        mDataBind.tvNo3Content.text = "答题次数：${it.list[2].exercise_times}\n答题平均分：${it.list[2].avg_score}\n答题正确率：${it.list[2].right_percent}"
                    }
                }
                val resultList = mutableListOf<ExcellentRankListEntity>() // 创建一个空的可变列表
                for (i in 0 until 7) {
                    resultList.add(ExcellentRankListEntity(seize_seat = true))
                }
                excellentAdapter.setList(resultList)
            }
        } else {
            goneViews(mDataBind.recycler, mDataBind.llNo1, mDataBind.llNo2, mDataBind.llNo3)
            visibleViews(mDataBind.llEmpty)
            excellentAdapter.setList(null)
        }
    }

    private fun loadAvatar(imageView: ImageView, avatar: String) {
        Glide.with(appContext).asBitmap().load(avatar)
            .apply(RequestOptions.bitmapTransform(CircleCrop()))
            .placeholder(R.drawable.def_pic).error(R.drawable.def_pic)
            .into(imageView)
    }

    private fun resetViewData(boolean: Boolean) {
        mSelectedType = boolean

        if (boolean) {//true是本月排行  false上月
            mDataBind.butMonth.setTextColor(Color.parseColor("#FF890613"))
            mDataBind.butLastMonth.setTextColor(getColor(R.color.white))
            mDataBind.butMonth.setBackgroundResource(R.drawable.shape_rank_left_selector)
            mDataBind.butLastMonth.setBackgroundResource(R.drawable.shape_rank_right_unselector)
        } else {
            mDataBind.butLastMonth.setTextColor(Color.parseColor("#FF890613"))
            mDataBind.butMonth.setTextColor(getColor(R.color.white))
            mDataBind.butMonth.setBackgroundResource(R.drawable.shape_rank_left_unselector)
            mDataBind.butLastMonth.setBackgroundResource(R.drawable.shape_rank_right_selector)
        }

//        excellentAdapter.setList(null)
        mDataBind.ivNo1.setImageResource(R.drawable.def_pic)
        mDataBind.tvNo1Name.text = ""
        mDataBind.tvNo1Content.text = "暂无数据"

        mDataBind.ivNo2.setImageResource(R.drawable.def_pic)
        mDataBind.tvNo2Name.text = ""
        mDataBind.tvNo2Content.text = "暂无数据"

        mDataBind.ivNo3.setImageResource(R.drawable.def_pic)
        mDataBind.tvNo3Name.text = ""
        mDataBind.tvNo3Content.text = "暂无数据"
    }

    override fun initObserver() {
        mDataBind.butMonth.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                resetViewData(true)
                mDataBind.butLastMonth.setTextColor(getColorExt(R.color.white))
//                mViewModel.geRankAll(type = "1")
                if (monthRankEntity != null) {
                    fillData(monthRankEntity)
                } else {
                    mViewModel.geRankAll(type = "1")
                }
            }
        }
        mDataBind.butLastMonth.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                resetViewData(false)
                mDataBind.butMonth.setTextColor(getColorExt(R.color.white))
//                mViewModel.geRankAll(type = "2")
                if (lastMonthRankEntity != null) {
                    fillData(lastMonthRankEntity)
                } else {
                    mViewModel.geRankAll(type = "2")
                }
            }
        }

        mDataBind.llNo1.setOnFocusChangeListener { v, hasFocus -> mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus) }
        mDataBind.llNo2.setOnFocusChangeListener { v, hasFocus -> mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus) }
        mDataBind.llNo3.setOnFocusChangeListener { v, hasFocus -> mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus) }
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_DOWN -> {

            }
        }
        return super.onKeyDown(keyCode, event)
    }
}