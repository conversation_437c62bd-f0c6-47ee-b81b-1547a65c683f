package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class SkillViewModel : BaseViewModel() {

    // 测评课程
    var auntSkillListEntity = MutableLiveData<AuntSkillEntity>()

    // 测评记录
    var recordListEntity = MutableLiveData<AuntSkillRecordEntity>()

    // 二维码
    var qrCodeEntity = MutableLiveData<QRCodeEntity>()

    /**
     * 获取全部的技能测评
     */
    fun getAuntSkillAll() {
        rxHttpRequest {
            onRequest = {
                auntSkillListEntity.value = RxHttp.get(NetUrl.AUNT_SKILL_ALL)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("is_record_total", "1")
                    .toResponse<AuntSkillEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.AUNT_SKILL_ALL
        }
    }

    /**
     * 获取全部的技能测评记录
     */
    fun getAuntSkillRecord(page: Int, skill_post_uuid: String? = null) {
        rxHttpRequest {
            onRequest = {
                recordListEntity.value = RxHttp.get(NetUrl.SKILL_RECORD_LIST)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("page", "$page")
                    .add("size", "" + ConstantMMVK.PAGE_SIZE)
                    .add("skill_post_uuid", skill_post_uuid)
                    .toResponse<AuntSkillRecordEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.AUNT_SKILL_ALL
        }
    }


    /**
     * 获取全部的技能测评二维码
     */
    fun getAuntSkillQrCode(skill_post_uuid: String) {
        rxHttpRequest {
            onRequest = {
                qrCodeEntity.value = RxHttp.get(NetUrl.SKILL_RECORD_QR_CODE)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("skill_post_uuid", skill_post_uuid)
                    .toResponse<QRCodeEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.AUNT_SKILL_ALL
        }
    }

}