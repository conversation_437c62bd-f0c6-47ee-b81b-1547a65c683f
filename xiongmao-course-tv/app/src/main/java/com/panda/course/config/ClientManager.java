package com.panda.course.config;

//import com.inuker.bluetooth.library.BluetoothClient;
import com.panda.course.config.base.Ktx;


public class ClientManager {
//
//    private static BluetoothClient mClient;
//
//    public static BluetoothClient getClient() {
//        if (mClient == null) {
//            synchronized (ClientManager.class) {
//                if (mClient == null) {
//                    mClient = new BluetoothClient(Ktx.app);
//                }
//            }
//        }
//        return mClient;
//    }
}
