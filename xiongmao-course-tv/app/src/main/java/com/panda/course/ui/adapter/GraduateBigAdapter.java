package com.panda.course.ui.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.panda.course.R;
import com.panda.course.entity.GraduateListEntity;
import com.panda.course.entity.GraduateListImgEntity;
import com.panda.course.entity.GraduateStudentListImgEntity;
import com.panda.course.entity.SourceList;
import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.DensityExtKt;
import com.panda.course.util.DisplayUtil;
import com.panda.course.util.GlideUtil;
import com.youth.banner.adapter.BannerAdapter;
import com.youth.banner.util.BannerUtils;

import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 自定义布局,多个不同UI切换
 */
public class GraduateBigAdapter extends BannerAdapter<GraduateListImgEntity, RecyclerView.ViewHolder> {

    private Context context;

    private String mCourse = "";

    private boolean isHidePictures;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");


    private String jump_type;
    private List<GraduateStudentListImgEntity> names;
    private int focusType;
    private Typeface typeFace;//face 字体
    private String path = "AlimamaShuHeiTi-Bold.ttf";

    public GraduateBigAdapter(Context context, String courseName, List<GraduateListImgEntity> mDatas, Boolean isHidePictures) {
        super(mDatas);
        this.context = context;
        this.mCourse = courseName;
        this.isHidePictures = isHidePictures;
        typeFace = Typeface.createFromAsset(context.getAssets(), path);
    }

    public GraduateBigAdapter(Context context, String courseName, List<GraduateListImgEntity> mDatas, Boolean isHidePictures, String jump_type) {
        super(mDatas);
        this.context = context;
        this.mCourse = courseName;
        this.isHidePictures = isHidePictures;
        this.jump_type = jump_type;
        typeFace = Typeface.createFromAsset(context.getAssets(), path);
    }

    public GraduateBigAdapter(Context context, String courseName, List<GraduateListImgEntity> mDatas, List<GraduateStudentListImgEntity> names, Boolean isHidePictures, String jump_type, int focusType) {
        super(mDatas);
        this.context = context;
        this.mCourse = courseName;
        this.isHidePictures = isHidePictures;
        this.jump_type = jump_type;
        this.names = names;
        this.focusType = focusType;
        typeFace = Typeface.createFromAsset(context.getAssets(), path);
    }

    @Override
    public RecyclerView.ViewHolder onCreateHolder(ViewGroup parent, int viewType) {
        return new ImageHolder(BannerUtils.getView(parent, R.layout.graduate_banner_image));
    }


    @Override
    public void onBindView(RecyclerView.ViewHolder holder, GraduateListImgEntity data, int position, int size) {
        ImageHolder imageHolder = (ImageHolder) holder;
        if ("interactive".equals(jump_type)) {
            imageHolder.tips.setVisibility(View.GONE);
            imageHolder.imageView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        } else {
            imageHolder.tips.setVisibility(View.VISIBLE);
            imageHolder.imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        }
        //整个大图
        Glide.with(holder.itemView)
                .load(data.getImg_url())
                .thumbnail(Glide.with(holder.itemView)
                        .load(R.drawable.icon_placeholder))
                .placeholder(R.drawable.icon_placeholder)
                .into(imageHolder.imageView);

        RelativeLayout.LayoutParams layoutParamsContent = new RelativeLayout.LayoutParams(imageHolder.mContent.getLayoutParams());

        if (0 == focusType) {//这里focusType 代表的是样式布局 0 代表默认
            layoutParamsContent.setMargins(DensityExtKt.dp2px(300), DensityExtKt.dp2px(280), 0, 0);
        } else if (1 == focusType) {
            layoutParamsContent.setMargins(DensityExtKt.dp2px(200), DensityExtKt.dp2px(280), DensityExtKt.dp2px(250), 0);
        }
        imageHolder.mContent.setLayoutParams(layoutParamsContent);

        if (isHidePictures) {
            imageHolder.mName.setVisibility(View.GONE);
            imageHolder.mContent.setVisibility(View.GONE);
            imageHolder.studentContent.setVisibility(View.GONE);
            imageHolder.tv_date.setVisibility(View.GONE);
        } else {
            imageHolder.mName.setVisibility(View.VISIBLE);
            imageHolder.mContent.setVisibility(View.VISIBLE);
            imageHolder.studentContent.setVisibility(View.VISIBLE);
            imageHolder.tv_date.setVisibility(View.VISIBLE);
        }

        if (!TextUtils.isEmpty(data.getContent_color())) {
            imageHolder.mName.setTextColor(Color.parseColor(data.getContent_color()));
            imageHolder.mContent.setTextColor(Color.parseColor(data.getContent_color()));
            imageHolder.tv_date.setTextColor(Color.parseColor(data.getContent_color()));
            imageHolder.tips.setTextColor(Color.parseColor(data.getContent_color()));
        } else {
            imageHolder.mName.setTextColor(ContextCompat.getColor(context, R.color.red));
            imageHolder.mContent.setTextColor(ContextCompat.getColor(context, R.color.red));
            imageHolder.tv_date.setTextColor(ContextCompat.getColor(context, R.color.red));
            imageHolder.tips.setTextColor(ContextCompat.getColor(context, R.color.red));
        }

        //增加一下names 学员列表
        if (names != null && names.size() > 0) {//说明有学员，那么显示学员名称
            GraduateStringAdapter mAdapter = new GraduateStringAdapter(data.getContent_color(), typeFace);
            mAdapter.setList(names);
            imageHolder.studentContent.setLayoutManager(new GridLayoutManager(imageHolder.mContent.getContext(), 6));
            imageHolder.studentContent.setAdapter(mAdapter);
        }

        imageHolder.mName.setText(AppExtKt.getEventViewModel().getAppUserInfo().getValue().getStore_name());
        imageHolder.mContent.setText("同学们在《" + mCourse + "》课程中，学习努力\n成绩优异，顺利毕业。");
        imageHolder.tv_date.setText(sdf.format(System.currentTimeMillis()));

        //设置字体
        imageHolder.mName.setTypeface(typeFace);
        imageHolder.mContent.setTypeface(typeFace);
        imageHolder.tv_date.setTypeface(typeFace);
        imageHolder.tips.setTypeface(typeFace);
    }


}
