package com.panda.course.dao;

import java.util.Map;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.AbstractDaoSession;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.identityscope.IdentityScopeType;
import org.greenrobot.greendao.internal.DaoConfig;

import com.panda.course.dao.MyVideoInfo;

import com.panda.course.dao.MyVideoInfoDao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.

/**
 * {@inheritDoc}
 * 
 * @see org.greenrobot.greendao.AbstractDaoSession
 */
public class DaoSession extends AbstractDaoSession {

    private final DaoConfig myVideoInfoDaoConfig;

    private final MyVideoInfoDao myVideoInfoDao;

    public DaoSession(Database db, IdentityScopeType type, Map<Class<? extends AbstractDao<?, ?>>, DaoConfig>
            daoConfigMap) {
        super(db);

        myVideoInfoDaoConfig = daoConfigMap.get(MyVideoInfoDao.class).clone();
        myVideoInfoDaoConfig.initIdentityScope(type);

        myVideoInfoDao = new MyVideoInfoDao(myVideoInfoDaoConfig, this);

        registerDao(MyVideoInfo.class, myVideoInfoDao);
    }
    
    public void clear() {
        myVideoInfoDaoConfig.clearIdentityScope();
    }

    public MyVideoInfoDao getMyVideoInfoDao() {
        return myVideoInfoDao;
    }

}
