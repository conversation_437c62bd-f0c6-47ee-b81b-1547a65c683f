package com.panda.course.util;

import android.graphics.Bitmap;
import android.graphics.Color;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;

import java.util.HashMap;
import java.util.Map;

public class QRCodeUtil {
    private static QRCodeUtil instance;

    private QRCodeUtil() {
        // 私有构造函数,防止外部实例化
    }

    public static QRCodeUtil getInstance() {
        if (instance == null) {
            synchronized (QRCodeUtil.class) {
                if (instance == null) {
                    instance = new QRCodeUtil();
                }
            }
        }
        return instance;
    }

    /**
     * 生成二维码
     *
     * @param content 二维码内容
     * @return 返回二维码图片
     */
    public Bitmap createQRCode(String content) {
        int widthPix = 1000;
        int heightPix = 1000;
        try {
            // 设置二维码参数
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
            hints.put(EncodeHintType.MARGIN, 1); // 设置白边大小

            // 使用 ZXing 库生成二维码
            BitMatrix bitMatrix = new MultiFormatWriter().encode(
                    content,
                    BarcodeFormat.QR_CODE,
                    widthPix,
                    heightPix,
                    hints);

            // 将二维码转换为 Bitmap
            int[] pixels = new int[widthPix * heightPix];
            for (int y = 0; y < heightPix; y++) {
                for (int x = 0; x < widthPix; x++) {
                    if (bitMatrix.get(x, y)) {
                        pixels[y * widthPix + x] = 0xff000000;
                    } else {
                        pixels[y * widthPix + x] = 0xffffffff;
                    }
                }
            }
            Bitmap bitmap = Bitmap.createBitmap(widthPix, heightPix, Bitmap.Config.ARGB_8888);
            bitmap.setPixels(pixels, 0, widthPix, 0, 0, widthPix, heightPix);
            return bitmap;
        } catch (WriterException e) {
            e.printStackTrace();
        }
        return null;
    }
}