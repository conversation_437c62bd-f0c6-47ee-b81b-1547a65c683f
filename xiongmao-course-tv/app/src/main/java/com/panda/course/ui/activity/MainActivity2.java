package com.panda.course.ui.activity;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.Manifest;
import android.app.AlertDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.wifi.ScanResult;
import android.net.wifi.SupplicantState;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;

import com.hacknife.wifimanager.IWifi;
import com.panda.course.R;
import com.panda.course.callback.OnViewFocus;
import com.panda.course.config.base.BaseDbActivity;
import com.panda.course.config.base.BaseViewModel;
import com.panda.course.data.WifiHelper;
import com.panda.course.databinding.ActivityWifiBinding;
import com.panda.course.entity.WifiModel;
import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.CommExtKt;
import com.panda.course.ext.LogExtKt;
import com.panda.course.ui.activity.wifi.NetDetailsActivity;
import com.panda.course.ui.activity.wifi.WiFiConnectActivity;
import com.panda.course.ui.adapter.WIFIList2Adapter;
import com.panda.course.util.ToastUtil;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;

public class MainActivity2 extends BaseDbActivity<BaseViewModel, ActivityWifiBinding> {

    public static final String LOCATION_PERMISSION = Manifest.permission.ACCESS_FINE_LOCATION;

    private static final int REQUEST_CODE = 11000;//权限请求code

    private WifiReceiver wifiReceiver;//广播接受器

    private WifiManager wifiManager;//wifi管理器

    private WIFIList2Adapter mAdapter;//wifi列表适配器

    private List<WifiModel> dataList;//wifi列表显示

    private boolean isGranted;//是否有权限

    private WifiModel mCurrentWifiModel = null;

    private int mPosition = 0;

    @Override
    protected void onResume() {
        super.onResume();
        wifiReceiver = new WifiReceiver();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION);//wifi的打开与关闭
        intentFilter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);//wifi扫描
        intentFilter.addAction(WifiManager.SUPPLICANT_STATE_CHANGED_ACTION);//wifi验证密码
        intentFilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);//wifi连接成功
        intentFilter.addAction(WifiManager.RSSI_CHANGED_ACTION);//wifi信号强度
        registerReceiver(wifiReceiver, intentFilter);
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        checkPermission();
        dataList = new ArrayList<>();
        mAdapter = new WIFIList2Adapter();
        mDataBind.recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mDataBind.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            mCurrentWifiModel = dataList.get(position);
            if (mCurrentWifiModel.isConnect()) {
                Bundle bundle = new Bundle();
                bundle.putString("wifi_name", "" + mCurrentWifiModel.getWifiName());
                bundle.putString("wifi_type", "1");
                CommExtKt.toStartActivity(NetDetailsActivity.class, bundle);
            } else {
                connectWifi(mCurrentWifiModel.getWifiName(), mCurrentWifiModel.getWifiType());
            }
        });
        mAdapter.setOnViewFocus((hasFocus, position, view) -> {
            if (hasFocus) {
                mPosition = position;
            }
        });

    }

    @Override
    public void initObserver() {
        super.initObserver();
        AppExtKt.getEventViewModel().getWifiConnectState().observe(this, s -> {
            if (mCurrentWifiModel != null) {
                WifiHelper.connectWifiConfig(wifiManager, mCurrentWifiModel.getWifiName(), s, mCurrentWifiModel.getWifiType());//有密码的开始进行连接
            }
        });

        AppExtKt.getEventViewModel().getWifiState().observe(this, aBoolean -> {
            if (aBoolean && mCurrentWifiModel != null) {
                wifiManager.disconnect();//断掉当前的
                WifiHelper.deleteWifiConfiguration(wifiManager, mCurrentWifiModel.getWifiName());//删除配置中保存过的
            }
        });

        mDataBind.llWifiStateLayout.setOnFocusChangeListener((v, hasFocus) -> {
            mDataBind.llWifiStateLayout.setBackgroundResource(hasFocus ? R.color.green : R.color.blue1);
        });
    }


    /**
     * 检查权限
     */
    private void checkPermission() {
        int perm = ContextCompat.checkSelfPermission(this, LOCATION_PERMISSION);
        if (perm == PackageManager.PERMISSION_GRANTED) {
            isGranted = true;
            openWifi();//打开WIFI
        } else {
            isGranted = false;
            mDataBind.tvTitle.setText("扫描WIFI缺少定位权限，请授予权限");
            ActivityCompat.requestPermissions(this,
                    new String[]{LOCATION_PERMISSION}, REQUEST_CODE);
        }
    }

    /**
     * 打开wifi
     */
    private void openWifi() {
        wifiManager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        wifiManager.setWifiEnabled(true);
        wifiManager.startScan();//启动扫描
        mDataBind.tvProgress.setText("扫描中...");
    }

    /**
     * 获取wifi列表
     */
    private void loadData() {
        LogExtKt.logE("}" + wifiManager.getConfiguredNetworks(), "扫描");
        LogExtKt.logE("}" + wifiManager.getScanResults(), "扫描");
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        String connectWifi = "";
        if (wifiInfo != null) {
            connectWifi = wifiInfo.getSSID().replaceAll("\"", "");
        }
        if (wifiManager.getScanResults() != null && wifiManager.getScanResults().size() > 0) {
            dataList.clear();
            for (ScanResult result : wifiManager.getScanResults()) {
                if (TextUtils.isEmpty(result.SSID)) {
                    continue;
                }
                WifiModel model = new WifiModel();
                model.setWifiName(result.SSID);
                StringBuilder detail = new StringBuilder();
                detail.append("加密方案:" + result.capabilities + "\n");
                detail.append("物理地址(MAC):" + result.BSSID + "\n");
                detail.append("信号电平(RSSI):" + result.level + "\n");
                detail.append("热点频率(MHz):" + result.frequency);
                model.setWifiDetail(detail.toString());
                model.setWifiTypeName(result.capabilities);
                if (result.capabilities.contains("WEP")) {
                    model.setWifiType(1);
                } else if (result.capabilities.contains("WPA")) {
                    model.setWifiType(2);
                } else {
                    model.setWifiType(0);
                }
                for (WifiConfiguration configuration : wifiManager.getConfiguredNetworks()) {
                    String configId = configuration.SSID.replaceAll("\"", "");
                    if (configId.equals(result.SSID)) {
                        if (configuration.networkId != -1) {//保存过的
                            model.setSave(true);
                        }
                        break;
                    }
                }
                model.setIntensity(wifiManager.calculateSignalLevel(result.level, 5));//信号强度
                model.setConnect(connectWifi.equals(result.SSID));//是否连接
                if (connectWifi.equals(result.SSID)) {
                    dataList.add(0, model);
                } else {
                    dataList.add(model);
                }
            }
        }
        if (dataList.size() > 0) {
            mDataBind.llProgress.setVisibility(View.GONE);
            mDataBind.recyclerView.setVisibility(View.VISIBLE);
        } else {
            mDataBind.llProgress.setVisibility(View.VISIBLE);
            mDataBind.tvProgress.setText("没有搜索到WIFI信息");
            mDataBind.recyclerView.setVisibility(View.GONE);
        }
        for (int i = 0; i < dataList.size(); i++) {//去掉相同的数据
            for (int j = dataList.size() - 1; j > i; j--) {
                if (dataList.get(i).getWifiName().equals(dataList.get(j).getWifiName())) {
                    dataList.remove(i);
                }
            }
        }
        if (dataList.size() <= 0) {
            return;
        }
        mAdapter.setList(dataList);
        mDataBind.llWifiStateLayout.setFocusable(false);
        mDataBind.llWifiStateLayout.setFocusableInTouchMode(false);
        mDataBind.llWifiStateLayout.postDelayed(() -> {
            mDataBind.recyclerView.scrollToPosition(0);
            mDataBind.llWifiStateLayout.setFocusable(true);
            mDataBind.llWifiStateLayout.setFocusableInTouchMode(true);
        }, 1000);

    }

    /**
     * 连接Wifi
     */
    private void connectWifi(final String ssid, final int wifiType) {
        int networkId = -1;
        for (WifiConfiguration configuration : wifiManager.getConfiguredNetworks()) {
            String configId = configuration.SSID.replaceAll("\"", "");
            if (configId.equals(ssid)) {
                networkId = configuration.networkId;
                break;
            }
        }
        if (networkId != -1) {//已经连接配置过的wifi
            wifiManager.disconnect();//断掉当前的
            WifiHelper.deleteWifiConfiguration(wifiManager, mCurrentWifiModel.getWifiName());//删除配置中保存过的
            wifiManager.enableNetwork(networkId, true);
            ToastUtil.show("正在连接，稍等...");
        } else {//新的连接
            if (wifiType != 0) {//需要密码
                Bundle bundle = new Bundle();
                bundle.putString("wifi_name", "" + ssid);
                CommExtKt.toStartActivity(WiFiConnectActivity.class, bundle);
            } else {//不需要密直接连接
                WifiConfiguration wifiConfig = WifiHelper.createWifiInfo(ssid, "", wifiType);
                int netId = wifiManager.addNetwork(wifiConfig);
                if (netId != -1) {
                    wifiManager.saveConfiguration();
                }
                boolean flag = wifiManager.enableNetwork(netId, true);
//                ToastUtil.show("启动连接：" + flag);
            }
        }
    }


    /**
     * 改变wifi的开关
     */
    private void changeWifiStatus() {
        if (mDataBind.tvWifiState.getText().toString().equals("开")) {
            mDataBind.tvWifiState.setText("关");
            wifiManager.setWifiEnabled(false);
            mAdapter.getData().clear();
            mAdapter.notifyDataSetChanged();
        } else {
            mDataBind.tvWifiState.setText("开");
            wifiManager.setWifiEnabled(true);
            wifiManager.startScan();
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, @NotNull KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_RIGHT:
            case KeyEvent.KEYCODE_DPAD_LEFT:
                if (mDataBind.llWifiStateLayout.isFocused()) {
                    changeWifiStatus();
                }
                break;
            case KeyEvent.KEYCODE_BACK:
                unregisterReceiver(wifiReceiver);
                finish();
                break;
        }
        if (keyCode == KeyEvent.KEYCODE_DPAD_UP && mPosition == 0) {
            mDataBind.llWifiStateLayout.requestFocus();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == REQUEST_CODE) {//请求权限结果
            int perm = ContextCompat.checkSelfPermission(this, LOCATION_PERMISSION);
            if (perm == PackageManager.PERMISSION_GRANTED) {
                isGranted = true;
                openWifi();
            } else {
                super.onRequestPermissionsResult(requestCode, permissions, grantResults);
            }
        }
    }

    /**
     * wifi广播
     */
    class WifiReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (!isGranted) {
                return;
            }
            String action = intent.getAction();
            LogExtKt.logE("接收到的 : " + action, "");
            if (WifiManager.WIFI_STATE_CHANGED_ACTION.equals(action)) {
                int wifiState = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, 0);
                switch (wifiState) {
                    case WifiManager.WIFI_STATE_DISABLED:
                        ToastUtil.show("WIFI关闭");
                        break;
                    case WifiManager.WIFI_STATE_ENABLED:
                        wifiManager.startScan();//Wifi打开,启动扫描
                        break;
                }
            } else if (WifiManager.SCAN_RESULTS_AVAILABLE_ACTION.equals(action)) {
                loadData();//扫描完成
            } else if (WifiManager.RSSI_CHANGED_ACTION.equals(action)) {
                wifiManager.startScan();//信号强度变化，重新扫描
            } else if (WifiManager.SUPPLICANT_STATE_CHANGED_ACTION.equals(action)) {
                WifiInfo info = wifiManager.getConnectionInfo();
                SupplicantState state = info.getSupplicantState();
                if (state == SupplicantState.COMPLETED) {
                    wifiManager.startScan();//验证成功,启动扫描
                }
                int errorCode = intent.getIntExtra(WifiManager.EXTRA_SUPPLICANT_ERROR, -1);
                if (errorCode == WifiManager.ERROR_AUTHENTICATING) {
                    ToastUtil.show("验证失败");
                }
            } else if ("android.net.conn.CONNECTIVITY_CHANGE".equals(action)) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                if (wifiInfo != null) {
                    String wifiSSID = wifiInfo.getSSID();
                    ToastUtil.show(wifiSSID + "连接成功");
                    AppExtKt.finishActivitys(WiFiConnectActivity.class);
                    mAdapter.notifyDataSetChanged();
                }
            }
        }
    }
}