package com.panda.course.ui.adapter

import android.text.TextUtils
import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.CommonFunctions
import com.panda.course.ext.gone
import com.panda.course.ext.visible
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper

class CommonSpecialFunctionsAdapter : BaseMultiItemQuickAdapter<CommonFunctions, BaseViewHolder>() {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var onItemFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null

    fun setOnViewFocus(onItemFocus: OnViewFocus?) {
        this.onItemFocus = onItemFocus
    }

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()

        addItemType(CommonFunctions.VIEW_DEF, R.layout.item_common)
        addItemType(CommonFunctions.VIEW_TEXT, R.layout.item_common)
        addItemType(CommonFunctions.VIEW_IMG, R.layout.item_common_img)
        addItemType(CommonFunctions.VIEW_BIG_DEF, R.layout.item_common_big)
    }

    override fun convert(holder: BaseViewHolder, item: CommonFunctions) {
        if (holder.itemViewType == CommonFunctions.VIEW_IMG) {
            holder.setText(R.id.tv_item_title, item.title)
            holder.setText(R.id.tv_item_content, item.content)
            holder.getView<TextView>(R.id.tv_item_content).visible()
            holder.getView<TextView>(R.id.tv_item_title).visible()

            Glide.with(context).load(item.url).apply(
                RequestOptions().transform(
                    CenterCrop(), RoundedCornersTransform(context, 10f)
                )
            ).dontAnimate().placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error)
                .into(holder.getView<ImageView>(R.id.iv_item_poster))
        } else {

            holder.setText(R.id.tv_item_title, item.title)
            holder.setText(R.id.tv_item_content, item.content)
            holder.setText(R.id.tv_item_pure_title, item.content)

            val tvContent = holder.getView<TextView>(R.id.tv_item_content)

            //不知道为什么在xml 布局中属性声明不生效，改成代码设置属性
//            tvContent.marqueeRepeatLimit = Integer.MAX_VALUE
//            tvContent.isFocusable = true
//            tvContent.ellipsize = TextUtils.TruncateAt.MARQUEE
//            tvContent.setSingleLine()
//            tvContent.isFocusableInTouchMode = true
//            tvContent.setHorizontallyScrolling(true)
//
//            //设置外边距
//            val lp = tvContent.layoutParams as RelativeLayout.LayoutParams
//            lp.setMargins(0, 10, 10, 0)
//            tvContent.layoutParams = lp

            if (item.showType == CommonFunctions.VIEW_TEXT) {
                holder.getView<TextView>(R.id.tv_item_title).gone()
                holder.getView<TextView>(R.id.tv_item_content).gone()
                holder.getView<ImageView>(R.id.iv_item_arrow).gone()
                holder.getView<TextView>(R.id.tv_item_pure_title).visible()
            } else if (item.showType == CommonFunctions.VIEW_DEF || item.showType == CommonFunctions.VIEW_BIG_DEF) {
                holder.getView<TextView>(R.id.tv_item_title).visible()
                holder.getView<TextView>(R.id.tv_item_content).visible()
                holder.getView<ImageView>(R.id.iv_item_arrow).visible()
                holder.getView<TextView>(R.id.tv_item_pure_title).gone()
            }

            holder.getView<TextView>(R.id.tv_item_pure_title).setTextColor(
                if (item.isSeleced) ContextCompat.getColor(
                    context, R.color.bright_green
                ) else ContextCompat.getColor(context, R.color.white)
            )

        }

        //父布局
        val view = holder.getView<RelativeLayout>(R.id.rl_common_item_view)


        view.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->

            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)

            onItemFocus?.onChangeFocus(hasFocus, getItemPosition(item), view)
            if (item.showType == CommonFunctions.VIEW_IMG) {

            } else {
                if (item.showType == CommonFunctions.VIEW_DEF || item.showType == CommonFunctions.VIEW_BIG_DEF) {
                    holder.getView<TextView>(R.id.tv_item_content).setTextColor(
                        if (hasFocus) ContextCompat.getColor(context, R.color.white) else ContextCompat.getColor(
                            context, R.color.bright_green
                        )
                    )
                    if (hasFocus) {
                        holder.getView<ImageView>(R.id.iv_item_arrow).setImageResource(R.drawable.icon_arrow_white)
                    } else {
                        holder.getView<ImageView>(R.id.iv_item_arrow).setImageResource(R.drawable.icon_arrow)
                    }
                } else {
                    holder.getView<ImageView>(R.id.iv_item_arrow).setImageResource(R.drawable.icon_arrow)
                }


                if (hasFocus && item.isSeleced) {
                    holder.getView<TextView>(R.id.tv_item_pure_title).setTextColor(
                        ContextCompat.getColor(context, R.color.white)
                    )
                } else {
                    holder.getView<TextView>(R.id.tv_item_pure_title).setTextColor(
                        if (item.isSeleced) ContextCompat.getColor(
                            context, R.color.bright_green
                        ) else ContextCompat.getColor(context, R.color.white)
                    )
                }

            }


            if (hasFocus) {
                view.clearAnimation()
                view.startAnimation(scaleAnimation)
            } else {
                view.clearAnimation()
            }
        }
    }


}