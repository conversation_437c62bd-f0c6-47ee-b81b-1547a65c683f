package com.panda.course.ui.activity

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.databinding.ActivitySystemInfoBinding
import com.panda.course.entity.SystemInfo
import com.panda.course.ext.*
import com.panda.course.ui.adapter.SystemAdapter
import com.panda.course.util.FileSizeUtil
import com.panda.course.util.MMKVHelper
import java.lang.reflect.InvocationTargetException


class SystemInfoActivity : BaseDbActivity<BaseViewModel, ActivitySystemInfoBinding>() {


    private val TAG = "SystemInfoActivity"

    private var mAdapter = SystemAdapter()


    override fun initView(savedInstanceState: Bundle?) {
        mDataBind.recycler.setColumnNumbers(1)
        mDataBind.recycler.adapter = mAdapter
        mAdapter.setList(getListData())
        "大小1 = ${FileSizeUtil.formatFileSize(FileSizeUtil.getTotalExternalMemorySize(), false)}".logE(TAG)
        "大小2 = ${FileSizeUtil.formatFileSize(FileSizeUtil.getTotalMemorySize(this), false)}".logE(TAG)
        "大小3 = ${FileSizeUtil.formatFileSize(FileSizeUtil.getAvailableMemory(this), false)}".logE(TAG)
        "大小1 = ${FileSizeUtil.formatFileSize(FileSizeUtil.getAvailableInternalMemorySize(), false)}".logE(TAG)
        "大小1 = ${FileSizeUtil.formatFileSize(FileSizeUtil.getTotalInternalMemorySize(), false)}".logE(TAG)
        "大小1 = ${FileSizeUtil.formatFileSize(FileSizeUtil.getAvailableExternalMemorySize(), false)}".logE(TAG)


        "设备id = ${eventViewModel.appUserInfo.value?.device_id}".logE()
    }


    override fun onBindViewClick() {
        super.onBindViewClick()
        mAdapter.setOnItemClickListener { _, _, position ->
            if (position == mAdapter.data.size - 2) {//如果是最后一个的恢复出厂设置
                showCustomMessageDialog(getString(R.string.sys_info_factory_default), getString(R.string.sys_info_factory_default_tips), getString(R.string.sys_info_factory_default_ok)) {
                    resetSystem()
                }
            } else if (position == mAdapter.data.size - 1) {
                toStartActivity(OTAUpdateActivity::class.java)
            }
        }
    }

    private fun getListData(): ArrayList<SystemInfo> {
        val arrayList = ArrayList<SystemInfo>()
        arrayList.add(SystemInfo(getString(R.string.sys_info_internal_storage), "总共16GB" + "，可用" + FileSizeUtil.formatFileSize(FileSizeUtil.getTotalInternalMemorySize(), false)))
        if (!TextUtils.isEmpty(MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID))) {
            arrayList.add(SystemInfo(getString(R.string.sys_info_device_id), MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID)))
        } else {
            arrayList.add(SystemInfo(getString(R.string.sys_info_device_id), eventViewModel.appUserInfo.value?.device_id))
        }
//        arrayList.add(SystemInfo("设备编号", "" + getSerialNumber()))
        arrayList.add(SystemInfo(getString(R.string.sys_info_device_mac), "" + getWireMac()))
        arrayList.add(SystemInfo(getString(R.string.sys_info_version), "" + getDeviceSystemInt()))
        arrayList.add(SystemInfo(getString(R.string.sys_info_g_version), "" + getDeviceRadio()))
        arrayList.add(SystemInfo(getString(R.string.sys_info_factory_default), getString(R.string.sys_info_factory_default)))
        arrayList.add(SystemInfo(getString(R.string.sys_info_version_update), if (eventViewModel.appOTAUpdate.value == false) getString(R.string.sys_app_version) else getString(R.string.sys_app_new_version)))
        return arrayList
    }


    /**
     * 恢复出厂设置，需要系统权限，以及系统签名
     */
    fun resetSystem() {
        val intent = Intent("android.intent.action.FACTORY_RESET")
        //8.0
        // intent = new Intent("android.intent.action.MASTER_CLEAR");
        //9.0
        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND)
        intent.setPackage("android")

        //以上区分不同系统
        intent.putExtra("android.intent.extra.REASON", "FactoryMode")
        //是否擦除SdCard
        intent.putExtra("android.intent.extra.WIPE_EXTERNAL_STORAGE", true)
        intent.putExtra("android.intent.extra.EXTRA_WIPE_ESIMS", true)
        sendBroadcast(intent)
    }


}