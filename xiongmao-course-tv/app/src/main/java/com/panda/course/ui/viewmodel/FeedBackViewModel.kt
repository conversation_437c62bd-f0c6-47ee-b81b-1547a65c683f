package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse
import java.util.*

class FeedBackViewModel : BaseViewModel() {

    // 测评课程
    var feedBackConfigEntity = MutableLiveData<FeedBackConfigEntity>()
    var questionEntity = MutableLiveData<FeedBackQuestionListEntity>()

    // 反馈成功
    var feedback = MutableLiveData<Any>()


    /**
     * 获取反馈的配置
     */
    fun getFeedBackConfig() {
        rxHttpRequest {
            onRequest = {
                feedBackConfigEntity.value = RxHttp.get(NetUrl.GET_FEEDBACK_SETTING)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<FeedBackConfigEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_FEEDBACK_SETTING
        }
    }


    /**
     * 获取问题列表
     */
    fun getHelpList(tag_name: String) {
        rxHttpRequest {
            onRequest = {
                questionEntity.value = RxHttp.get(NetUrl.HELP_QUESTION_LIST)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("tag_name", tag_name)
                    .toResponse<FeedBackQuestionListEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.HELP_QUESTION_LIST
        }
    }

    /**
     * 提交反馈申请
     */
    fun sendFeedback(tag_name: String) {
        rxHttpRequest {
            onRequest = {
                feedback.value = RxHttp.get(NetUrl.FEED_BACK_CREATE)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("tag_name", tag_name)
                    .toResponse<Any>()
                    .await()
            }
            loadingType = LoadingType.LOADING_DIALOG
            requestUrl = NetUrl.FEED_BACK_CREATE
        }
    }
}