package com.panda.course.ui.activity.dialog

import android.animation.Animator
import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.animation.Animation
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.appContext
import com.panda.course.databinding.ActivitySkillRecordBinding
import com.panda.course.entity.AuntSkillRecordListEntity
import com.panda.course.ext.*
import com.panda.course.ui.adapter.SkillRecordAdapter
import com.panda.course.ui.viewmodel.SkillViewModel
import com.panda.course.util.AnimationUtils
import com.panda.course.util.Constants
import com.panda.course.util.MMKVHelper
import java.util.*

class SkillRecordActivity : BaseDbActivity<SkillViewModel, ActivitySkillRecordBinding>() {

    val TAG = "WebViewHelper"

    private var mSkillRecordAdapter = SkillRecordAdapter(true)

    private var mPosition = -1

    val pointManger = LinearLayoutManager(this)

    private var numberCount = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        if (getCurrentAndroid9()) {
            hookWebView()
        }
        super.onCreate(savedInstanceState)
    }

    override fun initView(savedInstanceState: Bundle?) {

        initWebView()

        mDataBind.recyclerChild.adapter = mSkillRecordAdapter
        mDataBind.recyclerChild.setColumnNumbers(1)

        intent?.extras?.apply {
            val arr = this.getParcelableArrayList<AuntSkillRecordListEntity>("record")
            if (arr != null) {
                mSkillRecordAdapter.setList(arr)
            }
            mPosition = this.getInt("position")

            mDataBind.recyclerChild.layoutManager?.scrollToPosition(mPosition)

            if (mPosition <= mSkillRecordAdapter.data.size - 1) {
                mDataBind.recyclerChild.postDelayed({
                    mSkillRecordAdapter.getViewByPosition(mPosition, R.id.row_card_view)?.requestFocus()
                }, 100)
            }
        }


    }


    private fun initWebView() {

        mDataBind.webView.settings.apply {
            //开启js
            javaScriptEnabled = true
            //弹出框的设置
            //1.NARROW_COLUMNS：可能的话使所有列的宽度不超过屏幕宽度
            //2.NORMAL：正常显示不做任何渲染
            //3.SINGLE_COLUMN：把所有内容放大webview等宽的一列中
            layoutAlgorithm = WebSettings.LayoutAlgorithm.NARROW_COLUMNS
            //它会使用网页元标记中定义的属性加载WebView。因此它按照html中的定义缩放网页。
            useWideViewPort = true

            /************ 缓存模式 **********/
            //保存密码
            savePassword = true
            //保存表单数据
            saveFormData = true
            //开启数据库
            databaseEnabled = true
            //设置DOM Storage缓存
            domStorageEnabled = true
            //关闭webView中缓存
            cacheMode = WebSettings.LOAD_NO_CACHE
            //设置缓存路径
            setAppCachePath(cacheDir.absolutePath)

            /************ 页面自动适配 **********/
            //步骤1.隐藏webview缩放按钮
            displayZoomControls = true

            /************ 页面缩放支持 **********/
            //仅支持双击缩放，不支持触摸缩放（android4.0）
            setSupportZoom(true)
            //设置支持缩放,设置了此属性，setSupportZoom(true);也默认设置为true
            builtInZoomControls = true

            /************ 图片加载 **********/
            //默认为false,true表示阻塞图片请求
            blockNetworkImage = false
            //支持自动加载图片
            loadsImagesAutomatically = true

            /************ 字体相关 **********/
            //设置WebView标准字体库字体，默认字体“sans-serif”。
            standardFontFamily = ""
            //设置WebView字体最小值，默认值8，取值1到72
//            minimumFontSize = 8
            //设定编码格式
            defaultTextEncodingName = "UTF-8"
            //设置在WebView内部是否允许访问文件
            allowFileAccess = true
            //设置WebView中加载页面字体变焦百分比，默认100，整型数。
            textZoom = 100

            /************ 插件相关 **********/
            //支持插件
            pluginState = WebSettings.PluginState.ON
            setRenderPriority(WebSettings.RenderPriority.HIGH)
            //多窗口
            //当webview调用requestFocus时为webview设置节点 webview
            setNeedInitialFocus(true)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                //设置Web调试
                WebView.setWebContentsDebuggingEnabled(true)

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    //解决加载Https和Http混合模式网页加载问题
                    mixedContentMode = WebSettings.LOAD_NORMAL
                }
            }
            //追加自定义标识符，一定要+=
            val agent = "XmjzCapp/" + getAppVersion(appContext) + " " + userAgentString
            userAgentString = agent
        }

        mDataBind.webView.addJavascriptInterface(jsBridge(), Constants.JS_SPACE_NAME)


        mDataBind.webView.onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                mDataBind.flController.setBackgroundResource(R.drawable.skill_stoke_focus)
                mDataBind.tvTips.visible()
            } else {
                mDataBind.flController.background = null
                mDataBind.tvTips.gone()
            }
        }

        //设置进度条
        mDataBind.webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView, newProgress: Int) {

                if (newProgress == 100) {
                    mDataBind.webView.scrollTo(0, 0)
                    mDataBind.progress.gone()//加载完网页进度条消失
                } else {
                    mDataBind.progress.visible()//开始加载网页时显示进度条
                    mDataBind.progress.progress = newProgress //设置进度值
                }
            }
        }

    }


    private inner class jsBridge {
        @get:JavascriptInterface
        val userToken: Unit
            get() {
                val js = Constants.JS_JAVA_SCRIPT + Constants.JS_SPACE_NAME + "getUserToken" + Constants.JS_SPACE_CALLBACK + "('" + MMKVHelper.decodeString(ConstantMMVK.TOKEN) + "')"
                evaluateJavascript(js)
            }
    }


    private fun evaluateJavascript(js: String) {
        mDataBind.webView.post {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                mDataBind.webView.evaluateJavascript(js, null)
            } else {
                mDataBind.webView.loadUrl(js)
            }
        }
    }

    /**
     * 给链接增加header参数
     *
     * @return
     */
    private val header: Map<String, String>
        private get() {
            val stringMap: MutableMap<String, String> = HashMap()
            stringMap["xmjztoken"] = "${MMKVHelper.decodeString(ConstantMMVK.TOKEN)}"
            stringMap["xmjzdevice"] = Build.MODEL
            stringMap["xmjzplatform"] = "tvbox"
            stringMap["xmjzversion"] = getAppVersion(appContext)
            return stringMap
        }


    override fun initObserver() {
        super.initObserver()

        mSkillRecordAdapter.setOnViewFocus(OnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                numberCount = 0
                mDataBind.tvTips.visible()
                mDataBind.webView.loadUrl(mSkillRecordAdapter.data[position].h5_url.toString(), header)
            }
        })

    }

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (event.keyCode == KeyEvent.KEYCODE_DPAD_DOWN && mDataBind.webView.isFocused) {
            "WebView ===  $numberCount".logE()
            numberCount++
            if (numberCount == 10) {
                mDataBind.tvTips.startAnimation(AnimationUtils.getHiddenAlphaAnimation(1000, object : Animation.AnimationListener {
                    override fun onAnimationStart(animation: Animation?) {

                    }

                    override fun onAnimationEnd(animation: Animation?) {
                        mDataBind.tvTips.gone()
                    }

                    override fun onAnimationRepeat(animation: Animation?) {
                    }

                }))
            }
        }
        return super.dispatchKeyEvent(event)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        return super.onKeyDown(keyCode, event)
    }


}