package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class UserEntity(
    var store_id: String? = null,
    var uuid: String? = null,
    var name: String? = null,
    var nick_name: String? = null,
    var company_name: String? = null,
    var store_name: String? = null,
    var avatar: String? = null,
    var token: String? = null,
    var login_status: Boolean = false,
    var store_log: String? = null,
    var im_user_id: String? = null,
    var im_user_sig: String? = null,
    var expire_start_date: String? = null,
    var expire_end_date: String? = null,
    var device_id: String? = "",
    var im_group_id: String? = "",
    var expiration_notice: String? = "",
) : Parcelable