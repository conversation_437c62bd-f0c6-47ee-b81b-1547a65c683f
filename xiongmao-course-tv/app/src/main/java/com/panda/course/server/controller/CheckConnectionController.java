package com.panda.course.server.controller;

import com.panda.course.ext.AppExtKt;
import com.panda.course.util.tvremote.NetUtil;
import com.yanzhenjie.andserver.RequestHandler;
import com.yanzhenjie.andserver.util.HttpRequestParser;

import org.apache.httpcore.HttpException;
import org.apache.httpcore.HttpRequest;
import org.apache.httpcore.HttpResponse;
import org.apache.httpcore.entity.StringEntity;
import org.apache.httpcore.protocol.HttpContext;

import java.io.IOException;
import java.util.Map;

//查看是否有存在通讯不成功

public class CheckConnectionController implements RequestHandler {

    @Override
    public void handle(HttpRequest request, HttpResponse response, HttpContext context) throws HttpException, IOException {
        //进来这里，代表已经建立通讯机制了，这个时候提示用户

        Map<String, String> params = HttpRequestParser.parseParams(request);
        StringEntity stringEntity = new StringEntity("0", "utf-8");
        response.setEntity(stringEntity);
        AppExtKt.getEventViewModel().getAppRemote().postValue(true);
    }
}
