package com.panda.course.ui.activity

import android.os.Bundle
import android.text.TextUtils
import com.amlogic.update.OtaUpgradeUtils
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityOtaUpdateBinding
import com.panda.course.entity.DownloadTask
import com.panda.course.entity.UpdateInfo
import com.panda.course.ext.*
import com.panda.course.service.MultiTaskDownloader
import com.panda.course.ui.viewmodel.SettingModel
import com.panda.course.util.Preferences
import okio.ByteString.Companion.encodeUtf8
import java.io.File
import java.text.DecimalFormat
import java.util.*

/**
 * /storage/emulated/0/Android/data/com.panda.course/cache/
 */

class OTAUpdateActivity : BaseDbActivity<SettingModel, ActivityOtaUpdateBinding>() {

    private var updateInfo: UpdateInfo? = null
    private var mUpdateUtils: OtaUpgradeUtils? = null


    override fun initView(savedInstanceState: Bundle?) {
        mViewModel.getOTAInfo()
        mUpdateUtils = OtaUpgradeUtils(this)

        "${getDeviceRadio()}".logE()
        "${getDeviceRadioFirmware()}".logE()

        mDataBind.tvTitle.text = "${getDeviceRadio()}"
        mDataBind.tvContent.text = "当前已是最新的系统"
        mDataBind.tvContent.textSize = 40f
    }


    override fun onRequestSuccess() {
        super.onRequestSuccess()
        mViewModel.updateInfo.observe(this, androidx.lifecycle.Observer{
            if (it == null) {
                return@Observer
            }
            updateInfo = it
            mDataBind.tvTitle.text = it.title
            mDataBind.tvContent.text = it.desc
            mDataBind.tvTitle.textSize = 40f
            mDataBind.tvContent.textSize = 30f
            mDataBind.butOtaUpdate.visible()
            mDataBind.butOtaUpdate.post { mDataBind.butOtaUpdate.requestFocus() }
        })
    }

    override fun onBindViewClick() {
        super.onBindViewClick()
        mDataBind.butOtaUpdate.setOnClickListener {
            startDownloadOTA()
            MultiTaskDownloader.allLiveTask.observe(this, androidx.lifecycle.Observer{
                if (it != null && it.size > 0) {
                    for (i in it.indices) {
                        if (it[i].url.contains(".zip")) {
                            val downloadTask = it[i]
                            mDataBind.butOtaUpdate.text = "正在下载...${downloadTask.progress}%"
                            "下载中 = ${downloadTask.localPath}".logE()
                            if (downloadTask.state == MultiTaskDownloader.COMPLETED) {
                                installOtaPackage(File(downloadTask.localPath))
                            } else if (downloadTask.state == MultiTaskDownloader.FAIL) {
                                mDataBind.butOtaUpdate.text = "点击重试"
                            }
                            break
                        }
                    }
                }
            })
        }
    }


    private fun startDownloadOTA() {
        if (!TextUtils.isEmpty(updateInfo?.url)) {
            val allTask = ArrayList<DownloadTask>() //所有下载任务
            val task = DownloadTask(updateInfo?.url)
            val suffix: String = updateInfo?.url!!.substring(updateInfo?.url!!.lastIndexOf("."))
            task.localPath = externalCacheDir.toString() + "/" + updateInfo?.url!!.encodeUtf8().md5().hex() + suffix
            allTask.add(task)
            MultiTaskDownloader.addTasks(allTask)
            MultiTaskDownloader.startAllDownloadTask()
            mDataBind.butOtaUpdate.text = "正在获取下载数据中"
        } else {
            mDataBind.butOtaUpdate.text = "远程服务器OTA文件有误"
        }
    }

    /**
     * 功能：检验、安装 OTA 包
     * 参数 otaFile ： OTA 包的 File 对象
     */
    private fun installOtaPackage(otaFile: File) {
//        val otaFile = File("/storage/emulated/0/update.zip")
        mUpdateUtils?.registerBattery()
        mUpdateUtils?.upgrade(otaFile, object : OtaUpgradeUtils.ProgressListener {
            override fun onProgress(p0: Int) {
                showLoadingExt(message = "正在更新中...$p0%")
            }

            override fun onVerifyFailed(p0: Int, p1: Any?) {
                mUpdateUtils?.unregistBattery()
                dismissLoadingExt()
            }

            override fun onCopyProgress(p0: Int) {
            }

            override fun onCopyFailed(p0: Int, p1: Any?) {
                mUpdateUtils?.unregistBattery()
                dismissLoadingExt()
            }

            override fun onStopProgress(p0: Int) {
                mUpdateUtils?.unregistBattery()
                dismissLoadingExt()
            }

        }, OtaUpgradeUtils.UPDATE_OTA)
    }


    override fun onDestroy() {
        super.onDestroy()
        dismissLoadingExt()
        MultiTaskDownloader.cancelAllTask()
        mUpdateUtils?.unregistBattery()
    }


}