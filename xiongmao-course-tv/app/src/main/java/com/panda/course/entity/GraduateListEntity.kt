package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class GraduateListEntity(
    var list: List<GraduateListImgEntity> = emptyList(),
    var student_list: List<GraduateStudentListImgEntity> = emptyList(),
    var current_date: String? = null
) : Parcelable


@Parcelize
data class GraduateListImgEntity(
    var img_url: String = "",
    var content_color: String = "",
    var click_type: String = "0",
) : Parcelable

@Parcelize
data class GraduateStudentListImgEntity(
    var service_true_name: String = "",
) : Parcelable