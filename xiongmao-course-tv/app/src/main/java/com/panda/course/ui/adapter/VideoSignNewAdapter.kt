package com.panda.course.ui.adapter

import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.noober.background.drawable.DrawableCreator
import com.noober.background.view.BLTextView
import com.panda.course.R
import com.panda.course.entity.VideoDynamicEffect
import com.panda.course.ext.gone
import com.panda.course.ext.visible
import com.tencent.qcloud.tuicore.util.ScreenUtil.dip2px


class VideoSignNewAdapter : BaseQuickAdapter<VideoDynamicEffect, BaseViewHolder>(R.layout.item_sign_new) {

    override fun convert(holder: BaseViewHolder, data: VideoDynamicEffect) {
        holder.setText(R.id.tv_item_name_new_sign, data.service_personnel_name)
                .setText(R.id.tv_item_name_new_score, data.score + "分")

        if (!TextUtils.isEmpty(data.score)) {
            holder.getView<TextView>(R.id.tv_item_name_new_score).visible()
        } else {
            holder.getView<TextView>(R.id.tv_item_name_new_score).gone()
        }

        Glide.with(context).asBitmap().apply(RequestOptions.bitmapTransform(CircleCrop()))
                .placeholder(R.drawable.def_pic).error(R.drawable.def_pic).load(data.avatar)
                .into(holder.getView(R.id.iv_item_new_pic))


    }


}