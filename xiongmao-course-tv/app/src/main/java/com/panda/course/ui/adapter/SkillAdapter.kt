package com.panda.course.ui.adapter

import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.AuntSkillListEntity
import com.panda.course.entity.RowStringEntity
import com.panda.course.ext.dp2px
import com.panda.course.ext.px2dp
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper

class SkillAdapter : BaseQuickAdapter<AuntSkillListEntity, BaseViewHolder>(R.layout.item_skill) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var onItemFocus: OnViewFocus? = null


    fun setOnViewFocus(onItemFocus: OnViewFocus?) {
        this.onItemFocus = onItemFocus
    }

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    MyFocusHighlightHelper.BrowseItemFocusHighlight(MyFocusHighlightHelper.ZOOM_FACTOR_XXXSMALL, false)
        }
    }

    override fun convert(holder: BaseViewHolder, item: AuntSkillListEntity) {

        holder.setText(R.id.tv_item_skill_name, item.title)
                .setText(R.id.tv_item_skill_salary, "行业工资：\n${item.min_salary?.let { resultSalary(it) }}-${item.max_salary?.let { resultSalary(it) }}元/月")
                .setText(R.id.tv_item_skill_info, item.intro)

        val cardView = holder.getView<CardView>(R.id.row_card_view)

        val salary = holder.getView<TextView>(R.id.tv_item_skill_salary)

        // 隐藏的代码是为了需求变更使用、之前是view放大，现在是View 选中状态
        cardView.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->

            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)

            onItemFocus?.onChangeFocus(hasFocus, getItemPosition(item), v)

            salary.setTextColor(if (hasFocus) context.getColor(R.color.white) else context.getColor(R.color.bright_green))

            // 设置阴影
            cardView.cardElevation = if (hasFocus) px2dp(20f).toFloat() else px2dp(0f).toFloat()

            cardView.setCardBackgroundColor(
                    if (hasFocus) ContextCompat.getColor(
                            context, R.color.black
                    ) else ContextCompat.getColor(context, R.color.transparent)
            )

            cardView.radius = if (hasFocus) 16f else 0f
        }
    }

    private fun resultSalary(salary: String): String {
        return salary.substring(0, salary.indexOf("."))
    }

}