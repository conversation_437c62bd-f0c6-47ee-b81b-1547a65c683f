package com.panda.course.ui.activity.dialog

import android.graphics.Color
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.Ktx
import com.panda.course.databinding.ActivityRankingBinding
import com.panda.course.entity.AuntSkillRecordListEntity
import com.panda.course.ext.*
import com.panda.course.ui.activity.ExamRankActivity
import com.panda.course.ui.adapter.TopAdapter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.ui.viewmodel.InteractionViewModel
import com.panda.course.util.QRCodeUtil
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.util.RxTimerUtil
import io.reactivex.rxjava3.disposables.Disposable

/**
 * 结业考试 - 排行榜
 */
class RankingDialogActivity : BaseDbActivity<InteractionViewModel, ActivityRankingBinding>() {

    private var mCourseCode = ""
    private var student_number = ""
    private var mCourseName = ""
    private var mAdapter = TopAdapter()
    private var isStore = true //记录是门店还是历史
    private var isType = 1 //记录天、月、历史
    private var mTopDisposable: Disposable? = null //用来循环当天的数据
    private var mListPosition = -1

    private var mTag = 1 //记录上一个在那里

    /**
     * 初始化view
     */
    override fun initView(savedInstanceState: Bundle?) {
        setDialogWindowParams(window, 1.0f, 1.0f)

        intent.extras?.apply {
            mCourseName = getString("mCourseName").toString()
            mCourseCode = getString("course_detail_code").toString()
            getString("qr_code_url").notTextNull {

                mDataBind.ivPopupQrCode.setImageBitmap(QRCodeUtil.getInstance().createQRCode(it))

//                Glide.with(this@RankingDialogActivity).asBitmap().load(it).apply(
//                    RequestOptions().transform(
//                        CenterCrop(),
//                        RoundedCornersTransform(this@RankingDialogActivity, 6f, rightBottom = false, leftBottom = false)
//                    )
//                ).into(mDataBind.ivPopupQrCode)
            }
        }

        mDataBind.tvPopupDay.post {
            mDataBind.tvPopupDay.requestFocus()
        }
        visibleViews(mDataBind.rlTopLayout, mDataBind.rlQrLayout, mDataBind.viewLine)

        //设置View
        mDataBind.recyclerTask.setColumnNumbers(1)
        mDataBind.recyclerTask.adapter = mAdapter
    }

    override fun initObserver() {
        super.initObserver()

        mAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                when (isType) {
                    1 -> {
                        if (isStore) {
                            mDataBind.tvPopupDay.setBackgroundResource(R.drawable.base_border_selecter_round)
                        } else {
                            mDataBind.tvPopupExamination.setBackgroundResource(R.drawable.base_border_selecter_round)
                        }
                    }

                    2 -> {
                        mDataBind.tvPopupInteraction.setBackgroundResource(R.drawable.base_border_selecter_round)
                    }
                }
                isType = -1
                mListPosition = position
            }
        }

        mAdapter.setOnItemClickListener { adapter, view, position ->
            //跳转新的技能测评 做区分 只有全国不跳转
            if (isStore) {
                toStartActivity(ExamRankActivity::class.java, Bundle().apply {
                    putString("code", mCourseCode)
                    putInt("student_number", position)
                    putInt("channel", mTag)
                })
            }
        }


        mDataBind.tvPopupExamination.setOnFocusChangeListener { _, hasFocus -> //全国
            if (hasFocus) {
                appUMEvent(UMConstant.National)
                mDataBind.tvPopupExamination.setBackgroundResource(R.drawable.base_border_round)
                isStore = false
                mAdapter.setStore(isStore)
                isType = 1
                mTag = 3
                getTopList()
            }
        }
        mDataBind.tvPopupInteraction.setOnFocusChangeListener { _, hasFocus -> //门店当月
            if (hasFocus) {
                appUMEvent(UMConstant.TOP_WEEK)
                mDataBind.tvPopupInteraction.setBackgroundResource(R.drawable.base_border_round)
                isStore = true
                mAdapter.setStore(isStore)
                isType = 2
                mTag = 2
                getTopList()
            }
        }
        mDataBind.tvPopupDay.setOnFocusChangeListener { _, hasFocus -> //门店当日
            appUMEvent(UMConstant.TOP_DAY)
            if (hasFocus) {
                mDataBind.tvPopupDay.setBackgroundResource(R.drawable.base_border_round)
                isStore = true
                mAdapter.setStore(isStore)
                isType = 1
                mTag = 1
                getTopList()
            }
        }


    }


    override fun onRequestSuccess() {
        super.onRequestSuccess()
        mViewModel.topList.observe(this, androidx.lifecycle.Observer {
            mAdapter.setList(it.list)
            mDataBind.progress.gone()
            if (it.list != null && it.list.size > 0) {
                mDataBind.tvEmpty.gone()
            } else {
                mDataBind.tvEmpty.visible()
            }
        })

    }

    /**
     * 记录当前的View
     * type 1 天 2 月 3 历史
     */
    private fun getTopList() {
        showProgressBar()
        closeStoreDispose()
        if (isStore) {
            mViewModel.getTopList(code = mCourseCode, type = isType)
        } else {
            mViewModel.getAllTopList(code = mCourseCode)
        }
        if (isStore && isType == 1) {
            mTopDisposable = RxTimerUtil.interval(3000).subscribe {
                mViewModel.getTopList(code = mCourseCode, type = isType)
            }
        } else {
            closeStoreDispose()
        }
    }


    /**
     * 进度条的处理
     */
    private fun showProgressBar() {
        if (mAdapter.data.size >= 1) {
            mDataBind.progress.gone()
        } else {
            mDataBind.progress.visible()
        }
    }

    /**
     * 销毁门店的定时请求
     */
    private fun closeStoreDispose() {
        mTopDisposable?.dispose()
        mTopDisposable = null
    }

    override fun onDestroy() {
        super.onDestroy()
        closeStoreDispose()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_DPAD_LEFT,
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (isType == -1) {
                    return true
                }
            }

            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (mAdapter.data.size <= 0) {
                    if (mDataBind.tvPopupDay.isFocused || mDataBind.tvPopupExamination.isFocused || mDataBind.tvPopupInteraction.isFocused) {
                        return true
                    }

                }
            }

            KeyEvent.KEYCODE_DPAD_UP -> {
                if (mDataBind.tvPopupDay.isFocused || mDataBind.tvPopupExamination.isFocused || mDataBind.tvPopupInteraction.isFocused) {
                    return true
                }
                if (isType == -1 && mListPosition == 0) {//说明当前的焦点在adapter
                    when (mTag) {
                        1 -> {
                            mDataBind.tvPopupDay.requestFocus()
                        }

                        2 -> {
                            mDataBind.tvPopupInteraction.requestFocus()
                        }

                        3 -> {
                            mDataBind.tvPopupExamination.requestFocus()
                        }
                    }
//                    if (isStore) { //判断是那个bug
//                        mDataBind.tvPopupDay.requestFocus();
//                    } else {
//                        mDataBind.tvPopupExamination.requestFocus();
//                    }
                }
            }
        }
        return super.onKeyDown(keyCode, event)
    }
}