package com.panda.course.widget

import android.animation.*
import android.content.Context
import android.graphics.PointF
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import com.panda.course.R
import kotlin.math.pow

/**
 * 赞飘❤️的动画视图
 */
class LikeAnimView : FrameLayout {

    private val likeImages = mutableListOf<Int>()
    private val likeViewSize = 89 // 单个❤️的size
    private var likeImageIndex = 0

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        likeImages.apply {
            add(R.drawable.dianzan1)
            add(R.drawable.dianzan2)
            add(R.drawable.dianzan4)
            add(R.drawable.dianzan5)
            add(R.drawable.dianzan6)
            add(R.drawable.dianzan7)
        }
    }


    fun addFavor() {
        try {

            val imageView = ImageView(context)
            imageView.layoutParams = ViewGroup.LayoutParams(likeViewSize, likeViewSize)
            imageView.setImageResource(likeImages[likeImageIndex++ % likeImages.size]) // 指定初始位置为底部居中
            imageView.x = ((measuredWidth - imageView.layoutParams.width) / 2).toFloat()
            imageView.y = (measuredHeight - imageView.layoutParams.height).toFloat()
            this.addView(imageView)

            // 开启动画
            startAnim(imageView)

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun startAnim(target: View) {
        try {
            if (measuredWidth <= likeViewSize) {//如果当前绘制的宽度小于89 则不处理了 不然会报Cannot get random in empty range: 0..-89
                return
            }
            if (measuredHeight <= likeViewSize) {//如果当前绘制的宽度小于89 则不处理了 不然会报Cannot get random in empty range: 0..-89
                return
            }
            val enterAnim = generateEnterAnim(target)
            val curveAnim = generateCurveAnim(target)
            val anim = AnimatorSet().apply {
                playSequentially(enterAnim, curveAnim)
            }
            anim.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator?) {
                    removeView(target)
                }
            })
            anim.start()

        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    /**
     * 生成❤️出现动画
     */
    private fun generateEnterAnim(target: View): AnimatorSet {
        val alpha = ObjectAnimator.ofFloat(target, "alpha", 0.2f, 1f)
        val scaleX = ObjectAnimator.ofFloat(target, "scaleX", 0.5f, 1f)
        val scaleY = ObjectAnimator.ofFloat(target, "scaleY", 0.5f, 1f)
        return AnimatorSet().apply {
            playTogether(alpha, scaleX, scaleY)
            duration = 150
        }
    }

    /**
     * 生成❤️上浮轨迹动画
     */
    private fun generateCurveAnim(target: View): ValueAnimator { // 起始点，以target的初始位置为准
        val startPointF = PointF(target.x, target.y) // 结束点，顶部，给个随机范围
        val endPointF = PointF(
            (0..(measuredWidth - likeViewSize)).random().toFloat(), 0f
        ) // 贝塞尔曲线第一个控制点
        val pointF1 = PointF(
            (0..(measuredWidth - likeViewSize)).random().toFloat(), (0..measuredHeight / 2).random().toFloat()
        ) // 贝塞尔曲线第二个控制点
        val pointF2 = PointF(
            (0..(measuredWidth - likeViewSize)).random().toFloat(),
            (0..measuredHeight / 2).random().toFloat() + measuredHeight / 2
        ) // 估值器
        val evaluator = CurveEvaluator(pointF2, pointF1)
        val valueAnimator = ValueAnimator.ofObject(evaluator, startPointF, endPointF).apply {
            duration = 2000
            interpolator = LinearInterpolator() // 线性插值器
        }
        valueAnimator.setTarget(target)
        valueAnimator.addUpdateListener { animator ->
            val pointF = animator.animatedValue as PointF
            target.apply {
                x = pointF.x
                y = pointF.y
                alpha = 1 - animator.animatedFraction
            }
        }
        return valueAnimator
    }

    /**
     * 估值器（根据当前属性值改变的百分比来计算改变后的属性值）
     * 三阶贝塞尔曲线，两个控制点
     */
    class CurveEvaluator(
        private val ctrlPointF1: PointF, private val ctrlPointF2: PointF
    ) : TypeEvaluator<PointF> {
        override fun evaluate(
            fraction: Float, startValue: PointF, endValue: PointF
        ): PointF { // 三阶贝塞尔曲线公式 B(t) = P0 * (1-t)^3 + 3 * P1 * t * (1-t)^2 + 3 * P2 * t^2 * (1-t) + P3 * t^3, t ∈ [0,1]
            // P0、P3分别为起始点和终止点，P1、P2为两个控制点，t为曲线长度比例
            val leftFraction = 1.0f - fraction
            return PointF().apply {
                x =
                    leftFraction.pow(3) * startValue.x + 3 * leftFraction.pow(2) * fraction * ctrlPointF1.x + 3 * leftFraction * fraction.pow(
                        2
                    ) * ctrlPointF2.x + fraction.pow(3) * endValue.x
                y =
                    leftFraction.pow(3) * startValue.y + 3 * leftFraction.pow(2) * fraction * ctrlPointF1.y + 3 * leftFraction * fraction.pow(
                        2
                    ) * ctrlPointF2.y + fraction.pow(3) * endValue.y
            }
        }
    }
}
