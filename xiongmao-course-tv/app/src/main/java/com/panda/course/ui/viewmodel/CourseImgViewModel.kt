package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.CourseImgEntity
import com.panda.course.entity.QRCodeEntity
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class CourseImgViewModel : BaseViewModel() {

    // 登录二维码
    var courseImgEntity = MutableLiveData<CourseImgEntity>()

    /**
     * 获取courseImg
     */
    fun getCourseImg(code: String) {
        rxHttpRequest {
            onRequest = {
                courseImgEntity.value = RxHttp.get(NetUrl.GET_SCHEDULE_IMG).add("device_code", getSerialNumber())
                    .add("device_mac", getWireMac()).add("device_id", getAndroidId()).add("code", code)
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac())).toResponse<CourseImgEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_CUSTOM
            requestUrl = NetUrl.GET_SCHEDULE_IMG
        }
    }
}