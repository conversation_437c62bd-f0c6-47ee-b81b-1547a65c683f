package com.panda.course.ui.viewmodel

import androidx.lifecycle.MutableLiveData
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.AuntSkillEntity
import com.panda.course.entity.QRCodeEntity
import com.panda.course.entity.RedPaperEntity
import com.panda.course.ext.getAndroidId
import com.panda.course.ext.getWireMac
import com.panda.course.ext.md5Digest
import com.panda.course.ext.rxHttpRequest
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class ExpandInterfaceViewModel : BaseViewModel() {

    // 获取红包动效
    var redPaperEntity = MutableLiveData<RedPaperEntity>()

    /**
     * 获取全部的红包特效
     */
    fun getRedPaper(number: String?, tv_device_id: String) {
        rxHttpRequest {
            onRequest = {
                redPaperEntity.value = RxHttp.get(NetUrl.GET_RED_PAPER)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .add("number", "" + number)
                    .add("tv_device_id", "" + tv_device_id)
                    .toResponse<RedPaperEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.GET_RED_PAPER
        }
    }
}