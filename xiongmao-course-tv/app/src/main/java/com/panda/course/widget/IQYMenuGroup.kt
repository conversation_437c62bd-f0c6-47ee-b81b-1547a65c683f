package com.panda.course.widget;

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.ViewTreeObserver
import android.widget.LinearLayout
import com.panda.course.R

class IQYMenuGroup @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    constructor(context: Context, targetView: View) : this(context) {
        setTargetView(targetView)
    }

    /**
     * 设置目标控件
     */
    private lateinit var targetView: View

    /**
     * 当按下键失去焦点时，是否让目标控件不可见，默认true
     */
    private var goneWhenDownLostFocus: Boolean = true

    /**
     * 往上失去焦点时，是否让目标控件不可见，默认false
     */
    private var goneWhenUpLostFocus: Boolean = false

    /**
     * 当获取焦点时，是否让目标view可见
     */
    private var visibleWhenGainFocus: Boolean = true

    private var isMemoryEnabled: Boolean = true

    private var targetViewId: Int = NO_ID

    init {
        isFocusableInTouchMode = true
        descendantFocusability = FOCUS_AFTER_DESCENDANTS

        val ta = context.obtainStyledAttributes(attrs, R.styleable.IQYMenuGroup)
        try {
            targetViewId = ta.getResourceId(R.styleable.IQYMenuGroup_iqy_targetViewId, targetViewId)
        } finally {
            ta.recycle()
        }
    }

    private var onTargetViewFocusChanged: ((hasFocus: Boolean) -> Unit)? = null

    /**
     * 当前焦点记忆的View
     */
    private var memoryView: View? = null

    private var expectTargetViewVisibility: Int = VISIBLE
    private var preHasFocus: Boolean = hasFocus()


    private val globalViewTreeObserver = object : ViewTreeObserver.OnGlobalFocusChangeListener {
        override fun onGlobalFocusChanged(oldFocus: View?, newFocus: View?) {
            val new = hasFocus()
            if (preHasFocus == new)
                return
            preHasFocus = new
            if (::targetView.isInitialized && targetView.visibility != expectTargetViewVisibility) {
                targetView.visibility = expectTargetViewVisibility
            }
            onTargetViewFocusChanged?.invoke(new)
        }
    }

    fun getTargetView(): View {
        return targetView
    }

    fun hideTargetView() {
        expectTargetViewVisibility = GONE
        if (::targetView.isInitialized) {
            targetView.visibility = expectTargetViewVisibility
        }
    }

    fun visibleTargetView() {
        expectTargetViewVisibility = VISIBLE
        if (::targetView.isInitialized) {
            targetView.visibility = expectTargetViewVisibility
        }
    }

    fun resetMemoryEnabled() {
        memoryView = null
    }

    /**
     * 设置目标view：暂时用这种方法更好表述逻辑，实际为了方便代码调用，可以考虑自定义属性指向目标view的id，也可以用自定义构造函数传递依赖
     */
    fun setTargetView(view: View) {
        this.targetView = view
        this.expectTargetViewVisibility = view.visibility
    }

    fun setTargetViewFocusChangeListener(listener: ((hasFocus: Boolean) -> Unit)?) {
        this.onTargetViewFocusChanged = listener
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        preHasFocus = hasFocus()
        viewTreeObserver.addOnGlobalFocusChangeListener(globalViewTreeObserver)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        viewTreeObserver.removeOnGlobalFocusChangeListener(globalViewTreeObserver)
    }

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        val handled = super.dispatchKeyEvent(event)
        if (handled)
            return true

        if (event.action != KeyEvent.ACTION_DOWN)
            return handled

        when (event.keyCode) {
            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (goneWhenDownLostFocus && ::targetView.isInitialized && targetView.visibility != GONE) {
                    expectTargetViewVisibility = GONE
                }
                return handled
            }

            KeyEvent.KEYCODE_DPAD_UP -> {
                if (goneWhenUpLostFocus && ::targetView.isInitialized && targetView.visibility != GONE) {
                    expectTargetViewVisibility = GONE
                }
                return handled
            }
            else -> {
                return handled
            }
        }
    }

    override fun requestChildFocus(child: View?, focused: View?) {
        Log.d(
            "IQYMenu",
            "${this.hashCode()} requestChildFocus(child=${child} focused=${focused},focusedChild = $focusedChild)"
        )
        focused?.let {
            requestChildFocusForTargetVisibility(it)
        }
        super.requestChildFocus(child, focused)
        Log.d(
            "IQYMenu",
            "${this.hashCode()} requestChildFocus2(child=${child} focused=${focused},focusedChild = $focusedChild)"
        )
        if (!isMemoryEnabled) {
            memoryView = null
            return
        }
        memoryView =
            if (descendantFocusability == FOCUS_BLOCK_DESCENDANTS || focused == null) {
                null
            } else {
                focused
            }
    }

    override fun onRequestFocusInDescendants(
        direction: Int,
        previouslyFocusedRect: Rect?
    ): Boolean {
        onRequestFocusInDescendantsForTargetView()
        if (onRequestFocusInDescendantsForFocusMemory(direction, previouslyFocusedRect))
            return true
        return super.onRequestFocusInDescendants(direction, previouslyFocusedRect)
    }

    private fun onRequestFocusInDescendantsForTargetView() {
        if (!visibleWhenGainFocus || !::targetView.isInitialized)
            return
        if (targetView.visibility != VISIBLE) {
            expectTargetViewVisibility = VISIBLE
            targetView.visibility = VISIBLE
        }
    }

    private fun onRequestFocusInDescendantsForFocusMemory(
        direction: Int,
        previouslyFocusedRect: Rect?
    ): Boolean {
        val memoryView = getAvailableMemoryView()
        return if (memoryView == null) {
            false
        } else {
            memoryView.requestFocus(direction)
            true
        }
    }

    private fun getAvailableMemoryView(): View? {
        val memoryView = this.memoryView ?: return null
        if (isContainingView(this, memoryView))
            return memoryView
        this.memoryView = null
        return null
    }

    private fun isContainingView(parent: View, view: View): Boolean {
        if (view == parent)
            return true
        val parentGroup = view.parent as View? ?: return false
        return isContainingView(parent, parentGroup)
    }

    private fun requestChildFocusForTargetVisibility(focused: View) {
        if (!visibleWhenGainFocus || !::targetView.isInitialized || targetView.visibility == VISIBLE)
            return
        if (isContainingView(targetView, focused)) {
            Log.d("IQYMenu", "child request focus but parent is not visible")
            targetView.visibility = VISIBLE
            expectTargetViewVisibility = VISIBLE
        }
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        if (targetViewId > 0) {
            findViewById<View>(targetViewId)?.let {
                targetView = it
            }
        }
    }
}