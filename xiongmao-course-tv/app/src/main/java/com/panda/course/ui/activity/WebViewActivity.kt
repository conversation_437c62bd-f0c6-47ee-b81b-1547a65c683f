package com.panda.course.ui.activity

import android.annotation.TargetApi
import android.os.Bundle
import android.view.KeyEvent
import android.webkit.*
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityWebViewBinding
import com.panda.course.ext.logE
import com.panda.course.ui.viewmodel.CourseViewModel


class WebViewActivity : BaseDbActivity<CourseViewModel, ActivityWebViewBinding>() {

    private val TAG = "WebViewActivity"

    override fun initView(savedInstanceState: Bundle?) {

//    }

//        intent.extras?.let {
//            it.getString("jump_url")?.let { url ->
//                "网页连接=$url".logE(TAG)
//                mDataBind.webView.loadUrl(url)
//            }
//        }

        mDataBind.webView.webViewClient = object : WebViewClient() {

            @TargetApi(21)
            override fun shouldInterceptRequest(view: WebView, request: WebResourceRequest): WebResourceResponse? {
                return shouldInterceptRequest(view, request.url.toString())
            }

            override fun shouldInterceptRequest(view: WebView, url: String): WebResourceResponse? {
                return super.shouldInterceptRequest(view, url)
            }

            override fun shouldOverrideKeyEvent(view: WebView?, event: KeyEvent?): Boolean {
                return super.shouldOverrideKeyEvent(view, event)
            }

            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                return super.shouldOverrideUrlLoading(view, request)
            }
        }

        val webSettings: WebSettings = mDataBind.webView.getSettings()

        webSettings.javaScriptEnabled = true
        webSettings.allowContentAccess = true
        webSettings.databaseEnabled = true
        webSettings.domStorageEnabled = true
        webSettings.setAppCacheEnabled(true)
        webSettings.savePassword = false
        webSettings.saveFormData = false
        webSettings.useWideViewPort = true
        webSettings.loadWithOverviewMode = true

        mDataBind.webView.loadUrl("https://chart-dev.jiazhengye.cn/#/index")

    }


}