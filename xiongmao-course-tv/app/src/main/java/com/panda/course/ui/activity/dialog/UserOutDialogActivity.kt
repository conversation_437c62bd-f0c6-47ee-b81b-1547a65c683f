package com.panda.course.ui.activity.dialog

import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityUserOutDialogBinding
import com.panda.course.entity.DeviceExpireDate
import com.panda.course.entity.UserEntity
import com.panda.course.ext.*
import com.panda.course.ui.activity.MainActivity
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.util.GlideCacheUtil
import com.panda.course.util.MMKVHelper
import com.panda.course.util.im.TUIUtils
import com.tencent.qcloud.tuicore.TUILogin
import com.umeng.analytics.MobclickAgent

class UserOutDialogActivity : BaseDbActivity<CourseViewModel, ActivityUserOutDialogBinding>() {


    private var commitCount: Int = 0
    private var cancelCount: Int = 0

    override fun initView(savedInstanceState: Bundle?) {

        setDialogWindowParams(window, 0.6f)

//        mViewModel.getDeviceExpireDate()

    }


    override fun initObserver() {
        eventViewModel.appUserInfo.value?.let {
            Glide.with(this).asBitmap().apply(RequestOptions.bitmapTransform(CircleCrop()))
                .placeholder(R.drawable.def_pic).error(R.drawable.def_pic).load(it.store_log)
                .into(mDataBind.ivDialogPic)

            mDataBind.tvStoryName.text = it.store_name
        }

//        mViewModel.deviceExpireDate.observe(this@UserOutDialogActivity, androidx.lifecycle.Observer{
//            if (it == null) {
//                return@observe
//            }
//            if (TextUtils.isEmpty(it.expire_start_date) && TextUtils.isEmpty(it.expire_end_date)) {
//                mDataBind.tvServiceDate.text = "设备不存在"
//            } else {
//                mDataBind.tvServiceDate.text = "服务日期至 ${it?.expire_end_date}"
//            }
//        })
    }

    override fun onBindViewClick() {
        super.onBindViewClick()
        mDataBind.tvDialogCancel.setOnClickListener { finish() }
        mDataBind.tvDialogOut.setOnClickListener {
            isPropagandaVideo = false
            eventViewModel.appUserInfo.value = null
            MobclickAgent.onProfileSignOff()
            TUILogin.addGroup = false
            TUIUtils.logout(null)
            MMKVHelper.clearAll()
            GlideCacheUtil.getInstance().clearImageAllCache(this)
            toStartActivity(MainActivity::class.java)
            "退出登录".toast()
        }
    }

    private fun downUpAction(): Boolean {
        if (mDataBind.tvDialogCancel.isFocused) {
            mDataBind.tvDialogCancel.clearAnimation()
            mDataBind.tvDialogCancel.startAnimation(mShakeAnimation)
            return true
        }
        if (mDataBind.tvDialogOut.isFocused) {
            mDataBind.tvDialogOut.clearAnimation()
            mDataBind.tvDialogOut.startAnimation(mShakeAnimation)
            return true
        }
        return false
    }

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (onMyKeyDown(event.keyCode, event)) { // 加一层判断，实现android 9 以及其他的情况
            return true
        }

        when (event.keyCode) {
            KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_DPAD_RIGHT, KeyEvent.KEYCODE_DPAD_LEFT -> {
                return downUpAction()
            }
            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (mDataBind.tvDialogOut.isFocused) {
                    commitCount += 1
                    if (commitCount >= 2) {
                        mDataBind.tvDialogOut.clearAnimation()
                        mDataBind.tvDialogOut.startAnimation(mShakeYAnimation)
                    }
                }
                cancelCount = 0
            }
            KeyEvent.KEYCODE_DPAD_UP -> {
                if (mDataBind.tvDialogCancel.isFocused) {
                    cancelCount += 1
                    if (cancelCount >= 2) {
                        mDataBind.tvDialogCancel.clearAnimation()
                        mDataBind.tvDialogCancel.startAnimation(mShakeYAnimation)
                    }
                }
                commitCount = 0
            }

        }
        return super.dispatchKeyEvent(event)
    }


}