package com.panda.course.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;

import com.panda.course.util.NetworkUtil;

import java.util.ArrayList;
import java.util.List;

public class NetBroadcastReceiver extends BroadcastReceiver {

    private List<NetStateChangeObserver> mObservers = new ArrayList<>();
    private int mType = -1;
    private static boolean isRegister = false;

    private static class InstanceHolder {
        private static final NetBroadcastReceiver INSTANCE = new NetBroadcastReceiver();
    }

    @Override
    public void onReceive(Context context, Intent intent) {

        if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
            int connectivityStatus = NetworkUtil.getConnectivityStatus(context);
            notifyObservers(connectivityStatus);
        }

    }

    public static void registerReceiver(Context context, NetStateChangeObserver observer) {
        IntentFilter intentFilter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        context.registerReceiver(InstanceHolder.INSTANCE, intentFilter);
        isRegister = true;
        if (observer == null) {
            return;
        }
        if (!InstanceHolder.INSTANCE.mObservers.contains(observer)) {
            InstanceHolder.INSTANCE.mObservers.add(observer);
        }
    }

    public static void unRegisterReceiver(Context context, NetStateChangeObserver observer) {
        if (isRegister) {
            context.unregisterReceiver(InstanceHolder.INSTANCE);
            if (observer == null) {
                return;
            }
            if (InstanceHolder.INSTANCE.mObservers == null) {
                return;
            }
            InstanceHolder.INSTANCE.mObservers.remove(observer);
        }
    }

    private void notifyObservers(int networkType) {
        if (mType == networkType) {
            return;
        }
        mType = networkType;
        if (networkType == NetworkUtil.NETWORK_CONNECTED) {
            for (NetStateChangeObserver observer : mObservers) {
                observer.onNetWorkConnect();
            }
        } else {
            for (NetStateChangeObserver observer : mObservers) {
                observer.onDisconnect();
            }
        }
    }

    public interface NetStateChangeObserver {

        void onDisconnect();

        void onNetWorkConnect();
    }
}
