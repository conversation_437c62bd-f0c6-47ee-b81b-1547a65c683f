package com.panda.course.ui.adapter

import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.util.GlideUtil

/**
 * 大图展示
 */
class ImageBigAdapter : BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_image_big) {

    override fun convert(holder: BaseViewHolder, item: String) {
        val imageView = holder.getView<ImageView>(R.id.iv_item_image_big)
        GlideUtil.loadPic(context, item, imageView)
    }


}