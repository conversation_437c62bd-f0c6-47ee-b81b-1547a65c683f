package com.panda.course.ui.activity

import android.os.Bundle
import android.text.TextUtils
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityPlayerBinding
import com.panda.course.entity.CourseDetailInfo
import com.panda.course.ext.gone
import com.panda.course.ext.visible
import com.panda.course.ui.viewmodel.CourseViewModel
import com.tencent.liteav.demo.superplayer.SuperPlayerModel
import com.tencent.liteav.demo.superplayer.model.entity.VideoQuality

class PlayerNewActivity : BaseDbActivity<CourseViewModel, ActivityPlayerBinding>() {


    //记录某个渠道来的
    private var JumpType = 0
    private var mCourseInfo: CourseDetailInfo? = null

    private var mCourseCode: String? = ""
    private var mCourseStatus: String? = ""


    override fun initView(savedInstanceState: Bundle?) {
        JumpType = intent.getIntExtra("JumpType", 0)
        mCourseInfo = intent.getParcelableExtra("course_detail")
        mCourseCode = intent.getStringExtra("course_detail_code")
        mCourseStatus = intent.getStringExtra("course_status")

        val videoQualityList = ArrayList<VideoQuality>()
        val multiURLs = ArrayList<SuperPlayerModel.SuperPlayerURL>()
        val superPlayerModelList = ArrayList<SuperPlayerModel>()

        videoQualityList.add(VideoQuality(0, "高清", mCourseInfo?.video_url))
        multiURLs.add(SuperPlayerModel.SuperPlayerURL(mCourseInfo?.video_url, "高清"))

        if (!TextUtils.isEmpty(mCourseInfo?.hd_video_url_list?.standard)) {
            multiURLs.add(SuperPlayerModel.SuperPlayerURL(mCourseInfo?.hd_video_url_list?.standard, "标清"))
            videoQualityList.add(VideoQuality(1, "标清", mCourseInfo?.hd_video_url_list?.standard))
        } else {
            multiURLs.add(SuperPlayerModel.SuperPlayerURL(mCourseInfo?.hd_video_url_list?.hd, "标清"))
            videoQualityList.add(VideoQuality(1, "标清", mCourseInfo?.hd_video_url_list?.hd))
        }


        val superPlayerModelV3 = SuperPlayerModel()
        superPlayerModelV3.title = mCourseInfo?.title_main
        superPlayerModelV3.url = mCourseInfo?.video_url
        superPlayerModelV3.appId = ConstantMMVK.DEFAULT_APPID
        superPlayerModelV3.multiURLs = multiURLs
        superPlayerModelV3.videoQualityList = videoQualityList
        superPlayerModelList.add(superPlayerModelV3)

        mDataBind.superVodPlayerView.playWithModelList(superPlayerModelList, true, 0) //循环播放列表，并且从第0位开始

    }
}