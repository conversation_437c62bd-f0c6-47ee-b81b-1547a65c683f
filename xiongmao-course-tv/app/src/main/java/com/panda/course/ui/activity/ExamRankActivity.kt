package com.panda.course.ui.activity

import android.graphics.Paint
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.view.animation.Animation
import android.widget.LinearLayout
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityExamRankBinding
import com.panda.course.databinding.HeaderExamRankAnswerBinding
import com.panda.course.ext.*
import com.panda.course.ui.adapter.ExamRankAnswerAdapter
import com.panda.course.ui.adapter.SkillRecordAdapter
import com.panda.course.ui.viewmodel.ExamViewModel
import com.panda.course.util.AnimationUtils
import com.panda.course.widget.TriangleView

class ExamRankActivity : BaseDbActivity<ExamViewModel, ActivityExamRankBinding>() {

    private var mSkillRecordAdapter = SkillRecordAdapter(true)

    private var mAdapter = ExamRankAnswerAdapter()

    private var page = 1

    private var mCourseCode: String? = ""

    private var status = "1"

    private var numberCount = 0

    private var numberPosition = -1

    //记录距离
    private var distance = 0

    private var homeHeaderView: HeaderExamRankAnswerBinding? = null

    private var mPosition = -1

    /**
     * 顶部
     */
    private val focus_top = 1

    /**
     * item
     */
    private val focus_item = 2

    /**
     * 列表
     */
    private val focus_recycler = 3

    private var isFocus = false


    /**
     * 记录当前的焦点在哪里
     */
    private var currentFocusType: Int = focus_top

    override fun initView(savedInstanceState: Bundle?) {
        intent?.extras?.apply {
            mCourseCode = getString("code")
            mPosition = getInt("student_number")

            val channel = getInt("channel")
            if (1 == channel) {//是当天
                status = "1"
                mDataBind.tvPopupDay.requestFocus()
            } else {//是当月
                status = "2"
                mDataBind.tvPopupInteraction.requestFocus()
            }

            if (!TextUtils.isEmpty(mCourseCode)) {
                mViewModel.getExamRankAll(status = status, code = mCourseCode, page = page)
            }
        }

        mDataBind.recyclerChild.adapter = mSkillRecordAdapter
        mDataBind.recyclerChild.layoutManager = LinearLayoutManager(this)
//        mDataBind.recyclerChild.setColumnNumbers(1)

        mDataBind.recycler.layoutManager = LinearLayoutManager(this)
        mDataBind.recycler.adapter = mAdapter

        val header = View.inflate(this, R.layout.header_exam_rank_answer, null)

        homeHeaderView = DataBindingUtil.bind(header)
        mAdapter.addHeaderView(header)

        mAdapter.setEmptyView(getEmptyView(tips = getString(R.string.rank_empty)))
        mSkillRecordAdapter.setEmptyView(getEmptyView(tips = getString(R.string.rank_empty)))
    }

    override fun onRequestSuccess() {
        mViewModel.recordListEntity.observe(this, Observer {
            if (it == null) {
                return@Observer
            }

            if (it.list.size <= 0) {
                mDataBind.flController.invisible()
            } else {
                mDataBind.flController.visible()
            }

            mSkillRecordAdapter.setList(it.list)

            if (mPosition <= mSkillRecordAdapter.data.size - 1 && !isFocus) {
                mDataBind.recyclerChild.postDelayed({
                    mSkillRecordAdapter.getViewByPosition(mPosition, R.id.row_card_view)?.requestFocus()
                    isFocus = true
                }, 100)
            }
        })
        mViewModel.classResultEntity.observe(this, Observer {
            if (it == null) {
                return@Observer
            }
            mDataBind.progress.gone()
            mDataBind.tvTips.visible()
            //对RecyclerView做处理 , 在这里对头部的data 进行填充
            homeHeaderView?.tvExamRankClassScore?.paintFlags = Paint.LINEAR_TEXT_FLAG
            mAdapter.setList(it.list)

            homeHeaderView?.tvExamRankClassName?.text = it.course_title
            homeHeaderView?.tvExamRankClassScoreTips?.text = "${it.service_personnel_name} , 您本次测评得分"
            homeHeaderView?.tvExamRankClassScore?.text = "${it.score}分"



            homeHeaderView?.llExamRankClassScoreView?.let { it1 -> homeHeaderView?.triangleUp?.let { it2 -> it.score?.toInt()?.let { it3 -> moveViewOnLine(it1, it2, it3) } } }

            when (it.score_level) {//分数等级 1差 2中等 3良好 4优秀
                "1" -> {
                    homeHeaderView?.ivExamRankScoreLevel?.visible()
                    homeHeaderView?.ivExamRankScoreLevel?.setImageResource(R.drawable.icon_exam_rank_1)
                }
                "2" -> {
                    homeHeaderView?.ivExamRankScoreLevel?.visible()
                    homeHeaderView?.ivExamRankScoreLevel?.setImageResource(R.drawable.icon_exam_rank_2)
                }
                "3" -> {
                    homeHeaderView?.ivExamRankScoreLevel?.visible()
                    homeHeaderView?.ivExamRankScoreLevel?.setImageResource(R.drawable.icon_exam_rank_3)
                }
                "4" -> {
                    homeHeaderView?.ivExamRankScoreLevel?.visible()
                    homeHeaderView?.ivExamRankScoreLevel?.setImageResource(R.drawable.icon_exam_rank_4)
                }
                else -> {
                    homeHeaderView?.ivExamRankScoreLevel?.gone()
                }
            }
        })
    }

    override fun initObserver() {
        mDataBind.flController.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                currentFocusType = focus_recycler
                mDataBind.flController.setBackgroundResource(R.drawable.skill_stoke_focus)
            } else {
                mDataBind.flController.background = null
            }
        }
        mDataBind.tvPopupDay.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                status = "1"
                currentFocusType = focus_top
                mDataBind.tvPopupDay.setBackgroundResource(R.drawable.base_border_round)
                if (!TextUtils.isEmpty(mCourseCode)) {
                    mViewModel.getExamRankAll(status = status, code = mCourseCode, page = page)
                }
            }
        }
        mDataBind.tvPopupInteraction.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                status = "2"
                currentFocusType = focus_top
                mDataBind.tvPopupInteraction.setBackgroundResource(R.drawable.base_border_round)
                if (!TextUtils.isEmpty(mCourseCode)) {
                    mViewModel.getExamRankAll(status = status, code = mCourseCode, page = page)
                }
            }
        }

        mSkillRecordAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                numberPosition = position
                numberCount = 0
                mDataBind.recycler.scrollToPosition(0)
                currentFocusType = focus_item
                when (status) {
                    "1" -> {
                        mDataBind.tvPopupDay.setBackgroundResource(R.drawable.base_border_selecter_round)
                    }
                    "2" -> {
                        mDataBind.tvPopupInteraction.setBackgroundResource(R.drawable.base_border_selecter_round)
                    }
                }
                if (mSkillRecordAdapter.data.size == ConstantMMVK.PAGE_SIZE && position == mSkillRecordAdapter.data.size - 1) {
                    page++
                    if (!TextUtils.isEmpty(mCourseCode)) {
                        mViewModel.getExamRankAll(status = status, code = mCourseCode, page = page)
                    }
                } else {
                    mDataBind.progress.visible()
                    mSkillRecordAdapter.data[position].number?.let { mViewModel.getExamResult(number = it) }
                }
            }
        }
    }

    /**
     * 改变recyclerView
     */
    private fun scrollToRecyclerViewHeight(up: Boolean) {
        if (up) {
            if (distance != 0) {
                mDataBind.recycler.smoothScrollBy(0, -distance)
            }
        } else {
            if (distance == 0) {
                distance = getScreenHeight() / 6
            }
            mDataBind.recycler.smoothScrollBy(0, distance)
        }
    }

    /**
     * 移动View
     */
    fun moveViewOnLine(linearLayout: LinearLayout, view: TriangleView, value: Int) {
        linearLayout.post {
            val totalLength = linearLayout.width.toFloat() // LinearLayout 的总长度
            val viewWidth = view.width.toFloat() // 视图的宽度
            val positionRatio = value.toFloat() / 100f // 位置比例

            val translationX = totalLength * positionRatio - viewWidth / 2
            view.translationX = translationX
        }
    }


    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (event.keyCode == KeyEvent.KEYCODE_DPAD_DOWN && mDataBind.flController.isFocused) {
            "WebView ===  $numberCount".logE()
            numberCount++
            if (numberCount == 3) {
                mDataBind.tvTips.startAnimation(AnimationUtils.getHiddenAlphaAnimation(1000, object : Animation.AnimationListener {
                    override fun onAnimationStart(animation: Animation?) {

                    }

                    override fun onAnimationEnd(animation: Animation?) {
                        mDataBind.tvTips.gone()
                    }

                    override fun onAnimationRepeat(animation: Animation?) {
                    }

                }))
            }
        }
        return super.dispatchKeyEvent(event)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_DPAD_UP -> {
                if (currentFocusType != focus_top && !mDataBind.flController.isFocused && numberPosition <= 0) {
                    //顶部获去焦点
                    if ("1" == status) {
                        mDataBind.tvPopupDay.requestFocus()
                    } else {
                        mDataBind.tvPopupInteraction.requestFocus()
                    }
                }
                if (mDataBind.flController.isFocused) {
                    scrollToRecyclerViewHeight(true)
                }
                if (mSkillRecordAdapter.data.size <= 0) {
                    return true
                }
            }
            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (mDataBind.flController.isFocused) {
                    scrollToRecyclerViewHeight(false)
                }
                if (mSkillRecordAdapter.data.size <= 0) {
                    return true
                }
            }
            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (currentFocusType == focus_item) {
                    return true
                }
            }
        }
        return super.onKeyDown(keyCode, event)
    }
}