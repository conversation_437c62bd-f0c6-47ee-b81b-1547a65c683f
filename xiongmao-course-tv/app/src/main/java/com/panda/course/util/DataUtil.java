package com.panda.course.util;

import com.panda.course.R;
import com.panda.course.entity.DataBean;
import com.panda.course.entity.SignEntity;

import java.util.ArrayList;
import java.util.List;

public class DataUtil {

    public static List<SignEntity> getSign() {
        List<SignEntity> mList = new ArrayList<>();
        mList.add(new SignEntity("https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLpfvHHpHP7oGxdWFqzDF6rOiaxqChl2CVZvgOPWLx747LdLibEXRfMf4BUWuiazV8iaeLe63trvwL6sw/132", 0, "今天天气真好呀", 13));
        mList.add(new SignEntity("", R.drawable.def_pic, "陈露", 12));
        mList.add(new SignEntity("", R.drawable.image2, "李志强", 11));
        mList.add(new SignEntity("", R.drawable.image3, "王凯旋", 10));
        mList.add(new SignEntity("", R.drawable.image4, "牛雪军", 9));
        mList.add(new SignEntity("", R.drawable.image5, "杨明亮", 8));
        mList.add(new SignEntity("", R.drawable.image2, "薛晓旭", 7));
        mList.add(new SignEntity("", R.drawable.image6, "我可不能再吃啦", 6));
        mList.add(new SignEntity("", R.drawable.image3, "陈欢", 5));
        mList.add(new SignEntity("", R.drawable.image2, "冯丽娟", 4));
        mList.add(new SignEntity("", R.drawable.image5, "王星星", 3));
        mList.add(new SignEntity("", R.drawable.image6, "李鹏飞", 2));
        mList.add(new SignEntity("", R.drawable.image4, "花儿为什么那么红", 1));
        return mList;
    }


    public static List<DataBean> getTestData() {
        List<DataBean> list = new ArrayList<>();
        list.add(new DataBean(R.drawable.image2, "极致简约,梦幻小屋", 3));
        list.add(new DataBean(R.drawable.image3, "超级卖梦人", 3));
        list.add(new DataBean(R.drawable.image4, "夏季新搭配", 1));
        list.add(new DataBean(R.drawable.image5, "绝美风格搭配", 1));
        list.add(new DataBean(R.drawable.image6, "微微一笑 很倾城", 3));
        return list;
    }

}
