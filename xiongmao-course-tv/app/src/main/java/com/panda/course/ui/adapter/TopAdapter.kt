package com.panda.course.ui.adapter

import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.entity.TopListEntity
import com.panda.course.widget.focus.MyFocusHighlightHelper

class TopAdapter : BaseQuickAdapter<TopListEntity, BaseViewHolder>(R.layout.item_top) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var onItemFocus: OnViewFocus? = null

    private var isStore: Boolean = false


    fun setOnViewFocus(onItemFocus: OnViewFocus) {
        this.onItemFocus = onItemFocus
    }

    fun setStore(store: Boolean) {
        this.isStore = store
    }


    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight = MyFocusHighlightHelper.BrowseItemFocusHighlight(MyFocusHighlightHelper.ZOOM_FACTOR_XXSMALL, false)
        }
    }


    override fun convert(holder: BaseViewHolder, data: TopListEntity) {
        val rl_item_top_layout = holder.getView<RelativeLayout>(R.id.rl_item_top_layout)
        val iv_item_top = holder.getView<ImageView>(R.id.iv_item_top)
        val tv_item_top = holder.getView<TextView>(R.id.tv_item_top)
        val tv_item_pic = holder.getView<ImageView>(R.id.iv_item_pic)

        Glide.with(context).asBitmap().apply(RequestOptions.bitmapTransform(CircleCrop()))
            .placeholder(R.drawable.def_pic).error(R.drawable.def_pic).load(data.avatar)
            .into(tv_item_pic)

        if (getItemPosition(data) == 0) {
            tv_item_top.visibility = View.GONE
            iv_item_top.visibility = View.VISIBLE
            iv_item_top.setImageResource(R.drawable.icon_top_1)
        } else if (getItemPosition(data) == 1) {
            tv_item_top.visibility = View.GONE
            iv_item_top.visibility = View.VISIBLE
            iv_item_top.setImageResource(R.drawable.icon_top_2)
        } else if (getItemPosition(data) == 2) {
            tv_item_top.visibility = View.GONE
            iv_item_top.visibility = View.VISIBLE
            iv_item_top.setImageResource(R.drawable.icon_top_3)
        } else {
            tv_item_top.visibility = View.VISIBLE
            iv_item_top.visibility = View.GONE
            tv_item_top.text = "" + (getItemPosition(data) + 1)
        }
        holder.setText(R.id.tv_item_top_score, data.score + "    |   " + data.level)

        if (isStore) {
            holder.setText(R.id.tv_item_top_name, data.service_personnel_name)
        } else {
            val spannable = SpannableStringBuilder()
            if (data.service_personnel_name.length > 10) {
                spannable.append(data.service_personnel_name.substring(0, 10))
                spannable.append("...")
            } else {
                spannable.append(data.service_personnel_name)
            }
            spannable.append("(")
            if (data.store_name.length > 4) {
                spannable.append(data.store_name.substring(0, 2))
                spannable.append("...")
                spannable.append(data.store_name.substring(data.store_name.length - 2, data.store_name.length))
            } else {
                spannable.append("...")
                spannable.append(data.store_name)
            }
            spannable.append(")")

            holder.setText(R.id.tv_item_top_name, spannable)
        }

        holder.getView<TextView>(R.id.tv_item_top_name).ellipsize = if (isStore) TextUtils.TruncateAt.END else TextUtils.TruncateAt.MIDDLE


        rl_item_top_layout.setOnFocusChangeListener { v, hasFocus ->
            rl_item_top_layout.setBackgroundResource(if (hasFocus) R.drawable.base_border_selecter else R.drawable.base_border_rank_unselecter)
            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
            onItemFocus?.onChangeFocus(hasFocus, getItemPosition(data), v)
        }
    }


}