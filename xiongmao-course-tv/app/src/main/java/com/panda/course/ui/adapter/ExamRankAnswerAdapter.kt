package com.panda.course.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.alibaba.fastjson.JSON
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.ExamClassResultEntity
import com.panda.course.entity.ExamClassResultListEntity
import com.panda.course.entity.RowEntity
import com.panda.course.entity.RowStringEntity
import com.panda.course.ext.*
import com.panda.course.util.GlideUtil
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper
import rxhttp.wrapper.entity.KeyValuePair

class ExamRankAnswerAdapter : BaseQuickAdapter<ExamClassResultListEntity, BaseViewHolder>(R.layout.item_exam_rank_answer) {

    val gson = Gson()

    override fun convert(holder: BaseViewHolder, item: ExamClassResultListEntity) {
        holder.setText(R.id.tv_item_title, "${getItemPosition(item) + 1} 、${item.title}")

        when (item.type) {
            "1" -> {
                holder.setText(R.id.tv_item_sub, "（单选题）")
            }
            "2" -> {
                holder.setText(R.id.tv_item_sub, "（多选题）")
            }
            "3" -> {
                holder.setText(R.id.tv_item_sub, "（判断题）")
            }
            "4" -> {
                holder.setText(R.id.tv_item_sub, "（填空题）")
            }
            else -> {
                holder.setText(R.id.tv_item_sub, "")
            }
        }

        val layout = holder.getView<LinearLayout>(R.id.ll_item_layout)

        val childCount = layout.childCount

        if (childCount > 2) {
            layout.removeViews(2, childCount - 2)
        }

        val myAnswerArr = JSON.parseArray(item.my_answer, String::class.java)
        val answer = JSON.parseArray(item.answer, String::class.java)

        val keyValuePairs = item.content?.let { parseContent(it) }
        if (keyValuePairs != null) {
            for (pair in keyValuePairs) {
                //循环增加View 到界面上
                layout.addView(createItemView(pair, item, layout, myAnswerArr, answer))
            }
        }
        //这里增加答案、内容等
        layout.addView(createResultView(layout, item, answer))
    }

    private fun createResultView(layout: LinearLayout, item: ExamClassResultListEntity, answer: List<String>): View {
        val resultView = LayoutInflater.from(context).inflate(R.layout.item_exam_rank_answer_child_two, layout, false)
        val tvResult = resultView.findViewById<TextView>(R.id.tv_item_exam_result)
        val correctAnswer = resultView.findViewById<TextView>(R.id.tv_item_exam_correct_answer)
        val answerKey = resultView.findViewById<TextView>(R.id.tv_item_exam_answer_key)
        if ("1" == item.iscorrect) {//是否正确
            tvResult.text = "回答正确"
            tvResult.setTextColor(getColorExt(R.color.green))
        } else {
            tvResult.text = "回答错误"
            tvResult.setTextColor(getColorExt(R.color.red))
        }
        //正确答案
        correctAnswer.text = answer.toString()
        //题目解析
        answerKey.text = "解析：${item.analyse}"
        return resultView
    }

    //增加View
    private fun createItemView(pair: KeyValuePair, item: ExamClassResultListEntity, layout: LinearLayout, myAnswerArr: List<String>, answer: List<String>): View {

        val itemView = LayoutInflater.from(context).inflate(R.layout.item_exam_rank_answer_child, layout, false)
        val ivStatus = itemView.findViewById<ImageView>(R.id.iv_item_status)
        val checkBox = itemView.findViewById<CheckBox>(R.id.checkbox_item)
        val textView = itemView.findViewById<TextView>(R.id.tv_item_name)
        val imageView = itemView.findViewById<ImageView>(R.id.iv_item_pic)
        if ("0" == item.is_image) {//内容的展示
            textView.text = "${pair.key}.${pair.value}"
            textView.visible()
            imageView.gone()
        } else {
            imageView.visible()
            textView.gone()
            GlideUtil.loadPic(context, pair.value, imageView)
        }
        //处理下对错
        if (answer.contains(pair.key)) {
            ivStatus.setImageResource(R.drawable.icon_exam_ok)
        } else {
            ivStatus.setImageResource(R.drawable.icon_exam_error)
        }

        //处理下选中情况
        checkBox.isChecked = myAnswerArr.contains(pair.key)
        return itemView
    }

    data class KeyValuePair(val key: String, val value: String)

    fun parseContent(content: String): List<KeyValuePair> {
        val jsonObject: JsonObject = gson.fromJson(content, JsonObject::class.java)

        val keyValuePairs: MutableList<KeyValuePair> = mutableListOf()

        for ((key, value) in jsonObject.entrySet()) {
            keyValuePairs.add(KeyValuePair(key, value.asString))
        }

        return keyValuePairs
    }
}