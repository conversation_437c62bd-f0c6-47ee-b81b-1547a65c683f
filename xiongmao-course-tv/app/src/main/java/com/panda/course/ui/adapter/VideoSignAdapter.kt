package com.panda.course.ui.adapter

import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.noober.background.drawable.DrawableCreator
import com.noober.background.view.BLTextView
import com.panda.course.R
import com.panda.course.entity.VideoDynamicEffect
import com.tencent.qcloud.tuicore.util.ScreenUtil.dip2px


class VideoSignAdapter : BaseQuickAdapter<VideoDynamicEffect, BaseViewHolder>(R.layout.item_sign) {

    var mViewBg: String? = null
    var mViewTextColor: String? = null

    override fun convert(holder: BaseViewHolder, data: VideoDynamicEffect) {
        holder.setText(R.id.tv_item_number, "${data.index}")
            .setText(R.id.tv_item_name, "${data.service_personnel_name}")
            .setText(R.id.tv_item_name_sign, "${data.service_personnel_name}")

        if (!TextUtils.isEmpty(data.score)) {
            holder.setText(R.id.tv_item_score, data.score + "分")
            holder.getView<TextView>(R.id.tv_item_score).visibility = View.VISIBLE
            holder.getView<TextView>(R.id.tv_item_name).visibility = View.VISIBLE
            holder.getView<TextView>(R.id.tv_item_name_sign).visibility = View.GONE
        } else {
            holder.getView<TextView>(R.id.tv_item_score).visibility = View.GONE
            holder.getView<TextView>(R.id.tv_item_name).visibility = View.GONE
            holder.getView<TextView>(R.id.tv_item_name_sign).visibility = View.VISIBLE
        }

        Glide.with(context).asBitmap().apply(RequestOptions.bitmapTransform(CircleCrop()))
            .placeholder(R.drawable.def_pic).error(R.drawable.def_pic).load(data.avatar)
            .into(holder.getView(R.id.iv_item_pic))


        mViewBg?.let {

            val drawable: Drawable = DrawableCreator.Builder().setCornersRadius(dip2px(20f).toFloat())
                .setSolidColor(Color.parseColor(if (it.contains("#")) mViewBg else "#$mViewBg")).build()

            holder.getView<TextView>(R.id.tv_item_number).background = drawable
            holder.getView<RelativeLayout>(R.id.rl_info_layout).background = drawable
        }

        mViewTextColor?.let {
            holder.getView<TextView>(R.id.tv_item_number)
                .setTextColor(Color.parseColor(if (it.contains("#")) it else "#$it"))
            holder.getView<TextView>(R.id.tv_item_name)
                .setTextColor(Color.parseColor(if (it.contains("#")) it else "#$it"))
        }

    }


    fun setViewBg(view: String? = null, mViewTextColor: String? = null) {
        this.mViewBg = view
        this.mViewTextColor = mViewTextColor
    }

}