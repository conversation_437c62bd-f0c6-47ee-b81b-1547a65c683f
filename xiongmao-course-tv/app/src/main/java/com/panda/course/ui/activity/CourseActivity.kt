package com.panda.course.ui.activity

import android.app.Dialog
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.leanback.widget.FocusHighlight
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import com.alibaba.fastjson.JSON
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.google.gson.Gson
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.appContext
import com.panda.course.data.CommonAnimationUtil
import com.panda.course.data.MyVideoInfoDaoUtil
import com.panda.course.databinding.ActivityCourseBinding
import com.panda.course.databinding.CourseHeaderHistoryBinding
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.NetUrl
import com.panda.course.receiver.HomeReceiver
import com.panda.course.receiver.NetBroadcastReceiver
import com.panda.course.service.DownloadVideoService
import com.panda.course.service.MultiTaskDownloader
import com.panda.course.ui.activity.dialog.*
import com.panda.course.ui.adapter.HistoryAdapter
import com.panda.course.ui.adapter.MyCourseAdapter
import com.panda.course.ui.adapter.MyCourseHorizontalAdapter
import com.panda.course.ui.adapter.RowAdapter
import com.panda.course.ui.presenter.CoursePresenter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.util.*
import com.panda.course.util.im.IMUtils
import com.panda.course.util.im.TUIUtils
import com.panda.course.widget.focus.MyFocusHighlightHelper
import com.tencent.imsdk.v2.V2TIMAdvancedMsgListener
import com.tencent.imsdk.v2.V2TIMCallback
import com.tencent.imsdk.v2.V2TIMManager
import com.tencent.imsdk.v2.V2TIMMessage
import com.tencent.qcloud.tuicore.TUILogin
import com.tencent.qcloud.tuicore.component.interfaces.IUIKitCallback
import com.umeng.analytics.MobclickAgent
import io.reactivex.rxjava3.disposables.Disposable
import okio.ByteString.Companion.encodeUtf8
import rxhttp.wrapper.param.RxHttp
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.set


class CourseActivity : BaseDbActivity<CourseViewModel, ActivityCourseBinding>(),
    NetBroadcastReceiver.NetStateChangeObserver {

    private val TAG = "CourseActivity"

    // 列表
    private var mRowAdapter = RowAdapter()

    // 左边的封面课程
    private var mCourseAdapter = MyCourseAdapter()

    //左边横向的列表
    private var mCourseHorizontalAdapter = MyCourseHorizontalAdapter()

    // 视频观看进度
    private var mVideoHistoryAdapter = HistoryAdapter(R.layout.item_history, R.layout.item_history_title, null)

    // 定时器来刷新看客奖励
    private var mQueryCourseRewardDisposable: Disposable? = null

    // 暂存id
    private var type_id: String? = null

    // 暂存
    private var mPosition: Int = 0

    private var mRowPosition: Int = 0

    // History的 下标
    private var mHistoryPosition = 0

    // 记录课程分类下的
    private var mCourseFocusPosition = 0

    // 是否观影记录
    private var isVideoHistory = true

    // 默认某个宣传片
    private var mAdData: Ad? = null

    // 记录是否加载成功
    private var mLoadCourseSuccess = false

    private var mRetryCount = 0

    // 记录当前View的状态
    private var mCourseTypeFocus = false

    // 记录当前View
    private var mCourseTypeView: View? = null

    private val mHomeReceiver = HomeReceiver()

    private var v2TIMAdvancedMsgListener: V2TIMAdvancedMsgListener? = null // Im 消息接收

    private var mNoticeDialog: Dialog? = null
    private var mAppUpdateDialog: Dialog? = null

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var mHeaderHistoryBinding: CourseHeaderHistoryBinding? = null //首页观看记录增加hedaer

    private var isRemoveHeaderView = true //记录首页的技能测评有没有


    /**
     * 默认分类
     */
    private val focus_type = 1

    /**
     * 分类课程
     */
    private val focus_course = 2

    /**
     * 视频
     */
    private val focus_video = 3

    /**
     * 顶部
     */
    private val focus_top = 4

    /**
     * 记录当前的焦点在哪里
     */
    private var currentFocusType: Int = focus_type

    //记录当前显示的View 是那种方式
    private var currentCourseStyle = false

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
    }

    override fun startDownloadVideo() {
        super.startDownloadVideo()
        mViewModel.getCacheVideoList()
    }

    fun getNowTime(): String {
        val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        return simpleDateFormat.format(Date())
    }

    override fun onLoadRetry() {
        mViewModel.getRowList()
        mViewModel.getVideoAllDot()
        mViewModel.getVideoDotResource()
        mViewModel.updateAppVersion()
        if (getCurrentAndroid9()) {
            mViewModel.getOTAInfo()
        }
        MMKVHelper.decodeString(ConstantMMVK.TOKEN).notTextNull { t ->
            mViewModel.getUserInfo(token = t) // 获取
        }
        mViewModel.getDeviceExpireDate()
        if (NetworkUtil.isAvailable(this)) {
            dismissLoadingNetWorkExt()
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        val filter = IntentFilter()
        filter.addAction(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)
        filter.addAction(Intent.ACTION_TIME_TICK)
        registerReceiver(mHomeReceiver, filter)

        NetBroadcastReceiver.registerReceiver(this, this)
        initRequest()
        initRecyclerView()
        locationData()
        readClassCache()
        onLoadRetry()
        initIMNotice()
//        if (!mInitServer) {
//            startHttpServer()
//        }

        if (!NetworkUtil.isAvailable(this)) {
            this.showNetDialog { onLoadRetry() }
        }

        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false)
        }

        GlideUtil.loadGif(this, R.drawable.surprise_gif, mDataBind.baseHead.ivSurprise)

        toStartService(DownloadVideoService::class.java)

        // 监听网速
        NetWorkListenerUtils(this).startShowNetSpeed()

        "${getIPAddress()}".logE("网络内容")
        "${getNowTime()}".logE("网络内容")

        "${getWireMac()} | ${getSerialNumber()}".logE("测试内容")

        deleteLocalZip(File(ConstantMMVK.OTA_ZIP_PATH))


    }


    private fun initRequest() {

        mViewModel.uploadDeviceVersion() // 同步设备版本到服务器

        uploadOfflineCourse()

        requestVideo()

        startDownloading()

        // 跑定时器1小时一次，来查询看课奖励是否完成
        startQueryCourseReward()
    }

    /* 上传本地数据库的内容 */
    private fun uploadOfflineCourse() {
        val list = MyVideoInfoDaoUtil.getVideoDao()
        if (!list.isNullOrEmpty()) {
            "$list".logE(TAG)
            "${MyVideoInfoDaoUtil.getVideoDao().size}".logE(TAG)
            JSON.toJSONString(list).logE(TAG)
            mViewModel.uploadOfflineCourse(JSON.toJSONString(list))
        }
    }

    fun deleteLocalZip(makeDataFile: File?) {
        val collectionPath: String = makeDataFile?.path ?: ""
        if (!TextUtils.isEmpty(collectionPath)) {
            val file = File(collectionPath)
            val tempList = file.listFiles()
            if (tempList != null && tempList.size > 1) {
                for (i in tempList.indices) {
                    if (tempList[i].name.contains(".zip")) {
                        tempList[i].delete()
                    }
                }
            }
        }
    }

    /**
     * 监听IM发送过来的消息
     * 3是Ai 上下架，需要刷新列表
     * 4是系统消息通知，去更新系统消息，并展示弹窗
     */
    private fun initIMNotice() {
        v2TIMAdvancedMsgListener = object : V2TIMAdvancedMsgListener() {
            override fun onRecvNewMessage(msg: V2TIMMessage) {
                if (!TextUtils.isEmpty(msg.textElem?.text)) {
                    "接收到消息了 = ${msg.textElem?.text}".logE()
                    mDataBind.baseHead.tvHeadTips.text = "${msg.textElem?.text}"
                    val effect = Gson().fromJson(msg.textElem?.text, VideoDynamicEffect::class.java)
                    if (effect != null) {
                        if ("3" == effect.type) {
                            mViewModel.getRowList()
                        } else if ("4" == effect.type) {
                            mDataBind.baseHead.tvHeadTips.text = "进群成功了，所以收到消息了"
                            mNoticeDialog?.dismiss()
                            if (mNoticeDialog != null) {
                                mNoticeDialog = null
                            }
                            if ("1" == effect.status) { // 状态 1正常 2撤回
                                mViewModel.checkAcceptNotice(effect.data_id)
                            } else {
                                mViewModel.getNoticeTotal(is_notice_content = "0")
                            }
                        } else if ("6" == effect.type) {//6是发布特效，收到了通知
                            gotoRedPaperActivity(effect.number)
                        } else if ("40" == effect.type) {//续费成功
                            finishActivitys(OnlineRenewalActivity::class.java)
                            showMessageDialog("恭喜您～\n续费成功")
                            MMKVHelper.decodeString(ConstantMMVK.TOKEN).notTextNull { t ->
                                mViewModel.getUserInfo(token = t) // 获取
                            }
                        } else {
                            eventViewModel.listenForIMPushMessages.value = effect
                        }
                    }
                }
            }
        }
        V2TIMManager.getMessageManager().addAdvancedMsgListener(v2TIMAdvancedMsgListener)
    }

    private fun gotoRedPaperActivity(number: String = "") {
        if (TextUtils.isEmpty(number)) {
            val mDeviceID =
                if (!TextUtils.isEmpty(MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID))) {
                    MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID)
                } else {
                    eventViewModel.appUserInfo.value?.device_id
                }
            mViewModel.getRedPaper(number, "$mDeviceID")
        } else {
            finishActivitys(ExpandInterfaceActivity::class.java)
            toStartActivity(ExpandInterfaceActivity::class.java, Bundle().apply {
                putString("channel", "" + currentActivity)
                putString("number", "" + number)
            })
        }
    }


    /**
     * 启动定时器来刷新看课奖励
     */
    private fun startQueryCourseReward() {
        if (mQueryCourseRewardDisposable != null) {
            mQueryCourseRewardDisposable?.dispose()
            mQueryCourseRewardDisposable = null
        }
        if (mQueryCourseRewardDisposable == null) {
            mQueryCourseRewardDisposable = RxTimerUtil.interval(1000 * 60 * 60).subscribe {
                mViewModel.getUserQueryCourseReward()
            }
        }
    }

    /**
     * 上报视频下载记录
     */
    private fun requestVideo() {
        if (TXVodUtil.getInstance().uploadTXVideo().size > 0) {
            mViewModel.uploadVideoDownLoadState(TXVodUtil.getInstance().uploadTXVideo())
        }
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerView() {
        mDataBind.recyclerRow.adapter = mRowAdapter

        mDataBind.recyclerCourse.adapter = mCourseAdapter
        mCourseAdapter.setEmptyView(R.layout.layout_empty)
        mCourseAdapter.animationEnable = false

        mDataBind.recyclerCourseExperience.adapter = mCourseHorizontalAdapter
        mCourseHorizontalAdapter.setEmptyView(R.layout.layout_empty)
        mCourseHorizontalAdapter.animationEnable = false
        mDataBind.recyclerCourseExperience.setColumnNumbers(4)
        mDataBind.recyclerCourseExperience.addItemDecoration(SpaceItemDecoration(4, px2dp(60f), true))

        mVideoHistoryAdapter.animationEnable = false
        mDataBind.recyclerHistory.layoutManager = GridLayoutManager(this, 4)
        mDataBind.recyclerHistory.adapter = mVideoHistoryAdapter
        mVideoHistoryAdapter.setEmptyView(R.layout.layout_history_empty)

    }

    /**
     * 获取本地数据
     */
    private fun locationData() {
        eventViewModel.appUserInfo.value?.let {
            MobclickAgent.onProfileSignIn(getWireMac())

            Glide.with(this).asBitmap().load(it.store_log)
                .apply(RequestOptions.bitmapTransform(CircleCrop()))
                .placeholder(R.drawable.def_pic).error(R.drawable.def_pic)
                .into(mDataBind.baseHead.ivHeadPic)

            mDataBind.baseHead.tvHeadStoreName.text = it.store_name

//            mDataBind.baseHead.tvHeadAppVersionCode.text = "V${getAppVersion(this)}"

            if (!TextUtils.isEmpty(it.im_user_id) && !TextUtils.isEmpty(it.im_user_sig)) {
                initIMLogin(it.im_user_id, it.im_user_sig)
            } else {
                mViewModel.getIMInfo()
            }

            // 在这里需要重新判断下在线续费的情况
//            if (!TextUtils.isEmpty(eventViewModel.appUserInfo.value?.expiration_notice)) {
//                mHeaderHistoryBinding?.flExpirationNotice?.visible()
//                mHeaderHistoryBinding?.flRenewalLayout?.visible()
//                mHeaderHistoryBinding?.tvExpirationNotice?.text = eventViewModel.appUserInfo.value?.expiration_notice
//            } else {
//                mHeaderHistoryBinding?.flRenewalLayout?.gone()
//                mHeaderHistoryBinding?.flExpirationNotice?.gone()
//            }
        }

        // 取本地活动
        val info = MMKVHelper.decodeString(ConstantMMVK.SURPRISE_ENTITY)
        if (info != null) {
            eventViewModel.surpriseToggle.value = JSON.parseObject(info, Surprise::class.java)
        }
    }

    override fun initObserver() {
        MultiTaskDownloader.allLiveTask.observe(
            this,
            object : androidx.lifecycle.Observer<ArrayList<DownloadTask>> {
                override fun onChanged(it: ArrayList<DownloadTask>) {
                    for (i in it.indices) {
                        if (it[i].url.contains(".mp3")) { // 监听所有.mp3文件
                            if (it[i].state == MultiTaskDownloader.COMPLETED) {
                                MMKVHelper.encode(
                                    it[i].url.encodeUtf8().md5().hex(),
                                    true
                                ) // 标识下载状态
                            } else if (it[i].state == MultiTaskDownloader.FAIL) { // 不管其他状态是什么，全部删除
                                val mFile = File(it[i].localPath)
                                mFile.delete()
                            }
                        }
                    }
                }
            }
        )
        //整点查询红包
        eventViewModel.hourReporting.observe(this, Observer {
            if (currentActivity !is PlayerLiveActivity) {
                gotoRedPaperActivity()
            }
        })
        //遥控器
        eventViewModel.appRemote.observe(this, object : androidx.lifecycle.Observer<Boolean> {

            override fun onChanged(t: Boolean?) {
                if (t != null && t!!) {
                    "手机遥控器已连接成功".toast()
                }
            }
        })
        // 查看消息状态的未读数量
        eventViewModel.noticeCount.observe(
            this,
            object : androidx.lifecycle.Observer<Int> {
                override fun onChanged(it: Int) {
                    if (it != null) {
                        when {
                            it >= 99 -> {
                                mDataBind.baseHead.tvNoticeNumber.visible()
                                mDataBind.baseHead.tvNoticeNumber.text = "99+"
                            }

                            it > 0 -> {
                                mDataBind.baseHead.tvNoticeNumber.visible()
                                mDataBind.baseHead.tvNoticeNumber.text = "$it"
                            }

                            else -> {
                                mDataBind.baseHead.tvNoticeNumber.gone()
                            }
                        }
                    } else {
                        mDataBind.baseHead.tvNoticeNumber.gone()
                    }
                }
            }
        )

        // 监听头像
        eventViewModel.appUserHead.observe(
            this,
            object : androidx.lifecycle.Observer<Boolean> {
                override fun onChanged(it: Boolean) {
                    if (it) {
                        eventViewModel.appUserInfo.value?.let {
                            Glide.with(appContext).asBitmap().load(it.store_log)
                                .apply(RequestOptions.bitmapTransform(CircleCrop()))
                                .placeholder(R.drawable.def_pic).error(R.drawable.def_pic)
                                .into(mDataBind.baseHead.ivHeadPic)
                        }
                    }
                }
            }
        )

        // 刷新历史记录x
        eventViewModel.refreshHistoryList.observe(
            this,
            object : androidx.lifecycle.Observer<Boolean> {
                override fun onChanged(it: Boolean) {
                    if (it && 0 == mRowPosition) {
                        if (mRowAdapter.data.size > 0) {
                            requestRowOne()
                        }
                        getVideoHistory()
                    }
                }
            }
        )

        // 监听是否需要更新
//        eventViewModel.appUpdate.observe(
//            this,
//            object : androidx.lifecycle.Observer<Boolean> {
//                override fun onChanged(it: Boolean) {
//                    mDataBind.baseHead.llUpdateLayout.visibility = if (it) View.VISIBLE else View.GONE
//                }
//            }
//        )

        // 上报网速
        eventViewModel.netWorkSpeed.observe(
            this,
            object : Observer<String> {
                override fun onChanged(it: String) {
                    requestUploadSpeed(it)
                }
            }
        )

        // home 键的监听
        eventViewModel.appHomeClick.observe(
            this,
            object : androidx.lifecycle.Observer<Boolean> {
                override fun onChanged(it: Boolean) {
                    if (it && currentActivity !is CourseActivity) {
                        finishOtherActivity()
                    }
                }
            }
        )

        // 消息通知的监听，实现无焦点的情况下，执行跑马灯的效果
        mDataBind.baseHead.llNoticeLayout.setOnFocusChangeListener { v, hasFocus ->
            if (!hasFocus) {
                if (!TextUtils.isEmpty(mViewModel.noticeEntity.value?.notice?.title)) {
                    mDataBind.baseHead.tvNotice.text = mViewModel.noticeEntity.value?.notice?.title
                }
            }
        }
    }

    /**
     * =
     */
    private fun requestUploadSpeed(net_speed: String) {
        RxHttp.get(NetUrl.MONITOR_URL + NetUrl.VIDEO_STAT).add("xmjz_from", "tv")
            .add("xmjz_version", "" + getAppVersion(this)).add("net_speed", "$net_speed")
            .add("type", "testspeed")
            .add("xmjz_store", eventViewModel.appUserInfo.value?.store_name)
            .add("xmjz_user_number", eventViewModel.appUserInfo.value?.uuid)
            .add("xmjz_user", eventViewModel.appUserInfo.value?.store_name).asString()
            .subscribe({ s: String ->
            }) { throwable: Throwable ->
            }
    }

    // 获取课程列表
    private fun courseList(type_id: String) {
        readCourseCache()
        mViewModel.getCourseList(type_id, readCourseCache())
    }

    // 获取观看记录
    private fun getVideoHistory() {
        mViewModel.getVideoHistoryList()
    }

    private fun readClassCache() {
        val jsonData = MMKVHelper.decodeString(ConstantMMVK.COURSE_CLASS)
        if (jsonData != null) {
            val rowEntity = Gson().fromJson(jsonData, RowEntity::class.java)
            if (rowEntity != null && rowEntity.list.size > 0) {
                mRowAdapter.setList(rowEntity.list)
                requestOne()
            }
        }
    }

    private fun readCourseCache(): Boolean {
        val jsonData = type_id?.let { MMKVHelper.decodeString(it) }
        if (jsonData != null) {
            val courseList = Gson().fromJson(jsonData, CourseList::class.java)
            return if (courseList != null) {
                if ("2" == mRowAdapter.data[mRowPosition].content_style) {
                    mDataBind.recyclerCourseExperience.post {
                        mCourseHorizontalAdapter.setList(courseList.list)
                        mDataBind.recyclerCourseExperience.backToTop()
                    }
                } else {
                    mDataBind.recyclerCourse.post {
                        mCourseAdapter.setList(courseList.list)
                        mDataBind.recyclerCourse.backToTop()
                    }
                }
                true
            } else {
                false
            }
        }
        return false
    }

    override fun onBindViewClick() {
        mDataBind.baseHead.llSurpriseLayout.setOnClickListener {
            appUMEvent(UMConstant.HOME_AD_FIXED)
            CoursePresenter.toSurpriseDetails(eventViewModel.surpriseToggle.value)
        }

        mDataBind.baseHead.llNoticeLayout.setOnClickListener {
            toStartActivity(NoticeActivity::class.java)
        }

        mDataBind.baseHead.llUpdateLayout.setOnClickListener {
            appUMEvent(UMConstant.HOME_HELP_FEEDBACK)
            toStartActivity(FeedbackActivity::class.java)
        }

        mDataBind.baseHead.llSettingLayout.setOnClickListener {
            appUMEvent(UMConstant.HOME_SETTING)
            toStartActivity(SettingsActivity::class.java)
        }
        mDataBind.baseHead.llUserLayout.setOnClickListener {
            toStartActivity(UserOutDialogActivity::class.java)
        }

        mVideoHistoryAdapter.apply {
            setOnItemClickListener { _, _, position ->
                mViewModel.checkDevice()
                mPosition = position
                isVideoHistory = true
//                if (mVideoHistoryAdapter.data[position].`object` is CourseDetailInfo) {
//                    val info = mVideoHistoryAdapter.data[position].`object` as CourseDetailInfo
//                    final_exam_pic = "${info.final_exam_pic}"
//                }
            }

            setOnViewFocus { hasFocus, position, _ ->
                mHistoryPosition = if (position < 0) {
                    0
                } else {
                    position
                }
                if (hasFocus && currentFocusType != focus_video) {
                    mCourseTypeView?.background = ContextCompat.getDrawable(
                        this@CourseActivity,
                        R.drawable.base_border_selecter
                    )
                    currentFocusType = focus_video
                }

                if (hasFocus && position <= 4) {
//                    mDataBind.recyclerHistory.post {
//                        mDataBind.recyclerHistory.smoothScrollToPosition(0)
//                    }
                }
            }
        }

        mCourseAdapter.apply {
            setOnItemClickListener { _, _, position ->
                mViewModel.checkDevice()
                mPosition = position
                isVideoHistory = false
//                final_exam_pic = "${this.data[position].final_exam_pic}"
            }
            setOnItemFocus { hasFocus, position, _ ->
                mCourseFocusPosition = if (position < 0) {
                    0
                } else {
                    position
                }
                if (hasFocus && currentFocusType != focus_course) {
                    mCourseTypeView?.background = ContextCompat.getDrawable(
                        this@CourseActivity,
                        R.drawable.base_border_selecter
                    )
                    currentFocusType = focus_course
                }
            }
        }

        mCourseHorizontalAdapter.apply {
            setOnItemClickListener { _, _, position ->
                mViewModel.checkDevice()
                mPosition = position
                isVideoHistory = false
//                final_exam_pic = "${this.data[position].final_exam_pic}"
            }
            setOnItemFocus { hasFocus, position, _ ->
                mCourseFocusPosition = if (position < 0) {
                    0
                } else {
                    position
                }
                if (hasFocus && currentFocusType != focus_course) {
                    mCourseTypeView?.background = ContextCompat.getDrawable(
                        this@CourseActivity,
                        R.drawable.base_border_selecter
                    )
                    currentFocusType = focus_course
                }
            }
        }

        mRowAdapter.apply {
            setOnViewFocus { hasFocus, position, view ->
                mCourseTypeFocus = hasFocus
                if (!hasFocus) {
                    view.background = ContextCompat.getDrawable(
                        this@CourseActivity,
                        R.drawable.base_border_unselecter
                    )
                    return@setOnViewFocus
                }
                val index: Int = if (position < 0) {
                    0
                } else {
                    position
                }

                currentFocusType = focus_type
                mCourseTypeView = view
                mCourseTypeView?.background =
                    ContextCompat.getDrawable(this@CourseActivity, R.drawable.base_border_selecter)

                mRowPosition = index

                if (mRowAdapter.data.size > 0) {


                    type_id = mRowAdapter.data[index].id

                    if (index == 0) {
                        getVideoHistory()
                    } else {
                        courseList(mRowAdapter.data[index].id)
                    }


                    if (index == 0) {
                        mDataBind.recyclerHistory.visible()
                        mDataBind.recyclerCourseExperience.gone()
                        mDataBind.recyclerCourse.gone()
                    } else {
                        mDataBind.recyclerHistory.gone()
                        if ("2" == mRowAdapter.data[position].content_style) {
                            mDataBind.recyclerCourse.gone()
                            mDataBind.recyclerCourseExperience.visible()
                            currentCourseStyle = true
                        } else {
                            mDataBind.recyclerCourseExperience.gone()
                            mDataBind.recyclerCourse.visible()
                            val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                            layoutParams.setMargins(dp2px(14f), dp2px(0f), dp2px(14f), dp2px(20f))
                            mDataBind.recyclerCourse.layoutParams = layoutParams
                            currentCourseStyle = false
                        }
                    }

                    if (index == 0 && mDataBind.recyclerHistory.visibility == View.VISIBLE) {
                        mDataBind.recyclerHistory.post {
                            mDataBind.recyclerHistory.smoothScrollToPosition(0)
                        }
                    }
                }
                appUMEvent(UMConstant.CLASS_EVENT)
            }
        }
    }

    override fun onRequestSuccess() {
        mViewModel.apply {
            //是否有红包配置
            redPaperConfigEntity.observe(this@CourseActivity, Observer {
                if (it != null) {
                    val config = MMKVHelper.decodeBoolean(ConstantMMVK.RED_PAPER_CONFIG)
                    if (config == false) {
                        showRedPaperConfig(it, {
                            val mDeviceID =
                                if (!TextUtils.isEmpty(MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID))) {
                                    MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID)
                                } else {
                                    eventViewModel.appUserInfo.value?.device_id
                                }
                            mViewModel.createRedPaper("$mDeviceID")
                        }, {
                            MMKVHelper.encode(ConstantMMVK.RED_PAPER_CONFIG, true)
                        })
                    }
                    MMKVHelper.encode(ConstantMMVK.RED_PAPER_CONFIG, true)
                } else {
                    mViewModel.getNoticeTotal(is_notice_content = "1", null) //弹公告 消息
                }
            })
            //是否有场次红包
            redPaperEntity.observe(this@CourseActivity, Observer {
                if (it.list.isNotEmpty() && currentActivity !is PlayerLiveActivity) {
                    finishActivitys(ExpandInterfaceActivity::class.java)
                    toStartActivity(ExpandInterfaceActivity::class.java, Bundle().apply {
                    })
                }
            })
            //设备信息
            deviceExpireDate.observe(this@CourseActivity, androidx.lifecycle.Observer {
                if (it == null) {
                    return@Observer
                }
                eventViewModel.deviceExpireDate.value = it
            })
            // 离线数据上报,如果成功
            mViewModel.uploadOffline.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer {
                    MyVideoInfoDaoUtil.deleteAll()
                }
            )

            // 查询是否可以接收消息体
            mViewModel.checkAcceptNotice.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer {
                    if (it == null) {
                        return@Observer
                    }
                    if ("true" == it.status) {
                        mViewModel.getNoticeTotal(is_notice_content = "1", null, null)
                    }
                }
            )

            // 类似于活动展示弹窗
            mViewModel.noticeEntity.observe(this@CourseActivity, androidx.lifecycle.Observer {
                if (it == null) {
                    mViewModel.getSurpriseActivityAd() //活动 惊喜活动
                    return@Observer
                }
                if (!TextUtils.isEmpty(it.notice?.title)) {
                    mDataBind.baseHead.tvNotice.text =
                        mViewModel.noticeEntity.value?.notice?.title
                }
                if (it.show_total != null) {
                    if (it.show_total!! >= 99) {
                        mDataBind.baseHead.tvNoticeNumber.visible()
                        mDataBind.baseHead.tvNoticeNumber.text = "99+"
                    } else if (it.show_total!! > 0) {
                        mDataBind.baseHead.tvNoticeNumber.visible()
                        mDataBind.baseHead.tvNoticeNumber.text = "${it.show_total!!}"
                    }
                } else {
                    mDataBind.baseHead.tvNoticeNumber.gone()
                }
                if ("0" != it.notice?.is_popup) {
                    if (mNoticeDialog == null) {
                        mNoticeDialog = it.notice?.img_url?.let { it1 ->
                            showNoticeDialog(it1) {
                                toStartActivity(NoticeActivity::class.java)
                                mNoticeDialog?.dismiss()
                            }
                        }
                    }
                    mNoticeDialog?.show()
                }
            }
            )

            todayFinishEntity.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer {
                    if (it.status) {
                        mDataBind.baseHead.tvSurprise.text =
                            mViewModel.surpriseEntity.value?.surprise?.finished_title
                    }
                }
            )

            surpriseEntity.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer { it ->
                    if (it == null) {
                        return@Observer
                    }
                    "${it.pub}".logE("活动")
                    "${it.surprise}".logE("活动")

                    it.pub?.let { pub -> // pub 是否弹出广告  每日弹出一次
                        CoursePresenter.resultEverydayPub(pub, mDataBind.baseHead.llSurpriseLayout)
                    }

                    eventViewModel.surpriseToggle.value = it.surprise

                    MMKVHelper.encode(ConstantMMVK.SURPRISE_ENTITY, JSON.toJSONString(it.surprise))

                    if (it.surprise != null) {
                        GlideUtil.loadGif(
                            this@CourseActivity,
                            it.surprise?.icon_img_url,
                            mDataBind.baseHead.ivSurprise
                        )
                        mDataBind.baseHead.tvSurprise.text = it.surprise?.title
                        mDataBind.baseHead.llSurpriseLayout.visible()
                    } else {
                        mDataBind.baseHead.llSurpriseLayout.gone()
                    }
                }
            )

            userEntity.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer { userEntity: UserEntity ->
                    if (userEntity == null) {
                        return@Observer
                    }
                    saveUserInfo(userEntity = userEntity)
                    locationData()
                }
            )

            mIMInfoEntity.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer {
                    if (it != null && it.im_user_id != null && it.im_user_sig != null) {
                        mRetryCount++
                        if (mRetryCount >= 3) {
                            return@Observer
                        }
                        eventViewModel.appUserInfo.value?.im_user_id = it.im_user_id
                        eventViewModel.appUserInfo.value?.im_user_sig = it.im_user_sig // 这里要重新保存到本地

                        MMKVHelper.encode(
                            ConstantMMVK.USER_INFO,
                            JSON.toJSONString(eventViewModel.appUserInfo.value)
                        )
                        initIMLogin(it.im_user_id, it.im_user_sig)
                    }
                }
            )

            mVideoHistory.observe(this@CourseActivity, androidx.lifecycle.Observer { it ->
                if (it == null) {
                    return@Observer
                }

                MMKVHelper.encode(ConstantMMVK.COURSE_VIDEO_HISTORY, JSON.toJSONString(it))

                mDataBind.recyclerHistory.smoothScrollBy(0, 0)
                val mList = ArrayList<DateSection>()
                // 增价Ai直播课
                if (it.is_ai_live_course) {
                    mList.add(DateSection(true, "AI直播课"))
                }


                if (it.live_course_list != null && it.live_course_list!!.size > 0) {
                    for (i in it.live_course_list!!.indices) {
                        mList.add(
                            DateSection(
                                false, CourseDetailInfo(
                                    code = it.live_course_list!![i].code,
                                    chapter_code = null,
                                    course_code = null,
                                    title_main = it.live_course_list!![i].title_main,
                                    title_sub = it.live_course_list!![i].examination_start_time + " 至 " + it.live_course_list!![i].examination_end_time,
                                    course_cover_url = it.live_course_list!![i].course_cover_image,
                                    video_cover_image = it.live_course_list!![i].course_cover_image,
                                    video_introduction = null,
                                    video_url = null,
                                    presetTsSize = null,
                                    totalTsSize = null,
                                    hd_video_url_list = null,
                                    video_watch_time = it.live_course_list!![i].course_status_name,
                                    video_watch_min = null,
                                    video_time = null,
                                    type = ConstantMMVK.AI_LIVE,
                                    tencent_video_id = null,
                                    hd_video_subtitle_url = null,
                                    status = it.live_course_list!![i].course_status,
                                    training_aid_introduction = null,
                                )
                            )
                        )
                    }
                    if (it.is_ai_live_course) {
                        mList.add(
                            DateSection(
                                false, CourseDetailInfo(
                                    code = "0",
                                    chapter_code = null,
                                    course_code = null,
                                    title_main = "全部",
                                    title_sub = null,
                                    course_cover_url = null,
                                    video_cover_image = null,
                                    video_introduction = null,
                                    video_url = null,
                                    presetTsSize = null,
                                    totalTsSize = null,
                                    hd_video_url_list = null,
                                    video_watch_time = null,
                                    video_watch_min = null,
                                    video_time = null,
                                    type = ConstantMMVK.AI_LIVE_DEF,
                                    tencent_video_id = null,
                                    hd_video_subtitle_url = null,
                                    status = null,
                                    training_aid_introduction = null,
                                )
                            )
                        )
                    }
                } else {
                    if (it.is_ai_live_course) {
                        mList.add(
                            DateSection(
                                false, CourseDetailInfo(
                                    code = "0",
                                    chapter_code = null,
                                    course_code = null,
                                    title_main = "全部",
                                    title_sub = null,
                                    course_cover_url = null,
                                    video_cover_image = null,
                                    video_introduction = null,
                                    video_url = null,
                                    presetTsSize = null,
                                    totalTsSize = null,
                                    hd_video_url_list = null,
                                    video_watch_time = null,
                                    video_watch_min = null,
                                    video_time = null,
                                    type = ConstantMMVK.AI_LIVE_DEF,
                                    tencent_video_id = null,
                                    hd_video_subtitle_url = null,
                                    status = null,
                                    training_aid_introduction = null,
                                )
                            )
                        )
                    }
                }
                mList.add(DateSection(true, "观看记录"))
                if (it.history_list != null && it.history_list!!.size > 0) {
                    for (i in it.history_list!!.indices) {
                        it.history_list!![i].isLeft = i % 4 == 0
                        mList.add(DateSection(false, it.history_list!![i]))
                    }
                } else {
                    mList.add(
                        DateSection(
                            true, "暂无观看记录"
                        )
                    )
                }
                mList.add(DateSection(true, "我的视频"))
                if (it.ad != null && it.ad!!.size > 0) {
                    for (i in it.ad!!.indices) {
                        if (it.ad!![i].source_list != null && it.ad!![i].source_list!!.size > 0) {
                            mList.add(
                                DateSection(
                                    false,
                                    CourseDetailInfo(
                                        code = it.ad!![i].id,
                                        chapter_code = null,
                                        course_code = null,
                                        title_main = if (!TextUtils.isEmpty(it.ad!![i].media_name)) it.ad!![i].media_name else "",
                                        title_sub = null,
                                        course_cover_url = it.ad!![i].cover_url,
                                        video_cover_image = null,
                                        video_introduction = null,
                                        video_url = null,
                                        presetTsSize = null,
                                        totalTsSize = null,
                                        hd_video_url_list = null,
                                        video_watch_time = null,
                                        video_watch_min = null,
                                        video_time = null,
                                        type = ConstantMMVK.AD_VIDEO,
                                        tencent_video_id = null,
                                        ad_Info = it.ad!![i],
                                        isLeft = i % 4 == 0, training_aid_introduction = null,
                                        hd_video_subtitle_url = null,
                                        status = null
                                    )
                                )
                            )
                        }
                        if ("1" == it.ad!![i].is_default) {
                            mAdData = it.ad!![i]
                        }
                    }
                    // 如果adData 为空 并且数组大小不为null ，那么取第一个，默认播放第一个
                    if (mAdData != null) { // 设置默认时间
                        isPropagandaVideo = true // 告诉activity 有ad
                        mAdData?.effects_time?.let { t ->
                            mTime = t.toLong()
                            "宣传片启动的时间 $mTime  isPropagandaVideo=$isPropagandaVideo".logD(TAG)
                        }
                    } else {
                        isPropagandaVideo = false
                    }
                } else {
                    mList.add(
                        DateSection(true, ConstantMMVK.MY_VIDEO_TIPS)
                    )
                    isPropagandaVideo = false // 告诉activity 没有默认的ad
                }
                mVideoHistoryAdapter.setList(mList)

                if (!TextUtils.isEmpty(it.aunt_skill_pic)) {
                    mVideoHistoryAdapter.removeAllHeaderView()
                    val mHeaderHistory = View.inflate(this@CourseActivity, R.layout.course_header_history, null)
                    mHeaderHistoryBinding = DataBindingUtil.bind<CourseHeaderHistoryBinding>(mHeaderHistory)

                    var mShowCount = 1 //记录显示了几个View  1显示长图、2是中图 3是短图 因为技能测评、默认就是显示的，所以默认是1

//                    GlideUtil.loadPicRound(this@CourseActivity, R.drawable.big2121212, mHeaderHistoryBinding?.ivHeaderSkill, 10)


                    if (!TextUtils.isEmpty(it.recruitment_order_pic)) {
                        mShowCount += 1
                        mHeaderHistoryBinding?.flOrderHallLayout?.visible()
//                        GlideUtil.loadPicRound(this@CourseActivity, it.recruitment_order_pic, mHeaderHistoryBinding?.ivOrderHall, 10)
                    } else {
                        mHeaderHistoryBinding?.flOrderHallLayout?.gone()
                    }

                    if (!TextUtils.isEmpty(it.student_ranking_pic)) {
                        mShowCount += 1
                        mHeaderHistoryBinding?.flRankLayout?.visible()
//                        GlideUtil.loadPicRound(this@CourseActivity, it.student_ranking_pic, mHeaderHistoryBinding?.ivRank, 10)
                    } else {
                        mHeaderHistoryBinding?.flRankLayout?.gone()
                    }


                    when (mShowCount) {
                        1 -> {
                            GlideUtil.loadPicRound(
                                this@CourseActivity, R.mipmap.bg_big_skill_unselect, mHeaderHistoryBinding?.ivHeaderSkill, 10
                            )
                        }

                        2 -> {
                            GlideUtil.loadPicRound(
                                this@CourseActivity, R.mipmap.bg_in_skill_unselect, mHeaderHistoryBinding?.ivHeaderSkill, 10
                            )
                            GlideUtil.loadPicRound(
                                this@CourseActivity, R.mipmap.bg_in_rank_unselect, mHeaderHistoryBinding?.ivRank, 10
                            )
                        }

                        else -> {
                            GlideUtil.loadPicRound(
                                this@CourseActivity, R.mipmap.bg_short_skill_unselect, mHeaderHistoryBinding?.ivHeaderSkill, 10
                            )
                            GlideUtil.loadPicRound(
                                this@CourseActivity, R.mipmap.bg_short_order_hall_unselect, mHeaderHistoryBinding?.ivOrderHall, 10
                            )
                            GlideUtil.loadPicRound(
                                this@CourseActivity, R.mipmap.bg_short_rank_unselect, mHeaderHistoryBinding?.ivRank, 10
                            )
                        }
                    }

//                    if (!TextUtils.isEmpty(eventViewModel.appUserInfo.value?.expiration_notice)) {
//                        mHeaderHistoryBinding?.flExpirationNotice?.visible()
//                        mHeaderHistoryBinding?.flRenewalLayout?.visible()
//                        mHeaderHistoryBinding?.tvExpirationNotice?.text = eventViewModel.appUserInfo.value?.expiration_notice
//                    } else {
//                        mHeaderHistoryBinding?.flRenewalLayout?.gone()
//                        mHeaderHistoryBinding?.flExpirationNotice?.gone()
//                    }

//                    GlideUtil.loadPicRound(this@CourseActivity, it.honor_pic, mHeaderHistoryBinding?.ivHeaderHonor, 10)

                    //技能测评
                    mHeaderHistoryBinding?.flHeaderSkill?.setOnFocusChangeListener { v, hasFocus ->
                        mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
                        if (hasFocus) {
                            currentFocusType = focus_video
//                            mDataBind.recyclerHistory.post {
//                                mDataBind.recyclerHistory.smoothScrollToPosition(0)
//                            }
                        }
                        when (mShowCount) {
                            1 -> {
                                GlideUtil.loadPicRound(
                                    this@CourseActivity,
                                    if (hasFocus) R.mipmap.bg_big_skill_select else R.mipmap.bg_big_skill_unselect,
                                    mHeaderHistoryBinding?.ivHeaderSkill, 10
                                )
                            }

                            2 -> {
                                GlideUtil.loadPicRound(
                                    this@CourseActivity,
                                    if (hasFocus) R.mipmap.bg_in_skill_select else R.mipmap.bg_in_skill_unselect,
                                    mHeaderHistoryBinding?.ivHeaderSkill, 10
                                )
                            }

                            else -> {
                                GlideUtil.loadPicRound(
                                    this@CourseActivity,
                                    if (hasFocus) R.mipmap.bg_short_skill_select else R.mipmap.bg_short_skill_unselect,
                                    mHeaderHistoryBinding?.ivHeaderSkill, 10
                                )
                            }
                        }
//                        mHeaderHistoryBinding?.tvFloatingSubTitle?.visibility = if (hasFocus) View.VISIBLE else View.GONE
                    }
                    // 订单大厅
                    mHeaderHistoryBinding?.flOrderHallLayout?.setOnFocusChangeListener { v, hasFocus ->
                        mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
                        if (hasFocus) {
                            currentFocusType = focus_video
//                            mDataBind.recyclerHistory.post {
//                                mDataBind.recyclerHistory.smoothScrollToPosition(0)
//                            }
                        }
                        when (mShowCount) {
                            1 -> {
                                GlideUtil.loadPicRound(
                                    this@CourseActivity,
                                    if (hasFocus) R.mipmap.bg_big_order_hall_select else R.mipmap.bg_big_order_hall_unselect,
                                    mHeaderHistoryBinding?.ivOrderHall, 6
                                )
                            }

                            2 -> {
                                GlideUtil.loadPicRound(
                                    this@CourseActivity,
                                    if (hasFocus) R.mipmap.bg_in_order_hall_select else R.mipmap.bg_in_order_hall_unselect,
                                    mHeaderHistoryBinding?.ivOrderHall, 6
                                )
                            }

                            else -> {
                                GlideUtil.loadPicRound(
                                    this@CourseActivity,
                                    if (hasFocus) R.mipmap.bg_short_order_hall_select else R.mipmap.bg_short_order_hall_unselect,
                                    mHeaderHistoryBinding?.ivOrderHall, 6
                                )
                            }
                        }

//                        mHeaderHistoryBinding?.tvOrderHallSubTitle?.visibility = if (hasFocus) View.VISIBLE else View.GONE
                    }

                    //排名
                    mHeaderHistoryBinding?.flRankLayout?.setOnFocusChangeListener { v, hasFocus ->
                        mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
                        if (hasFocus) {
                            currentFocusType = focus_video
//                            mDataBind.recyclerHistory.post {
//                                mDataBind.recyclerHistory.smoothScrollToPosition(0)
//                            }
                        }

                        when (mShowCount) {
                            1 -> {
                                GlideUtil.loadPicRound(
                                    this@CourseActivity,
                                    if (hasFocus) R.mipmap.bg_big_rank_select else R.mipmap.bg_big_rank_unselect,
                                    mHeaderHistoryBinding?.ivRank, 10
                                )
                            }

                            2 -> {
                                GlideUtil.loadPicRound(
                                    this@CourseActivity,
                                    if (hasFocus) R.mipmap.bg_in_rank_select else R.mipmap.bg_in_rank_unselect,
                                    mHeaderHistoryBinding?.ivRank, 10
                                )
                            }

                            else -> {
                                GlideUtil.loadPicRound(
                                    this@CourseActivity,
                                    if (hasFocus) R.mipmap.bg_short_rank_select else R.mipmap.bg_short_rank_unselect,
                                    mHeaderHistoryBinding?.ivRank, 10
                                )
                            }
                        }

//                        mHeaderHistoryBinding?.tvRankSubTitle?.visibility =
//                            if (hasFocus) View.VISIBLE else View.GONE
                    }

                    mHeaderHistoryBinding?.flRenewalLayout?.setOnFocusChangeListener { v, hasFocus ->
                        mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
                        if (hasFocus) {
                            currentFocusType = focus_video
//                            mDataBind.recyclerHistory.post {
//                                mDataBind.recyclerHistory.smoothScrollToPosition(0)
//                            }
                        }
//                        mHeaderHistoryBinding?.flRankLayout?.visibility =
//                            if (hasFocus) View.VISIBLE else View.GONE
                    }

                    mHeaderHistoryBinding?.flRankLayout?.setOnClickListener {
                        appUMEvent(UMConstant.HOME_EXCELLENT_STUDENT)
                        toStartActivity(ExcellentRankingActivity::class.java)
                    }

                    mHeaderHistoryBinding?.flOrderHallLayout?.setOnClickListener {
                        appUMEvent(UMConstant.HOME_ORDER_HALL)
                        toStartActivity(OrderHallActivity::class.java)
                    }

                    mHeaderHistoryBinding?.flHeaderSkill?.setOnClickListener {
                        appUMEvent(UMConstant.HOME_SKILL_EVALUATION)
                        toStartActivity(SkillActivity::class.java)
                    }

                    mHeaderHistoryBinding?.tvRenewal?.setOnClickListener {
                        toStartActivity(OnlineRenewalActivity::class.java)
                    }

                    mVideoHistoryAdapter.addHeaderView(mHeaderHistory)
                    isRemoveHeaderView = false
                } else {
                    isRemoveHeaderView = true
                    mVideoHistoryAdapter.removeAllHeaderView()
                }
                goPropagandaVideo() // 存默认的某个到本地
                MMKVHelper.encode(ConstantMMVK.COMPANY_PROPAGANDA, mAdData)
            }
            )

            mRowList.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer { rowEntity: RowEntity ->
                    dismissLoadingExt()
                    if (checkLocationDataNULL(ConstantMMVK.COURSE_CLASS)) {
                        if (mRowAdapter.data.size != rowEntity.list.size) {
                            mRowAdapter.setList(rowEntity.list)
                        }
                    } else {
                        mRowAdapter.setList(rowEntity.list)
                    }
                    requestOne()
                    MMKVHelper.encode(ConstantMMVK.COURSE_CLASS, JSON.toJSONString(rowEntity))
                    dismissLoadingNetWorkExt()
                }
            )

            mCourseList.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer { data: CourseList ->
                    dismissLoadingExt()
                    if (data.list.isNullOrEmpty()) {
                        mViewModel.getRowList()
                    }
                    type_id?.let {
                        if (checkLocationDataNULL(it)) {
                            if (currentCourseStyle) {
                                mCourseHorizontalAdapter.setList(data.list)
                            } else {
                                mCourseAdapter.setList(data.list)
                            }
                        }
                        MMKVHelper.encode(it, JSON.toJSONString(data))
                    }
                }
            )

            mUpdateInfo.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer { updateInfo: UpdateInfo? ->
                    if (updateInfo == null) {
                        eventViewModel.appUpdate.value = null
                        mViewModel.getRedPaperConfig() //请求红包
                        return@Observer
                    }
                    eventViewModel.appUpdate.value = updateInfo
                    toStartActivity(UpdateDialogActivity::class.java)
                }
            )

            checkDevice.observe(this@CourseActivity, androidx.lifecycle.Observer {
                if (isVideoHistory) {
                    if (mVideoHistoryAdapter.data[mPosition].`object` != null && mVideoHistoryAdapter.data[mPosition].`object` is CourseDetailInfo) {
                        val info = mVideoHistoryAdapter.data[mPosition].`object` as CourseDetailInfo

                        val bundle = Bundle()

                        when (info.type) {
                            ConstantMMVK.AD_VIDEO -> {
                                bundle.putInt("JumpType", 1)
                                bundle.putParcelable("AD_INFO", info.ad_Info)
                                toStartActivity(PropagandaActivity::class.java, bundle)
                                appUMEvent(UMConstant.PRO_VIDEO_EVENT)
                            }

                            ConstantMMVK.AI_LIVE -> {
                                //在这里判断一下 课程的状态，然后进行不同的处理
                                when (info.status) {
                                    "3" -> {
                                        "直播已结束".toast()
                                    }

                                    "2" -> {
                                        bundle.putString("code", info.code)
                                        bundle.putString("title_name", info.title_main)
                                        toStartActivity(LiveClassHourActivity::class.java, bundle)
                                    }

                                    else -> {
                                        bundle.putString("course_code", info.code)
                                        bundle.putString("title_name", info.title_main)
                                        toStartActivity(PlayerLiveActivity::class.java, bundle)
                                    }
                                }

                            }

                            ConstantMMVK.AI_LIVE_DEF -> {
                                toStartActivity(LiveListActivity::class.java, bundle)
                                appUMEvent(UMConstant.AI_ALL_CLICK)
                            }

                            else -> {
                                val mCourseInfo =
                                    mVideoHistoryAdapter.data[mPosition].`object` as CourseDetailInfo
                                bundle.putParcelable("course_detail", mCourseInfo)
                                bundle.putString("store_logo", mViewModel.mVideoHistory.value?.store_log)
                                bundle.putString("course_status", mCourseInfo.status)
                                bundle.putString("course_detail_code", info.course_code)
                                bundle.putInt("JumpType", 1)
                                //                            if (!TextUtils.isEmpty(final_exam_pic)) { //结业考试图片
                                //                                bundle.putBoolean("isExam", true)
                                //                            }
                                toStartActivity(PlayerActivity::class.java, bundle)
                                appUMEvent(UMConstant.HISTORY_EVENT)
                            }
                        }
                    }
                } else {

                    val bundle = Bundle()
                    bundle.putString("course_cover_url", if (currentCourseStyle) mCourseHorizontalAdapter.data[mPosition].course_cover_image else mCourseAdapter.data[mPosition].course_cover_image)
                    bundle.putString("course_status", if (currentCourseStyle) mCourseHorizontalAdapter.data[mPosition].status else mCourseAdapter.data[mPosition].status)
                    bundle.putString("course_code", if (currentCourseStyle) mCourseHorizontalAdapter.data[mPosition].code else mCourseAdapter.data[mPosition].code)
                    bundle.putString("uuid", eventViewModel.appUserInfo.value?.uuid)
                    toStartActivity(ContentActivity::class.java, bundle)

                    if (currentCourseStyle) {
                        appUMEvent(UMConstant.COURSE_CARD_LIST)
                    } else {
                        appUMEvent(UMConstant.COURSE_VIDEO)
                    }
                }
            }
            )

            checkTipsDevice.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer {
                    showMessageDialog(it)
                }
            )

            checkCodeDevice.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer {
                    if (it == 2000) {
                        isPropagandaVideo = false
                    }
                }
            )

            cacheVideoList.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer {
                    if (it == null) {
                        return@Observer
                    }
                    eventViewModel.appCacheVideo.value = it
                }
            )

            cacheVideoDotList.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer {
                    if (it == null) {
                        return@Observer
                    }
                    MMKVHelper.encode(ConstantMMVK.VIDEO_CACHE_DOT, it)
                }
            )

            cacheVideoDotResource.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer {
                    if (it == null) {
                        return@Observer
                    }
                    MMKVHelper.encode(ConstantMMVK.VIDEO_CACHE_DOT_RESOURCE, it)
                }
            )


            mOTAUpdateInfo.observe(
                this@CourseActivity,
                androidx.lifecycle.Observer {
                    if (it == null) {
                        eventViewModel.appOTAUpdate.value = false
                        return@Observer
                    }
                    eventViewModel.appOTAUpdate.value = true
                }
            )
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        "keyCode = $keyCode".logE("按键操作")
//        findRootViewFocus() // 获取界面全部焦点
        goPropagandaVideo() // 宣传片

        if (onMyKeyDown(keyCode, event)) { // 加一层判断，实现android 9 以及其他的情况
            return true
        }

        when (keyCode) {
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (mDataBind.baseHead.llSettingLayout.isFocused) {
                    mDataBind.baseHead.llSettingLayout.clearAnimation()
                    mDataBind.baseHead.llSettingLayout.startAnimation(mShakeAnimation)
                    return true
                }

                if (mDataBind.baseHead.llUserLayout.isFocused || mDataBind.baseHead.llNoticeLayout.isFocused || mDataBind.baseHead.llSurpriseLayout.isFocused || mDataBind.baseHead.llUpdateLayout.isFocused) {
                    return false
                }

                if (mRowPosition != 0) {
                    if (currentCourseStyle) {
                        if (mCourseHorizontalAdapter.data.size <= 0) {
                            rowStartAnimation(true)
                            return true
                        }
                    } else {
                        if (mCourseAdapter.data.size <= 0) {
                            rowStartAnimation(true)
                            return true
                        }
                    }
                }

                if (mDataBind.recyclerHistory.visibility == View.VISIBLE && currentFocusType != focus_video) {

                    if (!requestHeaderFocus()) {
                        // 🔥🔥🔥注意如果有俩个View同时需要改动东西，要先把请求焦点的执行完毕后，再去设置bg
                        requestHistoryOneFocus()
                    }

                    mCourseTypeView?.background = ContextCompat.getDrawable(this@CourseActivity, R.drawable.base_border_selecter)
                    currentFocusType = focus_video
                    return true
                }

                if (mDataBind.recyclerCourse.visibility == View.VISIBLE && currentFocusType != focus_course) {
                    // 🔥🔥🔥注意如果有俩个View同时需要改动东西，要先把请求焦点的执行完毕后，再去设置bg
                    mCourseAdapter.getViewByPosition(0, R.id.card_view)?.requestFocus()

                    mCourseTypeView?.background = ContextCompat.getDrawable(this@CourseActivity, R.drawable.base_border_selecter)

                    currentFocusType = focus_course
                    return true
                }

                if (currentFocusType == focus_video && mHeaderHistoryBinding?.flOrderHallLayout?.visibility == View.GONE && mHeaderHistoryBinding?.flRankLayout?.visibility == View.GONE) {
                    CommonAnimationUtil().startShakeAnimation(false, mHeaderHistoryBinding?.flHeaderSkill)
                    return true
                }

                if (currentFocusType == focus_video && mHeaderHistoryBinding?.flOrderHallLayout?.visibility == View.VISIBLE && mHeaderHistoryBinding?.flOrderHallLayout?.isFocused == true && mHeaderHistoryBinding?.flRankLayout?.visibility == View.GONE
                ) {
                    CommonAnimationUtil().startShakeAnimation(false, mHeaderHistoryBinding?.flOrderHallLayout)
                    return true
                }


                if (currentFocusType == focus_video && mHeaderHistoryBinding?.flRankLayout?.isFocused == true
                ) {
                    CommonAnimationUtil().startShakeAnimation(false, mHeaderHistoryBinding?.flRankLayout)
                    return true
                }

                if (currentFocusType == focus_course) {
                    if (currentCourseStyle) {
                        if (mCourseFocusPosition % 4 == 3 || mCourseFocusPosition == (mCourseHorizontalAdapter.data.size - 1)) {
                            rowStartAnimation(selectRow = false)
                        } else {
                            return false
                        }
                        return true
                    } else {
                        rowStartAnimation(selectRow = false)
                    }
                    return true
                }
            }

            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (mDataBind.baseHead.llNoticeLayout.isFocused || mDataBind.baseHead.llSettingLayout.isFocused ||
                    mDataBind.baseHead.llSurpriseLayout.isFocused || mDataBind.baseHead.llUpdateLayout.isFocused
                ) {
                    return false
                }


                if (mDataBind.baseHead.llUserLayout.isFocused) { // 边界没有值了
                    CommonAnimationUtil().startShakeAnimation(false, mDataBind.baseHead.llUserLayout)
                    return true
                }

                mDataBind.baseHead.llUserLayout.isFocusable = false

                if (mHeaderHistoryBinding?.flHeaderSkill?.isFocused == true) {
                    requestRowOne()
                    return true
                }

                if (mHeaderHistoryBinding?.flOrderHallLayout?.isFocused == true) {
                    mHeaderHistoryBinding?.flHeaderSkill?.isFocusable = true
                    mHeaderHistoryBinding?.flHeaderSkill?.requestFocus()
                    return true
                }

                if (mRowAdapter.data.size > 0 && currentFocusType == focus_type) {
                    rowStartAnimation(selectRow = true)
                    return true
                }
            }


            KeyEvent.KEYCODE_DPAD_UP -> {
                if (mRowPosition == 0 && mCourseTypeFocus) { // 只有在选中分类第一个的时候，才可以去up 🔥
                    mDataBind.baseHead.llUserLayout.isFocusable = true
                    mDataBind.baseHead.llUserLayout.requestFocus()
                    return true
                }

                if (mDataBind.baseHead.llUserLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llUserLayout)
                    return true
                }

                if (mDataBind.baseHead.llNoticeLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llNoticeLayout)
                    return true
                }

                if (mDataBind.baseHead.llSurpriseLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llSurpriseLayout)
                    return true
                }

                if (mDataBind.baseHead.llUpdateLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llUpdateLayout)
                    return true
                }

                if (mDataBind.baseHead.llSettingLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llSettingLayout)
                    return true
                }

                if (mDataBind.recyclerHistory.visibility == View.VISIBLE && currentFocusType == focus_video && mVideoHistoryAdapter.headerLayout != null && !isRemoveHeaderView) {
                    if (!isRemoveHeaderView && mHistoryPosition <= 4 && mHeaderHistoryBinding?.flHeaderSkill?.isFocused == false
                        && mHeaderHistoryBinding?.flOrderHallLayout?.visibility == View.GONE && mHeaderHistoryBinding?.flRankLayout?.visibility == View.GONE
                    ) {
                        mHeaderHistoryBinding?.flHeaderSkill?.isFocusable = true
                        mHeaderHistoryBinding?.flHeaderSkill?.requestFocus()
                        return true
                    } else {
                        mHeaderHistoryBinding?.flHeaderSkill?.isFocusable = true
                    }
                    requestHistoryOneFocus()
                    return false
                }

                if (currentFocusType == focus_course && mCourseFocusPosition == 0) { // 可以让course也往上
                    currentFocusType = focus_top
                    mDataBind.baseHead.llUserLayout.isFocusable = true
                    when {
                        mDataBind.baseHead.llUpdateLayout.visibility == View.VISIBLE -> {
                            mDataBind.baseHead.llUpdateLayout.requestFocus()
                        }

                        mDataBind.baseHead.llSurpriseLayout.visibility == View.VISIBLE -> {
                            mDataBind.baseHead.llSurpriseLayout.requestFocus()
                        }

                        mDataBind.baseHead.llNoticeLayout.visibility == View.VISIBLE -> {
                            mDataBind.baseHead.llNoticeLayout.requestFocus()
                        }

                        else -> {
                            mDataBind.baseHead.llSettingLayout.requestFocus()
                        }
                    }
                } else if (currentFocusType == focus_course && "2" == mRowAdapter.data[mRowPosition].content_style && mCourseFocusPosition <= 3) {
                    currentFocusType = focus_top
                    mDataBind.baseHead.llUserLayout.isFocusable = true
                    when {
                        mDataBind.baseHead.llUpdateLayout.visibility == View.VISIBLE -> {
                            mDataBind.baseHead.llUpdateLayout.requestFocus()
                        }

                        mDataBind.baseHead.llSurpriseLayout.visibility == View.VISIBLE -> {
                            mDataBind.baseHead.llSurpriseLayout.requestFocus()
                        }

                        mDataBind.baseHead.llNoticeLayout.visibility == View.VISIBLE -> {
                            mDataBind.baseHead.llNoticeLayout.requestFocus()
                        }

                        else -> {
                            mDataBind.baseHead.llSettingLayout.requestFocus()
                        }
                    }
                } else if (currentFocusType == focus_video && mHistoryPosition <= 4) {
                    when {
                        mDataBind.baseHead.llUpdateLayout.visibility == View.VISIBLE -> {
                            mDataBind.baseHead.llUpdateLayout.requestFocus()
                        }

                        mDataBind.baseHead.llSurpriseLayout.visibility == View.VISIBLE -> {
                            mDataBind.baseHead.llSurpriseLayout.requestFocus()
                        }

                        mDataBind.baseHead.llNoticeLayout.visibility == View.VISIBLE -> {
                            mDataBind.baseHead.llNoticeLayout.requestFocus()
                        }

                        else -> {
                            mDataBind.baseHead.llSettingLayout.requestFocus()
                        }
                    }
                }
            }

            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (currentFocusType == focus_video && mHeaderHistoryBinding?.flHeaderSkill?.isFocused == true) { //为了获取第一个焦点
                    mHeaderHistoryBinding?.flHeaderSkill?.isFocusable = false
                    mVideoHistoryAdapter.getViewByPosition(2, R.id.card_item_view_history)?.requestFocus(100)
                    return true
                }
                return false
            }

            KeyEvent.KEYCODE_BACK, KeyEvent.KEYCODE_PROFILE_SWITCH -> return true
        }
        return super.onKeyDown(keyCode, event)
    }

    /**
     * 处理一下是否存在hedaer的情况
     */
    private fun requestHeaderFocus(): Boolean {
        "${mVideoHistoryAdapter.headerLayout?.visibility}".logE("按键操作")
        if (mVideoHistoryAdapter.headerLayout != null && !isRemoveHeaderView) {//判断是否有测评/荣誉
            mHeaderHistoryBinding?.flHeaderSkill?.requestFocus(100)
            return true
        }
        return false
    }

    /**
     * 默认获取观看记录第一item的焦点
     */
    private fun requestHistoryOneFocus() {
        // 🔥🔥🔥注意如果有俩个View同时需要改动东西，要先把请求焦点的执行完毕后，再去设置bg
        mVideoHistoryAdapter.getViewByPosition(1, R.id.card_item_view_history)?.requestFocus()
    }

    private fun findRootViewFocus() {
        val focusView = window.decorView.findFocus()
        Log.e("获取界面焦点", "===当前获取焦点的View===$focusView")
    }

    /**
     * Row 列表 动画效果
     */
    private fun rowStartAnimation(selectRow: Boolean, actionY: Boolean? = false) {
        val mView = if (selectRow) mRowAdapter.getViewByPosition(mRowPosition, R.id.row_card_view) else if (currentCourseStyle) mCourseHorizontalAdapter.getViewByPosition(mCourseFocusPosition, R.id.card_view) else mCourseAdapter.getViewByPosition(mCourseFocusPosition, R.id.card_view)
        mView?.clearAnimation()
        mView?.startAnimation(if (actionY == true) mShakeYAnimation else mShakeAnimation)
    }

    /**
     * IM登陆
     */
    private fun initIMLogin(im_user_id: String?, im_user_sig: String?) {
        if (TUILogin.isUserLogined()) {
            if (TUILogin.isUserAddGroup()) {
                return
            } else {
                addUserGroup()
            }
            return
        }
        TUIUtils.login(
            im_user_id,
            im_user_sig,
            object : V2TIMCallback {
                override fun onSuccess() {
                    addUserGroup()
                }

                override fun onError(p0: Int, p1: String?) {
                    "登录失败 code = $p0 , msg = $p1".logE("HttpLog-IM")
                    mViewModel.getIMInfo()
                    val hashMap = HashMap<String, Any>()
                    hashMap["user_id"] = "$im_user_id"
                    hashMap["im_code"] = "$p0"
                    hashMap["im_error"] = "$p1"
                    appUMEventObject(UMConstant.KEY_IM_ERROR, hashMap)
                }
            }
        )
    }

    /**
     * 用户增加IM
     */
    private fun addUserGroup() {
        if (!TextUtils.isEmpty(eventViewModel.appUserInfo.value?.im_group_id) && !TUILogin.isUserAddGroup()) { // 加入群组
            IMUtils().joinGroup(
                eventViewModel.appUserInfo.value?.im_group_id,
                "",
                object : IUIKitCallback<Void> {
                    override fun onSuccess(data: Void?) {
                        mDataBind.baseHead.tvHeadTips.text =
                            "IM 进群成功了 组id ${eventViewModel.appUserInfo.value?.im_group_id}"
                    }

                    override fun onError(module: String?, errCode: Int, errMsg: String?) {
                        mDataBind.baseHead.tvHeadTips.text = "IM 进群失败,错误是$errMsg"
                    }
                })
        }
    }

    private fun requestRowOne() {
        val mView = mRowAdapter.getViewByPosition(0, R.id.iv_item_row)
        mView?.requestFocus()
    }

    private fun requestOne() {
        if (!mLoadCourseSuccess) {
            Handler().postDelayed({
                if (mRowAdapter.data.size > 0) {
                    requestRowOne()
                    mLoadCourseSuccess = true
                }
            }, 200)
        }
    }

    /**
     * 防止用户再进来二维码页面，直接在这个界面请求 在这里请求一定是登录成功过的
     */
    private fun saveUserInfo(userEntity: UserEntity) {
        eventViewModel.appUserInfo.value = userEntity
        MMKVHelper.encode(ConstantMMVK.DEVICE_ID, userEntity.device_id)
        MMKVHelper.encode(ConstantMMVK.TOKEN, userEntity.token)
        MMKVHelper.encode(ConstantMMVK.USER_INFO, JSON.toJSONString(userEntity))
        "登录成功 ${JSON.toJSONString(userEntity)}".logD(TAG)
        MobclickAgent.onProfileSignIn(getWireMac())
    }

    override fun onDestroy() {
        NetWorkListenerUtils(this).unbindShowNetSpeed()
        NetBroadcastReceiver.unRegisterReceiver(this, this)
        if (mQueryCourseRewardDisposable != null) {
            mQueryCourseRewardDisposable?.dispose()
            mQueryCourseRewardDisposable = null
        }
        unregisterReceiver(mHomeReceiver)
        V2TIMManager.getMessageManager().removeAdvancedMsgListener(v2TIMAdvancedMsgListener)
        TXVodUtil.getInstance().stopAllVideoDownload()
        eventViewModel.appDownloadVideoState.value = false
        super.onDestroy()
    }

    override fun onDisconnect() {
        if (this == currentActivity) {
            showNetDialog { onLoadRetry() }
        }
        eventViewModel.monitorNetWork.value = false
    }

    override fun onNetWorkConnect() {
        if (mRowAdapter.data.size > 0 && mVideoHistoryAdapter.data.size <= 0) {
            if (mRowPosition == 0) {
//                val jsonData = MMKVHelper.decodeString(ConstantMMVK.COURSE_VIDEO_HISTORY)
//                if (jsonData != null) {
//                    val indexEntity = Gson().fromJson(jsonData, IndexEntity::class.java)
//                    if (indexEntity != null) {
//                        mViewModel.mVideoHistory.value = indexEntity
//                    }
//                }
                mDataBind.recyclerRow.postDelayed({
                    getVideoHistory()
                }, 2000)
            } else {
                courseList(mRowAdapter.data[mRowPosition].id)
            }
        }
        eventViewModel.monitorNetWork.value = true
        if (NetworkUtil.isAvailable(this)) {
            dismissLoadingNetWorkExt()
        }
        if (this == currentActivity) {
            if (eventViewModel.appUserInfo.value == null) {
                toStartActivity(MainActivity::class.java)
                finishActivity(this)
                return
            }
            onLoadRetry()
        }
    }
}
