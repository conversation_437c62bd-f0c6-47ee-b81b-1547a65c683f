package com.panda.course.entity;

import com.chad.library.adapter.base.entity.MultiItemEntity;

public class CommonFunctions implements MultiItemEntity {

    public final static int VIEW_DEF = 1; //正常多文字
    public final static int VIEW_TEXT = 2;// 纯文本
    public final static int VIEW_IMG = 3;// 图片模式
    public final static int VIEW_BIG_DEF = 4;// 大图模式

    private String title;
    private String content;
    private int showType;
    private String url;
    private boolean seleced;

    public CommonFunctions(String title, String content) {
        this.title = title;
        this.content = content;
        this.showType = VIEW_DEF;
    }

    public CommonFunctions(String title, String content, int showType) {
        this.title = title;
        this.content = content;
        this.showType = showType;
    }

    public CommonFunctions(String title, String content, int showType, boolean seleced) {
        this.title = title;
        this.content = content;
        this.showType = showType;
        this.seleced = seleced;
    }

    public void setSeleced(boolean seleced) {
        this.seleced = seleced;
    }

    public boolean isSeleced() {
        return seleced;
    }

    public void setShowType(int showType) {
        this.showType = showType;
    }

    public int getShowType() {
        return showType;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrl() {
        return url;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public String getTitle() {
        return title;
    }

    @Override
    public int getItemType() {
        return showType;
    }
}
