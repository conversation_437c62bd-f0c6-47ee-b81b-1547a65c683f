package com.panda.course.util;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;

import com.tencent.liteav.demo.superplayer.entity.ThumbnailEntity;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FilenameFilter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

public class FileUtil {

    // 存储 Bitmap 到指定位置
    public static boolean saveBitmapToFile(Bitmap bitmap, String filePath) {

        File file = new File(filePath);
        if (!file.getParentFile().exists()) {
            // 如果文件夹不存在，则创建文件夹
            file.getParentFile().mkdirs();
        }
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);
            outputStream.flush();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    // 存储 List<Bitmap> 到指定位置
    public static boolean saveBitmapListToFile(List<Bitmap> bitmapList, String video_id, String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        for (int i = 0; i < bitmapList.size(); i++) {
            String filePath = dirPath + File.separator + video_id + "_" + i + ".png";
            if (!saveBitmapToFile(bitmapList.get(i), filePath)) {
                return false;
            }
        }
        return true;
    }

    // 获取按顺序存放的 Bitmap
    public static Bitmap getBitmapFromFile(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            return null;
        }
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inPreferredConfig = Bitmap.Config.RGB_565;
        return BitmapFactory.decodeFile(filePath, options);
    }


    public static List<Bitmap> getSortedBitmapListFromFolder(String dirPath) {
        List<Bitmap> bitmapList = new ArrayList<>();
        File dir = new File(dirPath);
        if (dir.listFiles() != null) {
            File[] files = dir.listFiles();
            for (File file : files) {
                if (file.getName().endsWith(".jpg") || file.getName().endsWith(".png")) {
                    Bitmap bitmap = BitmapFactory.decodeFile(file.getAbsolutePath());
                    if (bitmap != null) {
                        bitmapList.add(bitmap);
                    }
                }
            }
            if (bitmapList.size() > 0) {
                Collections.sort(bitmapList, new Comparator<Bitmap>() {
                    @Override
                    public int compare(Bitmap b1, Bitmap b2) {
                        String name1 = files[bitmapList.indexOf(b1)].getName();
                        String name2 = files[bitmapList.indexOf(b2)].getName();
                        return name1.compareToIgnoreCase(name2);
                    }
                });
            }
            if (bitmapList.size() > 0) {
                for (Bitmap bitmap : bitmapList) {
                    String name = files[bitmapList.indexOf(bitmap)].getName();
                    Log.e("雪碧图", "BitmapName" + name);
                }
            }
        }
        return bitmapList;
    }

    //参数一、文件的byte流
    //参数二、文件要保存的路径
    //参数三、文件保存的名字
    public static void saveFile(Bitmap bitmap, String filePath, String fileName) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;

        File file = null;
        try {

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, baos);
            byte[] bfile = baos.toByteArray();

            //通过创建对应路径的下是否有相应的文件夹。
            File dir = new File(filePath);
            if (!dir.exists()) {// 判断文件目录是否存在
                //如果文件存在则删除已存在的文件夹。
                dir.mkdirs();
            }

            //如果文件存在则删除文件
            file = new File(filePath, fileName);
            if (file.exists()) {
                file.delete();
            }
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            //把需要保存的文件保存到SD卡中
            bos.write(bfile);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }


    /**
     * 直接拿到转换完成的数据
     */
    public static List<ThumbnailEntity> getResultThumbnails(File folder) {
        List<ThumbnailEntity> thumbnailEntities = new ArrayList<>();
        HashMap<String, Bitmap> bitmaps = getBitmapFiles(folder);
        for (HashMap.Entry<String, Bitmap> entry : bitmaps.entrySet()) {
            thumbnailEntities.add(new ThumbnailEntity(entry.getValue(), Integer.parseInt(entry.getKey())));
        }
        return thumbnailEntities;
    }


    /**
     * 获取指定目录下的图片全部文件
     *
     * @param folder
     * @return
     */
    public static HashMap<String, Bitmap> getBitmapFiles(File folder) {
        HashMap<String, Bitmap> bitmapList = new HashMap<>();

        if (folder == null || folder.listFiles() == null) {
            return bitmapList;
        }
        File[] files = folder.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                // 如果是目录，则递归遍历
                bitmapList.putAll(getBitmapFiles(file));
            } else {
                // 如果是文件，则判断是否是Bitmap文件
                String fileName = file.getName();
                if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") || fileName.endsWith(".png")) {
                    Bitmap bitmap = BitmapFactory.decodeFile(file.getPath());
                    if (bitmap != null) {
                        bitmapList.put(fileName.substring(0, fileName.indexOf(".")), bitmap);
                    }
                }
            }
        }
        return bitmapList;
    }

}
