package com.panda.course.ui.adapter

import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.CourseListEntity
import com.panda.course.ext.dp2px
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.RoundCornerImageView
import com.panda.course.widget.focus.MyFocusHighlightHelper


/**
 * DiskCacheStrategy.NONE：表示不缓存任何内容。
 * DiskCacheStrategy.SOURCE：表示只缓存原始图片。
 * DiskCacheStrategy.RESULT：表示只缓存转换过后的图片（默认选项）。
 * DiskCacheStrategy.ALL ：表示既缓存原始图片，也缓存转换过后的图片。
 */
class MyCourseHorizontalAdapter : BaseQuickAdapter<CourseListEntity, BaseViewHolder>(R.layout.item_horizontal_course) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var mOnViewFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null

    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(MyFocusHighlightHelper.ZOOM_FACTOR_XXSMALL, false)
        }

        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()

    }

    fun setOnItemFocus(onItemFocus: OnViewFocus?) {
        this.mOnViewFocus = onItemFocus
    }

    override fun convert(holder: BaseViewHolder, item: CourseListEntity) {

        holder.setText(R.id.tv_item_title, "${item.title_main}")

        val imageViewISNew = holder.getView<ImageView>(R.id.iv_item_is_new)
        imageViewISNew.visibility = if ("1" == item.is_new) View.VISIBLE else View.GONE

        val imageView = holder.getView<ImageView>(R.id.item_iv_course_cover)

        Glide.with(context).asBitmap().load(item.card_cover_img).diskCacheStrategy(DiskCacheStrategy.RESOURCE) //磁盘缓存模式
            .placeholder(R.drawable.icon_placeholder)
            .apply(
                RequestOptions().transform(
                    RoundedCornersTransform(context, 20f, rightTop = true, rightBottom = false, leftTop = true, leftBottom = false)
                )
            ).into(imageView)


        val ivPlay = holder.getView<ImageView>(R.id.iv_item_play)
        val cardView = holder.getView<CardView>(R.id.card_view)


        cardView.onFocusChangeListener = View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->
            mBrowseItemFocusHighlight?.onItemFocused(cardView, hasFocus)
            mOnViewFocus?.onChangeFocus(hasFocus, getItemPosition(item), cardView)
            ivPlay.visibility = if (hasFocus) View.VISIBLE else View.GONE

            cardView.cardElevation = if (hasFocus) dp2px(20f).toFloat() else dp2px(0f).toFloat()
            cardView.setCardBackgroundColor(
                if (hasFocus) ContextCompat.getColor(context, R.color.black) else ContextCompat.getColor(context, R.color.transparent)
            )
            cardView.radius = if (hasFocus) 16f else 0f


            if (hasFocus) {
                cardView.clearAnimation()
                cardView.startAnimation(scaleAnimation)
            } else {
                cardView.clearAnimation()
            }

        }

    }


}