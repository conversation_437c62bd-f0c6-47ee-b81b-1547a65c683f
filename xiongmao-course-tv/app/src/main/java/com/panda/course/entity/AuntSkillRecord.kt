package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class AuntSkillRecordEntity(
    val page: Int? = null,
    val total: Int,
    var list: List<AuntSkillRecordListEntity> = emptyList(),
) : Parcelable


@Parcelize
data class AuntSkillRecordListEntity(
    var uuid: String? = null,
    var name: String? = null,
    var store_name: String? = null,
    var number: String? = null,
    var service_personnel_name: String? = null,
    var skill_post_name: String? = null,
    var score: String? = null,
    var label: String? = null,
    var service_personnel_avatar: String? = null,
    var update_time: String? = null,
    var h5_url: String? = null,
) : Parcelable