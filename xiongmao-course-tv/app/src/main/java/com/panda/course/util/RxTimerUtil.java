package com.panda.course.util;

import androidx.annotation.NonNull;

import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Function;

public class RxTimerUtil {


    /**
     * 每隔milliseconds毫秒后执行next操作
     *
     * @param milliseconds
     */
    public static Observable interval(long milliseconds) {
        return Observable.interval(milliseconds, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * milliseconds毫秒后执行next操作
     *
     * @param milliseconds
     */
    public static Observable timerAction(long milliseconds) {
        return Observable.timer(milliseconds, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread());
    }

    public static Observable<Integer> timer(int time) {
        if (time < 0) time = 0;
        final int countTime = time;
        return Observable.interval(0, 1000, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .map(new Function<Long, Integer>() {
                    @Override
                    public Integer apply(Long increaseTime) throws Exception {
                        return countTime - increaseTime.intValue();
                    }
                })
                .take(countTime + 1);
    }


    /**
     * 根据倍速 来计算 秒数
     *
     * @param time
     * @param speed
     * @return
     */
    public static Observable<Integer> timer(int time, float speed) {
        if (time < 0) time = 0;
        final int countTime = time;
        final float interval = 1000 / speed;
        return Observable.interval(0, (long) interval, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .map(new Function<Long, Integer>() {
                    @Override
                    public Integer apply(Long increaseTime) throws Exception {
                        return countTime - (int) (increaseTime * speed);
                    }
                })
                .take((int) Math.ceil(countTime / speed) + 1);
    }

    /**
     * 执行次数1次
     *
     * @param timeUnit
     * @return
     */
    public static Observable<Integer> timer(long milliseconds, TimeUnit timeUnit) {
        final int countTime = 0;
        return Observable.interval(milliseconds, timeUnit)
                .observeOn(AndroidSchedulers.mainThread())
                .map(new Function<Long, Integer>() {
                    @Override
                    public Integer apply(Long increaseTime) throws Exception {
                        return countTime - increaseTime.intValue();
                    }
                })
                .take(countTime + 1);
    }

    /**
     * 执行次数1次
     *
     * @return
     */
    public static Observable<Integer> timer(int time, long milliseconds) {
        if (time < 0) time = 0;
        final int countTime = time;
        return Observable.interval(milliseconds, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .map(new Function<Long, Integer>() {
                    @Override
                    public Integer apply(Long increaseTime) throws Exception {
                        return countTime - increaseTime.intValue();
                    }
                })
                .take(countTime + 1);
    }

}