package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class UpdateInfo(
    var id: String? = null,
    var title: String? = null,
    var desc: String? = null,
    var version: String? = null,
    var url: String? = null,
    var upgrade_demand: String? = null,
    var upgrade_user: String? = null,
    var create_time: String? = null,
) : Parcelable