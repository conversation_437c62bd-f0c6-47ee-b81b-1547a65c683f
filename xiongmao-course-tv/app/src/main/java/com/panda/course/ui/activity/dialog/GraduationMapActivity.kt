package com.panda.course.ui.activity.dialog

import android.graphics.Rect
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.panda.course.R
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityGraduationMapBinding
import com.panda.course.entity.DateSection
import com.panda.course.entity.GraduateListEntity
import com.panda.course.entity.GraduateListImgEntity
import com.panda.course.ext.appUMEvent
import com.panda.course.ext.toStartActivity
import com.panda.course.ext.toast
import com.panda.course.ui.adapter.GraduationMapNewAdapter
import com.panda.course.ui.viewmodel.CourseViewModel


class GraduationMapActivity : BaseDbActivity<CourseViewModel, ActivityGraduationMapBinding>() {

    //    private val mAdapter = GraduationMapAdapter()
    private val mAdapter = GraduationMapNewAdapter(R.layout.item_graduation_map, R.layout.item_graduation_title, null)
    private var listEntity = GraduateListEntity()
    private var mCourseName = ""
    private var course_code = ""
    private var focusType = 0

    /**
     * 默认分类
     */
    private val focus_header = 1

    /**
     * 分类课程
     */
    private val focus_course = 2


    private var currentFocusType: Int = focus_header


    override fun initView(savedInstanceState: Bundle?) {
        intent.extras?.apply {
            mCourseName = getString("mCourseName").toString()
            course_code = getString("course_code").toString()
        }
        mDataBind.recycler.layoutManager = GridLayoutManager(this, 4)
        mDataBind.recycler.adapter = mAdapter
        mAdapter.setEmptyView(R.layout.layout_empty)


//        mDataBind.recyclerStudent.adapter = mAdapterStudent
//        mDataBind.recyclerStudent.layoutManager = GridLayoutManager(this, 2)
//        mAdapterStudent.setEmptyView(R.layout.layout_empty)

        mViewModel.getGraduationList(type = "1", course_code = course_code) //获取接口
    }

    override fun onRequestSuccess() {
        mViewModel.mGraduationList.observe(this, androidx.lifecycle.Observer {
//            mAdapter.setList(it.list)
            listEntity = it
            mViewModel.getGraduationList2(type = "2", course_code = course_code) //获取接口
        })
        mViewModel.mGraduationList2.observe(this, androidx.lifecycle.Observer {
//            mAdapter.setList(it.list)
            val mList = ArrayList<DateSection>()
            mList.add(DateSection(true, getString(R.string.graduate_store_title)))
            for (item in listEntity.list.indices) {
                mList.add(DateSection(false, listEntity.list[item]))
            }
            mList.add(DateSection(true, getString(R.string.graduate_studen_title)))
            for (item in it.list.indices) {
                it.list[item].click_type = "1"//区分是学员还是门店
                mList.add(DateSection(false, it.list[item]))
            }
            mAdapter.setList(mList)
        })
    }

    override fun initObserver() {
        mAdapter.setOnItemFocus { hasFocus, position, _ ->
            if (hasFocus) {
                currentFocusType = focus_course
            }
        }

    }

    override fun onBindViewClick() {
        mAdapter.setOnItemClickListener { adapter, view, position ->
            if (focusType == 0) {
                appUMEvent(UMConstant.GRADUATION_STORE)
            } else {
                appUMEvent(UMConstant.GRADUATION_STUDENT)
            }
//            "${(mAdapter.data[position].`object` as GraduateListImgEntity).click_type}".toast()
//            position.toast()
            val bundle = Bundle()
            bundle.putString("jump_type", "map")
            bundle.putString("mCourseName", "" + mCourseName)
            bundle.putInt("focusType", focusType)
            if ("0" == (mAdapter.data[position].`object` as GraduateListImgEntity).click_type) {
                bundle.putInt("position", position)
                bundle.putParcelable("data", mViewModel.mGraduationList.value)
            } else {
                if (mViewModel.mGraduationList.value != null) {//如果是学员的状态 就单独处理，因为数据做倆分层
                    bundle.putInt("position", position - (mViewModel.mGraduationList.value!!.list.size + 1))
                    bundle.putParcelable("data", mViewModel.mGraduationList2.value)
                }

            }
            toStartActivity(GraduateDialogActivity::class.java, bundle)
        }

    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (mAdapter.data.size <= 0) {
                    return true
                }
            }

        }
        return super.onKeyDown(keyCode, event)
    }


    fun createGridItemDecoration(spanCount: Int, spacing: Int): ItemDecoration {
        return object : ItemDecoration() {
            override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
                val position = parent.getChildAdapterPosition(view)
                val column = position % spanCount
                outRect.left = column * spacing / spanCount
                outRect.right = spacing - (column + 1) * spacing / spanCount
                if (position >= spanCount) {
                    outRect.top = spacing
                }
            }
        }
    }

}