package com.panda.course.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.panda.course.R;
import com.panda.course.entity.GraduateListImgEntity;
import com.panda.course.ext.AppExtKt;
import com.panda.course.util.GlideUtil;

import java.util.ArrayList;
import java.util.List;

public class ViewPager2Adapter extends RecyclerView.Adapter<ViewPager2Adapter.ViewPagerViewHolder> {

    // 图片数组
    private final List<GraduateListImgEntity> img = new ArrayList<>();

    private String mCourse = "";

    // 在适配器的构造函数中添加图片数组
    public ViewPager2Adapter() {
    }

    public void setList(List<GraduateListImgEntity> strings, String string) {
        img.addAll(strings);
        mCourse = string;
        notifyDataSetChanged();
    }

    public int getSize() {
        return img.size();
    }

    // onCreatedViewHolder()方法作用是绑定item视图
    @NonNull
    @Override
    public ViewPagerViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewPagerViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_pager, parent, false));
    }


    // 该方法的作用是将数据跟视图（ViewHolder）绑定
    @Override
    public void onBindViewHolder(@NonNull ViewPagerViewHolder holder, int position) {
        GlideUtil.loadPic(holder.mIv.getContext(), img.get(position).getImg_url(), holder.mIv);
        holder.mName.setText(AppExtKt.getEventViewModel().getAppUserInfo().getValue().getStore_name());
        holder.mContent.setText("的同学们在《" + mCourse + "》课程中，学习努力\n成绩优异，顺利毕业。");
    }


    // 返回Item的数量，即可以ViewPager可以滑动的页数
    @Override
    public int getItemCount() {
        return img.size();
    }


    // 创建ViewHolder内部类，用来存储并绑定实例化的Item对象
    class ViewPagerViewHolder extends RecyclerView.ViewHolder {

        ImageView mIv;
        TextView mContent;
        TextView mName;

        public ViewPagerViewHolder(@NonNull View itemView) {
            super(itemView);
            mIv = itemView.findViewById(R.id.iv_item_viewpager);
            mName = itemView.findViewById(R.id.tv_store_name);
            mContent = itemView.findViewById(R.id.tv_store_content);
        }
    }
}
