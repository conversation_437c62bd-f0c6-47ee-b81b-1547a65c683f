package com.panda.course.entity;

/**
 * 用来记录控制台的View 点击渠道，方便处理View
 */
public class ControllerViewState {
    public final static int VIEW_CONTROLLER_COMMON = 0; //常用功能
    public final static int VIEW_CONTROLLER_COURSE = 1; // 课程
    public final static int VIEW_CONTROLLER_DEFINITION = 2;//清晰度
    public final static int VIEW_CONTROLLER_SPEED = 3;//速度
    public final static int VIEW_CONTROLLER_KONWLEDGE = 4;//知识点
    public final static int VIEW_CONTROLLER_SUBTITLE = 5;//字幕

    private int channelState = VIEW_CONTROLLER_COMMON;
    private boolean clickState = false;

    public ControllerViewState(int channelState, boolean clickState) {
        this.channelState = channelState;
        this.clickState = clickState;
    }

    public int getChannelState() {
        return channelState;
    }

    public void setChannelState(int channelState) {
        this.channelState = channelState;
    }

    public boolean isClickState() {
        return clickState;
    }

    public void setClickState(boolean clickState) {
        this.clickState = clickState;
    }
}
