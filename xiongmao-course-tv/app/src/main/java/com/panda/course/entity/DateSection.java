package com.panda.course.entity;

import com.chad.library.adapter.base.entity.JSectionEntity;

public class DateSection extends JSectionEntity {

    private boolean isHeader;
    private Object object;

    public DateSection(boolean isHeader, Object object) {
        this.isHeader = isHeader;
        this.object = object;
    }

    public Object getObject() {
        return object;
    }

    @Override
    public boolean isHeader() {
        return isHeader;
    }


}
