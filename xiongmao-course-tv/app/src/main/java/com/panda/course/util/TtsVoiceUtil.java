package com.panda.course.util;

import android.util.Log;

import com.panda.course.config.App;
import com.panda.course.config.base.KtxKt;
import com.panda.course.ext.LogExtKt;
import com.panda.course.network.interception.logging.util.LogUtils;
import com.tencent.qcloudtts.LongTextTTS.LongTextTtsController;
import com.tencent.qcloudtts.VoiceLanguage;
import com.tencent.qcloudtts.VoiceSpeed;
import com.tencent.qcloudtts.VoiceType;
import com.tencent.qcloudtts.callback.QCloudPlayerCallback;
import com.tencent.qcloudtts.callback.TtsExceptionHandler;
import com.tencent.qcloudtts.exception.TtsException;
import com.tencent.qcloudtts.exception.TtsNotInitializedException;


public class TtsVoiceUtil {

    private String TAG = "TtsVoiceUtil";

    //类初始化时，不初始化这个对象(延时加载，真正用的时候再创建)
    private static TtsVoiceUtil instance;

    //参数：普通音色，云小曼，语速0.6，音量10
    private int tts_speed = VoiceSpeed.VOICE_SPEED_NORMAL.getNum();
    //    private int tts_voice = VoiceType.VOICE_TYPE_Zhi_Mei.getNum();
    private int tts_voice = 101003;
    private int tts_language = VoiceLanguage.VOICE_LANGUAGE_CHINESE.getNum();
    private final LongTextTtsController mTtsController;

    //更多音色id可查看官网文档https://cloud.tencent.com/document/product/1073/37995
//    public enum VoiceType {
//        VOICE_TYPE_Zhi_Yu(1001, "智瑜"),
//        VOICE_TYPE_Zhi_Xia(1000, "智侠"),
//        VOICE_TYPE_Zhi_Ling(1002, "智聆"),
//        VOICE_TYPE_Zhi_Mei(1003, "智美"),
//        VOICE_TYPE_We_Jack(1050, "WeJack，英文男声"),
//        VOICE_TYPE_We_Rose(1051, "WeRose，英文女声"),
//        VOICE_TYPE_Xiao_Yao( 10510000,"智逍遥，阅读男声"),
//    }
//
//    public enum VoiceSpeed {
//        VOICE_SPEED_VERY_SLOW(-2, 0.6f, "0.6倍"),
//        VOICE_SPEED_SLOWDOWN(-1, 0.8f, "0.8倍"),
//        VOICE_SPEED_NORMAL(0, 1.0f, "正常语速(默认)"),
//        VOICE_SPEED_ACCELERATE(1, 1.2f, "1.2倍"),
//        VOICE_SPEED_VERY_FAST(2, 1.5f, "1.5倍"),
//    }
//
//    public enum VoiceLanguage {
//        VOICE_LANGUAGE_CHINESE(1, "中文（默认）"),
//        VOICE_LANGUAGE_ENGLISH(2, "英文"),
//    }


    //构造器私有化
    private TtsVoiceUtil() {
        mTtsController = new LongTextTtsController();
        /**直接鉴权**/
        mTtsController.init(KtxKt.getAppContext(),
                1300467987L,                           //腾讯云 appId
                "AKIDlOM9l9OUP6Zu646JersjQvGQQGyTP7A1",   //腾讯云 secretId
                "mKhrqaAXjhS0KSKwLWUEl7F7k9LIzSil"        //腾讯云 secretKey
        );
        //设置语速
        mTtsController.setVoiceSpeed(tts_speed);

        //设置音色
        mTtsController.setVoiceType(tts_voice);

        //设置音量
        mTtsController.setVoiceVolume(10);

        //设置语言
        mTtsController.setVoiceLanguage(tts_language);

        //设置ProjectId
        mTtsController.setProjectId(0);
    }

    //方法同步，调用效率低
    public static synchronized TtsVoiceUtil getInstance() {
        if (instance == null) {
            instance = new TtsVoiceUtil();
        }
        return instance;
    }

    //接收接口异常
    private final TtsExceptionHandler mTtsExceptionHandler = new TtsExceptionHandler() {
        @Override
        public void onRequestException(TtsException e) {
            Log.e(TAG, "tts onRequestException");
            LogExtKt.logE("-------tts onRequestException-----" + e.getErrMsg(), TAG);
        }
    };

    public void startPlay(String voice) {
        try {
            mTtsController.startTts(voice, mTtsExceptionHandler, new QCloudPlayerCallback() {
                //播放开始
                @Override
                public void onTTSPlayStart() {
                    Log.d("tts", "onPlayStart");
                }

                //音频缓冲中
                @Override
                public void onTTSPlayWait() {
                    Log.d("tts", "onPlayWait");
                }

                //缓冲完成，继续播放
                @Override
                public void onTTSPlayResume() {
                    Log.d("tts", "onPlayResume");
                }

                //连续播放下一句
                @Override
                public void onTTSPlayNext() {
                    Log.d("tts", "onPlayNext");
                }

                //播放中止
                @Override
                public void onTTSPlayStop() {
                    Log.d("tts", "onPlayStop");
                }

                //播放结束
                @Override
                public void onTTSPlayEnd() {
                    Log.d("tts", "onPlayEnd");
                }

                @Override
                public void onTTSPlayProgress(String s, int i) {
                    Log.d("tts", "onTTSPlayProgress" + s + "===i===" + i);
                }

                @Override
                public void onTTSPlayAudioCachePath(String s) {
                    Log.d("tts", "onTTSPlayAudioCachePath" + s);
                }
            });
        } catch (TtsNotInitializedException e) {
            e.printStackTrace();
            LogExtKt.logE("-------TtsNotInitializedException-----" + e.getErrMsg(), TAG);
        }
    }

    public void onPause() {
        if (mTtsController != null)
            mTtsController.pause();
    }

    public void onStop() {
        if (mTtsController != null)
            mTtsController.stop();
    }

    public void onResume() {
        if (mTtsController != null)
            mTtsController.resume();
    }
}
