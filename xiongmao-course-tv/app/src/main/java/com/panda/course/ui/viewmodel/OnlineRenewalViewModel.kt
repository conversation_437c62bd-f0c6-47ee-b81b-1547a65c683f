package com.panda.course.ui.viewmodel

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseViewModel
import com.panda.course.entity.*
import com.panda.course.ext.*
import com.panda.course.network.LoadingType
import com.panda.course.network.NetUrl
import com.panda.course.util.MMKVHelper
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class OnlineRenewalViewModel : BaseViewModel() {


    // 获取在线续费
    var onlineRenewalEntity = MutableLiveData<OnlineRenewalEntity>()


    //二维码
    var qrCodeEntity = MutableLiveData<QRCodeEntity>()

    /**
     * 获取在线续费的列表
     */
    fun getOnlineRenewal() {
        val mDeviceID =
            if (!TextUtils.isEmpty(MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID))) {
                MMKVHelper.decodeString(ConstantMMVK.DEVICE_ID).toString()
            } else {
                eventViewModel.appUserInfo.value?.device_id.toString()
            }
        rxHttpRequest {
            onRequest = {
                onlineRenewalEntity.value = RxHttp.get(NetUrl.ONLINE_RENEWAL)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("tv_device_id", mDeviceID)
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<OnlineRenewalEntity>()
                    .await()
            }
//            loadingType = LoadingType.LOADING_XML
            requestUrl = NetUrl.ONLINE_RENEWAL
        }
    }


    /**
     * 获取在线续费的二维码
     */
    fun getOnlineRenewalQrCode(tv_device_id: String, year: String) {
        rxHttpRequest {
            onRequest = {
                qrCodeEntity.value = RxHttp.get(NetUrl.ONLINE_RENEWAL_QR_CODE)
                    .add("device_mac", getWireMac())
                    .add("device_id", getAndroidId())
                    .add("tv_device_id", tv_device_id)
                    .add("year", year)
                    .add("device_unique_code", md5Digest(getAndroidId() + getWireMac()))
                    .toResponse<QRCodeEntity>()
                    .await()
            }
            loadingType = LoadingType.LOADING_NULL
            requestUrl = NetUrl.ONLINE_RENEWAL_QR_CODE
        }
    }

}