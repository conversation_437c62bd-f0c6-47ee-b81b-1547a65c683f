package com.panda.course.ui.activity;

import static com.panda.course.ext.CommExtKt.toStartActivity;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.panda.course.R;
import com.panda.course.callback.OnViewFocus;
import com.panda.course.config.base.BaseDbActivity;
import com.panda.course.config.base.BaseViewModel;
import com.panda.course.databinding.ActivityPdfChooseBinding;
import com.panda.course.entity.ClassHourListEntity;
import com.panda.course.entity.ClassPdfListEntity;
import com.panda.course.entity.CourseDetail;
import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.LogExtKt;
import com.panda.course.ui.adapter.ClassHourAdapter;
import com.panda.course.ui.adapter.FeedBackQuestionAdapter;
import com.panda.course.ui.adapter.RowClassCourseAdapter;
import com.panda.course.ui.adapter.RowSkillAdapter;
import com.panda.course.ui.viewmodel.CourseViewModel;
import com.panda.course.util.ToastUtil;

import java.util.ArrayList;
import java.util.List;

public class PdfChooseActivity extends BaseDbActivity<CourseViewModel, ActivityPdfChooseBinding> {

    private RowClassCourseAdapter mRowAdapter = new RowClassCourseAdapter();
    private View mRowView = null;

    private ClassHourAdapter mAdapter = new ClassHourAdapter();

    /**
     * 默认分类
     */
    private int focus_row = 1;

    /**
     * 在线反馈
     */
    private int focus_pdf = 2;

    private int currentFocusType = focus_row;

    private int mRowPosition = 0;

    private int page = 1;

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        mDataBind.recyclerRow.setAdapter(mRowAdapter);
        mDataBind.recycler.setAdapter(mAdapter);
        mAdapter.setEmptyView(R.layout.layout_empty);
        if (getIntent().getExtras() != null) {

            mViewModel.getClassPdfRows(getIntent().getExtras().getString("course_code"));
//            mViewModel.getCourseDetail(true, getIntent().getExtras().getString("course_code"),
//                    AppExtKt.getEventViewModel().getAppUserInfo().getValue().getUuid(), true);
        }
    }

    @Override
    public void initObserver() {
        mRowAdapter.setOnViewFocus(new OnViewFocus() {
            @Override
            public void onChangeFocus(boolean hasFocus, int position, View view) {
                if (hasFocus) {
                    mRowView = view;
                    mRowPosition = position;
                    mViewModel.getCourseHourDetails(true, mRowAdapter.getData().get(position).getCode());
                } else {
                    mRowView.setBackground(ContextCompat.getDrawable(PdfChooseActivity.this, R.drawable.base_border_unselecter));
                }
                currentFocusType = focus_row;
            }
        });

        mAdapter.setOnViewFocus(new OnViewFocus() {
            @Override
            public void onChangeFocus(boolean hasFocus, int position, View view) {
                if (hasFocus) {
                    if (mRowView != null) {
                        mRowView.setBackground(ContextCompat.getDrawable(PdfChooseActivity.this, R.drawable.base_border_selecter));
                    }
                    currentFocusType = focus_pdf;
                }
            }
        });


        mAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                if (!TextUtils.isEmpty(mAdapter.getData().get(position).getCourseware_url())) {
                    Bundle bundle = new Bundle();
                    bundle.putString("pdf_url", mAdapter.getData().get(position).getCourseware_url());
                    toStartActivity(PdfViewActivity.class, bundle);
                } else {
                    ToastUtil.show("无效的地址，请联系客成人员反馈");
                }
            }
        });
    }


    @Override
    public void onRequestSuccess() {

        mViewModel.getClassPdfRows().observe(this, new Observer<ClassPdfListEntity>() {
            @Override
            public void onChanged(ClassPdfListEntity classPdfListEntity) {
                if (classPdfListEntity != null && classPdfListEntity.getList() != null) {
                    mRowAdapter.setList(classPdfListEntity.getList());
                }
            }
        });
        mViewModel.getMHourEntity().observe(this, new Observer<ClassHourListEntity>() {
            @Override
            public void onChanged(ClassHourListEntity classHourListEntity) {
                mAdapter.setList(classHourListEntity.getList());
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        goPropagandaVideo(); // 宣传片

        if (onMyKeyDown(keyCode, event)) { // 加一层判断，实现android 9 以及其他的情况
            return true;
        }

        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_LEFT:
                if (currentFocusType == focus_row) {
                    if (mRowView != null) {
                        mRowView.setBackground(ContextCompat.getDrawable(this, R.drawable.base_border));
                    }
                }
                break;
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                if (mRowPosition != 0 && mAdapter.getData().size() <= 0) {
                    return true;
                }
                break;

        }
        return super.onKeyDown(keyCode, event);
    }
}