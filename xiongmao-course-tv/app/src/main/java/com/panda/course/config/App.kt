package com.panda.course.config

import android.app.Application
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner
import com.effective.android.anchors.AnchorsManager
import com.effective.android.anchors.task.project.Project
import com.mallotec.reb.localeplugin.LocaleConstant
import com.mallotec.reb.localeplugin.LocalePlugin
import com.mallotec.reb.localeplugin.utils.ActivityHelper
import com.mallotec.reb.localeplugin.utils.LocaleHelper
import com.panda.course.BuildConfig
import com.panda.course.callback.EventViewModel
import com.panda.course.config.base.Ktx
import com.panda.course.config.base.appContext
import com.panda.course.dao.DataDao
import com.panda.course.ext.getScreenHeight
import com.panda.course.ext.getScreenWidth
import com.panda.course.util.mvvmHelperLog
import com.tencent.mmkv.MMKV
import me.jessyan.autosize.AutoSizeConfig
import me.jessyan.autosize.unit.Subunits
import java.util.Locale


class App : Application(), ViewModelStoreOwner {

    private var mFactory: ViewModelProvider.Factory? = null
    private lateinit var mAppViewModelStore: ViewModelStore


    companion object {
        lateinit var eventViewModelInstance: EventViewModel
    }

    override fun onCreate() {
        super.onCreate()
        Ktx.app = this

        LocalePlugin.init(this, LocaleConstant.RECREATE_CURRENT_ACTIVITY)

        AutoSizeConfig.getInstance().unitsManager.setSupportDP(true).setSupportSP(true)
            .setDesignSize(getScreenWidth().toFloat(), getScreenHeight().toFloat()).supportSubunits = Subunits.NONE

        DataDao.initGreenDao(this)

        mAppViewModelStore = ViewModelStore()

        eventViewModelInstance = getAppViewModelProvider().get(EventViewModel::class.java)

        //框架全局打印日志开关
        mvvmHelperLog = BuildConfig.DEBUG

        MMKV.initialize(appContext)

        TXCSDKService.init(applicationContext)

        onMainProcessInit()

    }


    /**
     * @description  代码的初始化请不要放在onCreate直接操作，按照下面新建异步方法
     */
    private fun onMainProcessInit() {
        AnchorsManager.getInstance().debuggable(BuildConfig.DEBUG) //设置锚点
            .addAnchor(
                InitDefault.TASK_ID,
                InitNetWork.TASK_ID,
                InitUtils.TASK_ID,
                InitComm.TASK_ID,
                InitIM.TASK_ID,
                InitBugly.TASK_ID,
                InitUM.TASK_ID,
                InitServer.TASK_ID,
            ).start(
                Project.Builder("panda_app", AppTaskFactory()).add(InitDefault.TASK_ID).add(InitNetWork.TASK_ID)
                    .add(InitComm.TASK_ID).add(InitUtils.TASK_ID).add(InitIM.TASK_ID).add(InitBugly.TASK_ID)
                    .add(InitUM.TASK_ID).add(InitServer.TASK_ID).build()
            )
    }

    /**
     * 获取一个全局的ViewModel
     */
    private fun getAppViewModelProvider(): ViewModelProvider {
        return ViewModelProvider(this, this.getAppFactory())
    }

    private fun getAppFactory(): ViewModelProvider.Factory {
        if (mFactory == null) {
            mFactory = ViewModelProvider.AndroidViewModelFactory.getInstance(this)
        }
        return mFactory as ViewModelProvider.Factory
    }

    override fun getViewModelStore(): ViewModelStore {
        return mAppViewModelStore
    }


}