package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
data class SurpriseEntity(
    var pub: Surprise? = null,
    var surprise: Surprise? = null,
) : Parcelable


@Parcelize
data class Surprise(
    var title: String? = null,
    var finished_title: String? = null,
    var img_url: String? = null,
    var icon_img_url: String? = null,
    var jump_url: String? = null,
    var status: String? = null,
    var jump_type: String? = null,
    var course_code: String? = null,
    var activity_id: String? = null,
) : Parcelable