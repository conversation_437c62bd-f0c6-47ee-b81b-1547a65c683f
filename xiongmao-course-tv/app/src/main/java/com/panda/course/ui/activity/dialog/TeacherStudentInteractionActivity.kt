package com.panda.course.ui.activity.dialog

import android.os.Bundle
import android.view.KeyEvent
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.panda.course.R
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.data.CommonDataUtil
import com.panda.course.databinding.ActivityTeacherStudentInteractionBinding
import com.panda.course.entity.QRCodeEntity
import com.panda.course.ext.*
import com.panda.course.ui.adapter.InteractiveAdapter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.ui.viewmodel.InteractionViewModel
import com.panda.course.util.GlideUtil
import com.panda.course.util.QRCodeUtil
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.V7LinearLayoutManager

/**
 * 师生互动
 */
class TeacherStudentInteractionActivity : BaseDbActivity<InteractionViewModel, ActivityTeacherStudentInteractionBinding>() {

    private var mCourseCode = ""

    private var page = 1

    private var mAdapter = InteractiveAdapter()

    override fun initView(savedInstanceState: Bundle?) {
        setDialogWindowParams(window, 1.0f, 1.0f)

        intent.extras?.apply {
            mCourseCode = getString("course_detail_code").toString()
            getString("qr_code_url").notTextNull {
                mDataBind.ivPopupQrTeacherCode.setImageBitmap(QRCodeUtil.getInstance().createQRCode(it))
//                Glide.with(this@TeacherStudentInteractionActivity).asBitmap().load(it).apply(
//                    RequestOptions().transform(
//                        CenterCrop(),
//                        RoundedCornersTransform(this@TeacherStudentInteractionActivity, 6f, rightBottom = false, leftBottom = false)
//                    )
//                ).into(mDataBind.ivPopupQrTeacherCode)
            }

            val qrCodeEntity = getSerializable("qr_code_info") as QRCodeEntity

            mDataBind.tvTeacherName.text = "讲师：" + qrCodeEntity.teacher_name

            GlideUtil.loadPicRound(this@TeacherStudentInteractionActivity, qrCodeEntity.teacher_pic, mDataBind.ivTeacher)

            mViewModel.getInteractiveList(page, mCourseCode)
        }

        mDataBind.recyclerTask.adapter = mAdapter
    }

    override fun initObserver() {
        mAdapter.setOnItemFocus { has, position, _ ->
            if (has && position == mAdapter.data.size - 1) {
                page++
                mViewModel.getInteractiveList(page, mCourseCode)
            }
        }
        mAdapter.setOnItemChildClickListener { _, view, position ->
            if (view.id == R.id.iv_item_look_interactive_content) {
                val bundle = Bundle()
                bundle.putParcelable("teacher_data", mAdapter.data[position])
                toStartActivity(TeacherReplyActivity::class.java, bundle)
            }
        }
    }

    override fun onRequestSuccess() {
        mViewModel.interactiveListEntity.observe(this, Observer {
            if (it.list.isNullOrEmpty()) {
                return@Observer
            }
            mDataBind.tvEmpty.gone()
            if (it.page != null) {
                page = it.page!!
            }
            if (1 == page) {
                mAdapter.setList(it.list)
            } else {
                mAdapter.addData(it.list)
            }
        })
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        if (keyCode == KeyEvent.KEYCODE_ENTER || keyCode == KeyEvent.KEYCODE_DPAD_CENTER || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE
            || keyCode == KeyEvent.KEYCODE_HOME || keyCode == KeyEvent.KEYCODE_PROFILE_SWITCH
        ) {
            return true
        }

        return super.onKeyDown(keyCode, event)
    }

}