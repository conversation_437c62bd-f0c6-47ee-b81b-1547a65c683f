package com.panda.course.config

import rxhttp.wrapper.annotation.DefaultDomain
import rxhttp.wrapper.annotation.Domain
import com.panda.course.BuildConfig


object ConstantMMVK {

    const val MY_VIDEO_TIPS = "还没上传过视频，打开熊猫APP，使用扫一扫，上传您的视频吧\n" +
            "请确保熊猫系统App版本号是6.5.3以上\n" +
            "(或者：打开熊猫系统App，首页点击“我的视频”上传)\n"

    const val USER_INFO = "USER_INFO_NEW"

    const val VIDEO_CACHE_DOT = "VIDEO_CACHE_DOT"
    const val VIDEO_CACHE_DOT_RESOURCE = "VIDEO_CACHE_DOT_RESOURCE"

    const val TOKEN = "TOKEN"

    const val DEVICE_ID = "DEVICE_ID"

    const val INSIDE_MAC = "INSIDE_MAC"

    const val INSIDE_NUMBER = "INSIDE_NUMBER"

    const val COURSE_CLASS = "COURSE_CLASS"

    const val COURSE_VIDEO_HISTORY = "COURSE_VIDEO_HISTORY"

    const val RED_PAPER_CONFIG = "RED_PAPER_CONFIG"

    const val SURPRISE_SHOW_COUNT = "SURPRISE_SHOW_COUNT"

    const val SURPRISE_SHOW_TOMORROW = "SURPRISE_SHOW_TOMORROW"

    // 公司宣传片
    const val COMPANY_PROPAGANDA = "COMPANY_PROPAGANDA"

    //惊喜活动
    const val SURPRISE_ENTITY = "SURPRISE_ENTITY"

    //存储分辨率
    const val HDMI_SCREEN = "HDMI_SCREEN"


    // 分页最大的数量
    const val PAGE_SIZE = 100

    const val PAGE_SIZE_18 = 18

    const val BASE_URL = "BASE_URL"

    const val IM_TEST_KEY = 1400482273
    const val IM_KEY = 1400320823

    // 腾讯云的appid
    const val DEFAULT_APPID = 1300467987


    const val OTA_ZIP_PATH = "/storage/emulated/0/Android/data/com.panda.course/cache/"

    const val POPUP_TEACHER = 1

    const val POPUP_EXAMINATION = 2

    const val POPUP_GRADUATION = 4

    const val AD_VIDEO = 3

    const val AI_LIVE = 5

    const val AI_LIVE_DEF = 6


    // 给内部打包 改成true
    var INSIDE_RELEASE = false


}
