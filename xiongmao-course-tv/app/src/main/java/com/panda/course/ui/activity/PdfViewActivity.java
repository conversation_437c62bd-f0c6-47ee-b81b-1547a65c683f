package com.panda.course.ui.activity;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import android.net.Uri;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.animation.Animation;

import com.github.barteksc.pdfviewer.listener.OnErrorListener;
import com.github.barteksc.pdfviewer.listener.OnLoadCompleteListener;
import com.github.barteksc.pdfviewer.listener.OnPageChangeListener;
import com.github.barteksc.pdfviewer.listener.OnPageErrorListener;
import com.panda.course.R;
import com.panda.course.config.base.BaseDbActivity;
import com.panda.course.data.CommonViewUtil;
import com.panda.course.databinding.ActivityPdfViewBinding;
import com.panda.course.entity.ControllerViewState;
import com.panda.course.ext.LogExtKt;
import com.panda.course.ui.viewmodel.LiveModel;
import com.panda.course.util.AnimationUtils;
import com.panda.course.util.ToastUtil;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

public class PdfViewActivity extends BaseDbActivity<LiveModel, ActivityPdfViewBinding> implements OnPageErrorListener,
        OnLoadCompleteListener, OnPageChangeListener {

    private String TAG = "PdfViewActivity";
    private String pdfUrl = null;

    private Disposable disposable;

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        pdfUrl = getIntent().getExtras().getString("pdf_url");
        loadPdf();
    }


    private Runnable mHideButtonRunnable = new Runnable() {
        @Override
        public void run() {
            mDataBind.butBack.clearAnimation();
            mDataBind.butBack.startAnimation(AnimationUtils.getHiddenAlphaAnimation(1000, new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    mDataBind.butBack.setVisibility(View.GONE);
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            }));

        }
    };


    /**
     * 3秒后隐藏这个控制器
     */
    private void taskButton() {
        mDataBind.butBack.setVisibility(View.VISIBLE);
        mDataBind.butBack.removeCallbacks(mHideButtonRunnable);
        mDataBind.butBack.postDelayed(mHideButtonRunnable, 3000);
    }

    private void loadPdf() {
        mDataBind.flProgress.setVisibility(View.VISIBLE);
        Observable<InputStream> observable = Observable.create(new ObservableOnSubscribe<InputStream>() {
            @Override
            public void subscribe(ObservableEmitter<InputStream> emitter) throws Exception {
                try {
                    URL url = new URL(pdfUrl);
                    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                    connection.connect();

                    if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                        emitter.onNext(new BufferedInputStream(connection.getInputStream()));
                        emitter.onComplete();
                    } else {
                        emitter.onError(new IOException("Failed to load PDF"));
                    }
                } catch (Exception e) {
                    emitter.onError(e);
                }
            }
        });

        disposable = observable
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(inputStream -> {
                            mDataBind.pdfView.fromStream(inputStream)
                                    .onPageChange(PdfViewActivity.this)
                                    .onLoad(PdfViewActivity.this)
                                    .onPageError(PdfViewActivity.this)
                                    .load();
                        },
                        error -> {
                            mDataBind.loadingTips.setText("课件加载失败，请退出重试～");
                            LogExtKt.logE("Error loading PDF: " + error.getMessage(), TAG);
                        }
                );
    }


    @Override
    public void loadComplete(int nbPages) {
        LogExtKt.logE("loadComplete --> " + nbPages, TAG);
        mDataBind.flProgress.setVisibility(View.GONE);
        taskButton();
        mDataBind.tvPageCount.setText("1/" + nbPages);
    }

    @Override
    public void onPageError(int page, Throwable t) {
        LogExtKt.logE("onPageError --> " + t.getMessage(), TAG);
        mDataBind.loadingTips.setText("课件加载失败，请退出重试～");
    }

    @Override
    public void onPageChanged(int page, int pageCount) {
        mDataBind.tvPageCount.setText((page + 1) + "/" + pageCount);

        LogExtKt.logE("onPageChanged --> page " + page + " ; pageCount " + pageCount, TAG);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (onMyKeyDown(event.getKeyCode(), event)) { // 加一层判断，实现android 9 以及其他的情况
            return true;
        }
        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_DOWN:
                int currentPage = mDataBind.pdfView.getCurrentPage();
                int totalPages = mDataBind.pdfView.getPageCount();
                if (currentPage < totalPages - 1) {
                    mDataBind.pdfView.jumpTo(currentPage + 1, true);
                } else {
                    ToastUtil.show("已经到达最后一页");
                }
                break;
            case KeyEvent.KEYCODE_DPAD_UP:
                if (mDataBind.pdfView.getCurrentPage() > 0) {
                    mDataBind.pdfView.jumpTo(mDataBind.pdfView.getCurrentPage() - 1, true);
                } else {
                    ToastUtil.show("已经到达第一页");
                }
                break;
        }
        return super.onKeyDown(keyCode, event);
    }

}