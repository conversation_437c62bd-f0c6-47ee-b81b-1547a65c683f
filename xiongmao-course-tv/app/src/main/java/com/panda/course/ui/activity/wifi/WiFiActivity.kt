package com.panda.course.ui.activity.wifi

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.hacknife.wifimanager.*
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.databinding.ActivityWifiBinding
import com.panda.course.ext.*
import com.panda.course.ui.adapter.WIFIListAdapter

class WiFiActivity : BaseDbActivity<BaseViewModel, ActivityWifiBinding>(), OnWifiChangeListener, OnWifiConnectListener,
    OnWifiStateChangeListener {

    private val TAG = "WiFiActivity"

    private var mPosition = 0

    //两个危险权限需要动态申请
    private val NEEDED_PERMISSIONS = arrayOf(
        Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION
    )

    private var mWifiManager: IWifiManager? = null

    private var mAdapter = WIFIListAdapter()

    private var mCurrentWifi: IWifi? = null

    override fun initView(savedInstanceState: Bundle?) {
        if (!checkPermission()) {
            mDataBind.tvTitle.text = "无权限操作"
            return
        }

        mDataBind.recyclerView.layoutManager = LinearLayoutManager(this)
        mDataBind.recyclerView.adapter = mAdapter

        mWifiManager = WifiManager.create(this)

        if (mWifiManager?.isOpened == false) {
            mWifiManager?.openWifi()
            mDataBind.tvWifiState.text = "关"
        } else {
            mDataBind.tvWifiState.text = "开"
        }
        mWifiManager?.setOnWifiChangeListener(this)
        mWifiManager?.setOnWifiConnectListener(this)
        mWifiManager?.setOnWifiStateChangeListener(this)
        mWifiManager?.scanWifi()

    }

    override fun onWifiChanged(wifis: MutableList<IWifi>) {
        "onWifiChanged = ${wifis.size}".logE()
        if (wifis.size <= 0) {
            return
        }
        mAdapter.setList(wifis)
        if (wifis.size > 0) {
            mDataBind.llProgress.gone()
            mDataBind.llWifiStateLayout.isFocusable = false
            mDataBind.llWifiStateLayout.isFocusableInTouchMode = false
            mDataBind.llWifiStateLayout.clearFocus()
            mDataBind.llWifiStateLayout.postDelayed({
                mDataBind.recyclerView.scrollToPosition(0)
                mDataBind.llWifiStateLayout.isFocusable = true
                mDataBind.llWifiStateLayout.isFocusableInTouchMode = true
            }, 1000)
        }
    }

    override fun onConnectChanged(status: Boolean) {
        "onConnectChanged = $status".logE()
        if (status) { //连接成功了
            finishActivitys(WiFiConnectActivity::class.java)
        }
    }

    override fun onStateChanged(state: State?) {

    }

    override fun onBindViewClick() {
        super.onBindViewClick()
        mAdapter.setOnItemClickListener { _, _, position ->
            mCurrentWifi = mAdapter.data[position]
            if (mAdapter.data[position].isConnected) { // 如果是已经连接的那么就断开，手动断开
                val bundle = Bundle()
                bundle.putString("wifi_name", "${mAdapter.data[position].name()}")
                toStartActivity(NetDetailsActivity::class.java, bundle)
            } else if (mAdapter.data[position].isSaved) {//如果是已经保存过的直接连接
                mWifiManager?.connectSavedWifi(mAdapter.data[position])
            } else if (!mAdapter.data[position].isEncrypt) {//如果是没有密码锁的也直接连接
                mWifiManager?.connectOpenWifi(mAdapter.data[position])
            } else { //有密码的进行连接  弹窗
                val bundle = Bundle()
                bundle.putString("wifi_name", "${mAdapter.data[position].name()}")
                toStartActivity(WiFiConnectActivity::class.java, bundle)
            }
        }
    }


    override fun initObserver() {
        super.initObserver()
        mAdapter.setOnViewFocus { hasFocus, position, _ ->
            if (hasFocus) {
                mPosition = position
            }
        }
        mDataBind.llWifiStateLayout.setOnFocusChangeListener { v, hasFocus ->
            mDataBind.llWifiStateLayout.setBackgroundResource(if (hasFocus) R.color.green else R.color.blue1)
        }

        //监听状态
        eventViewModel.wifiState.observe(this, androidx.lifecycle.Observer{
            if (it) {
                mWifiManager?.disConnectWifi() //断开后重新进行扫描，查看列表中是否有已经保存过的wifi 并去连接
                mCurrentWifi?.let { wifi ->
                    mWifiManager?.removeWifi(wifi)
                }
                reScanSaveWifi()
            }
        })

        eventViewModel.wifiConnectState.observe(this, androidx.lifecycle.Observer{
            mCurrentWifi?.let { wifi ->
                mWifiManager?.connectEncryptWifi(wifi, it)
                mAdapter.data.clear()
            }
        })
    }

    /**
     * 监测当前的wifi列表
     */
    private fun reScanSaveWifi() {
        if (mAdapter.data.size > 0) {
            for (i in mAdapter.data.indices) {
                if (mAdapter.data[i].isSaved) {
                    mWifiManager?.connectSavedWifi(mAdapter.data[i])
                    break
                }
            }
            flushAdapter()
        }
    }

    /**
     * 用来刷新adapter
     */
    private fun flushAdapter() {
        mAdapter.notifyDataSetChanged()
    }

    /**
     * 检查是否已经授予权限
     *
     * @return
     */
    private fun checkPermission(): Boolean {
        for (permission in NEEDED_PERMISSIONS) {
            if (ActivityCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }
        return true
    }

    override fun onDestroy() {
        super.onDestroy()
        mWifiManager?.destroy()
    }

    /**
     * 改变wifi的开关
     */
    private fun changeWifiStatus() {
        if (mDataBind.tvWifiState.text == "开") {
            mDataBind.tvWifiState.text = "关"
            mWifiManager?.closeWifi()
        } else {
            mDataBind.tvWifiState.text = "开"
            mWifiManager?.scanWifi()
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                changeWifiStatus()
            }
            KeyEvent.KEYCODE_DPAD_LEFT -> {
                changeWifiStatus()
            }
            KeyEvent.KEYCODE_DPAD_UP -> {
                return if (mPosition == 0) {
                    mDataBind.llWifiStateLayout.requestFocus()
                    true
                } else {
                    false
                }
            }
        }
        return super.onKeyDown(keyCode, event)
    }
}