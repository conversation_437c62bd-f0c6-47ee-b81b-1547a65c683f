package com.panda.course.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ExcellentRankEntity(
    var current_month_total: Int = 0,
    var last_month_total: Int = 0,
    var list: List<ExcellentRankListEntity> = emptyList(),
) : Parcelable


@Parcelize
data class ExcellentRankListEntity(
    var service_personnel_name: String? = null,
    var avatar: String? = null,
    var avg_score: String? = null,
    var right_percent: String? = null,
    var exercise_times: String? = null,
    var seize_seat: Boolean = false,
) : Parcelable