package com.panda.course.ui.activity

import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import com.panda.course.R
import com.panda.course.config.ConstantMMVK
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.data.CommonAnimationUtil
import com.panda.course.data.CommonDataUtil
import com.panda.course.data.CommonViewUtil
import com.panda.course.databinding.ActivityOtherPlayBinding
import com.panda.course.entity.ControllerViewState
import com.panda.course.ext.*
import com.panda.course.ui.adapter.CommonSpecialFunctionsAdapter
import com.tencent.liteav.demo.superplayer.SuperPlayerDef
import com.tencent.liteav.demo.superplayer.SuperPlayerModel
import com.tencent.liteav.demo.superplayer.SuperPlayerView
import com.tencent.liteav.demo.superplayer.model.entity.VideoQuality
import java.util.ArrayList

/**
 * 公共播放视频的地方
 */
class OtherPlayActivity : BaseDbActivity<BaseViewModel, ActivityOtherPlayBinding>() {

    private var mCurrentVideoProgress: Long = -1 //记录当前播放的点
    private var mVideoWatchUploadCount: Int = 0

    private var mCommonPosition = 0;
    private var mDefinitionPosition = 0;
    private var mSpeedPosition = 0;

    //记录清晰度、倍速
    private var currentSpeedPosition = 0;
    private var currentDefinitionPosition = 0

    /**
     * 常用功能
     */
    private val focus_common = 1

    /**
     * 清晰度
     */
    private val focus_definition = 3

    /**
     * 倍速
     */
    private val focus_speed = 4

    /**
     * 记录当前的焦点在哪里
     */
    private var currentFocusType: Int = focus_common

    // 控制器的adpater
    val commonFunctionsAdapter = CommonSpecialFunctionsAdapter()
    val commonDefinitionAdapter = CommonSpecialFunctionsAdapter()
    val commonSpeedAdapter = CommonSpecialFunctionsAdapter()

    //用来处理快速点击
    var mLongClickTime: Long = 0
    private var isFastClick = false

    override fun initView(savedInstanceState: Bundle?) {
        setDialogWindowParams(window, 1f, 1f)
        initRecyclerView()
        intent.extras?.apply {
            if ("interactive" == getString("jump_type")) {//是从师生互动过来的，从intent获取数据模型
                val urls = getStringArrayList("data_urls")
                if (!urls.isNullOrEmpty()) {
                    "拿到的url = ${urls[0]}".logE()
                    playVideo(urls[0], true)
                }
            }
        }
    }

    private fun initRecyclerView() {

        // 设置控制器的recycler
        mDataBind.gridCommon.initPlayRecyclerCommon(
            this, commonFunctionsAdapter, CommonDataUtil().propagandaCommonFunctions
        )

        mDataBind.gridDefinition.initPlayRecyclerCommon(
            this, commonDefinitionAdapter, CommonDataUtil().videoDefinitions
        )

        mDataBind.gridSpeed.initPlayRecyclerCommon(this, commonSpeedAdapter, CommonDataUtil().videoSpeed)

        mDataBind.superplayer.windowPlayer.vodNextView.gone()
    }

    /**
     * 播放视频
     */
    private fun playVideo(url: String, loop: Boolean) {

        val videoQualityList = ArrayList<VideoQuality>()
        val multiURLs = ArrayList<SuperPlayerModel.SuperPlayerURL>()

        val superPlayerModelV3 = SuperPlayerModel()
        superPlayerModelV3.title = ""
        superPlayerModelV3.url = url
        superPlayerModelV3.appId = ConstantMMVK.DEFAULT_APPID


        videoQualityList.add(VideoQuality(1, "高清", url))
        multiURLs.add(SuperPlayerModel.SuperPlayerURL(url, "高清"))

        multiURLs.add(SuperPlayerModel.SuperPlayerURL(url, "标清"))
        videoQualityList.add(VideoQuality(0, "标清", url))

        superPlayerModelV3.multiURLs = multiURLs
        superPlayerModelV3.videoQualityList = videoQualityList
        mDataBind.superplayer.setLoop(loop)
        mDataBind.superplayer.playWithModel(superPlayerModelV3)
    }


    override fun initObserver() {
        mDataBind.superplayer.setPlayerViewCallback(object : SuperPlayerView.OnSuperPlayerViewCallback {

            /**
             * 开始全屏播放
             */
            override fun onStartFullScreenPlay() {
            }

            /**
             * 结束全屏播放
             */
            override fun onStopFullScreenPlay() {
            }

            /**
             * 点击悬浮窗模式下的x按钮
             */
            override fun onClickFloatCloseBtn() {
            }

            /**
             * 点击小播放模式的返回按钮
             */
            override fun onClickSmallReturnBtn() {
            }

            /**
             * 开始悬浮窗播放
             */
            override fun onStartFloatWindowPlay() {
            }

            /**
             * 开始播放回调
             */
            override fun onPlaying() {
            }

            /**
             * 播放暂停
             */
            override fun onPause() {

            }

            /**
             * 播放结束
             */
            override fun onPlayEnd() {
            }

            /**
             * 播放下一节
             */
            override fun playNext() {

            }

            /**
             * 当播放失败的时候回调
             *
             * @param code
             */
            override fun onError(code: Int) {
            }

            /**
             * 更新进度回调
             *
             * @param current
             * @param duration
             */
            override fun updateVideoProgress(current: Long, duration: Long) {
                if (current == duration) { //当前的时间点与总时间点一致，说明播放完成了，没必须继续了
                    return
                }
                if (mCurrentVideoProgress == current) {
                    return
                } else {
                    mCurrentVideoProgress = current
                }
                if (current > 0 && mDataBind.superplayer.playerState == SuperPlayerDef.PlayerState.PAUSE) { //暂停的时候，不去触发打点信息
                    mVideoWatchUploadCount = 0
                    return
                }
                mVideoWatchUploadCount += 1
                if (mVideoWatchUploadCount >= 5) {
                    mVideoWatchUploadCount = 0 // 归零
                }
            }

            /**
             * 下载页面，点击了缓存列表按钮
             */
            override fun onShowCacheListClick() {
            }

            /**
             * 切换清晰度
             */
            override fun onChangeVideoQuality() {
            }
        })
        commonFunctionsAdapter.setOnItemClickListener { _, _, position ->
            when (position) {
                0 -> {
                    controllerScrollViewDown()
                    CommonViewUtil().requestFocusDelay(commonDefinitionAdapter, currentDefinitionPosition)
                }
                1 -> {
                    controllerScrollViewDown()
                    CommonViewUtil().requestFocusDelay(commonSpeedAdapter, currentSpeedPosition)
                }
            }
        }

        // 常用功能 - 倍速
        commonSpeedAdapter.setOnItemClickListener { _, _, position -> // 倍速
            currentSpeedPosition = position
            mDataBind.superplayer.setVideoRete(position) //调整倍速
            CommonViewUtil().changeAdapterSelected(
                commonFunctionsAdapter,
                mDataBind.flController,
                commonSpeedAdapter,
                ControllerViewState.VIEW_CONTROLLER_SPEED,
                position
            )
            onResume()
        }

        // 常用功能 - 清晰度
        commonDefinitionAdapter.setOnItemClickListener { _, _, position -> // 清晰度
            currentDefinitionPosition = position

            if (mDataBind.superplayer.currentSuperPlayerModel.videoQualityList.size > 0) {
                if (!TextUtils.isEmpty(mDataBind.superplayer.currentSuperPlayerModel.videoQualityList[position].url)) {
                    mDataBind.superplayer.changeVideoQuality(mDataBind.superplayer.currentSuperPlayerModel.videoQualityList[position])
                }
            }

            CommonViewUtil().changeAdapterSelected(
                commonFunctionsAdapter,
                mDataBind.flController,
                commonDefinitionAdapter,
                ControllerViewState.VIEW_CONTROLLER_DEFINITION,
                position
            )
            onResume()
        }

        commonFunctionsAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mCommonPosition = position
                currentFocusType = focus_common
                mDataBind.scrollViewController.scrollTo(0, 0)
            }
        }

        commonDefinitionAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mDefinitionPosition = position
                currentFocusType = focus_definition
                controllerScrollViewDown()
            }
        }
        commonSpeedAdapter.setOnViewFocus { hasFocus, position, view ->
            if (hasFocus) {
                mSpeedPosition = position
                currentFocusType = focus_speed
                controllerScrollViewDown()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mDataBind.superplayer.onResume()
    }


    override fun onStop() {
        super.onStop()
        mDataBind.superplayer.onPause()
    }

    override fun onPause() {
        super.onPause()
        mDataBind.superplayer.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        mDataBind.superplayer.release()
        mDataBind.superplayer.resetPlayer()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {

            KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                return true
            }

            KeyEvent.KEYCODE_BACK -> {
                if (mDataBind.flController.visibility == View.VISIBLE) {
                    hideControllerView()
                    return true
                }
                finish()
            }
            KeyEvent.KEYCODE_DPAD_UP -> {
                if (mDataBind.flController.visibility == View.VISIBLE && currentFocusType == currentFocusType) {
                    hideControllerView()
                }
            }
            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (mDataBind.flController.visibility == View.GONE && mDataBind.superplayer.visibility == View.VISIBLE) {
                    mDataBind.flController.visible()
                    mDataBind.flController.post {
                        CommonViewUtil().controllerViewAction(
                            true,
                            mDataBind.flController,
                            commonFunctionsAdapter.getViewByPosition(0, R.id.rl_common_item_view),
                            ControllerViewState.VIEW_CONTROLLER_COMMON
                        )
                    }
                }
            }
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER -> {
                if (mDataBind.superplayer.visibility == View.VISIBLE) {
                    if (mDataBind.superplayer.playerState == SuperPlayerDef.PlayerState.PLAYING) {
                        mDataBind.superplayer.onPause()
                    } else if (mDataBind.superplayer.playerState == SuperPlayerDef.PlayerState.PAUSE) {
                        mDataBind.superplayer.onResume()
                    }
                }
            }

            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (mDataBind.flController.visibility == View.VISIBLE) {
                    return selectCommonView(false)
                }
                if (mDataBind.superplayer.visibility == View.GONE) {
                    return true
                }
                if (event.repeatCount > 1) {
                    isFastClick = true
                    mDataBind.superplayer.seekFastTo(true, event.repeatCount)
                } else {
                    isFastClick = false
                    mDataBind.superplayer.seekToOne(
                        true, (mDataBind.superplayer.vodProgress - 10).toInt()
                    )
                }
                return true
            }
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (mDataBind.flController.visibility == View.VISIBLE) {
                    return selectCommonView(true)
                }
                if (mDataBind.superplayer.visibility == View.GONE) {
                    return true
                }
                if (event.repeatCount > 1) {
                    isFastClick = true
                    mDataBind.superplayer.seekFastTo(false, event.repeatCount)
                } else {
                    isFastClick = false
                    mDataBind.superplayer.seekToOne(
                        false, (mDataBind.superplayer.vodProgress + 10).toInt()
                    )
                }
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }


    override fun onKeyUp(keyCode: Int, event: KeyEvent): Boolean {
        if (mDataBind.superplayer.visibility == View.GONE) {
            return true
        }
        if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
            val diff = System.currentTimeMillis() - mLongClickTime
            if (diff > 2000 && isFastClick) { // 长按
                isFastClick = false
                mDataBind.superplayer.seekToFast(mDataBind.superplayer.fastFinalProgress)
                mDataBind.superplayer.fastCurrentProgress = 0
                mDataBind.superplayer.fastFinalProgress = 0
                onResume()
                return true
            }
        }
        return super.onKeyUp(keyCode, event)
    }

    private fun hideControllerView() {
        mDataBind.flController.gone()
    }

    /**
     * 控制器滑动到最底部
     */
    private fun controllerScrollViewDown() {
        mDataBind.scrollViewController.post {
            mDataBind.scrollViewController.smoothScrollBy(0, mDataBind.scrollViewController.measuredHeight)
        }
    }

    /**
     * 多功能界面下区分 左右点击
     */
    private fun selectCommonView(action: Boolean): Boolean {
        when (currentFocusType) {
            focus_common -> { //判断是否到了边界
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonFunctionsAdapter, mCommonPosition, action
                )
            }
            focus_definition -> {
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonDefinitionAdapter, mDefinitionPosition, action
                )
            }
            focus_speed -> {
                return CommonAnimationUtil().startPlayerViewAnimationShake(
                    mShakeAnimation, commonSpeedAdapter, mSpeedPosition, action
                )
            }
        }
        return false
    }
}