package com.panda.course.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

public class TVBoxRecyclerView extends RecyclerView {

    public TVBoxRecyclerView(Context context) {
        super(context);
    }

    public TVBoxRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TVBoxRecyclerView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent e) {
        return false;
    }

    @Override
    public void requestChildFocus(View child, View focused) {
        // 当子项获得焦点时，将其居中显示
        if (focused != null) {
            centerChildView(child, focused);
        }
        super.requestChildFocus(child, focused);
    }

    private void centerChildView(View child, View focused) {
        int parentWidth = getWidth();
        int parentHeight = getHeight();
        int childLeft = child.getLeft();
        int childTop = child.getTop();
        int childWidth = child.getWidth();
        int childHeight = child.getHeight();
        int focusedLeft = focused.getLeft();
        int focusedTop = focused.getTop();
        int focusedWidth = focused.getWidth();
        int focusedHeight = focused.getHeight();

        int dx = childLeft + (childWidth / 2) - (parentWidth / 2);
        int dy = childTop + (childHeight / 2) - (parentHeight / 2);
        smoothScrollBy(dx, dy);
    }
}