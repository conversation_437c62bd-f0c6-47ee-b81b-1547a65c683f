package com.panda.course.entity;


public class CourseListEntity {
    private String code;
    private String title_main;
    private String title_sub;
    private String course_cover_image;
    private String course_introduction;
    private String teacher_name;
    private String status;
    private String card_cover_img;
    private String videos_count;
    private String final_exam_pic;
    private String is_new;
    private String create_time;

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setIs_new(String is_new) {
        this.is_new = is_new;
    }

    public String getIs_new() {
        return is_new;
    }

    public void setFinal_exam_pic(String final_exam_pic) {
        this.final_exam_pic = final_exam_pic;
    }

    public String getFinal_exam_pic() {
        return final_exam_pic;
    }

    public void setVideos_count(String videos_count) {
        this.videos_count = videos_count;
    }

    public String getVideos_count() {
        return videos_count;
    }

    public void setCard_cover_img(String card_cover_img) {
        this.card_cover_img = card_cover_img;
    }

    public String getCard_cover_img() {
        return card_cover_img;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle_main() {
        return title_main;
    }

    public void setTitle_main(String title_main) {
        this.title_main = title_main;
    }

    public String getTitle_sub() {
        return title_sub;
    }

    public void setTitle_sub(String title_sub) {
        this.title_sub = title_sub;
    }

    public String getCourse_cover_image() {
        return course_cover_image;
    }

    public void setCourse_cover_image(String course_cover_image) {
        this.course_cover_image = course_cover_image;
    }

    public String getCourse_introduction() {
        return course_introduction;
    }

    public void setCourse_introduction(String course_introduction) {
        this.course_introduction = course_introduction;
    }

    public String getTeacher_name() {
        return teacher_name;
    }

    public void setTeacher_name(String teacher_name) {
        this.teacher_name = teacher_name;
    }
}
