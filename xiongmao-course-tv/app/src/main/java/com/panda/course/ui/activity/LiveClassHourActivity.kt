package com.panda.course.ui.activity

import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.KeyEvent
import android.view.View
import androidx.lifecycle.Observer
import com.baijiayun.livecore.LiveSDK
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.UMConstant
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.data.CommonAnimationUtil
import com.panda.course.databinding.ActivityLiveClassHourBinding
import com.panda.course.ext.appUMEvent
import com.panda.course.ext.eventViewModel
import com.panda.course.ext.getEmptyView
import com.panda.course.ext.goSystemTvBoxSetting
import com.panda.course.ext.gone
import com.panda.course.ext.logE
import com.panda.course.ext.px2dp
import com.panda.course.ext.toStartActivity
import com.panda.course.ext.toast
import com.panda.course.ext.visible
import com.panda.course.ui.activity.dialog.UserOutDialogActivity
import com.panda.course.ui.adapter.LiveListAdapter
import com.panda.course.ui.adapter.LiveListClassHourAdapter
import com.panda.course.ui.presenter.CoursePresenter
import com.panda.course.ui.viewmodel.CourseViewModel
import com.panda.course.ui.viewmodel.LiveModel
import com.panda.course.util.GlideUtil
import com.panda.course.util.NetworkUtil
import com.panda.course.util.SpaceItemDecoration

class LiveClassHourActivity : BaseDbActivity<LiveModel, ActivityLiveClassHourBinding>() {

    private var mAdapter = LiveListClassHourAdapter()

    /**
     * 默认分类
     */
    private val focus_header = 1

    /**
     * 分类课程
     */
    private val focus_course = 2


    private var currentFocusType: Int = focus_header


    private var mPosition = 0


    override fun initView(savedInstanceState: Bundle?) {

        mAdapter.setEmptyView(getEmptyView(tips = "暂无直播", R.color.white))

        mDataBind.recyclerView.adapter = mAdapter

        mDataBind.recyclerView.setColumnNumbers(2)

        mDataBind.recyclerView.addItemDecoration(SpaceItemDecoration(2, px2dp(60f), true))

        onLoadRetry()

        locationData()


    }

    private fun locationData() {
        eventViewModel.appUserInfo.value?.let {
            mDataBind.baseHead.llUserLayout.visibility = View.VISIBLE
            mDataBind.baseHead.tvHeadStoreName.text = it.store_name
            GlideUtil.loadPicRound(this, it.store_log, mDataBind.baseHead.ivHeadPic)
        }

        eventViewModel.surpriseToggle.value.let {
            if (it != null) {
                GlideUtil.loadGif(this, it.icon_img_url, mDataBind.baseHead.ivSurprise)
                mDataBind.baseHead.tvSurprise.text = it.title
                mDataBind.baseHead.llSurpriseLayout.visible()
            } else {
                mDataBind.baseHead.llSurpriseLayout.gone()
            }
        }
    }


    fun viewsFocus(vararg views: View?) {
        views.forEach {
            it?.setOnFocusChangeListener { v, hasFocus ->
                if (hasFocus) {
                    currentFocusType = focus_header
                }
            }
        }
    }

    override fun initObserver() {

        mDataBind.baseHead.llNoticeLayout.setOnClickListener {
            toStartActivity(NoticeActivity::class.java)
        }
        mDataBind.baseHead.llSurpriseLayout.setOnClickListener {
            CoursePresenter.toSurpriseDetails(eventViewModel.surpriseToggle.value)
        }
        mDataBind.baseHead.llUpdateLayout.setOnClickListener {
            appUMEvent(UMConstant.HOME_HELP_FEEDBACK)
            toStartActivity(FeedbackActivity::class.java)
        }
        mDataBind.baseHead.llUserLayout.setOnClickListener {
            toStartActivity(UserOutDialogActivity::class.java)
        }
        mDataBind.baseHead.llSettingLayout.setOnClickListener {
            appUMEvent(UMConstant.HOME_SETTING)
            toStartActivity(SettingsActivity::class.java)
        }


        mAdapter.setOnItemClickListener { adapter, view, position ->
            mPosition = position
            // 1进行中 2未开始 3已结束
            when (mAdapter.data[position].course_video_status) {
                "2" -> {
                    "当前直播未开始".toast()
                }

                "3" -> {
                    "直播已结束".toast()
                }

                else -> {
                    if (!NetworkUtil.isAvailable(this)) {
                        "当前无网络，请检查当前网络情况是否正常".toast()
                        return@setOnItemClickListener
                    }
                    val bundle = Bundle()
                    bundle.putString("course_code", "" + intent?.extras?.getString("code"))
                    bundle.putString("code", "" + mAdapter.data[position].code)
                    bundle.putString("title_name", "" + mAdapter.data[position].title_main)
                    toStartActivity(PlayerLiveActivity::class.java, bundle)
                }
            }
        }

        mAdapter.setOnViewFocus(object : OnViewFocus {
            override fun onChangeFocus(hasFocus: Boolean, position: Int, view: View?) {
                if (hasFocus) {
                    mAdapter.data[position].logE("1")
                    mPosition = position
                    currentFocusType = focus_course
                }
            }
        })


        viewsFocus(
            mDataBind.baseHead.llNoticeLayout, mDataBind.baseHead.llSurpriseLayout,
            mDataBind.baseHead.llUpdateLayout, mDataBind.baseHead.llUserLayout, mDataBind.baseHead.llSettingLayout
        )
    }

    override fun onLoadRetry() {
        mViewModel.getLiveHoursList(intent?.extras?.getString("code"))
    }

    override fun onRequestSuccess() {
        mViewModel.hourListEntity.observe(this, Observer {
            mAdapter.setList(it.video_list)
            requestOne()
        })
    }


    fun requestOne() {
        Handler().postDelayed({
            if (mAdapter.data.size > 0) {
                val mView = mAdapter.getViewByPosition(0, R.id.card_item_view_class_hours)
                mView?.requestFocus()
            }
        }, 100)
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        Log.e("获取界面焦点", "===当前获取焦点的View===${window.decorView.findFocus()}")
        goPropagandaVideo()
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {

            KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE -> {
                onLoadRetry()
                return true
            }

            KeyEvent.KEYCODE_MENU -> {
                goSystemTvBoxSetting(this)
                return true
            }

            KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                return true
            }

            KeyEvent.KEYCODE_DPAD_UP -> {
                if (mDataBind.baseHead.llUserLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llUserLayout)
                    return true
                }
                if (mDataBind.baseHead.llNoticeLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llNoticeLayout)
                    return true
                }
                if (mDataBind.baseHead.llSurpriseLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llSurpriseLayout)
                    return true
                }
                if (mDataBind.baseHead.llUpdateLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llUpdateLayout)
                    return true
                }

                if (mDataBind.baseHead.llSettingLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(true, mDataBind.baseHead.llSettingLayout)
                    return true
                }

                if (mPosition == 0 || mPosition == 1 ) {
                    val isAtTop = !mDataBind.recyclerView.canScrollVertically(-1)
                    if (!isAtTop) {
                        mDataBind.recyclerView.scrollToPosition(0)
                    }
                    return mDataBind.baseHead.llUserLayout.requestFocus()
                }
            }

            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (currentFocusType == focus_course && mPosition == mAdapter.data.size - 1) {
                    return true
                }
            }

            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (mPosition % 2 == 0 && currentFocusType == focus_course) {
                    return true
                }
                if (mDataBind.baseHead.llSettingLayout.isFocused) {
                    return false
                }
                if (mDataBind.baseHead.llUserLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(false, mDataBind.baseHead.llUserLayout)
                    return true
                }
            }

            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (mPosition % 2 == 1 && currentFocusType == focus_course) {
                    "点击倆左边 1  = " + mPosition + "  - focus_course = " + focus_course.logE()
                    return true
                }
                if (mPosition == mAdapter.data.size - 1 && currentFocusType == focus_course) {
                    "点击倆左边 2".logE()
                    return true
                }
                if (mDataBind.baseHead.llSettingLayout.isFocused) {
                    CommonAnimationUtil().startShakeAnimation(false, mDataBind.baseHead.llSettingLayout)
                    "点击倆左边 3".logE()
                    return true
                }

            }
        }
        return super.onKeyDown(keyCode, event)
    }

}