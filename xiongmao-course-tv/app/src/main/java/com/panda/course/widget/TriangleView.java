package com.panda.course.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;

public class TriangleView extends View {
    private Paint paint;
    private int triangleColor = Color.parseColor("#7FD56C");


    public TriangleView(Context context) {
        super(context);
        init();
    }

    public TriangleView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }


    private void init() {
        paint = new Paint();
        paint.setAntiAlias(true);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        int width = getWidth();
        int height = getHeight();

        paint.setColor(triangleColor);

        // 绘制三角形路径
        canvas.drawPath(createTrianglePath(width, height), paint);
    }

    private Path createTrianglePath(int width, int height) {
        Path path = new Path();
        path.moveTo(0, height); // 左下角点
        path.lineTo(width / 2, 0); // 顶点
        path.lineTo(width, height); // 右下角点
        path.close(); // 封闭路径

        return path;
    }

    public void setTriangleColor(int color) {
        triangleColor = color;
        invalidate(); // 重新绘制视图
    }
}