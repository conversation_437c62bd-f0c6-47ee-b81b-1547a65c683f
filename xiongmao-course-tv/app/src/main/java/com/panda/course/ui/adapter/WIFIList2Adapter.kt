package com.panda.course.ui.adapter

import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.hacknife.wifimanager.IWifi
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.entity.WifiModel
import com.panda.course.ext.getColorExt

class WIFIList2Adapter : BaseQuickAdapter<WifiModel, BaseViewHolder>(R.layout.item_wifi) {

    private var onItemFocus: OnViewFocus? = null

    fun setOnViewFocus(onItemFocus: OnViewFocus?) {
        this.onItemFocus = onItemFocus
    }

    override fun convert(holder: BaseViewHolder, item: WifiModel) {
        holder.setText(R.id.tv_item_wifi_name, item.wifiName)
        val mTextView = holder.getView<TextView>(R.id.tv_item_wifi_state)
        val mIvStates = holder.getView<ImageView>(R.id.iv_item_wifi_state)
        val mRootView = holder.getView<LinearLayout>(R.id.ll_item_wifi_layout)

        if (item.wifiTypeName.contains("WPA2-PSK") && item.wifiTypeName.contains("WPA-PSK")) {
            holder.setText(R.id.tv_item_wifi_state, "WPA/WPA2")
        } else if (item.wifiTypeName.contains("WPA-PSK")) {
            holder.setText(R.id.tv_item_wifi_state, "WPA")
        } else if (item.wifiTypeName.contains("WPA2-PSK")) {
            holder.setText(R.id.tv_item_wifi_state, "WPA2")
        }

//        //可以传递给adapter的数据都是经过处理的，已连接或者正在连接状态的wifi都是处于集合中的首位，所以可以写出如下判断
        if (item.isConnect) {
            mIvStates.setImageResource(R.drawable.icon_wifi_connect)
            mRootView.setBackgroundColor(getColorExt(R.color.green))
        } else {
            mIvStates.setImageResource(R.drawable.icon_wifi_lock)
            mRootView.setBackgroundColor(getColorExt(R.color.blue1))
        }

        if (item.isConnect) {
            mTextView.text = "已连接"
        }
        if (item.isSave && !item.isConnect) {
            mTextView.text = "已保存"
        }

        mRootView.setOnFocusChangeListener { v, hasFocus ->
            onItemFocus?.onChangeFocus(true, getItemPosition(item), v)
            mRootView.setBackgroundColor(if (hasFocus) getColorExt(R.color.green) else getColorExt(R.color.blue1))
        }
    }


}