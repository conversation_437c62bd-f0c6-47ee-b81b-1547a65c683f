package com.panda.course.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.panda.course.config.App;
import com.panda.course.ext.AppExtKt;
import com.panda.course.ext.LogExtKt;

import java.util.Calendar;

public class HomeReceiver extends BroadcastReceiver {

    public final String SYSTEM_DIALOG_REASON_KEY = "reason";
    public final String SYSTEM_DIALOG_REASON_HOME_KEY = "homekey";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (action.equals(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)) {

            String reason = intent.getStringExtra(SYSTEM_DIALOG_REASON_KEY);
            if (SYSTEM_DIALOG_REASON_HOME_KEY.equals(reason)) {
                AppExtKt.getEventViewModel().getAppHomeClick().postValue(true);
            }

        } else if (action.equals(Intent.ACTION_TIME_TICK)) { //每一分钟更新时间
            Calendar c = Calendar.getInstance();
            int minute = c.get(Calendar.MINUTE);
            if (minute == 0) {
                AppExtKt.getEventViewModel().getHourReporting().postValue(true);
            } else if (minute == 30) {
                AppExtKt.getEventViewModel().getHourReporting().postValue(true);
            }
        }
    }
}

