package com.panda.course.ui.adapter

import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.ScaleAnimation
import android.widget.*
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.FocusHighlight
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.callback.OnViewFocus
import com.panda.course.config.base.Ktx
import com.panda.course.entity.CourseDetailInfo
import com.panda.course.ext.dp2px
import com.panda.course.ext.gone
import com.panda.course.ext.px2dp
import com.panda.course.ext.visible
import com.panda.course.util.RoundedCornersTransform
import com.panda.course.widget.NineOverShootInterPolator
import com.panda.course.widget.focus.MyFocusHighlightHelper

class MyCourseContentAdapter : BaseQuickAdapter<CourseDetailInfo, BaseViewHolder>(R.layout.item_course_content) {

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var mOnViewFocus: OnViewFocus? = null

    private var scaleAnimation: ScaleAnimation? = null

    private var mTeacherName: String? = null

    private var ellipsis = 110

    fun setTeacherName(name: String?) {
        this.mTeacherName = name
    }


    init {
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false)
        }
        scaleAnimation = AnimationUtils.loadAnimation(Ktx.app, R.anim.scale_row) as ScaleAnimation
        scaleAnimation?.interpolator = NineOverShootInterPolator()
    }

    fun setOnItemFocus(onItemFocus: OnViewFocus?) {
        this.mOnViewFocus = onItemFocus
    }

    override fun convert(holder: BaseViewHolder, item: CourseDetailInfo) {
        val bigImageView = holder.getView<ImageView>(R.id.iv_item_big_cover)
        val imageView = holder.getView<ImageView>(R.id.iv_item_cover)
        val ivPlayer = holder.getView<ImageView>(R.id.iv_player)
        val flLayout = holder.getView<RelativeLayout>(R.id.fl_item_cover_layout)
        val rlLayout = holder.getView<RelativeLayout>(R.id.rl_item_text_layout)

        val imageViewISNew = holder.getView<ImageView>(R.id.iv_item_is_new)
        imageViewISNew.visibility = if ("1" == item.is_new) View.VISIBLE else View.GONE


        if (item.type != 0) {
            bigImageView.visibility = View.VISIBLE
            flLayout.visibility = View.GONE
            rlLayout.visibility = View.GONE


            Glide.with(context).load(item.video_cover_image)
                .apply(RequestOptions().transform(CenterCrop(), RoundedCornersTransform(context, 20f))).dontAnimate()
                .placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error).into(bigImageView)

        } else {
            bigImageView.visibility = View.GONE
            flLayout.visibility = View.VISIBLE
            rlLayout.visibility = View.VISIBLE
            Glide.with(context).load(item.video_cover_image).apply(
                RequestOptions().transform(
                    CenterCrop(), RoundedCornersTransform(context, 20f, rightTop = false, rightBottom = false)
                )
            ).dontAnimate().placeholder(R.drawable.icon_placeholder).error(R.drawable.icon_error).into(imageView)



            Log.i("PlayMedia", "imageView" + imageView.width + "=========" + imageView.height)
            holder.setText(R.id.iv_item_course_title, "${item.title_main}")
            holder.setText(R.id.iv_item_course_desc, "${item.video_introduction}")
            holder.setText(R.id.iv_item_course_teacher, "课程讲师：$mTeacherName")
            if (!TextUtils.isEmpty(item.training_aid_introduction)) { //有教具的话，就不显示更新时间
                holder.getView<TextView>(R.id.iv_item_course_teaching_aids).visible()
                holder.getView<TextView>(R.id.iv_item_course_update_time).gone()
                holder.setText(R.id.iv_item_course_teaching_aids, "必备教具：" + item.training_aid_introduction)
            } else {
                holder.getView<TextView>(R.id.iv_item_course_teaching_aids).gone()
                // 如果没有教具的情况下，有时间，就显示
                if (!TextUtils.isEmpty(item.update_time) && !TextUtils.isEmpty(item.video_introduction) && "1" == item.is_new) {
                    holder.getView<TextView>(R.id.iv_item_course_update_time).visible()
                    if (item.video_introduction?.length!! > ellipsis) {
                        holder.setText(R.id.iv_item_course_desc, "${item.video_introduction?.subSequence(0, ellipsis)}...")
                    } else {
                        holder.setText(R.id.iv_item_course_desc, "${item.video_introduction}")
                    }
                    holder.setText(R.id.iv_item_course_update_time, "更新时间：${item.update_time}")
                } else {
                    holder.setText(R.id.iv_item_course_desc, "${item.video_introduction}")
                    holder.setText(R.id.iv_item_course_update_time, "")
                }
            }


        }


        val cardView = holder.getView<CardView>(R.id.content_card_view)


        when {
            getItemPosition(item) % 2 == 0 -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(150f))
                layoutParams.setMargins(dp2px(20f), dp2px(20f), dp2px(20f), dp2px(0f))
                cardView.layoutParams = layoutParams
            }

            else -> {
                val layoutParams = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(150f))
                layoutParams.setMargins(dp2px(0f), dp2px(20f), dp2px(20f), dp2px(0f))
                cardView.layoutParams = layoutParams
            }
        }


        cardView.setOnFocusChangeListener { v, hasFocus ->
            mBrowseItemFocusHighlight?.onItemFocused(cardView, hasFocus)
            ivPlayer.visibility = if (hasFocus) View.VISIBLE else View.GONE

            mOnViewFocus?.onChangeFocus(hasFocus, getItemPosition(item), cardView)

            cardView.cardElevation = if (hasFocus) px2dp(10f).toFloat() else px2dp(0f).toFloat()
            cardView.setCardBackgroundColor(
                if (hasFocus) ContextCompat.getColor(
                    context, R.color.black
                ) else ContextCompat.getColor(context, R.color.transparent)
            )
            cardView.radius = if (hasFocus) 16f else 0f

            if (hasFocus) {
                cardView.startAnimation(scaleAnimation)
            } else {
                cardView.clearAnimation()
            }
        }
    }
}