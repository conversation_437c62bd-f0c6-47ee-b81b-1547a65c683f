package com.panda.course.ui.activity.dialog

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import com.panda.course.R
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.databinding.ActivityTeacherReplyBinding
import com.panda.course.entity.InteractiveListEntityEntity
import com.panda.course.ext.setDialogWindowParams
import com.panda.course.ui.viewmodel.CourseImgViewModel

class TeacherReplyActivity : BaseDbActivity<CourseImgViewModel, ActivityTeacherReplyBinding>() {


    override fun initView(savedInstanceState: Bundle?) {
        setDialogWindowParams(window, 1.0f, 1.0f)

        intent?.extras?.let {
            var entityEntity = it.getParcelable<InteractiveListEntityEntity>("teacher_data")
            if (entityEntity != null) {
                if (!entityEntity.teacher_comment.isNullOrEmpty()) {
                    val stringBuffer = StringBuffer()
                    for (i in entityEntity.teacher_comment!!.indices) {
                        stringBuffer.append("老师回复：${entityEntity.teacher_comment!![i].txt_content}")
                        if (i != entityEntity.teacher_comment!!.size - 1) {
                            stringBuffer.append("\n")
                        }
                    }
                    mDataBind.tvTeacherReply.text = stringBuffer.toString()
                } else {
                    mDataBind.tvTeacherReply.text = "老师暂无回复"
                }
            }
        }
    }
}