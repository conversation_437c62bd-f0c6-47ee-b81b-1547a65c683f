package com.panda.course.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.annotation.LayoutRes
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.panda.course.R
import com.panda.course.entity.CourseListEntity
import com.panda.course.entity.SurpriseContentListEntity
import com.panda.course.ext.gone
import com.panda.course.ext.notNull
import com.panda.course.ext.notTextNull
import com.panda.course.ext.visible
import com.panda.course.util.GlideUtil
import com.youth.banner.adapter.BannerAdapter

class SurpriseContentAdapter :
    BaseQuickAdapter<SurpriseContentListEntity, BaseViewHolder>(R.layout.item_surprise_content) {

    private var mLockBg = ""
    private var mUnLockBg = ""

    override fun convert(holder: BaseViewHolder, item: SurpriseContentListEntity) {
        holder.setText(R.id.tv_surprise_price, item.worth)
        holder.setText(R.id.tv_item_reward_tips, item.rule_msg)

        holder.setText(R.id.tv_surprise_title, item.activity_name)
        val mImageView = holder.getView<ImageView>(R.id.iv_item_surprise_red_envelopes)

        if ("1" == item.is_finished) {
            mLockBg.notTextNull({
                GlideUtil.loadPic(context, mLockBg, mImageView)
            }, {
                GlideUtil.loadPic(context, R.drawable.icon_surprise_red_envelopes, mImageView)
            })
            holder.getView<ImageView>(R.id.iv_item_surprise_red_envelopes_layer).gone()
            holder.getView<ImageView>(R.id.iv_item_surprise_lock).gone()
        } else {
            mUnLockBg.notTextNull({
                GlideUtil.loadPic(context, mUnLockBg, mImageView)
            }, {
                GlideUtil.loadPic(context, R.drawable.icon_surprise_red_envelopes, mImageView)
            })
            holder.getView<ImageView>(R.id.iv_item_surprise_red_envelopes_layer).visible()
            holder.getView<ImageView>(R.id.iv_item_surprise_lock).visible()
        }
    }


    fun setLockBg(bg: String) {
        this.mLockBg = bg
    }

    fun setUnLockBg(unBg: String) {
        this.mUnLockBg = unBg
    }
}