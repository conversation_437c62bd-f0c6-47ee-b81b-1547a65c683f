package com.panda.course.entity;

public class TestPointEntity {
    private String name;
    private String server;
    private int id;
    private String dlURL;
    private String ulURL;
    private String pingURL;
    private String getIpURL;
    private String sponsorName;
    private String sponsorURL;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getServer() {
        return server;
    }

    public void setServer(String server) {
        this.server = server;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDlURL() {
        return dlURL;
    }

    public void setDlURL(String dlURL) {
        this.dlURL = dlURL;
    }

    public String getUlURL() {
        return ulURL;
    }

    public void setUlURL(String ulURL) {
        this.ulURL = ulURL;
    }

    public String getPingURL() {
        return pingURL;
    }

    public void setPingURL(String pingURL) {
        this.pingURL = pingURL;
    }

    public String getGetIpURL() {
        return getIpURL;
    }

    public void setGetIpURL(String getIpURL) {
        this.getIpURL = getIpURL;
    }

    public String getSponsorName() {
        return sponsorName;
    }

    public void setSponsorName(String sponsorName) {
        this.sponsorName = sponsorName;
    }

    public String getSponsorURL() {
        return sponsorURL;
    }

    public void setSponsorURL(String sponsorURL) {
        this.sponsorURL = sponsorURL;
    }
}
