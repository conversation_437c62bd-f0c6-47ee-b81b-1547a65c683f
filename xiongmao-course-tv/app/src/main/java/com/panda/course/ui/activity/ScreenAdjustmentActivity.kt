package com.panda.course.ui.activity

import android.os.Bundle
import android.view.KeyEvent
import com.droidlogic.app.DisplayPositionManager
import com.droidlogic.app.OutputModeManager
import com.panda.course.config.base.BaseDbActivity
import com.panda.course.config.base.BaseViewModel
import com.panda.course.databinding.ActivityScreenAdjustmentBinding
import com.panda.course.ext.logE
import com.panda.course.ext.toStartActivity


class ScreenAdjustmentActivity : BaseDbActivity<BaseViewModel, ActivityScreenAdjustmentBinding>() {

    private val TAG = "ScreenAdjustmentActivity"
    private var dpm: DisplayPositionManager? = null
    private var defPercent = 100

    override fun initView(savedInstanceState: Bundle?) {
        dpm = DisplayPositionManager(this)
        defPercent = dpm?.currentRateValue!!
        "${dpm?.currentRateValue!!}".logE(TAG)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (onMyKeyDown(keyCode, event)) {//加一层判断，实现android 9 以及其他的情况
            return true
        }
        when (keyCode) {
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_HOME, KeyEvent.KEYCODE_PROFILE_SWITCH -> {
                onLoadRetry()
                return true
            }
            KeyEvent.KEYCODE_DPAD_UP -> {
                if (defPercent < 100) {
                    defPercent += 2
                    dpm?.zoomByPercent(defPercent)
                    flushActivity()
                }
            }
            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (defPercent > 80) {
                    defPercent -= 2
                    dpm?.zoomByPercent(defPercent)
                    flushActivity()
                }
            }
        }
        return super.onKeyDown(keyCode, event)
    }


    private fun flushActivity() {
        finish()
        toStartActivity(ScreenAdjustmentActivity::class.java)
    }


}