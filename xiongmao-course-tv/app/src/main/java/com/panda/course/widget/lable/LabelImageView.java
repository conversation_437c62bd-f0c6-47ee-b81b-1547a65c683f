package com.panda.course.widget.lable;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Path;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.panda.course.R;
import com.panda.course.ext.DensityExtKt;

public class LabelImageView extends ImageView {

    LabelViewHelper utils;

    private int cornerSize;//圆角大小
    private int leftTop;
    private int leftBottom;
    private int rightBottom;
    private int rightTop;
    private float[] cornerRadii;


    public LabelImageView(Context context) {
        this(context, null);
    }

    public LabelImageView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LabelImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        utils = new LabelViewHelper(context, attrs, defStyleAttr);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.RoundCornerImageView, defStyleAttr, 0);
        cornerSize = a.getInt(R.styleable.RoundCornerImageView_corner_size, 0);
        leftTop = a.getInt(R.styleable.RoundCornerImageView_corner_top_left_size, 0);
        leftBottom = a.getInt(R.styleable.RoundCornerImageView_corner_bottom_left_size, 0);
        rightTop = a.getInt(R.styleable.RoundCornerImageView_corner_top_right_size, 0);
        rightBottom = a.getInt(R.styleable.RoundCornerImageView_corner_bottom_right_size, 0);
        cornerRadii = new float[]{leftTop, leftTop, rightTop, rightTop, rightBottom, rightBottom, leftBottom, leftBottom};
        a.recycle();
    }

    public void setCornerRadii(int leftTopRadius, int rightTopRadius, int rightBottomRadius, int leftBottomRadius) {
        cornerRadii = new float[]{leftTopRadius, leftTopRadius, rightTopRadius, rightTopRadius, rightBottomRadius, rightBottomRadius, leftBottomRadius, leftBottomRadius};
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        Path path = new Path();
        int w = getWidth();
        int h = getHeight();
        if (cornerSize > 0) {
            //这里对path添加一个圆角区域，这里一般需要将dp转换为pixel
            path.addRoundRect(new RectF(0, 0, w, h), DensityExtKt.dp2px(cornerSize),
                    DensityExtKt.dp2px(cornerSize), Path.Direction.CW);
        } else {
            path.addRoundRect(new RectF(0, 0, w, h), cornerRadii, Path.Direction.CW);
        }
        canvas.clipPath(path);//将Canvas按照上面的圆角区域截取
        super.onDraw(canvas);
        utils.onDraw(canvas, getMeasuredWidth(), getMeasuredHeight());
    }

    public void setLabelHeight(int height) {
        utils.setLabelHeight(this, height);
    }

    public int getLabelHeight() {
        return utils.getLabelHeight();
    }

    public void setLabelDistance(int distance) {
        utils.setLabelDistance(this, distance);
    }

    public int getLabelDistance() {
        return utils.getLabelDistance();
    }

    public boolean isLabelVisual() {
        return utils.isLabelVisual();
    }

    public void setLabelVisual(boolean enable) {
        utils.setLabelVisual(this, enable);
    }

    public int getLabelOrientation() {
        return utils.getLabelOrientation();
    }

    public void setLabelOrientation(int orientation) {
        utils.setLabelOrientation(this, orientation);
    }

    public int getLabelTextColor() {
        return utils.getLabelTextColor();
    }

    public void setLabelTextColor(int textColor) {
        utils.setLabelTextColor(this, textColor);
    }

    public int getLabelBackgroundColor() {
        return utils.getLabelBackgroundColor();
    }

    public void setLabelBackgroundColor(int backgroundColor) {
        utils.setLabelBackgroundColor(this, backgroundColor);
    }

    public void setLabelBackgroundAlpha(int alpha) {
        utils.setLabelBackgroundAlpha(this, alpha);
    }

    public String getLabelText() {
        return utils.getLabelText();
    }

    public void setLabelText(String text) {
        utils.setLabelText(this, text);
    }

    public int getLabelTextSize() {
        return utils.getLabelTextSize();
    }

    public void setLabelTextSize(int textSize) {
        utils.setLabelTextSize(this, textSize);
    }

    public int getLabelTextStyle() {
        return utils.getLabelTextStyle();
    }

    public void setLabelTextStyle(int textStyle) {
        utils.setLabelTextStyle(this, textStyle);
    }
}


