<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/xiongmao-course-tv.iml" filepath="$PROJECT_DIR$/.idea/modules/xiongmao-course-tv.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/Muti-Barrage/xiongmao-course-tv.Muti-Barrage.iml" filepath="$PROJECT_DIR$/.idea/modules/Muti-Barrage/xiongmao-course-tv.Muti-Barrage.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/xiongmao-course-tv.app.iml" filepath="$PROJECT_DIR$/.idea/modules/app/xiongmao-course-tv.app.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/ntplibrary/xiongmao-course-tv.ntplibrary.iml" filepath="$PROJECT_DIR$/.idea/modules/ntplibrary/xiongmao-course-tv.ntplibrary.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/subtitles/xiongmao-course-tv.subtitles.iml" filepath="$PROJECT_DIR$/.idea/modules/subtitles/xiongmao-course-tv.subtitles.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/superplayerkit/xiongmao-course-tv.superplayerkit.iml" filepath="$PROJECT_DIR$/.idea/modules/superplayerkit/xiongmao-course-tv.superplayerkit.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/tuicore/xiongmao-course-tv.tuicore.iml" filepath="$PROJECT_DIR$/.idea/modules/tuicore/xiongmao-course-tv.tuicore.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/wifimanager/xiongmao-course-tv.wifimanager.iml" filepath="$PROJECT_DIR$/.idea/modules/wifimanager/xiongmao-course-tv.wifimanager.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/Muti-Barrage/xiongmao-tv.xiongmao-course-tv.Muti-Barrage.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/Muti-Barrage/xiongmao-tv.xiongmao-course-tv.Muti-Barrage.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/Muti-Barrage/xiongmao-tv.xiongmao-course-tv.Muti-Barrage.main.iml" filepath="$PROJECT_DIR$/.idea/modules/Muti-Barrage/xiongmao-tv.xiongmao-course-tv.Muti-Barrage.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/Muti-Barrage/xiongmao-tv.xiongmao-course-tv.Muti-Barrage.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/Muti-Barrage/xiongmao-tv.xiongmao-course-tv.Muti-Barrage.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/xiongmao-tv.xiongmao-course-tv.app.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/app/xiongmao-tv.xiongmao-course-tv.app.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/xiongmao-tv.xiongmao-course-tv.app.main.iml" filepath="$PROJECT_DIR$/.idea/modules/app/xiongmao-tv.xiongmao-course-tv.app.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/xiongmao-tv.xiongmao-course-tv.app.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/app/xiongmao-tv.xiongmao-course-tv.app.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/ntplibrary/xiongmao-tv.xiongmao-course-tv.ntplibrary.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/ntplibrary/xiongmao-tv.xiongmao-course-tv.ntplibrary.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/ntplibrary/xiongmao-tv.xiongmao-course-tv.ntplibrary.main.iml" filepath="$PROJECT_DIR$/.idea/modules/ntplibrary/xiongmao-tv.xiongmao-course-tv.ntplibrary.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/ntplibrary/xiongmao-tv.xiongmao-course-tv.ntplibrary.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/ntplibrary/xiongmao-tv.xiongmao-course-tv.ntplibrary.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/subtitles/xiongmao-tv.xiongmao-course-tv.subtitles.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/subtitles/xiongmao-tv.xiongmao-course-tv.subtitles.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/subtitles/xiongmao-tv.xiongmao-course-tv.subtitles.main.iml" filepath="$PROJECT_DIR$/.idea/modules/subtitles/xiongmao-tv.xiongmao-course-tv.subtitles.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/subtitles/xiongmao-tv.xiongmao-course-tv.subtitles.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/subtitles/xiongmao-tv.xiongmao-course-tv.subtitles.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/superplayerkit/xiongmao-tv.xiongmao-course-tv.superplayerkit.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/superplayerkit/xiongmao-tv.xiongmao-course-tv.superplayerkit.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/superplayerkit/xiongmao-tv.xiongmao-course-tv.superplayerkit.main.iml" filepath="$PROJECT_DIR$/.idea/modules/superplayerkit/xiongmao-tv.xiongmao-course-tv.superplayerkit.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/superplayerkit/xiongmao-tv.xiongmao-course-tv.superplayerkit.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/superplayerkit/xiongmao-tv.xiongmao-course-tv.superplayerkit.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/tuicore/xiongmao-tv.xiongmao-course-tv.tuicore.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/tuicore/xiongmao-tv.xiongmao-course-tv.tuicore.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/tuicore/xiongmao-tv.xiongmao-course-tv.tuicore.main.iml" filepath="$PROJECT_DIR$/.idea/modules/tuicore/xiongmao-tv.xiongmao-course-tv.tuicore.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/tuicore/xiongmao-tv.xiongmao-course-tv.tuicore.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/tuicore/xiongmao-tv.xiongmao-course-tv.tuicore.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/wifimanager/xiongmao-tv.xiongmao-course-tv.wifimanager.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/wifimanager/xiongmao-tv.xiongmao-course-tv.wifimanager.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/wifimanager/xiongmao-tv.xiongmao-course-tv.wifimanager.main.iml" filepath="$PROJECT_DIR$/.idea/modules/wifimanager/xiongmao-tv.xiongmao-course-tv.wifimanager.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/wifimanager/xiongmao-tv.xiongmao-course-tv.wifimanager.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/wifimanager/xiongmao-tv.xiongmao-course-tv.wifimanager.unitTest.iml" />
    </modules>
  </component>
</project>