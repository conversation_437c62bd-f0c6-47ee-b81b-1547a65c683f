<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="testRunner" value="GRADLE" />
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="11" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/Muti-Barrage" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/ntplibrary" />
            <option value="$PROJECT_DIR$/subtitles" />
            <option value="$PROJECT_DIR$/superplayerkit" />
            <option value="$PROJECT_DIR$/tuicore" />
            <option value="$PROJECT_DIR$/wifimanager" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>