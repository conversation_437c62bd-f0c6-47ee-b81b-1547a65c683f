apply plugin: 'com.android.library'
//apply plugin: 'com.github.dcendents.android-maven'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion

        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    // TODO replace with https://issuetracker.google.com/issues/72050365 once released.
    libraryVariants.all {
        it.generateBuildConfig.enabled = false
    }
}

dependencies {

}

group='com.github.instacart'