apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api fileTree(include: ['*.aar'], dir: 'libs')

    implementation 'com.android.support:cardview-v7:28.0.0'

//    implementation project(':common')
    api rootProject.ext.liteavSdk
    implementation 'com.github.ctiao:DanmakuFlameMaster:0.5.3'

    implementation 'androidx.appcompat:appcompat:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'

    implementation 'com.github.bumptech.glide:glide:4.12.0'
    implementation 'androidx.exifinterface:exifinterface:1.2.0'
    implementation 'androidx.vectordrawable:vectordrawable-animated:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
}