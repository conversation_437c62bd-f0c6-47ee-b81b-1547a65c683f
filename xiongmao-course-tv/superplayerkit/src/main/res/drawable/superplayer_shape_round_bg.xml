<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <!-- 填充的颜色：这里设置背景透明 -->
    <solid android:color="@color/superplayer_default_bg" />
    <!-- 边框的颜色 ：不能和窗口背景色一样-->
    <stroke
        android:width="1dp"
        android:color="@color/superplayer_default_bg" />
    <!-- 设置按钮的四个角为弧形 -->
    <!-- android:radius 弧形的半径 -->
    <corners android:radius="5dip" />

    <!-- padding：Button里面的文字与Button边界的间隔 -->
    <padding
        android:bottom="5dp"
        android:left="5dp"
        android:right="5dp"
        android:top="5dp" />
</shape>