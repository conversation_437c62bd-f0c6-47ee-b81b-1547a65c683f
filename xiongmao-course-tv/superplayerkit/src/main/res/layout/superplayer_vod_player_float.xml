<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="320dp"
    android:layout_height="180dp"
    android:background="@color/superplayer_black">

    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/superplayer_float_cloud_video_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/superplayer_iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:src="@drawable/superplayer_ic_float_close" />

    <LinearLayout
        android:id="@+id/superplayer_dynamic_watermark_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical" />
    <com.tencent.liteav.demo.superplayer.ui.view.VipWatchView
        android:id="@+id/superplayer_vip_watch_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</RelativeLayout>