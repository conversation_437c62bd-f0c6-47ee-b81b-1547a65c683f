<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <!--标题-->
    <LinearLayout
        android:id="@+id/superplayer_rl_top"
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/superplayer_top_shadow"
        android:gravity="left"
        android:orientation="horizontal"
        android:paddingLeft="30dp"
        android:paddingTop="30dp">

        <ImageView
            android:id="@+id/superplayer_iv_back"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/superplayer_btn_back_play"
            android:visibility="gone" />

        <TextView
            android:id="@+id/superplayer_tv_title"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/superplayer_small_video_special_effects_editing"
            android:textColor="@android:color/white"
            android:textSize="40dp" />
    </LinearLayout>


    <!--重播-->
    <LinearLayout
        android:id="@+id/superplayer_ll_replay"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="10dp"
        android:visibility="gone">

        <ImageView
            android:id="@+id/superplayer_iv_replay"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/superplayer_ic_replay" />

    </LinearLayout>

    <TextView
        android:id="@+id/superplayer_tv_back_to_live"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/superplayer_ll_bottom"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="16dp"
        android:background="@drawable/superplayer_shape_round_bg"
        android:text="@string/superplayer_back_live"
        android:visibility="gone" />


    <com.tencent.liteav.demo.superplayer.ui.view.VolumeBrightnessProgressLayout
        android:id="@+id/superplayer_gesture_progress"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true" />

    <com.tencent.liteav.demo.superplayer.ui.view.VideoProgressLayout
        android:id="@+id/superplayer_video_progress_layout"
        style="@style/FocusCloseStyle"
        android:layout_width="500dp"
        android:layout_height="300dp"
        android:layout_centerInParent="true"
        android:gravity="center" />

    <ImageView
        android:id="@+id/superplayer_small_iv_water_mark"
        style="@style/FocusCloseStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/superplayer_small_iv_background"
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/superplayer_cover_view"
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/superplayer_fl_sub_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center|bottom"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:layout_marginBottom="40dp" />


    <FrameLayout
        android:id="@+id/superplayer_ll_bottom"
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/superplayer_bottom_shadow"
        android:clipChildren="false"
        android:clipToPadding="false">


        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/superplayer_bottom_shadow" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/superplayer_recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="50dp"
            android:visibility="gone" />

        <LinearLayout
            style="@style/FocusCloseStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:orientation="vertical"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!--播放/暂停-->
                <ImageView
                    android:id="@+id/superplayer_iv_pause"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="20dp"
                    android:src="@drawable/superplayer_ic_vod_pause_normal" />
                <!--播放位置-->
                <TextView
                    android:id="@+id/superplayer_tv_current"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="00:00"
                    android:textColor="@android:color/white"
                    android:textSize="28dp"
                    android:visibility="gone" />

                <com.tencent.liteav.demo.superplayer.ui.view.seekbar.RangeSeekBar
                    android:id="@+id/superplayer_seekbar_progress"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    app:rsb_gravity="center"
                    app:rsb_indicator_background_color="@color/superplayer_transparent"
                    app:rsb_indicator_height="wrap_content"
                    app:rsb_indicator_radius="100dp"
                    app:rsb_indicator_show_mode="alwaysShow"
                    app:rsb_indicator_text_color="#41C965"
                    app:rsb_indicator_text_size="18dp"
                    app:rsb_mode="single"
                    app:rsb_progress_color="#601EB847"
                    app:rsb_progress_default_color="#30cfcfcf"
                    app:rsb_progress_height="6dp"
                    app:rsb_progress_knowledge_color="#40FFFFFF"
                    app:rsb_thumb_drawable="@drawable/thumb_green_alpha"
                    app:rsb_thumb_height="18dp"
                    app:rsb_thumb_inactivated_drawable="@drawable/thumb_green_alpha"
                    app:rsb_thumb_width="18dp"
                    app:rsb_tick_mark_mode="other" />

                <!--        <com.tencent.liteav.demo.superplayer.ui.view.PointSeekBar-->
                <!--            android:id="@+id/superplayer_seekbar_progress"-->
                <!--            style="@style/FocusCloseStyle"-->
                <!--            android:layout_width="0.0dip"-->
                <!--            android:layout_height="wrap_content"-->
                <!--            android:layout_gravity="center_vertical"-->
                <!--            android:layout_marginLeft="5.0dip"-->
                <!--            android:layout_marginRight="5.0dip"-->
                <!--            android:layout_weight="1.0"-->
                <!--            app:psb_backgroundColor="@color/superplayer_biz_audio_progress_second"-->
                <!--            app:psb_max="100"-->
                <!--            app:psb_progress="0"-->
                <!--            app:psb_progressColor="#FF584C"-->
                <!--            app:psb_progressHeight="2dp"-->
                <!--            app:psb_thumbBackground="@drawable/superplayer_ic_vod_thumb" />-->

                <!--总时长-->
                <TextView
                    android:id="@+id/superplayer_tv_duration"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="00:00"
                    android:textColor="@android:color/white"
                    android:textSize="20dp" />

                <ImageView
                    android:id="@+id/superplayer_iv_play_next"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/superplayer_play_next"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/superplayer_iv_fullscreen"
                    style="@style/FocusCloseStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/superplayer_ic_vod_fullscreen"
                    android:visibility="invisible" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="62dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/superplayer_tv_vod_next"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="30dp"
                    android:text="下一个"
                    android:textColor="@color/superplayer_white"
                    android:textSize="20dp" />

                <TextView
                    android:id="@+id/superplayer_tv_vod_knowledge"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="30dp"
                    android:text="本节知识点"
                    android:textColor="@color/superplayer_color_green"
                    android:textSize="20dp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/superplayer_tv_vod_definition"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="30dp"
                    android:text="清晰度"
                    android:textColor="@color/superplayer_white"
                    android:textSize="20dp" />

                <TextView
                    android:id="@+id/superplayer_tv_vod_speed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="30dp"
                    android:text="倍速"
                    android:textColor="@color/superplayer_white"
                    android:textSize="20dp" />


                <TextView
                    android:id="@+id/superplayer_tv_vod_sub_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="字幕"
                    android:textColor="@color/superplayer_white"
                    android:textSize="20dp"
                    android:visibility="gone" />

            </LinearLayout>

            <ImageView
                android:id="@+id/superplayer_iv_more"
                style="@style/FocusCloseStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/superplayer_bottom_arrow" />
        </LinearLayout>
    </FrameLayout>


    <!-- 网速相关 -->
    <LinearLayout
        android:id="@+id/superplayer_ll_net"
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/superplayer_tv_net_title"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="@color/superplayer_white"
            android:textSize="36dp" />

        <ProgressBar
            android:id="@+id/superplayer_pb_live"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:visibility="gone" />

        <com.tencent.liteav.demo.superplayer.ui.view.CustomLoadingProgress
            android:id="@+id/superplayer_progress_net"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_marginLeft="300dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="300dp"
            android:layout_marginBottom="20dp"
            android:indeterminateBehavior="repeat" />

        <TextView
            android:id="@+id/superplayer_progress_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="播放中按【下键】可切换剧集和清晰度"
            android:textColor="@color/superplayer_white"
            android:textSize="28dp" />

        <TextView
            android:id="@+id/superplayer_tv_net_content"
            style="@style/FocusCloseStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text=""
            android:textColor="@color/superplayer_color_green"
            android:textSize="20dp" />
    </LinearLayout>


    <ImageView
        android:id="@+id/superplayer_resume"
        style="@style/FocusCloseStyle"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_centerInParent="true"
        android:src="@drawable/superplayer_ic_vod_play_normal"
        android:visibility="gone" />

    <com.tencent.liteav.demo.superplayer.ui.view.VipWatchView
        android:id="@+id/superplayer_vip_watch_view"
        style="@style/FocusCloseStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true" />
</RelativeLayout>