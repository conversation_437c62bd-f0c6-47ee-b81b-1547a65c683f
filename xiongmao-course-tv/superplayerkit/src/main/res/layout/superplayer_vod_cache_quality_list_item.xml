<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:background="@color/superplayer_transparent">

    <TextView
        android:id="@+id/superplayer_tv_quality_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:ems="8"
        android:gravity="center"
        android:paddingTop="20dp"
        android:paddingBottom="20dp"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:textSize="14sp"
        android:textColor="@color/superplayer_white"
        tools:text="流畅"/>

</LinearLayout>