<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.tencent.rtmp.ui.TXCloudVideoView
        android:id="@+id/superplayer_cloud_video_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <com.tencent.liteav.demo.superplayer.ui.player.WindowPlayer
        android:id="@+id/superplayer_controller_small"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.tencent.liteav.demo.superplayer.ui.player.FullScreenPlayer
        android:id="@+id/superplayer_controller_large"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/superplayer_dynamic_watermark_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.tencent.liteav.demo.superplayer.ui.view.DynamicWatermarkView
            android:id="@+id/superplayer_dynamic_watermark"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

    <com.tencent.liteav.demo.superplayer.ui.view.DanmuView
        android:id="@+id/superplayer_danmuku_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.tencent.liteav.demo.superplayer.ui.player.FloatPlayer
        android:id="@+id/superplayer_controller_float"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</RelativeLayout>