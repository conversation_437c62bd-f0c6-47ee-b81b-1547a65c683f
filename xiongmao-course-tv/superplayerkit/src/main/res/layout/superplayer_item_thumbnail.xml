<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/superplayer_rl_thumbnail"
    android:layout_width="210dp"
    android:layout_height="120dp"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <ImageView
        android:id="@+id/superplayer_iv_left_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:rotation="-90"
        android:src="@drawable/super_play_thumbnail_arrow"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/superplayer_iv_right_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:src="@drawable/super_play_thumbnail_arrow"
        android:visibility="gone" />


    <ImageView
        android:id="@+id/superplayer_iv_right_bottom_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:rotation="90"
        android:src="@drawable/super_play_thumbnail_arrow"
        android:visibility="gone" />


    <ImageView
        android:id="@+id/superplayer_iv_left_bottom_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:rotation="180"
        android:src="@drawable/super_play_thumbnail_arrow"
        android:visibility="gone" />


    <!--    <androidx.cardview.widget.CardView-->
    <!--        android:id="@+id/superplayer_card_view"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:layout_margin="4dp"-->
    <!--        app:cardCornerRadius="0dp"-->
    <!--        app:cardPreventCornerOverlap="false"-->
    <!--        app:cardUseCompatPadding="true">-->

    <com.tencent.liteav.demo.superplayer.RoundCornerImageView
        android:id="@+id/superplayer_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="4dp"
        android:background="@drawable/super_default_thumb"
        android:scaleType="fitXY"
        app:corner_size="0" />
    <!--    </androidx.cardview.widget.CardView>-->

</RelativeLayout>