<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="RoundCornerImageView">
        <attr name="corner_size" format="integer" />
    </declare-styleable>

    <declare-styleable name="RangeSeekBar">
        <!--RangeSeekBar common attrs-->

        <!--The maximum-->
        <attr name="rsb_max" format="float" />

        <!--The minimum-->
        <attr name="rsb_min" format="float" />

        <!--RangeSeekBar mode, single is normal seekBar, range is RangeSeekBar-->
        <attr name="rsb_mode" format="enum">
            <enum name="single" value="1" />
            <enum name="range" value="2" />
        </attr>

        <!--RangeSeekBar gravity-->
        <attr name="rsb_gravity" format="enum">
            <enum name="top" value="0" />
            <enum name="bottom" value="1" />
            <enum name="center" value="2" />
        </attr>

        <!--The min interval of the thumbs -->
        <attr name="rsb_min_interval" format="float" />

        <!-- 0 for the normal mode, greater than 1 to switch to scale mode-->
        <attr name="rsb_tick_mark_number" format="integer" />

        <!--Scale mode
        Number according to the scale of the actual proportion of the distribution of the location (markTextArray must be a number)
        Other bisects the current layout (markTextArray can be any character)
        -->
        <attr name="rsb_tick_mark_mode" format="enum">
            <enum name="number" value="0" />
            <enum name="other" value="1" />
        </attr>

        <!--The tick mark text gravity -->
        <attr name="rsb_tick_mark_gravity" format="enum">
            <enum name="left" value="0" />
            <enum name="center" value="1" />
            <enum name="right" value="2" />
        </attr>

        <!--The tick mark text layout gravity -->
        <attr name="rsb_tick_mark_layout_gravity" format="enum">
            <enum name="top" value="0" />
            <enum name="bottom" value="1" />
        </attr>

        <!--The tick mark text array -->
        <attr name="rsb_tick_mark_text_array" format="reference" />

        <!--The tick mark text margin bottom to progress -->
        <attr name="rsb_tick_mark_text_margin" format="dimension" />

        <attr name="rsb_tick_mark_text_size" format="dimension" />

        <attr name="rsb_tick_mark_text_color" format="color" />

        <!--it just work in range && number mode now-->
        <attr name="rsb_tick_mark_in_range_text_color" format="color" />

        <attr name="rsb_progress_height" format="dimension" />

        <attr name="rsb_progress_radius" format="dimension" />

        <!--the color of progress bar when in progress-->
        <attr name="rsb_progress_color" format="color" />

        <attr name="rsb_progress_knowledge_color" format="color" />
        <!--the default color of the progress bar-->
        <attr name="rsb_progress_default_color" format="color" />

        <attr name="rsb_progress_drawable" format="reference" />

        <attr name="rsb_progress_drawable_default" format="reference" />

        <!--SeekBar attrs-->
        <attr name="rsb_indicator_show_mode" format="enum">
            <enum name="showWhenTouch" value="0" />
            <enum name="alwaysHide" value="1" />
            <enum name="alwaysShowAfterTouch" value="2" />
            <enum name="alwaysShow" value="3" />
        </attr>
        <attr name="rsb_indicator_height" format="dimension">
            <enum name="wrap_content" value="-1" />
        </attr>
        <attr name="rsb_indicator_width" format="dimension">
            <enum name="wrap_content" value="-1" />
        </attr>
        <!--indicator margin bottom to progress bar-->
        <attr name="rsb_indicator_margin" format="dimension" />
        <attr name="rsb_indicator_text_size" format="dimension" />
        <attr name="rsb_indicator_text_color" format="color" />
        <!--indicator arrow size, it just work when you not use rsb_indicator_drawable -->
        <attr name="rsb_indicator_arrow_size" format="dimension" />
        <!-- must use 9 path ！！！-->
        <attr name="rsb_indicator_drawable" format="reference" />
        <attr name="rsb_indicator_background_color" format="color" />
        <attr name="rsb_indicator_padding_left" format="dimension" />
        <attr name="rsb_indicator_padding_right" format="dimension" />
        <attr name="rsb_indicator_padding_top" format="dimension" />
        <attr name="rsb_indicator_padding_bottom" format="dimension" />
        <attr name="rsb_indicator_radius" format="dimension" />
        <attr name="rsb_thumb_drawable" format="reference" />
        <!--the thumb inactivated is when you don't touch the thumb button-->
        <attr name="rsb_thumb_inactivated_drawable" format="reference" />
        <attr name="rsb_thumb_width" format="dimension" />
        <attr name="rsb_thumb_height" format="dimension" />
        <attr name="rsb_thumb_scale_ratio" format="float" />

        <!--steps SeekBar-->
        <attr name="rsb_steps" format="integer" />
        <attr name="rsb_step_color" format="color" />
        <attr name="rsb_step_width" format="dimension" />
        <attr name="rsb_step_height" format="dimension" />
        <attr name="rsb_step_radius" format="dimension" />
        <attr name="rsb_step_auto_bonding" format="boolean" />
        <attr name="rsb_step_drawable" format="reference" />

    </declare-styleable>

    <declare-styleable name="VerticalRangeSeekBar">
        <!--the vertical RangeSeekBar draw orientation-->
        <attr name="rsb_orientation" format="enum">
            <enum name="left" value="1" />
            <enum name="right" value="2" />
        </attr>
        <attr name="rsb_tick_mark_orientation" format="enum">
            <enum name="vertical" value="1" />
            <enum name="horizontal" value="2" />
        </attr>
        <attr name="rsb_indicator_text_orientation" format="enum">
            <enum name="vertical" value="1" />
            <enum name="horizontal" value="2" />
        </attr>
    </declare-styleable>


</resources>
