package com.tencent.liteav.demo.superplayer;

import android.content.Context;
import android.graphics.Rect;
import android.util.Log;
import android.view.View;


import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by <PERSON> on 2020/8/17.
 */
public class LooppageListLayoutManager extends LinearLayoutManager {

    // Item高度，定焦第二个
    private final int mAdjustTop = 200;
    private final int mAdjustBottom = 200;

    public LooppageListLayoutManager(Context context) {
        super(context);
    }

    @Override
    public boolean requestChildRectangleOnScreen(@NonNull RecyclerView parent, @NonNull View child, @NonNull Rect rect, boolean immediate, boolean focusedChildVisible) {
        Log.e("雪碧图","执行requestChildRectangleOnScreen");
        final int parentLeft = getPaddingLeft() + mAdjustTop;
        final int parentRight = getWidth() - getPaddingRight() - mAdjustBottom;
        final int childLeft = child.getLeft() + rect.left;
        final int childRight = childLeft + rect.width();

        final int offScreenLeft = Math.min(0, childLeft - parentLeft);
        final int offScreenRight = Math.max(0, childRight - parentRight);

        int dx = offScreenLeft != 0 ? offScreenLeft : offScreenRight;

        if (dx != 0) {
            if (immediate) {
                parent.scrollBy(dx, 0);
            } else {
                parent.smoothScrollBy(dx, 0);
            }
            return true;
        }
        return false;
    }

    @Override
    public View onFocusSearchFailed(View focused, int focusDirection, RecyclerView.Recycler recycler, RecyclerView.State state) {
        return focused;
    }
}
