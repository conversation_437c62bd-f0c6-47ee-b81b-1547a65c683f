package com.tencent.liteav.demo.superplayer.uitls;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.tencent.liteav.demo.superplayer.entity.ThumbnailEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * 根据雪碧图获取小图
 */
public class SpritesUtil {

    public List<ThumbnailEntity> getBitmapSprites(Bitmap spriteSheet) {
        if (spriteSheet == null) {
            return null;
        }
        // 定义小图的尺寸和数量
        int spriteWidth = spriteSheet.getWidth() / 10; // 10 是小图数量
        int spriteHeight = spriteSheet.getHeight() / 10;
        int numSprites = 10 * 10;

        // 将所有小图保存到一个数组中
        List<ThumbnailEntity> sprites = new ArrayList<>();
        for (int i = 0; i < numSprites; i++) {
            int x = (i % 10) * spriteWidth;
            int y = (i / 10) * spriteHeight;
            sprites.add(new ThumbnailEntity(Bitmap.createBitmap(spriteSheet, x, y, spriteWidth, spriteHeight), i * 10));// i 是下表，记录10
        }
        return sprites;
    }
}
