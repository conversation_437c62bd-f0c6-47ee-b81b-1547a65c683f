package com.tencent.liteav.demo.superplayer.entity;

import android.graphics.Bitmap;

public class ThumbnailEntity {
    private Bitmap bitmap;
    private int nowProgress;

    public ThumbnailEntity(Bitmap bitmap, int nowPProgress) {
        this.bitmap = bitmap;
        this.nowProgress = nowPProgress;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }

    public void setNowProgress(int nowPProgress) {
        this.nowProgress = nowPProgress;
    }

    public Bitmap getBitmap() {
        return bitmap;
    }

    public int getNowProgress() {
        return nowProgress;
    }

    @Override
    public String toString() {
        return "{" +
                "nowProgress=" + nowProgress +
                '}';
    }
}
