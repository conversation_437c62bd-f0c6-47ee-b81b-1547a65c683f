package com.tencent.liteav.demo.superplayer.entity;

public class KnowledgePointsListEntity {
    private int type;
    private String title;
    private String img_url;
    private String millisecond;
    private String dot_time;
    private String end_millisecond;
    private String end_dot_time;


    public KnowledgePointsListEntity(int type, String millisecond, String dot_time, String end_millisecond, String end_dot_time) {
        this.type = type;
        this.millisecond = millisecond;
        this.dot_time = dot_time;
        this.end_millisecond = end_millisecond;
        this.end_dot_time = end_dot_time;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public void setEnd_dot_time(String end_dot_time) {
        this.end_dot_time = end_dot_time;
    }

    public void setEnd_millisecond(String end_millisecond) {
        this.end_millisecond = end_millisecond;
    }

    public String getEnd_dot_time() {
        return end_dot_time;
    }

    public String getEnd_millisecond() {
        return end_millisecond;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImg_url() {
        return img_url;
    }

    public void setImg_url(String img_url) {
        this.img_url = img_url;
    }

    public String getMillisecond() {
        return millisecond;
    }

    public void setMillisecond(String millisecond) {
        this.millisecond = millisecond;
    }

    public String getDot_time() {
        return dot_time;
    }

    public void setDot_time(String dot_time) {
        this.dot_time = dot_time;
    }

    @Override
    public String toString() {
        return "KnowledgePointsListEntity{" +
                "millisecond='" + millisecond + '\'' +
                ", dot_time='" + dot_time + '\'' +
                ", end_millisecond='" + end_millisecond + '\'' +
                ", end_dot_time='" + end_dot_time + '\'' +
                '}';
    }
}
