package com.tencent.liteav.demo.superplayer;

import android.content.Context;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class CustomLayoutManager extends LinearLayoutManager {
    private int mSelectedPosition;
    private RecyclerView mRecyclerView;
    private int mItemWidth;
    private int mScreenWidth;
    private int mHalfScreenWidth;

    public CustomLayoutManager(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
        mSelectedPosition = 0;
    }

    @Override
    public void onAttachedToWindow(RecyclerView view) {
        super.onAttachedToWindow(view);
        mRecyclerView = view;
        mScreenWidth = mRecyclerView.getResources().getDisplayMetrics().widthPixels;
        mHalfScreenWidth = mScreenWidth / 2;
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        super.onLayoutChildren(recycler, state);
        mItemWidth = getChildAt(0).getWidth(); // 假设所有 item 的宽度相同，因此只需要计算一个 item 的宽度即可
        setSelectedItem();
    }

    @Override
    public void onScrollStateChanged(int state) {
        super.onScrollStateChanged(state);
        setSelectedItem();
    }

    private void setSelectedItem() {
        int firstVisibleItem = findFirstVisibleItemPosition();
        int lastVisibleItem = findLastVisibleItemPosition();
        int middleItem = (lastVisibleItem - firstVisibleItem) / 2 + firstVisibleItem;

        if (middleItem != mSelectedPosition) {
            View view = findViewByPosition(mSelectedPosition);
            if (view != null) {
                // 取消上一个选中的状态
                view.setSelected(false);
            }
            mSelectedPosition = middleItem;
            view = findViewByPosition(mSelectedPosition);
            if (view != null) {
                view.setSelected(true);
            }
        }

        // 将中间的 item 移动到屏幕中央
        View middleView = findViewByPosition(middleItem);
        if (middleView != null) {
            int left = middleView.getLeft();
            int right = middleView.getRight();
            int middle = (left + right) / 2;
            int halfItemWidth = mItemWidth / 2;

            if (middle < mHalfScreenWidth) {
                // 中间的 item 靠近屏幕左侧，将其居中
                mRecyclerView.smoothScrollBy(middle - mHalfScreenWidth + halfItemWidth, 0);
            } else if (middle > mScreenWidth - mHalfScreenWidth) {
                // 中间的 item 靠近屏幕右侧，将其居中
                mRecyclerView.smoothScrollBy(middle - (mScreenWidth - mHalfScreenWidth) - halfItemWidth, 0);
            }
        }
    }

    @Override
    public void scrollToPosition(int position) {
        super.scrollToPosition(position);
        mSelectedPosition = position;
    }

    @Override
    public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int position) {
        super.smoothScrollToPosition(recyclerView, state, position);
        mSelectedPosition = position;
    }
}
