package com.tencent.liteav.demo.superplayer.ui.player;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.os.Build;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.tencent.liteav.demo.superplayer.CustomLayoutManager;
import com.tencent.liteav.demo.superplayer.LooppageListLayoutManager;
import com.tencent.liteav.demo.superplayer.MyLayoutManager;
import com.tencent.liteav.demo.superplayer.OnThumbnailListener;
import com.tencent.liteav.demo.superplayer.R;
import com.tencent.liteav.demo.superplayer.SuperPlayerDef;
import com.tencent.liteav.demo.superplayer.SuperPlayerModel;
import com.tencent.liteav.demo.superplayer.adapter.ThumbnailAdapter;
import com.tencent.liteav.demo.superplayer.entity.KnowledgePointsListEntity;
import com.tencent.liteav.demo.superplayer.entity.ThumbnailEntity;
import com.tencent.liteav.demo.superplayer.model.utils.VideoGestureDetector;
import com.tencent.liteav.demo.superplayer.ui.view.CustomLoadingProgress;
import com.tencent.liteav.demo.superplayer.ui.view.PointSeekBar;
import com.tencent.liteav.demo.superplayer.ui.view.VideoProgressLayout;
import com.tencent.liteav.demo.superplayer.ui.view.VipWatchView;
import com.tencent.liteav.demo.superplayer.ui.view.VolumeBrightnessProgressLayout;
import com.tencent.liteav.demo.superplayer.ui.view.seekbar.RangeSeekBar;

import java.util.List;

/**
 * 窗口模式播放控件
 * <p>
 * 除基本播放控制外，还有手势控制快进快退、手势调节亮度音量等
 * <p>
 * 1、点击事件监听{@link #onClick(View)}
 * <p>
 * 2、触摸事件监听{@link #onTouchEvent(MotionEvent)}
 * <p>
 * 2、进度条事件监听{@link #onProgressChanged(PointSeekBar, int, boolean)}
 * {@link #onStartTrackingTouch(PointSeekBar)}
 * {@link #onStopTrackingTouch(PointSeekBar)}
 */
public class WindowPlayer extends AbsPlayer implements View.OnClickListener,
        PointSeekBar.OnSeekBarChangeListener, VipWatchView.VipWatchViewClickListener {

    // UI控件
    private LinearLayout mLayoutTop;                             // 顶部标题栏布局
    private FrameLayout mLayoutBottom;                          // 底部进度条所在布局
    private LinearLayout mLayoutNet;                          // 网络状态
    private CustomLoadingProgress mNetProgress;                          // 网络状态
    private ImageView mIvPause;                               // 暂停播放按钮
    private ImageView mIvPlayNext;                            // 播放下一个按钮
    private ImageView mIvFullScreen;                          // 全屏按钮
    private TextView mTvTitle;                               // 视频名称文本
    private TextView mTvNetTitle;                               // 视频名称文本
    private TextView mTvNetVideoTips;                               // 视频名称文本
    private TextView mTvNetContent;                               // 视频名称文本
    private TextView mTvBackToLive;                          // 返回直播文本
    private ImageView mBackground;                            // 背景
    private ImageView mIvBack;                                // 返回按钮
    private ImageView mIvWatermark;                           // 水印
    private TextView mTvCurrent;                             // 当前进度文本
    private TextView mTvDuration;                            // 总时长文本
    private TextView mTvVodDefinition;                            // 视频清晰度
    private TextView mTvVodSpeed;                            // 视频倍速
    private TextView mTvVodKnowledge;                            // 知识点
    private TextView mTvVodSubTitle;                            // 字幕
    private TextView mTvVodNext;                            // 下一个
    private FrameLayout mFlVodSubTitle;                            // 字幕
    //    private PointSeekBar mSeekBarProgress;                       // 播放进度条
    private RangeSeekBar mSeekBarProgress;                       // 播放进度条
    private LinearLayout mLayoutReplay;                          // 重播按钮所在布局
    private ProgressBar mPbLiveLoading;                         // 加载圈
    private ImageView mImageStartAndResume;                   // 开始播放的三角
    private ImageView mImageCover;                            // 封面图
    private VolumeBrightnessProgressLayout mGestureVolumeBrightnessProgressLayout; // 音量亮度调节布局
    private VideoProgressLayout mGestureVideoProgressLayout;            // 手势快进提示布局
    private GestureDetector mGestureDetector;                       // 手势检测监听器
    private VideoGestureDetector mVideoGestureDetector;                      // 手势控制工具
    private boolean isShowing;                              // 自身是否可见
    private boolean mIsChangingSeekBarProgress;             // 进度条是否正在拖动，避免SeekBar由于视频播放的update而跳动
    private SuperPlayerDef.PlayerType mPlayType = SuperPlayerDef.PlayerType.VOD;                          // 当前播放视频类型
    private SuperPlayerDef.PlayerState mCurrentPlayState = SuperPlayerDef.PlayerState.END;                 // 当前播放状态
    private long mDuration;                              // 视频总时长
    private long mLivePushDuration;                      // 直播推流总时长
    private long mProgress;                              // 当前播放进度
    private Bitmap mBackgroundBmp;                         // 背景图
    private Bitmap mWaterMarkBmp;                          // 水印图
    private float mWaterMarkBmpX;                         // 水印x坐标
    private float mWaterMarkBmpY;                         // 水印y坐标
    private long mLastClickTime;                         // 上次点击事件的时间
    private boolean mIsOpenGesture = true;                  // 是否开启手势
    private boolean isDestroy = false;              // Activity 是否被销毁
    private RecyclerView mRecyclerView;
    private ThumbnailAdapter thumbnailAdapter;


    public WindowPlayer(Context context) {
        super(context);
        initialize(context);
    }

    public WindowPlayer(Context context, AttributeSet attrs) {
        super(context, attrs);
        initialize(context);
    }

    public WindowPlayer(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initialize(context);
    }

    /**
     * 初始化控件、手势检测监听器、亮度/音量/播放进度的回调
     */
    private void initialize(Context context) {
        initView(context);
        mGestureDetector = new GestureDetector(getContext(), new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (isShowingVipView()) {   //当展示了试看页面的时候，不处理双击事件
                    return true;
                }
                togglePlayState();
                show();
                if (mHideViewRunnable != null) {
                    removeCallbacks(mHideViewRunnable);
                    postDelayed(mHideViewRunnable, 7000);
                }
                return true;
            }

            @Override
            public boolean onScroll(MotionEvent downEvent, MotionEvent moveEvent, float distanceX, float distanceY) {
                if (downEvent == null || moveEvent == null) {
                    return false;
                }
                if (mVideoGestureDetector != null && mGestureVolumeBrightnessProgressLayout != null) {
                    mVideoGestureDetector.check(mGestureVolumeBrightnessProgressLayout.getHeight(), downEvent, moveEvent, distanceX, distanceY);
                }
                return true;
            }

            @Override
            public boolean onDown(MotionEvent e) {
                if (mVideoGestureDetector != null) {
                    mVideoGestureDetector.reset(getWidth(), (int) mSeekBarProgress.getLeftSeekBar().getProgress());
                }
                return true;
            }

        });
        mGestureDetector.setIsLongpressEnabled(false);

        mVideoGestureDetector = new VideoGestureDetector(getContext());
        mVideoGestureDetector.setVideoGestureListener(new VideoGestureDetector.VideoGestureListener() {
            @Override
            public void onBrightnessGesture(float newBrightness) {
                if (mGestureVolumeBrightnessProgressLayout != null) {
                    mGestureVolumeBrightnessProgressLayout.setProgress((int) (newBrightness * 100));
                    mGestureVolumeBrightnessProgressLayout.setImageResource(R.drawable.superplayer_ic_light_max);
                    mGestureVolumeBrightnessProgressLayout.show();
                }
            }

            @Override
            public void onVolumeGesture(float volumeProgress) {
                if (mGestureVolumeBrightnessProgressLayout != null) {
                    mGestureVolumeBrightnessProgressLayout.setImageResource(R.drawable.superplayer_ic_volume_max);
                    mGestureVolumeBrightnessProgressLayout.setProgress((int) volumeProgress);
                    mGestureVolumeBrightnessProgressLayout.show();
                }
            }

            @Override
            public void onSeekGesture(int progress) {
                mIsChangingSeekBarProgress = true;
                if (mGestureVideoProgressLayout != null) {

                    if (progress > mSeekBarProgress.getMax()) {
                        progress = (int) mSeekBarProgress.getMax();
                    }
                    if (progress < 0) {
                        progress = 0;
                    }
                    mGestureVideoProgressLayout.setProgress(progress);
                    mGestureVideoProgressLayout.show();

                    float percentage = ((float) progress) / mSeekBarProgress.getMax();
                    float currentTime = (mDuration * percentage);
                    if (mPlayType == SuperPlayerDef.PlayerType.LIVE || mPlayType == SuperPlayerDef.PlayerType.LIVE_SHIFT) {
                        if (mLivePushDuration > MAX_SHIFT_TIME) {
                            currentTime = (int) (mLivePushDuration - MAX_SHIFT_TIME * (1 - percentage));
                        } else {
                            currentTime = mLivePushDuration * percentage;
                        }
                        mGestureVideoProgressLayout.setTimeText(formattedTime((long) currentTime));
                    } else {
                        mGestureVideoProgressLayout.setTimeText(formattedTime((long) currentTime) + " / " + formattedTime((long) mDuration));
                    }

                }
                if (mSeekBarProgress != null)
                    mSeekBarProgress.setProgress(progress);
            }
        });
    }

    /**
     * 初始化view
     */
    private void initView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.superplayer_vod_player_window, this);
        mLayoutTop = (LinearLayout) findViewById(R.id.superplayer_rl_top);
        mLayoutTop.setOnClickListener(this);
        mNetProgress = (CustomLoadingProgress) findViewById(R.id.superplayer_progress_net);
        mLayoutNet = (LinearLayout) findViewById(R.id.superplayer_ll_net);
        mLayoutBottom = (FrameLayout) findViewById(R.id.superplayer_ll_bottom);
        mLayoutBottom.setOnClickListener(this);
        mLayoutReplay = (LinearLayout) findViewById(R.id.superplayer_ll_replay);
        mTvTitle = (TextView) findViewById(R.id.superplayer_tv_title);
        mTvNetTitle = (TextView) findViewById(R.id.superplayer_tv_net_title);
        mTvNetVideoTips = (TextView) findViewById(R.id.superplayer_progress_tips);
        mTvNetContent = (TextView) findViewById(R.id.superplayer_tv_net_content);
        mIvPause = (ImageView) findViewById(R.id.superplayer_iv_pause);
        mIvBack = (ImageView) findViewById(R.id.superplayer_iv_back);
        mTvCurrent = (TextView) findViewById(R.id.superplayer_tv_current);
        mTvDuration = (TextView) findViewById(R.id.superplayer_tv_duration);

        mTvVodDefinition = (TextView) findViewById(R.id.superplayer_tv_vod_definition);
        mTvVodSpeed = (TextView) findViewById(R.id.superplayer_tv_vod_speed);

        mTvVodKnowledge = (TextView) findViewById(R.id.superplayer_tv_vod_knowledge);
        mTvVodSubTitle = (TextView) findViewById(R.id.superplayer_tv_vod_sub_title);
        mTvVodNext = (TextView) findViewById(R.id.superplayer_tv_vod_next);
        mFlVodSubTitle = (FrameLayout) findViewById(R.id.superplayer_fl_sub_title);

//        mSeekBarProgress = (PointSeekBar) findViewById(R.id.superplayer_seekbar_progress);
        mSeekBarProgress = (RangeSeekBar) findViewById(R.id.superplayer_seekbar_progress);
        mSeekBarProgress.setProgress(100);
//        mSeekBarProgress.setMax(100);
        mIvFullScreen = (ImageView) findViewById(R.id.superplayer_iv_fullscreen);
        mTvBackToLive = (TextView) findViewById(R.id.superplayer_tv_back_to_live);
        mPbLiveLoading = (ProgressBar) findViewById(R.id.superplayer_pb_live);
        mImageCover = (ImageView) findViewById(R.id.superplayer_cover_view);
        mImageStartAndResume = (ImageView) findViewById(R.id.superplayer_resume);
        mIvPlayNext = (ImageView) findViewById(R.id.superplayer_iv_play_next);
        mImageStartAndResume.setOnClickListener(this);


        mIvBack.setOnClickListener(this);
        mTvBackToLive.setOnClickListener(this);
        mIvPause.setOnClickListener(this);
        mIvPlayNext.setOnClickListener(this);
        mIvFullScreen.setOnClickListener(this);
        mLayoutTop.setOnClickListener(this);
        mLayoutReplay.setOnClickListener(this);

//        mSeekBarProgress.setOnSeekBarChangeListener(this);

        mGestureVolumeBrightnessProgressLayout =
                (VolumeBrightnessProgressLayout) findViewById(R.id.superplayer_gesture_progress);

        mGestureVideoProgressLayout = (VideoProgressLayout) findViewById(R.id.superplayer_video_progress_layout);

        mBackground = (ImageView) findViewById(R.id.superplayer_small_iv_background);
        setBackground(mBackgroundBmp);

        mIvWatermark = (ImageView) findViewById(R.id.superplayer_small_iv_water_mark);
        mVipWatchView = findViewById(R.id.superplayer_vip_watch_view);
        mVipWatchView.setVipWatchViewClickListener(this);

        SpannableString spannableString = new SpannableString(mTvNetVideoTips.getText());
        spannableString.setSpan(new ForegroundColorSpan(Color.GREEN), 4, 8, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        mTvNetVideoTips.setText(spannableString);


        mRecyclerView = findViewById(R.id.superplayer_recycler);


        layoutManager = new LinearLayoutManager(context);
        layoutManager.setOrientation(RecyclerView.HORIZONTAL);
        mRecyclerView.setLayoutManager(layoutManager);
        thumbnailAdapter = new ThumbnailAdapter(mRecyclerView);
        mRecyclerView.setAdapter(thumbnailAdapter);

    }

    LinearLayoutManager layoutManager;

    public RecyclerView getThumbnailRecyclerView() {
        return mRecyclerView;
    }

    public ThumbnailAdapter getThumbnailAdapter() {
        return thumbnailAdapter;
    }

    /**
     * 动态更新reyclerView的位置
     */
    public void changeRecyclerThumbnail(int progress) {
        if (mRecyclerView != null && thumbnailAdapter != null && thumbnailAdapter.getItemCount() > 0 && mRecyclerView.getChildAt(0) != null) {
            thumbnailAdapter.setSelectedPosition(progress / 10); //标注谁被选中了
//            mRecyclerView.scrollToPosition(progress / 10); //移动到指定位置
            int position = (progress / 10)/* 目标 item 的位置 */;
            int itemWidth = mRecyclerView.getChildAt(0).getWidth()/* 目标 item 的宽度 */;
//            int parentWidth = mRecyclerView.getWidth();
//            int totalPadding = mRecyclerView.getPaddingLeft() + mRecyclerView.getPaddingRight();
//            int targetLeft = mRecyclerView.getPaddingLeft() + (parentWidth - totalPadding - itemWidth) / 2;
//            layoutManager.scrollToPositionWithOffset(position, targetLeft - 50);

            //计算出当前seekbar的百分比
            double seekBarPercentage = (1.0 * progress / mDuration) * 100;
            Log.e("移动的偏移量 计算百分比--->", seekBarPercentage + "%");
            int offset = (int) (itemWidth * (seekBarPercentage / 100f) + 0.5f);
            Log.e("移动的偏移量 后的--->", "" + offset);
            layoutManager.scrollToPositionWithOffset(position, (int) (offset * 4.4));
        } else {
            if (thumbnailAdapter != null && mRecyclerView != null) {
                thumbnailAdapter.setSelectedPosition(progress / 10); //标注谁被选中了
                mRecyclerView.scrollToPosition(progress / 10); //移动到指定位置
            }
        }
    }


    /**
     * 更新数据Adpater
     */
    public void setThumbnails(List<ThumbnailEntity> bitmaps, OnThumbnailListener onThumbnailListener) {
        if (thumbnailAdapter != null) {
            thumbnailAdapter.setThumbnails(bitmaps, onThumbnailListener);
        }
    }


    /**
     * 更新清晰度
     */
    public void updateVodDefinition(String definition) {
        if (mTvVodDefinition != null) {
            mTvVodDefinition.setText(definition);
        }
    }

    /**
     * 更新倍速
     */
    public void updateVodSpeed(String speed) {
        if (mTvVodSpeed != null) {
            mTvVodSpeed.setText(speed);
        }
    }

    /**
     * 获取知识点View
     **/
    public TextView getVodKnowledgeView() {
        return mTvVodKnowledge;
    }

    /**
     * 获取字幕View
     **/
    public TextView getVodSubTitleView() {
        return mTvVodSubTitle;
    }

    /**
     * 获取下一天
     */
    public TextView getVodNextView() {
        return mTvVodNext;
    }

    /**
     * 获取字幕的View装载
     **/
    public FrameLayout getFlVodSubTitle() {
        return mFlVodSubTitle;
    }


    /**
     * 切换播放状态
     * <p>
     * 双击和点击播放/暂停按钮会触发此方法
     */
    private void togglePlayState() {
        switch (mCurrentPlayState) {
            case INIT:
            case PAUSE:
            case END:
                if (mControllerCallback != null) {
                    mControllerCallback.onResume();
                }
                break;
            case PLAYING:
            case LOADING:
                if (mControllerCallback != null) {
                    mControllerCallback.onPause();
                }
                mLayoutReplay.setVisibility(View.GONE);
                break;
        }
        show();
    }

    public boolean isShowing() {
        return isShowing;
    }

    /**
     * 切换自身的可见性
     */
    public void toggle() {
        if (isShowing) {
            hide();
        } else {
            show();
            postDelayedView();
        }
    }

    /**
     * 延迟关闭控制器的任务
     */
    public void removeHideViewRunnable() {
        if (mHideViewRunnable != null) {
            removeCallbacks(mHideViewRunnable);
//            Log.e("WindowPlayer", "终止关闭延时器");
        }
    }

    /**
     * 延迟关闭控制器
     */
    public void postDelayedView() {
//        Log.e("WindowPlayer", "延迟关闭控制器");
        if (mHideViewRunnable != null) {
            removeCallbacks(mHideViewRunnable);
            postDelayed(mHideViewRunnable, 3000);
        }
    }

    /**
     * 延迟关闭控制器
     */
    public void postDelayedView(long delay) {
//        Log.e("WindowPlayer", "延迟关闭控制器");
        if (mHideViewRunnable != null) {
            removeCallbacks(mHideViewRunnable);
            postDelayed(mHideViewRunnable, delay);
        }
    }

    public void setPlayNextButtonVisibility(boolean isShowing) {
//        toggleView(mIvPlayNext, isShowing);
    }

    private void updateStartUI(boolean isAutoPlay) {
        if (isAutoPlay) {
            toggleView(mImageStartAndResume, false);
            toggleView(mLayoutNet, true);
            mNetProgress.startRun();
        } else {
            toggleView(mImageStartAndResume, false);
            toggleView(mLayoutNet, false);
            mNetProgress.stopRun();
        }
        toggleView(mLayoutReplay, false);
    }

    public void preparePlayVideo(SuperPlayerModel superPlayerModel) {
        if (!isDestroy) {
            if (superPlayerModel.coverPictureUrl != null) {
                Glide.with(getContext()).load(superPlayerModel.coverPictureUrl)
                        .placeholder(R.drawable.superplayer_default).into(mImageCover);
            } else {
                Glide.with(getContext()).load(superPlayerModel.placeholderImage)
                        .placeholder(R.drawable.superplayer_default).into(mImageCover);
            }
        }
        mLivePushDuration = 0;
        toggleView(mImageCover, true);
        mIvPause.setImageResource(R.drawable.superplayer_ic_vod_play_normal);
        updateVideoProgress(0, superPlayerModel.duration);
        mSeekBarProgress.setEnabled(superPlayerModel.playAction != SuperPlayerModel.PLAY_ACTION_MANUAL_PLAY);
        updateStartUI(superPlayerModel.playAction == SuperPlayerModel.PLAY_ACTION_AUTO_PLAY);
    }

    /**
     * 设置水印
     *
     * @param bmp 水印图
     * @param x   水印的x坐标
     * @param y   水印的y坐标
     */
    @Override
    public void setWatermark(final Bitmap bmp, float x, float y) {
        mWaterMarkBmp = bmp;
        mWaterMarkBmpX = x;
        mWaterMarkBmpY = y;
        if (bmp != null) {
            this.post(new Runnable() {
                @Override
                public void run() {
                    int width = WindowPlayer.this.getWidth();
                    int height = WindowPlayer.this.getHeight();

                    int x = (int) (width * mWaterMarkBmpX) - bmp.getWidth() / 2;
                    int y = (int) (height * mWaterMarkBmpY) - bmp.getHeight() / 2;

                    mIvWatermark.setX(x);
                    mIvWatermark.setY(y);

                    mIvWatermark.setVisibility(VISIBLE);
                    setBitmap(mIvWatermark, bmp);
                }
            });
        } else {
            mIvWatermark.setVisibility(GONE);
        }
    }

    /**
     * 显示控件
     */
    @Override
    public void show() {
        isShowing = true;
        mLayoutTop.setVisibility(View.VISIBLE);
        mLayoutBottom.setVisibility(View.VISIBLE);

        changeRecyclerThumbnail((int) mProgress);

        if (mPlayType == SuperPlayerDef.PlayerType.LIVE_SHIFT) {
            mTvBackToLive.setVisibility(View.VISIBLE);
        }
    }

    public void showOrHideBackBtn(boolean isShow) {
        mIvBack.setVisibility(isShow ? VISIBLE : GONE);
    }

    /**
     * 隐藏控件
     */
    @Override
    public void hide() {
        if (thumbnailAdapter != null && thumbnailAdapter.isShortPress()) {
            return;
        }
        isShowing = false;
        mLayoutTop.setVisibility(View.GONE);
        mLayoutBottom.setVisibility(View.GONE);
        mRecyclerView.setVisibility(View.GONE);

        if (mPlayType == SuperPlayerDef.PlayerType.LIVE_SHIFT) {
            mTvBackToLive.setVisibility(View.GONE);
        }
    }

    public void toggleCoverView(boolean isVisible) {
        toggleView(mImageCover, isVisible);
    }

    public void prepareLoading() {
        toggleView(mLayoutNet, true);
        toggleView(mImageStartAndResume, false);
        mNetProgress.startRun();
    }

    @Override
    public void updatePlayState(SuperPlayerDef.PlayerState playState) {
        switch (playState) {
            case INIT:
                mIvPause.setImageResource(R.drawable.superplayer_ic_vod_play_normal);
                break;
            case PLAYING:
                mSeekBarProgress.setEnabled(true);
                mIvPause.setImageResource(R.drawable.superplayer_ic_vod_pause_normal);
                toggleView(mImageStartAndResume, false);
                toggleView(mLayoutNet, false);
                toggleView(mLayoutReplay, false);
                mNetProgress.stopRun();
                break;
            case LOADING:
                mSeekBarProgress.setEnabled(true);
                mIvPause.setImageResource(R.drawable.superplayer_ic_vod_pause_normal);
                toggleView(mLayoutNet, true);
                toggleView(mLayoutReplay, false);
                mNetProgress.startRun();
                break;
            case PAUSE:
                mIvPause.setImageResource(R.drawable.superplayer_ic_vod_play_normal);
                toggleView(mLayoutNet, false);
                toggleView(mLayoutReplay, false);
                toggleView(mImageStartAndResume, false);
                mNetProgress.stopRun();
                //在这里再进行一个定位
                changeRecyclerThumbnail((int) mProgress);
                break;
            case END:
                mIvPause.setImageResource(R.drawable.superplayer_ic_vod_play_normal);
                toggleView(mLayoutNet, false);
                toggleView(mLayoutReplay, true);
                mNetProgress.stopRun();
                break;
        }
        mCurrentPlayState = playState;
    }

    public TextView getTvCurrent() {
        return mTvCurrent;
    }

    /**
     * 更新视频名称
     *
     * @param title 视频名称
     */
    @Override
    public void updateTitle(String title) {
        mTvTitle.setText(title);
        mTvNetTitle.setText(title);
    }

    /**
     * 更新网速
     */
    public void updateNetSpeed(int speed) {
        if (mLayoutNet.getVisibility() == View.VISIBLE) {
            mTvNetContent.setText("当前网速：" + speed + "kbps");
        }
    }

    /**
     * 更新视频播放进度
     *
     * @param current  当前进度(秒)
     * @param duration 视频总时长(秒)
     */
    @Override
    public void updateVideoProgress(long current, long duration) {
        mProgress = current < 0 ? 0 : current;
        mDuration = duration < 0 ? 0 : duration;
        mTvCurrent.setText(formattedTime(mProgress));
        mSeekBarProgress.setIndicatorText(formattedTime(mProgress));
        mSeekBarProgress.setVideoDuration(duration);

        float percentage = mDuration > 0 ? ((float) mProgress / (float) mDuration) : 1.0f;
        if (mProgress == 0) {
            percentage = 0;
        }
        if (mPlayType == SuperPlayerDef.PlayerType.LIVE || mPlayType == SuperPlayerDef.PlayerType.LIVE_SHIFT) {
            mLivePushDuration = mLivePushDuration > mProgress ? mLivePushDuration : mProgress;
            long leftTime = mDuration - mProgress;
            mDuration = mDuration > MAX_SHIFT_TIME ? MAX_SHIFT_TIME : mDuration;
            percentage = 1 - (float) leftTime / (float) mDuration;
        } else {
            mVipWatchView.setCurrentTime(current);
        }

        if (percentage >= 0 && percentage <= 1) {
            float progress = percentage * mSeekBarProgress.getMax();
            if (!mIsChangingSeekBarProgress) {
                if (mPlayType == SuperPlayerDef.PlayerType.LIVE) {
                    mSeekBarProgress.setProgress(mSeekBarProgress.getMax());
                } else {
                    mSeekBarProgress.setProgress(progress);
                }
            }
            mTvDuration.setText(formattedTime(mDuration));
        }
    }

    @Override
    public void updatePlayType(SuperPlayerDef.PlayerType type) {
        mPlayType = type;
        switch (type) {
            case VOD:
                mTvBackToLive.setVisibility(View.GONE);
                mTvDuration.setVisibility(View.VISIBLE);
                break;
            case LIVE:
                mTvBackToLive.setVisibility(View.GONE);
                mTvDuration.setVisibility(View.GONE);
                mSeekBarProgress.setProgress(100);
                break;
            case LIVE_SHIFT:
                if (mLayoutBottom.getVisibility() == VISIBLE)
                    mTvBackToLive.setVisibility(View.VISIBLE);
                mTvDuration.setVisibility(View.GONE);
                break;
        }
    }

    /**
     * 设置背景
     *
     * @param bitmap 背景图
     */
    @Override
    public void setBackground(final Bitmap bitmap) {
        this.post(new Runnable() {
            @Override
            public void run() {
                if (bitmap == null) return;
                if (mBackground == null) {
                    mBackgroundBmp = bitmap;
                } else {
                    setBitmap(mBackground, mBackgroundBmp);
                }
            }
        });
    }

    /**
     * 设置目标ImageView显示的图片
     */
    private void setBitmap(ImageView view, Bitmap bitmap) {
        if (view == null || bitmap == null) return;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            view.setBackground(new BitmapDrawable(getContext().getResources(), bitmap));
        } else {
            view.setBackgroundDrawable(new BitmapDrawable(getContext().getResources(), bitmap));
        }
    }

    /**
     * 显示背景
     */
    @Override
    public void showBackground() {
        post(new Runnable() {
            @Override
            public void run() {
                ValueAnimator alpha = ValueAnimator.ofFloat(0.0f, 1);
                alpha.setDuration(500);
                alpha.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        float value = (Float) animation.getAnimatedValue();
                        mBackground.setAlpha(value);
                        if (value == 1) {
                            mBackground.setVisibility(VISIBLE);
                        }
                    }
                });
                alpha.start();
            }
        });
    }

    @Override
    public void release() {
        isDestroy = true;
    }

    /**
     * 隐藏背景
     */
    @Override
    public void hideBackground() {
        post(new Runnable() {
            @Override
            public void run() {
                if (mBackground.getVisibility() != View.VISIBLE) return;
                ValueAnimator alpha = ValueAnimator.ofFloat(1.0f, 0.0f);
                alpha.setDuration(500);
                alpha.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        float value = (Float) animation.getAnimatedValue();
                        mBackground.setAlpha(value);
                        if (value == 0) {
                            mBackground.setVisibility(GONE);
                        }
                    }
                });
                alpha.start();
            }
        });
    }

    @Override
    public void setVideoQualityVisible(boolean isShow) {
    }

    /**
     * 重写触摸事件监听，实现手势调节亮度、音量以及播放进度
     */
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mIsOpenGesture && mGestureDetector != null) {
            mGestureDetector.onTouchEvent(event);
        }

        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            toggle();
        }

        boolean isCall = event.getAction() == MotionEvent.ACTION_CANCEL || event.getAction() == MotionEvent.ACTION_UP;
        if (isCall && mVideoGestureDetector != null && mVideoGestureDetector.isVideoProgressModel()) {
            int progress = mVideoGestureDetector.getVideoProgress();
            if (progress > mSeekBarProgress.getMax()) {
                progress = (int) mSeekBarProgress.getMax();
            }
            if (progress < 0) {
                progress = 0;
            }
            mSeekBarProgress.setProgress(progress);

            int seekTime;
            float percentage = progress * 1.0f / mSeekBarProgress.getMax();
            if (mPlayType == SuperPlayerDef.PlayerType.LIVE || mPlayType == SuperPlayerDef.PlayerType.LIVE_SHIFT) {
                if (mLivePushDuration > MAX_SHIFT_TIME) {
                    seekTime = (int) (mLivePushDuration - MAX_SHIFT_TIME * (1 - percentage));
                } else {
                    seekTime = (int) (mLivePushDuration * percentage);
                }
            } else {
                seekTime = (int) (percentage * mDuration);
            }
            if (mControllerCallback != null) {
                mControllerCallback.onSeekTo(seekTime);
            }
            mIsChangingSeekBarProgress = false;
            if (mPlayType == SuperPlayerDef.PlayerType.VOD) {
                mVipWatchView.setCurrentTime(seekTime);
            }

        }

        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            removeCallbacks(mHideViewRunnable);
        } else if (event.getAction() == MotionEvent.ACTION_UP) {
            postDelayed(mHideViewRunnable, 7000);
        }
        return true;
    }

    /**
     * 设置点击事件监听
     */
    @Override
    public void onClick(View view) {
        if (System.currentTimeMillis() - mLastClickTime < 300) { //限制点击频率
            return;
        }
        mLastClickTime = System.currentTimeMillis();
        int id = view.getId();
        if (id == R.id.superplayer_iv_back) { //顶部标题栏
            if (mControllerCallback != null) {
                mControllerCallback.onBackPressed(SuperPlayerDef.PlayerMode.WINDOW);
            }
        } else if (id == R.id.superplayer_iv_pause || id == R.id.superplayer_resume) { //暂停\播放按钮
            togglePlayState();
        } else if (id == R.id.superplayer_iv_fullscreen) { //全屏按钮
            if (mControllerCallback != null) {
                mControllerCallback.onSwitchPlayMode(SuperPlayerDef.PlayerMode.FULLSCREEN);
            }
        } else if (id == R.id.superplayer_ll_replay) { //重播按钮
            if (mControllerCallback != null) {
                mControllerCallback.onResume();
            }
        } else if (id == R.id.superplayer_tv_back_to_live) { //返回直播按钮
            if (mControllerCallback != null) {
                mControllerCallback.onResumeLive();
            }
        } else if (id == R.id.superplayer_iv_play_next) {
            if (mControllerCallback != null) {
                mControllerCallback.playNext();
            }
        }
    }

    @Override
    public void onProgressChanged(PointSeekBar seekBar, int progress, boolean fromUser) {
        if (mGestureVideoProgressLayout != null && fromUser) {
            mGestureVideoProgressLayout.show();
            float percentage = ((float) progress) / seekBar.getMax();
            float currentTime = (mDuration * percentage);
            if (mPlayType == SuperPlayerDef.PlayerType.LIVE || mPlayType == SuperPlayerDef.PlayerType.LIVE_SHIFT) {
                if (mLivePushDuration > MAX_SHIFT_TIME) {
                    currentTime = (int) (mLivePushDuration - MAX_SHIFT_TIME * (1 - percentage));
                } else {
                    currentTime = mLivePushDuration * percentage;
                }
                mGestureVideoProgressLayout.setTimeText(formattedTime((long) currentTime));
            } else {
                mGestureVideoProgressLayout.setTimeText(formattedTime((long) currentTime) + " / " + formattedTime((long) mDuration));
            }
            mGestureVideoProgressLayout.setProgress(progress);
        }
    }

    @Override
    public void onStartTrackingTouch(PointSeekBar seekBar) {
        removeCallbacks(mHideViewRunnable);
    }

    @Override
    public void onStopTrackingTouch(PointSeekBar seekBar) {
        int curProgress = seekBar.getProgress();
        int maxProgress = seekBar.getMax();

        switch (mPlayType) {
            case VOD:
                if (curProgress >= 0 && curProgress <= maxProgress) {
                    // 关闭重播按钮
                    toggleView(mLayoutReplay, false);
                    float percentage = ((float) curProgress) / maxProgress;
                    int position = (int) (mDuration * percentage);
                    boolean showResult = mVipWatchView.canShowVipWatchView(position);
                    if (mControllerCallback != null) {
                        mControllerCallback.onSeekTo(position);
                    }
                    if (showResult) {
                        mVipWatchView.setCurrentTime(position);
                    }
                }
                break;
            case LIVE:
            case LIVE_SHIFT:
                toggleView(mLayoutNet, true);
                mNetProgress.startRun();
                int seekTime = (int) (mLivePushDuration * curProgress * 1.0f / maxProgress);
                if (mLivePushDuration > MAX_SHIFT_TIME) {
                    seekTime = (int) (mLivePushDuration - MAX_SHIFT_TIME * (maxProgress - curProgress) * 1.0f / maxProgress);
                }
                if (mControllerCallback != null) {
                    mControllerCallback.onSeekTo(seekTime);
                }
                break;
        }
        postDelayed(mHideViewRunnable, 7000);
    }


    public void disableGesture(boolean flag) {
        this.mIsOpenGesture = !flag;
    }


    @Override
    public void onClickVipTitleBack() {
        if (mControllerCallback != null) {
            mControllerCallback.onClickVipTitleBack(SuperPlayerDef.PlayerMode.WINDOW);
            mControllerCallback.onSeekTo(0);
        }
    }

    @Override
    public void onClickVipRetry() {
        if (mControllerCallback != null) {
            mControllerCallback.onClickVipRetry();
        }
    }

    @Override
    public void onShowVipView() {
        if (mControllerCallback != null) {
            mControllerCallback.onPause();
        }
    }

    @Override
    public void onClickVipBtn() {
        if (mControllerCallback != null) {
            mControllerCallback.onClickHandleVip();
        }
    }

    @Override
    public void onCloseVipTip() {
        if (mControllerCallback != null) {
            mControllerCallback.onCloseVipTip();
        }
    }

    /**
     * @param knowledgePointsListEntityList
     */
    public void setKnowledgePoints(List<KnowledgePointsListEntity> knowledgePointsListEntityList) {
        if (mSeekBarProgress != null) {
            mSeekBarProgress.setKnowledgePoints(knowledgePointsListEntityList);
        }
    }
}
