package com.tencent.liteav.demo.superplayer;

import android.graphics.PointF;
import android.util.DisplayMetrics;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

// 创建自定义的SmoothScroller类
public class CustomSmoothScroller extends LinearSmoothScroller {
    private static final int SNAP_DISTANCE = 200; // 快进或快退的距离
    private final int targetPosition; // 目标位置
    private final RecyclerView recyclerView;

    public CustomSmoothScroller(RecyclerView recyclerView, int targetPosition) {
        super(recyclerView.getContext());
        this.recyclerView = recyclerView;
        this.targetPosition = targetPosition;
    }

    @Override
    protected void onTargetFound(View targetView, RecyclerView.State state, Action action) {
//        int[] snapDistances = recyclerView.getlacalculateDistanceToFinalSnap(recyclerView.getLayoutManager(), targetView);
//        int dx = snapDistances[0];
//        int dy = snapDistances[1];
//        int time = calculateTimeForDeceleration(Math.max(Math.abs(dx), Math.abs(dy)));
//        if (time > 0) {
//            action.update(-dx, -dy, time, mDecelerateInterpolator);
//        }
    }

    @Override
    protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
        return displayMetrics.densityDpi / 100f;
    }

    @Override
    protected int calculateTimeForScrolling(int dx) {
        return Math.min(500, super.calculateTimeForScrolling(dx));
    }

    @Nullable
    @Override
    public PointF computeScrollVectorForPosition(int targetPosition) {
        return new PointF(1, 0);
    }

    @Override
    public int calculateDtToFit(int viewStart, int viewEnd, int boxStart, int boxEnd, int snapPreference) {
        RecyclerView.LayoutManager layoutManager = getLayoutManager();
        if (layoutManager.canScrollHorizontally()) {
            int targetViewCenter = boxStart + SNAP_DISTANCE;
            int viewCenter = (viewStart + viewEnd) / 2;
            int dt = targetViewCenter - viewCenter;
            return Math.max(-boxStart, Math.min(dt, boxEnd - viewEnd));
        } else {
            return 0;
        }
    }
}

