package com.tencent.liteav.demo.superplayer;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class CenteredRecyclerView extends RecyclerView {
    private int mSelectedPosition = -1;

    public CenteredRecyclerView(Context context) {
        super(context);
        init();
    }

    public CenteredRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CenteredRecyclerView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    private void init() {
        setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        setFocusable(true);
        setFocusableInTouchMode(true);
        setDescendantFocusability(FOCUS_AFTER_DESCENDANTS);
        setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    centerChildView();
                }
            }
        });
    }

    @Override
    public void onChildAttachedToWindow(View child) {
        super.onChildAttachedToWindow(child);
        if (hasFocus() && getFocusedChild() == null) {
            child.requestFocus();
        }
    }

    @Override
    public void onChildDetachedFromWindow(View child) {
        super.onChildDetachedFromWindow(child);
        if (mSelectedPosition == getChildLayoutPosition(child)) {
            mSelectedPosition = -1;
        }
    }

    @Override
    public boolean onRequestFocusInDescendants(int direction, Rect previouslyFocusedRect) {
        View child = getLayoutManager().getChildAt(mSelectedPosition);
        if (child != null) {
            return child.requestFocus(direction, previouslyFocusedRect);
        }
        return super.onRequestFocusInDescendants(direction, previouslyFocusedRect);
    }

    private void centerChildView() {
        View centerChild = getFocusedChild();
        if (centerChild == null) {
            return;
        }
        int centerX = getWidth() / 2;
        int offset = (getWidth() - centerChild.getWidth()) / 2 - centerChild.getLeft();
        smoothScrollBy(offset, 0);
        mSelectedPosition = getChildLayoutPosition(centerChild);
    }
}
