package com.tencent.liteav.demo.superplayer;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;


public class MyLayoutManager extends LinearLayoutManager {

    private static final String TAG = MyLayoutManager.class.getSimpleName();
    private int focusIndex = -1;

    public MyLayoutManager(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
    }

    @Override
    public View onFocusSearchFailed(View focused, int focusDirection,
                                    RecyclerView.Recycler recycler, RecyclerView.State state) {
        // TODO Auto-generated method stub
        View search = super.onFocusSearchFailed(focused, focusDirection,
                recycler, state);
//            if (search != null && search.getParent() )
        return search;
    }

    @Override
    public boolean requestChildRectangleOnScreen(@NonNull RecyclerView parent, @NonNull View child, @NonNull Rect rect, boolean immediate, boolean focusedChildVisible) {
        return requestChildRectangleOnScreen(parent, child, rect, immediate);
    }

    @Override
    public boolean requestChildRectangleOnScreen(RecyclerView parent, View child, Rect rect, boolean immediate) {
        int parentLeft = getPaddingLeft();
        final int parentTop = getPaddingTop();
        int parentRight = getWidth() - getPaddingRight();
        final int parentBottom = getHeight() - getPaddingBottom();
        final int childLeft = child.getLeft() + rect.left - child.getScrollX();
        final int childTop = child.getTop() + rect.top - child.getScrollY();
        final int childRight = childLeft + rect.width();
        final int childBottom = childTop + rect.height();

        // 计算水平方向上的中心点位置
        int centerX = (parentLeft + parentRight) / 2;
        // 计算垂直方向上的中心点位置
        int centerY = (parentTop + parentBottom) / 2;
        // 计算滚动范围
        parentLeft = centerX - (childRight - childLeft) / 2;
        parentRight = centerX + (childRight - childLeft) / 2;

        final int offScreenLeft = Math.min(0, childLeft - parentLeft);
        final int offScreenTop = Math.min(0, childTop - centerY);
        final int offScreenRight = Math.max(0, childRight - parentRight);
        final int offScreenBottom = Math.max(0, childBottom - centerY);

        final int dx;
        if (ViewCompat.getLayoutDirection(parent) == ViewCompat.LAYOUT_DIRECTION_RTL) {
            dx = offScreenRight != 0 ? offScreenRight : offScreenLeft;
        } else {
            dx = offScreenLeft != 0 ? offScreenLeft : offScreenRight;
        }

        final int dy = offScreenTop != 0 ? offScreenTop : offScreenBottom;

        if (dx != 0 || dy != 0) {
            if (immediate) {
                parent.scrollBy(dx, dy);
            } else {
                parent.smoothScrollBy(dx, dy);
            }
            return true;
        }
        return false;
    }

    @Override
    public void onItemsAdded(@NonNull RecyclerView recyclerView, int positionStart, int itemCount) {

        //为了分页加载完成后可以往下滚  不然需要重新获得焦点才可以往下滚动
//        LogUtils.e(TAG, "current visiable is: "+ findLastCompletelyVisibleItemPosition());
//        LogUtils.e(TAG, "added items : "+ positionStart+"::::"+itemCount);
        View view = getFocusedChild();
        if (view != null) {
//            LogUtils.e(TAG, "focus pos : "+ getPosition(view));
            if (getPosition(view) == positionStart - 1) {
                scrollToPosition(positionStart);
            }
        }
        super.onItemsAdded(recyclerView, positionStart, itemCount);
    }

    @Override
    public void onItemsUpdated(@NonNull RecyclerView recyclerView, int positionStart, int itemCount) {
        super.onItemsUpdated(recyclerView, positionStart, itemCount);
    }

    @Nullable
    @Override
    public View onInterceptFocusSearch(@NonNull View focused, int direction) {
        return super.onInterceptFocusSearch(focused, direction);
    }

    @Override
    public void scrollToPositionWithOffset(int position, int offset) {
        super.scrollToPositionWithOffset(position, offset);
    }

    @Override
    public void onLayoutCompleted(RecyclerView.State state) {
        super.onLayoutCompleted(state);
        if (focusIndex > -1) {
            View view = findViewByPosition(focusIndex);
            if (view != null) {
                view.requestFocus();
            }
            focusIndex = -1;
        }
//        LogUtils.e(TAG,"rv state is : "+state.toString());
    }

    public void setFocusIndex(int pos) {
        focusIndex = pos;
    }
}
