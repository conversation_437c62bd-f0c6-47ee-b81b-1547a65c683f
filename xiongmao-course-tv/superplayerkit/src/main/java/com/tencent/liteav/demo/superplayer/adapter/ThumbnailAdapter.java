package com.tencent.liteav.demo.superplayer.adapter;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.tencent.liteav.demo.superplayer.OnThumbnailListener;
import com.tencent.liteav.demo.superplayer.R;
import com.tencent.liteav.demo.superplayer.entity.ThumbnailEntity;

import java.lang.ref.WeakReference;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 1、把下载好的大图，存储在本地数据库记录
 * 2、每次打开
 */
public class ThumbnailAdapter extends RecyclerView.Adapter<ThumbnailAdapter.ViewHolder> {

    private List<ThumbnailEntity> thumbnails;

    private int mSelectedPosition = 0;

    private RecyclerView recyclerView;

    private boolean shortPress;//是否快进快退

    public boolean isShortPress() {
        return shortPress;
    }

    public List<ThumbnailEntity> getThumbnails() {
        return thumbnails;
    }

    private OnThumbnailListener onThumbnailListener;


    public void setShortPress(boolean shortPress) {
        this.shortPress = shortPress;
    }

    public ThumbnailAdapter(RecyclerView recyclerView) {
        this.recyclerView = recyclerView;
    }

    public void cleanAllData() {
        if (thumbnails != null) {
            thumbnails.clear();
        }
        recycleBitmaps();
        notifyDataSetChanged();
    }


    public void recycleBitmaps() {
        if (recyclerView != null) {
            for (int i = 0; i < getItemCount(); i++) {
                ViewHolder holder = (ViewHolder) recyclerView.findViewHolderForAdapterPosition(i);
                if (holder != null) {
                    holder.recycleBitmap();
                }
            }
        }
    }

    public void setThumbnails(List<ThumbnailEntity> thumbnails, OnThumbnailListener onThumbnailListener) {
        this.thumbnails = thumbnails;
        this.onThumbnailListener = onThumbnailListener;
        notifyDataSetChanged();
    }

    public void setSelectedPosition(int position) {
        mSelectedPosition = position;
        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.superplayer_item_thumbnail, parent, false);
        return new ViewHolder(view);

    }


    @Override
    public void onViewRecycled(ViewHolder holder) {
        super.onViewRecycled(holder);
        // 清除 ImageView 中的 Bitmap 对象
//        holder.thumbnailView.setImageBitmap(null);
//        // 释放 Bitmap 对象占用的内存
//        holder.recycleBitmap();
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {

        holder.bind(thumbnails.get(position));

        Bitmap thumbnail = thumbnails.get(position).getBitmap();
        if (thumbnail != null && !shortPress) {
            // item 可见时执行的操作
            if (!thumbnail.isRecycled()) {
                Glide.with(holder.itemView.getContext())
                        .load(thumbnail)
                        .placeholder(R.drawable.super_default_thumb)
                        .into(holder.thumbnailView);
            }
        } else {
            holder.thumbnailView.setImageResource(R.drawable.super_default_thumb);
        }

        if (position == (mSelectedPosition)) {
            holder.itemView.setFocusable(true);
            holder.itemView.setFocusableInTouchMode(true);
            holder.itemView.requestFocus();
        } else {
            holder.itemView.setFocusable(false);
            holder.itemView.setFocusableInTouchMode(false);
        }

        holder.itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {

                ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
                ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) layoutParams;
                marginLayoutParams.setMargins(hasFocus ? 50 : 0, 0, hasFocus ? 50 : 0, 0);
                holder.itemView.setLayoutParams(layoutParams);

                if (hasFocus) {
                    holder.itemView.setTranslationZ(30);
                    holder.itemView.clearAnimation();
                    ofFloatAnimator(holder.itemView, 1f, 1.2f);//放大
                    viewVisible(holder, false);

//                    holder.mView.setCardElevation(30f);
//                    holder.mView.setCardBackgroundColor(Color.parseColor("#20FFFFFF"));
                } else {
//                    holder.mView.setCardElevation(0f);
//                    holder.mView.setCardBackgroundColor(Color.TRANSPARENT);


                    holder.itemView.clearAnimation();
                    holder.itemView.setTranslationZ(0);
                    ofFloatAnimator(holder.itemView, 1f, 1.0f);//正常
                    viewVisible(holder, true);
                }
            }
        });


        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onThumbnailListener != null) {
                    onThumbnailListener.onClick(position, thumbnails.get(position).getNowProgress());
                }
            }
        });

    }


    private void viewVisible(ViewHolder holder, boolean isHide) {
        holder.ivLeftArrow.setVisibility(isHide ? View.GONE : View.VISIBLE);
        holder.ivLeftBottomArrow.setVisibility(isHide ? View.GONE : View.VISIBLE);
        holder.ivRightArrow.setVisibility(isHide ? View.GONE : View.VISIBLE);
        holder.ivRightBottomArrow.setVisibility(isHide ? View.GONE : View.VISIBLE);
    }

    @Override
    public int getItemCount() {
        if (thumbnails != null) {
            if (thumbnails.size() > 0) {
                return thumbnails.size();
            }
        }
        return 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        private WeakReference<Bitmap> bitmapRef;


        public ImageView thumbnailView;
        public ImageView ivLeftArrow;
        public ImageView ivLeftBottomArrow;
        public ImageView ivRightArrow;
        public ImageView ivRightBottomArrow;
        public RelativeLayout thumbnailRlView;
//        public CardView mView;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            thumbnailView = itemView.findViewById(R.id.superplayer_iv);
            thumbnailRlView = itemView.findViewById(R.id.superplayer_rl_thumbnail);
            ivLeftArrow = itemView.findViewById(R.id.superplayer_iv_left_arrow);
            ivLeftBottomArrow = itemView.findViewById(R.id.superplayer_iv_left_bottom_arrow);
            ivRightArrow = itemView.findViewById(R.id.superplayer_iv_right_arrow);
            ivRightBottomArrow = itemView.findViewById(R.id.superplayer_iv_right_bottom_arrow);
//            mView = itemView.findViewById(R.id.superplayer_card_view);
        }

        public void bind(@NonNull ThumbnailEntity info) {
            // 获取 Bitmap 对象
            Bitmap bitmap = bitmapRef != null ? bitmapRef.get() : null;
            if (bitmap == null) {
                bitmap = info.getBitmap();
                bitmapRef = new WeakReference<>(bitmap);
            }

            // 设置 Bitmap 对象
            thumbnailView.setImageBitmap(bitmap);
        }

        public void recycleBitmap() {
            ImageView imageView = thumbnailView;
            if (imageView != null) {
                Bitmap bitmap = bitmapRef != null ? bitmapRef.get() : null;
                if (bitmap != null && !bitmap.isRecycled()) {
                    imageView.setImageBitmap(null); // 清除 ImageView 中的 Bitmap 对象
                    bitmap.recycle(); // 释放 Bitmap 对象占用的内存
                    bitmapRef = null;
                }
            }
        }
    }


    //放大动画
    private void ofFloatAnimator(View view, float start, float end) {
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.setDuration(0);//动画时间
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", start, end);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", start, end);
        animatorSet.setInterpolator(new DecelerateInterpolator());//插值器

        float startY = view.getTranslationY();
        float endY = startY - 20;

        // 创建一个往上移动的动画，将 View 的 Y 坐标逐渐减小
        ObjectAnimator translateAnimator = null;
        if (end > 1.0f) {
            translateAnimator = ObjectAnimator.ofFloat(view, "translationY", startY, endY);
        } else {
            translateAnimator = ObjectAnimator.ofFloat(view, "translationY", 0.0f, 0.0f);
        }
        translateAnimator.setDuration(0);
        translateAnimator.setInterpolator(new DecelerateInterpolator());

//        animatorSet.play(scaleX).with(scaleY).with(translateAnimator);//组合动画,同时基于x和y轴放大
        animatorSet.playTogether(scaleX, scaleY, translateAnimator);
        animatorSet.start();
    }

}