apply plugin: 'com.android.library'
//apply from: 'maven-deployer.gradle'

android {
    compileSdkVersion rootProject.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        versionCode 6
        versionName "1.0.6"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation "com.android.support:appcompat-v7:20.0.0"
    implementation 'org.jetbrains:annotations:15.0'
    testImplementation "junit:junit:4.12"
    androidTestImplementation "com.android.support.test:runner:1.0.2"
    androidTestImplementation "com.android.support.test.espresso:espresso-core:3.0.2"
    implementation project(':superplayerkit')

}

//apply from: 'jcenter-deployer.gradle'
