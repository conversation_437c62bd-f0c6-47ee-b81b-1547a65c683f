apply plugin: 'com.android.library'
//apply plugin: 'com.github.ben-manes.versions'
//apply plugin: 'com.github.hierynomus.license'

android {
  compileSdkVersion rootProject.ext.compileSdkVersion

  defaultConfig {
    minSdkVersion rootProject.ext.minSdkVersion

    versionCode rootProject.ext.versionCode
    versionName rootProject.ext.versionName

    testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

  }
  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    }
  }
}

dependencies {
//  implementation "androidx.viewpager:viewpager:${ANDROIDX_BASE_VERSION}"
//  implementation "androidx.fragment:fragment:${ANDROIDX_BASE_VERSION}"
}

//license {
//
//  sourceSets {
//    main.java.srcDirs = android.sourceSets.main.java.srcDirs
//    main.resources.srcDirs = android.sourceSets.main.resources.srcDirs
//  }
//
//  excludes(["**/*.xml"])
//
//}

//pkginfo.name = ARTIFACT_NAME
//apply from: "${project.rootDir}/publish.gradle"
