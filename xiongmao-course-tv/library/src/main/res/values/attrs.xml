<?xml version="1.0" encoding="utf-8"?>
<resources>
  <declare-styleable name="stl_SmartTabLayout">
    <attr name="stl_indicatorAlwaysInCenter" format="boolean"/>
    <attr name="stl_indicatorWithoutPadding" format="boolean"/>
    <attr name="stl_indicatorInFront" format="boolean"/>
    <attr name="stl_indicatorInterpolation" format="enum">
      <enum name="smart" value="0"/>
      <enum name="linear" value="1"/>
    </attr>
    <attr name="stl_indicatorGravity" format="enum">
      <enum name="bottom" value="0"/>
      <enum name="top" value="1"/>
      <enum name="center" value="2"/>
    </attr>
    <attr name="stl_indicatorColor" format="color"/>
    <attr name="stl_indicatorColors" format="reference"/>
    <attr name="stl_indicatorThickness" format="dimension"/>
    <attr name="stl_indicatorWidth" format="dimension">
      <enum name="auto" value="-1"/>
    </attr>
    <attr name="stl_indicatorCornerRadius" format="dimension"/>
    <attr name="stl_overlineColor" format="color"/>
    <attr name="stl_overlineThickness" format="dimension"/>
    <attr name="stl_underlineColor" format="color"/>
    <attr name="stl_underlineThickness" format="dimension"/>
    <attr name="stl_dividerColor" format="color"/>
    <attr name="stl_dividerColors" format="reference"/>
    <attr name="stl_dividerThickness" format="dimension"/>
    <attr name="stl_defaultTabBackground" format="reference"/>
    <attr name="stl_defaultTabTextAllCaps" format="boolean"/>
    <attr name="stl_defaultTabTextColor" format="color|reference"/>
    <attr name="stl_defaultTabTextSize" format="dimension"/>
    <attr name="stl_defaultTabTextHorizontalPadding" format="dimension"/>
    <attr name="stl_defaultTabTextMinWidth" format="dimension"/>
    <attr name="stl_customTabTextLayoutId" format="reference"/>
    <attr name="stl_customTabTextViewId" format="reference"/>
    <attr name="stl_distributeEvenly" format="boolean"/>
    <attr name="stl_clickable" format="boolean"/>
    <attr name="stl_titleOffset" format="dimension">
      <enum name="auto_center" value="-1"/>
    </attr>
    <attr name="stl_drawDecorationAfterTab" format="boolean"/>
  </declare-styleable>
</resources>
