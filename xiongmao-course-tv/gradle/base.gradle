android {
    compileSdkVersion androidCompileSdkVersion
    buildToolsVersion androidBuildToolsVersion

    defaultConfig {
        minSdkVersion androidMinSdkVersion
        targetSdkVersion androidTargetSdkVersion
        versionCode 21
        versionName PROJ_VERSION
    }
    buildTypes.all {
        it.buildConfigField "String", "VERSION_NAME", "\"${PROJ_VERSION}\""
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])
    implementation androidDependencies.annotation
}