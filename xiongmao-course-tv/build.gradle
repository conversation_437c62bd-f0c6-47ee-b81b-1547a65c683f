// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlin_version = '1.6.0'

    repositories {
        //公司配置
        google()
        mavenCentral()
        //公司配置
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://jitpack.io' }
    }
    dependencies {
        classpath "com.android.tools.build:gradle:4.2.0"
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        //公司配置
        google()
        mavenCentral()
//        maven { url 'http://jenkins.xiongmaoxitong.com/repository/aliyun-public/' }
//        maven { url 'http://jenkins.xiongmaoxitong.com/repository/aliyun-google/' }
//        maven { url 'http://jenkins.xiongmaoxitong.com/repository/maven-jitpack/' }
        //公司配置
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://jitpack.io' }
        //百家云
        maven { url 'http://nexus.baijiayun.com/nexus/content/groups/android-public/' }
        maven { url 'https://git2.baijiashilian.com/open-android/maven/raw/master/' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}


ext {
    compileSdkVersion = 31
    buildToolsVersion = "30.0.2"
    supportSdkVersion = "26.0.0"
    minSdkVersion = 26
    targetSdkVersion = 29
    versionCode = 1
    versionName = "v1.0"
    imSdk = 'com.tencent.imsdk:imsdk:4.9.1'
    liteavSdk = "com.tencent.liteav:LiteAVSDK_Player:11.3.0.13176"
//    liteavSdk = "com.tencent.liteav:LiteAVSDK_Professional:latest.release"
//    liteavSdk = "com.tencent.liteav:LiteAVSDK_Professional:10.6.0.11182"
//    liteavSdk = "com.tencent.liteav:LiteAVSDK_Professional:9.5.11583"
}
